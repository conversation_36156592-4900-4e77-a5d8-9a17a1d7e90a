import { PersistedClient, PersistRetryer, Persister } from '@tanstack/query-persist-client-core';

interface Storage {
    getItem: (key: string) => string | null;
    setItem: (key: string, value: string) => void;
    removeItem: (key: string) => void;
}
interface CreateSyncStoragePersisterOptions {
    /** The storage client used for setting and retrieving items from cache.
     * For SSR pass in `undefined`. Note that window.localStorage can be
     * `null` in Android WebViews depending on how they are configured.
     */
    storage: Storage | undefined | null;
    /** The key to use when storing the cache */
    key?: string;
    /** To avoid spamming,
     * pass a time in ms to throttle saving the cache to disk */
    throttleTime?: number;
    /**
     * How to serialize the data to storage.
     * @default `JSON.stringify`
     */
    serialize?: (client: PersistedClient) => string;
    /**
     * How to deserialize the data from storage.
     * @default `JSON.parse`
     */
    deserialize?: (cachedString: string) => PersistedClient;
    retry?: PersistRetryer;
}
/**
 * @deprecated use `createAsyncStoragePersister` from `@tanstack/query-async-storage-persister` instead.
 */
declare function createSyncStoragePersister({ storage, key, throttleTime, serialize, deserialize, retry, }: CreateSyncStoragePersisterOptions): Persister;

export { createSyncStoragePersister };
