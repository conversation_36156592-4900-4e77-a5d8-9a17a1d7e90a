import e from"../server/react-server/getConfig.js";import n from"../shared/use.js";function r(r){return function(e,r){try{return n(r)}catch(n){throw n instanceof TypeError&&n.message.includes("Cannot read properties of null (reading 'use')")?new Error(`\`${e}\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`,{cause:n}):n}}(r,e())}export{r as default};
