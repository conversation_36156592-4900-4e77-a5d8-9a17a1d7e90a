{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-progress.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MuiProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call MuiProgress() from the server but MuiProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/mui-progress.tsx <module evaluation>\",\n    \"MuiProgress\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-progress.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MuiProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call MuiProgress() from the server but MuiProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/mui-progress.tsx\",\n    \"MuiProgress\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,mDACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/%5Blocale%5D/progress/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { getTranslations } from 'next-intl/server';\nimport { generatePageMetadata } from \"@/lib/seo/utils\";\nimport { MuiProgress } from \"@/components/pages/mui-progress\";\n\ntype Props = {\n  params: { locale: string };\n};\n\nexport async function generateMetadata({ params }: Props): Promise<Metadata> {\n  const { locale } = await params;\n  const t = await getTranslations({ locale, namespace: 'progress' });\n\n  return generatePageMetadata({\n    title: t('title'),\n    description: t('subtitle'),\n    path: \"/progress\",\n  });\n}\n\nexport default function ProgressPage({ params }: Props) {\n  return <MuiProgress />;\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAS;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAW;IAEhE,OAAO,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1B,OAAO,EAAE;QACT,aAAa,EAAE;QACf,MAAM;IACR;AACF;AAEe,SAAS,aAAa,EAAE,MAAM,EAAS;IACpD,qBAAO,8OAAC,8IAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}]}