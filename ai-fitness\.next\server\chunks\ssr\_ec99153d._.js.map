{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/seo/utils.ts"], "sourcesContent": ["import { Metadata } from 'next';\nimport { siteConfig } from './config';\n\n// Generate canonical URL\nexport function generateCanonicalUrl(path: string): string {\n  const baseUrl = siteConfig.url;\n  const cleanPath = path.startsWith('/') ? path : `/${path}`;\n  return `${baseUrl}${cleanPath}`;\n}\n\n// Generate Open Graph image URL\nexport function generateOGImageUrl(title: string, description?: string): string {\n  const baseUrl = siteConfig.url;\n  const params = new URLSearchParams();\n  params.set('title', title);\n  if (description) {\n    params.set('description', description);\n  }\n  return `${baseUrl}/api/og?${params.toString()}`;\n}\n\n// Truncate text for meta descriptions\nexport function truncateText(text: string, maxLength: number = 160): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength - 3).trim() + '...';\n}\n\n// Generate keywords from content\nexport function generateKeywords(content: string, additionalKeywords: string[] = []): string[] {\n  const baseKeywords = siteConfig.keywords;\n  const contentWords = content\n    .toLowerCase()\n    .replace(/[^\\w\\s]/g, ' ')\n    .split(/\\s+/)\n    .filter(word => word.length > 3)\n    .slice(0, 10);\n  \n  return [...baseKeywords, ...additionalKeywords, ...contentWords];\n}\n\n// Generate exercise-specific metadata\nexport function generateExerciseMetadata(exercise: {\n  name: string;\n  description: string;\n  targetMuscles?: string[];\n  equipment?: string;\n  difficulty?: string;\n  category?: string;\n}): Metadata {\n  const title = `${exercise.name} - Exercise Guide | ${siteConfig.name}`;\n  const description = truncateText(\n    `Learn how to perform ${exercise.name} correctly. ${exercise.description} Target muscles: ${exercise.targetMuscles?.join(', ') || 'Multiple'}. Equipment: ${exercise.equipment || 'Bodyweight'}.`\n  );\n  \n  const keywords = generateKeywords(\n    `${exercise.name} ${exercise.description}`,\n    [\n      exercise.category || 'exercise',\n      exercise.equipment || 'bodyweight',\n      exercise.difficulty || 'fitness',\n      ...(exercise.targetMuscles || [])\n    ]\n  );\n\n  return {\n    title,\n    description,\n    keywords,\n    openGraph: {\n      title,\n      description,\n      type: 'article',\n      images: [\n        {\n          url: generateOGImageUrl(exercise.name, `${exercise.category} exercise for ${exercise.targetMuscles?.join(', ')}`),\n          width: 1200,\n          height: 630,\n          alt: `${exercise.name} exercise demonstration`\n        }\n      ]\n    },\n    twitter: {\n      title,\n      description,\n      card: 'summary_large_image'\n    }\n  };\n}\n\n// Generate workout-specific metadata\nexport function generateWorkoutMetadata(workout: {\n  title: string;\n  description: string;\n  duration?: number;\n  difficulty?: string;\n  type?: string;\n  targetMuscles?: string[];\n}): Metadata {\n  const title = `${workout.title} - Workout Program | ${siteConfig.name}`;\n  const description = truncateText(\n    `${workout.description} Duration: ${workout.duration || 'Flexible'} minutes. Difficulty: ${workout.difficulty || 'All levels'}. Type: ${workout.type || 'General fitness'}.`\n  );\n  \n  const keywords = generateKeywords(\n    `${workout.title} ${workout.description}`,\n    [\n      'workout program',\n      'fitness plan',\n      workout.type || 'training',\n      workout.difficulty || 'exercise',\n      ...(workout.targetMuscles || [])\n    ]\n  );\n\n  return {\n    title,\n    description,\n    keywords,\n    openGraph: {\n      title,\n      description,\n      type: 'article',\n      images: [\n        {\n          url: generateOGImageUrl(workout.title, `${workout.type} workout program - ${workout.difficulty} level`),\n          width: 1200,\n          height: 630,\n          alt: `${workout.title} workout program`\n        }\n      ]\n    },\n    twitter: {\n      title,\n      description,\n      card: 'summary_large_image'\n    }\n  };\n}\n\n// Generate FAQ schema from common questions\nexport function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {\n  return {\n    '@context': 'https://schema.org',\n    '@type': 'FAQPage',\n    mainEntity: faqs.map(faq => ({\n      '@type': 'Question',\n      name: faq.question,\n      acceptedAnswer: {\n        '@type': 'Answer',\n        text: faq.answer\n      }\n    }))\n  };\n}\n\n// Common fitness-related FAQs\nexport const commonFitnessQAs = [\n  {\n    question: \"How often should I work out?\",\n    answer: \"For general fitness, aim for at least 150 minutes of moderate-intensity exercise or 75 minutes of vigorous-intensity exercise per week, plus muscle-strengthening activities on 2 or more days per week.\"\n  },\n  {\n    question: \"What's the best time to exercise?\",\n    answer: \"The best time to exercise is when you can be consistent. Some people prefer morning workouts for energy, while others prefer evening sessions. Choose a time that fits your schedule and stick to it.\"\n  },\n  {\n    question: \"Do I need equipment to get fit?\",\n    answer: \"No, you can achieve great fitness results with bodyweight exercises. However, equipment like dumbbells, resistance bands, or kettlebells can add variety and progression to your workouts.\"\n  },\n  {\n    question: \"How long before I see results?\",\n    answer: \"You may notice improvements in energy and mood within a few days. Physical changes typically become noticeable after 2-4 weeks of consistent exercise, with significant changes after 8-12 weeks.\"\n  },\n  {\n    question: \"Should I do cardio or strength training?\",\n    answer: \"Both are important for overall fitness. Cardio improves heart health and endurance, while strength training builds muscle and bone density. A balanced program includes both types of exercise.\"\n  }\n];\n\n// Generate rich snippets for search results\nexport function generateRichSnippets(type: 'exercise' | 'workout' | 'article', data: any) {\n  switch (type) {\n    case 'exercise':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'HowTo',\n        name: `How to do ${data.name}`,\n        description: data.description,\n        totalTime: data.duration ? `PT${data.duration}M` : undefined,\n        supply: data.equipment ? [data.equipment] : undefined,\n        tool: data.equipment ? [data.equipment] : undefined,\n        step: data.instructions?.map((instruction: string, index: number) => ({\n          '@type': 'HowToStep',\n          position: index + 1,\n          text: instruction\n        }))\n      };\n    \n    case 'workout':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'ExercisePlan',\n        name: data.title,\n        description: data.description,\n        category: 'Fitness',\n        exerciseType: data.type,\n        intensity: data.difficulty,\n        workload: data.duration ? `${data.duration} minutes` : undefined\n      };\n    \n    default:\n      return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;;AAGO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAU,2HAAA,CAAA,aAAU,CAAC,GAAG;IAC9B,MAAM,YAAY,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAC1D,OAAO,GAAG,UAAU,WAAW;AACjC;AAGO,SAAS,mBAAmB,KAAa,EAAE,WAAoB;IACpE,MAAM,UAAU,2HAAA,CAAA,aAAU,CAAC,GAAG;IAC9B,MAAM,SAAS,IAAI;IACnB,OAAO,GAAG,CAAC,SAAS;IACpB,IAAI,aAAa;QACf,OAAO,GAAG,CAAC,eAAe;IAC5B;IACA,OAAO,GAAG,QAAQ,QAAQ,EAAE,OAAO,QAAQ,IAAI;AACjD;AAGO,SAAS,aAAa,IAAY,EAAE,YAAoB,GAAG;IAChE,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,GAAG,IAAI,KAAK;AACnD;AAGO,SAAS,iBAAiB,OAAe,EAAE,qBAA+B,EAAE;IACjF,MAAM,eAAe,2HAAA,CAAA,aAAU,CAAC,QAAQ;IACxC,MAAM,eAAe,QAClB,WAAW,GACX,OAAO,CAAC,YAAY,KACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG;IAEZ,OAAO;WAAI;WAAiB;WAAuB;KAAa;AAClE;AAGO,SAAS,yBAAyB,QAOxC;IACC,MAAM,QAAQ,GAAG,SAAS,IAAI,CAAC,oBAAoB,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACtE,MAAM,cAAc,aAClB,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,SAAS,WAAW,CAAC,iBAAiB,EAAE,SAAS,aAAa,EAAE,KAAK,SAAS,WAAW,aAAa,EAAE,SAAS,SAAS,IAAI,aAAa,CAAC,CAAC;IAGnM,MAAM,WAAW,iBACf,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,WAAW,EAAE,EAC1C;QACE,SAAS,QAAQ,IAAI;QACrB,SAAS,SAAS,IAAI;QACtB,SAAS,UAAU,IAAI;WACnB,SAAS,aAAa,IAAI,EAAE;KACjC;IAGH,OAAO;QACL;QACA;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,mBAAmB,SAAS,IAAI,EAAE,GAAG,SAAS,QAAQ,CAAC,cAAc,EAAE,SAAS,aAAa,EAAE,KAAK,OAAO;oBAChH,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,SAAS,IAAI,CAAC,uBAAuB,CAAC;gBAChD;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,MAAM;QACR;IACF;AACF;AAGO,SAAS,wBAAwB,OAOvC;IACC,MAAM,QAAQ,GAAG,QAAQ,KAAK,CAAC,qBAAqB,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACvE,MAAM,cAAc,aAClB,GAAG,QAAQ,WAAW,CAAC,WAAW,EAAE,QAAQ,QAAQ,IAAI,WAAW,sBAAsB,EAAE,QAAQ,UAAU,IAAI,aAAa,QAAQ,EAAE,QAAQ,IAAI,IAAI,kBAAkB,CAAC,CAAC;IAG9K,MAAM,WAAW,iBACf,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,EACzC;QACE;QACA;QACA,QAAQ,IAAI,IAAI;QAChB,QAAQ,UAAU,IAAI;WAClB,QAAQ,aAAa,IAAI,EAAE;KAChC;IAGH,OAAO;QACL;QACA;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,mBAAmB,QAAQ,KAAK,EAAE,GAAG,QAAQ,IAAI,CAAC,mBAAmB,EAAE,QAAQ,UAAU,CAAC,MAAM,CAAC;oBACtG,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,QAAQ,KAAK,CAAC,gBAAgB,CAAC;gBACzC;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,MAAM;QACR;IACF;AACF;AAGO,SAAS,kBAAkB,IAAiD;IACjF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,SAAS;gBACT,MAAM,IAAI,QAAQ;gBAClB,gBAAgB;oBACd,SAAS;oBACT,MAAM,IAAI,MAAM;gBAClB;YACF,CAAC;IACH;AACF;AAGO,MAAM,mBAAmB;IAC9B;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAGM,SAAS,qBAAqB,IAAwC,EAAE,IAAS;IACtF,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;gBAC9B,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,QAAQ,GAAG,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG;gBACnD,QAAQ,KAAK,SAAS,GAAG;oBAAC,KAAK,SAAS;iBAAC,GAAG;gBAC5C,MAAM,KAAK,SAAS,GAAG;oBAAC,KAAK,SAAS;iBAAC,GAAG;gBAC1C,MAAM,KAAK,YAAY,EAAE,IAAI,CAAC,aAAqB,QAAkB,CAAC;wBACpE,SAAS;wBACT,UAAU,QAAQ;wBAClB,MAAM;oBACR,CAAC;YACH;QAEF,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,MAAM,KAAK,KAAK;gBAChB,aAAa,KAAK,WAAW;gBAC7B,UAAU;gBACV,cAAc,KAAK,IAAI;gBACvB,WAAW,KAAK,UAAU;gBAC1B,UAAU,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,CAAC,QAAQ,CAAC,GAAG;YACzD;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/page.tsx"], "sourcesContent": ["import { Metada<PERSON> } from 'next';\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Navigation } from \"@/components/Navigation\"\nimport { <PERSON><PERSON><PERSON>, <PERSON>, BarChart3, BookO<PERSON> } from \"lucide-react\"\nimport { generatePageMetadata, commonFitnessQAs } from \"@/lib/seo/utils\"\nimport { FAQStructuredData } from \"@/components/seo/structured-data\"\nimport { Main, Section, SemanticHeader } from \"@/components/seo/semantic-html\"\nimport { ExerciseCategoryLinks, WorkoutProgramLinks, ContextualNav } from \"@/components/seo/internal-links\"\n\nexport const metadata: Metadata = generatePageMetadata({\n  title: \"AI-fitness-singles - Smart Fitness Platform for Singles\",\n  description: \"Transform your fitness journey with AI-powered workout plans, comprehensive exercise database, and detailed progress tracking. Perfect for singles looking to achieve their fitness goals.\",\n  path: \"/\",\n});\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n      <Main>\n        {/* Hero Section */}\n        <Section className=\"relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100\" ariaLabel=\"Hero section\">\n          <div className=\"container mx-auto px-4 py-20 sm:py-32\">\n            <div className=\"text-center\">\n              <SemanticHeader level={1} className=\"text-4xl sm:text-6xl font-bold text-gray-900 mb-6\">\n                AI-fitness-singles\n                <span className=\"block text-blue-600\">Smart Fitness Platform</span>\n              </SemanticHeader>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Create custom workout plans, access comprehensive exercise database,\n              and track your fitness progress with detailed analytics.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\">\n                <Play className=\"mr-2 h-5 w-5\" />\n                Start Training\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" className=\"border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3\">\n                <BookOpen className=\"mr-2 h-5 w-5\" />\n                Browse Exercises\n              </Button>\n            </div>\n          </div>\n        </div>\n        </Section>\n\n        {/* Core Features Section */}\n        <Section className=\"py-20 bg-white\" ariaLabel=\"Core features section\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"text-center mb-16\">\n              <SemanticHeader level={2} className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n                Everything You Need to Train\n              </SemanticHeader>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Comprehensive fitness platform with workout builder, exercise library, and progress tracking.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <Card className=\"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n              <CardHeader>\n                <div className=\"mx-auto w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n                  <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n                </div>\n                <CardTitle className=\"text-xl font-semibold text-gray-900\">Workout Builder</CardTitle>\n                <CardDescription className=\"text-gray-600\">\n                  Create custom workout plans with our intuitive drag-and-drop builder.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n              <CardHeader>\n                <div className=\"mx-auto w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n                  <BookOpen className=\"h-8 w-8 text-green-600\" />\n                </div>\n                <CardTitle className=\"text-xl font-semibold text-gray-900\">Exercise Database</CardTitle>\n                <CardDescription className=\"text-gray-600\">\n                  Access thousands of exercises with detailed instructions and video guides.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n              <CardHeader>\n                <div className=\"mx-auto w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                  <BarChart3 className=\"h-8 w-8 text-purple-600\" />\n                </div>\n                <CardTitle className=\"text-xl font-semibold text-gray-900\">Progress Tracking</CardTitle>\n                <CardDescription className=\"text-gray-600\">\n                  Monitor your improvements with detailed analytics and visual reports.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n              <CardHeader>\n                <div className=\"mx-auto w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mb-4\">\n                  <Play className=\"h-8 w-8 text-orange-600\" />\n                </div>\n                <CardTitle className=\"text-xl font-semibold text-gray-900\">Workout Sessions</CardTitle>\n                <CardDescription className=\"text-gray-600\">\n                  Execute your workouts with guided sessions and real-time tracking.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n          </div>\n        </div>\n        </Section>\n\n        {/* Exercise Categories Section */}\n        <Section className=\"py-20 bg-gray-50\" ariaLabel=\"Exercise categories\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"text-center mb-12\">\n              <SemanticHeader level={2} className=\"text-3xl font-bold text-gray-900 mb-4\">\n                Explore Exercise Categories\n              </SemanticHeader>\n              <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n                Discover exercises organized by type, muscle group, and equipment to build your perfect workout routine.\n              </p>\n            </div>\n            <ExerciseCategoryLinks />\n          </div>\n        </Section>\n\n        {/* Workout Programs Section */}\n        <Section className=\"py-20 bg-white\" ariaLabel=\"Workout programs\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"text-center mb-12\">\n              <SemanticHeader level={2} className=\"text-3xl font-bold text-gray-900 mb-4\">\n                AI-Powered Workout Programs\n              </SemanticHeader>\n              <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n                Choose from scientifically designed workout programs tailored to your fitness level and goals.\n              </p>\n            </div>\n            <WorkoutProgramLinks />\n          </div>\n        </Section>\n\n        {/* Quick Start Section */}\n        <Section className=\"py-20 bg-gray-50\" ariaLabel=\"Quick start section\">\n          <div className=\"container mx-auto px-4 text-center\">\n            <SemanticHeader level={2} className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n              Start Training Today\n            </SemanticHeader>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Build your first workout plan in minutes and begin your fitness journey.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\">\n              Create Workout Plan\n            </Button>\n            <Button variant=\"outline\" size=\"lg\" className=\"border-gray-300 text-gray-700 hover:bg-gray-100 px-8 py-3\">\n              Explore Exercises\n            </Button>\n          </div>\n        </div>\n        </Section>\n\n        {/* Contextual Navigation */}\n        <Section className=\"py-16 bg-white\" ariaLabel=\"Related pages\">\n          <div className=\"container mx-auto px-4\">\n            <ContextualNav currentPage=\"home\" />\n          </div>\n        </Section>\n      </Main>\n\n      {/* SEO: FAQ Structured Data */}\n      <FAQStructuredData faqs={commonFitnessQAs} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEO,MAAM,WAAqB,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;IACrD,OAAO;IACP,aAAa;IACb,MAAM;AACR;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC,6IAAA,CAAA,OAAI;;kCAEH,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAwE,WAAU;kCACnG,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6IAAA,CAAA,iBAAc;wCAAC,OAAO;wCAAG,WAAU;;4CAAoD;0DAEtF,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6IAAA,CAAA,iBAAc;4CAAC,OAAO;4CAAG,WAAU;sDAAoD;;;;;;sDAG1F,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsC;;;;;;kEAC3D,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAgB;;;;;;;;;;;;;;;;;sDAM/C,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsC;;;;;;kEAC3D,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAgB;;;;;;;;;;;;;;;;;sDAM/C,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsC;;;;;;kEAC3D,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAgB;;;;;;;;;;;;;;;;;sDAM/C,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsC;;;;;;kEAC3D,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnD,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAmB,WAAU;kCAC9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6IAAA,CAAA,iBAAc;4CAAC,OAAO;4CAAG,WAAU;sDAAwC;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAIzD,8OAAC,8IAAA,CAAA,wBAAqB;;;;;;;;;;;;;;;;kCAK1B,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6IAAA,CAAA,iBAAc;4CAAC,OAAO;4CAAG,WAAU;sDAAwC;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAIzD,8OAAC,8IAAA,CAAA,sBAAmB;;;;;;;;;;;;;;;;kCAKxB,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAmB,WAAU;kCAC9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6IAAA,CAAA,iBAAc;oCAAC,OAAO;oCAAG,WAAU;8CAAoD;;;;;;8CAG1F,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAqD;;;;;;sDAGjF,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAA4D;;;;;;;;;;;;;;;;;;;;;;;kCAQ9G,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8IAAA,CAAA,gBAAa;gCAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;0BAMjC,8OAAC,+IAAA,CAAA,oBAAiB;gBAAC,MAAM,0HAAA,CAAA,mBAAgB;;;;;;;;;;;;AAG/C", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "file": "dumbbell.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/lucide-react/src/icons/dumbbell.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M17.596 12.768a2 2 0 1 0 2.829-2.829l-1.768-1.767a2 2 0 0 0 2.828-2.829l-2.828-2.828a2 2 0 0 0-2.829 2.828l-1.767-1.768a2 2 0 1 0-2.829 2.829z',\n      key: '9m4mmf',\n    },\n  ],\n  ['path', { d: 'm2.5 21.5 1.4-1.4', key: '17g3f0' }],\n  ['path', { d: 'm20.1 3.9 1.4-1.4', key: '1qn309' }],\n  [\n    'path',\n    {\n      d: 'M5.343 21.485a2 2 0 1 0 2.829-2.828l1.767 1.768a2 2 0 1 0 2.829-2.829l-6.364-6.364a2 2 0 1 0-2.829 2.829l1.768 1.767a2 2 0 0 0-2.828 2.829z',\n      key: '1t2c92',\n    },\n  ],\n  ['path', { d: 'm9.6 14.4 4.8-4.8', key: '6umqxw' }],\n];\n\n/**\n * @component @name Dumbbell\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcuNTk2IDEyLjc2OGEyIDIgMCAxIDAgMi44MjktMi44MjlsLTEuNzY4LTEuNzY3YTIgMiAwIDAgMCAyLjgyOC0yLjgyOWwtMi44MjgtMi44MjhhMiAyIDAgMCAwLTIuODI5IDIuODI4bC0xLjc2Ny0xLjc2OGEyIDIgMCAxIDAtMi44MjkgMi44Mjl6IiAvPgogIDxwYXRoIGQ9Im0yLjUgMjEuNSAxLjQtMS40IiAvPgogIDxwYXRoIGQ9Im0yMC4xIDMuOSAxLjQtMS40IiAvPgogIDxwYXRoIGQ9Ik01LjM0MyAyMS40ODVhMiAyIDAgMSAwIDIuODI5LTIuODI4bDEuNzY3IDEuNzY4YTIgMiAwIDEgMCAyLjgyOS0yLjgyOWwtNi4zNjQtNi4zNjRhMiAyIDAgMSAwLTIuODI5IDIuODI5bDEuNzY4IDEuNzY3YTIgMiAwIDAgMC0yLjgyOCAyLjgyOXoiIC8+CiAgPHBhdGggZD0ibTkuNiAxNC40IDQuOC00LjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/dumbbell\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Dumbbell = createLucideIcon('dumbbell', __iconNode);\n\nexport default Dumbbell;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClD;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,SAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,oBAAsB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "file": "chart-column.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/lucide-react/src/icons/chart-column.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['path', { d: 'M18 17V9', key: '2bz60n' }],\n  ['path', { d: 'M13 17V5', key: '1frdt8' }],\n  ['path', { d: 'M8 17v-3', key: '17ska0' }],\n];\n\n/**\n * @component @name ChartColumn\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTE4IDE3VjkiIC8+CiAgPHBhdGggZD0iTTEzIDE3VjUiIC8+CiAgPHBhdGggZD0iTTggMTd2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartColumn = createLucideIcon('chart-column', __iconNode);\n\nexport default ChartColumn;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "file": "book-open.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/lucide-react/src/icons/book-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 7v14', key: '1akyts' }],\n  [\n    'path',\n    {\n      d: 'M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z',\n      key: 'ruj8y',\n    },\n  ],\n];\n\n/**\n * @component @name BookOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgN3YxNCIgLz4KICA8cGF0aCBkPSJNMyAxOGExIDEgMCAwIDEtMS0xVjRhMSAxIDAgMCAxIDEtMWg1YTQgNCAwIDAgMSA0IDQgNCA0IDAgMCAxIDQtNGg1YTEgMSAwIDAgMSAxIDF2MTNhMSAxIDAgMCAxLTEgMWgtNmEzIDMgMCAwIDAtMyAzIDMgMyAwIDAgMC0zLTN6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpen = createLucideIcon('book-open', __iconNode);\n\nexport default BookOpen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}