/**
 * Public Exercises API Route
 * Calls workout-cool server action directly for public exercises
 */

import { NextRequest, NextResponse } from 'next/server';

// Mock data for testing - replace with actual workout-cool integration
const mockExercises = [
  {
    id: '1',
    name: 'Push-ups',
    nameEn: 'Push-ups',
    description: 'Classic bodyweight exercise for chest, shoulders, and triceps',
    descriptionEn: 'Classic bodyweight exercise for chest, shoulders, and triceps',
    instructions: 'Start in plank position, lower body to ground, push back up',
    instructionsEn: 'Start in plank position, lower body to ground, push back up',
    tips: 'Keep core tight, maintain straight line from head to heels',
    tipsEn: 'Keep core tight, maintain straight line from head to heels',
    imageUrl: '/images/exercises/pushups.jpg',
    videoUrl: '/videos/exercises/pushups.mp4',
    attributes: [
      {
        id: '1',
        attributeName: { id: '1', name: 'Muscle Group', nameEn: 'Muscle Group' },
        attributeValue: { id: '1', value: 'Chest', valueEn: 'Chest' },
      },
      {
        id: '2',
        attributeName: { id: '2', name: 'Equipment', nameEn: 'Equipment' },
        attributeValue: { id: '2', value: 'None', valueEn: 'None' },
      },
    ],
  },
  {
    id: '2',
    name: 'Squats',
    nameEn: 'Squats',
    description: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings',
    descriptionEn: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings',
    instructions: 'Stand with feet shoulder-width apart, lower hips back and down, return to standing',
    instructionsEn: 'Stand with feet shoulder-width apart, lower hips back and down, return to standing',
    tips: 'Keep knees aligned with toes, chest up, weight in heels',
    tipsEn: 'Keep knees aligned with toes, chest up, weight in heels',
    imageUrl: '/images/exercises/squats.jpg',
    videoUrl: '/videos/exercises/squats.mp4',
    attributes: [
      {
        id: '3',
        attributeName: { id: '1', name: 'Muscle Group', nameEn: 'Muscle Group' },
        attributeValue: { id: '3', value: 'Legs', valueEn: 'Legs' },
      },
      {
        id: '4',
        attributeName: { id: '2', name: 'Equipment', nameEn: 'Equipment' },
        attributeValue: { id: '2', value: 'None', valueEn: 'None' },
      },
    ],
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');

    let filteredExercises = mockExercises;

    // Apply search filter if provided
    if (search) {
      filteredExercises = mockExercises.filter(exercise =>
        exercise.name.toLowerCase().includes(search.toLowerCase()) ||
        exercise.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // For now, return mock data
    // TODO: Integrate with workout-cool server actions when available
    return NextResponse.json(filteredExercises);
  } catch (error) {
    console.error('Error fetching public exercises:', error);
    return NextResponse.json(
      { error: 'Failed to fetch public exercises' },
      { status: 500 }
    );
  }
}
