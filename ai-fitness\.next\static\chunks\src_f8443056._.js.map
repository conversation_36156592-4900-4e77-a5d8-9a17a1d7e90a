{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar<PERSON>hart3, User, LogOut, Wifi, Wif<PERSON>Off, <PERSON> } from \"lucide-react\"\nimport { useAuth, useSignOut } from \"@/lib/hooks/use-auth\"\nimport { \n  useOfflineState, \n  useUnreadNotificationCount, \n  useHasPendingSync \n} from \"@/lib/store/app-store\"\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const signOutMutation = useSignOut()\n  \n  // State management hooks\n  const offlineState = useOfflineState()\n  const unreadCount = useUnreadNotificationCount()\n  const hasPendingSync = useHasPendingSync()\n\n  const toggleMenu = () => setIsOpen(!isOpen)\n\n  const handleSignOut = async () => {\n    try {\n      await signOutMutation.mutateAsync()\n    } catch (error) {\n      console.error('Sign out failed:', error)\n    }\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">AI-fitness-singles</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/workouts\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Workouts\n            </Link>\n            <Link\n              href=\"/exercises\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Exercises\n            </Link>\n            <Link\n              href=\"/progress\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Progress\n            </Link>\n\n            {/* Status Indicators */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Online/Offline Status */}\n              <div className=\"flex items-center space-x-1\">\n                {offlineState.isOnline ? (\n                  <Wifi className=\"h-4 w-4 text-green-600\" />\n                ) : (\n                  <WifiOff className=\"h-4 w-4 text-red-600\" />\n                )}\n                {hasPendingSync && (\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {offlineState.pendingSync.length}\n                  </Badge>\n                )}\n              </div>\n\n              {/* Notifications */}\n              <div className=\"relative\">\n                <Bell className=\"h-4 w-4 text-gray-600\" />\n                {unreadCount > 0 && (\n                  <Badge \n                    variant=\"destructive\" \n                    className=\"absolute -top-2 -right-2 h-4 w-4 p-0 text-xs flex items-center justify-center\"\n                  >\n                    {unreadCount > 9 ? '9+' : unreadCount}\n                  </Badge>\n                )}\n              </div>\n            </div>\n\n            {/* Auth Section */}\n            {isLoading ? (\n              <div className=\"w-8 h-8 bg-gray-200 rounded-full animate-pulse\" />\n            ) : isAuthenticated && user ? (\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-600\">\n                  Welcome, {user.name || user.email}\n                </span>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleSignOut}\n                  disabled={signOutMutation.isPending}\n                  className=\"flex items-center space-x-1\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Sign Out</span>\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button variant=\"ghost\" size=\"sm\" asChild>\n                  <Link href=\"/auth/signin\">Sign In</Link>\n                </Button>\n                <Button size=\"sm\" asChild>\n                  <Link href=\"/auth/signup\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleMenu}\n              className=\"p-2\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t\">\n              {/* Status Bar for Mobile */}\n              <div className=\"flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg mb-3\">\n                <div className=\"flex items-center space-x-2\">\n                  {offlineState.isOnline ? (\n                    <Wifi className=\"h-4 w-4 text-green-600\" />\n                  ) : (\n                    <WifiOff className=\"h-4 w-4 text-red-600\" />\n                  )}\n                  <span className=\"text-sm text-gray-600\">\n                    {offlineState.isOnline ? 'Online' : 'Offline'}\n                  </span>\n                  {hasPendingSync && (\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      {offlineState.pendingSync.length} pending\n                    </Badge>\n                  )}\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Bell className=\"h-4 w-4 text-gray-600\" />\n                  {unreadCount > 0 && (\n                    <Badge variant=\"destructive\" className=\"text-xs\">\n                      {unreadCount}\n                    </Badge>\n                  )}\n                </div>\n              </div>\n\n              {/* Navigation Links */}\n              <Link\n                href=\"/workouts\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                onClick={() => setIsOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <Dumbbell className=\"h-5 w-5\" />\n                  <span>Workouts</span>\n                </div>\n              </Link>\n              <Link\n                href=\"/exercises\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                onClick={() => setIsOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <BookOpen className=\"h-5 w-5\" />\n                  <span>Exercises</span>\n                </div>\n              </Link>\n              <Link\n                href=\"/progress\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                onClick={() => setIsOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <BarChart3 className=\"h-5 w-5\" />\n                  <span>Progress</span>\n                </div>\n              </Link>\n\n              {/* Auth Section for Mobile */}\n              <div className=\"pt-4 border-t\">\n                {isLoading ? (\n                  <div className=\"px-3 py-2\">\n                    <div className=\"w-full h-8 bg-gray-200 rounded animate-pulse\" />\n                  </div>\n                ) : isAuthenticated && user ? (\n                  <div className=\"space-y-2\">\n                    <div className=\"px-3 py-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <User className=\"h-5 w-5 text-gray-400\" />\n                        <span className=\"text-sm text-gray-600\">\n                          {user.name || user.email}\n                        </span>\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleSignOut}\n                      disabled={signOutMutation.isPending}\n                      className=\"w-full justify-start px-3\"\n                    >\n                      <LogOut className=\"h-4 w-4 mr-2\" />\n                      Sign Out\n                    </Button>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2 px-3\">\n                    <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                      <Link href=\"/auth/signin\" onClick={() => setIsOpen(false)}>\n                        Sign In\n                      </Link>\n                    </Button>\n                    <Button size=\"sm\" className=\"w-full\" asChild>\n                      <Link href=\"/auth/signup\" onClick={() => setIsOpen(false)}>\n                        Sign Up\n                      </Link>\n                    </Button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAcO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEjC,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD;IAC7C,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAEvC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,gBAAgB,WAAW;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAKD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;gDACZ,aAAa,QAAQ,iBACpB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAEhB,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAEpB,gCACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,aAAa,WAAW,CAAC,MAAM;;;;;;;;;;;;sDAMtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DAET,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;gCAOjC,0BACC,6LAAC;oCAAI,WAAU;;;;;2CACb,mBAAmB,qBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAwB;gDAC5B,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;;sDAEnC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB,SAAS;4CACnC,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;yDAIV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,OAAO;sDACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;sDAE5B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAOlC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,QAAQ,iBACpB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAErB,6LAAC;gDAAK,WAAU;0DACb,aAAa,QAAQ,GAAG,WAAW;;;;;;4CAErC,gCACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC,aAAa,WAAW,CAAC,MAAM;oDAAC;;;;;;;;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;0DACpC;;;;;;;;;;;;;;;;;;0CAOT,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;0CACZ,0BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;2CAEf,mBAAmB,qBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;;;;;;;;;;;;sDAI9B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB,SAAS;4CACnC,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;yDAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;4CAAS,OAAO;sDAC1D,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,SAAS,IAAM,UAAU;0DAAQ;;;;;;;;;;;sDAI7D,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;4CAAS,OAAO;sDAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,SAAS,IAAM,UAAU;0DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajF;GA5OgB;;QAE+B,qIAAA,CAAA,UAAO;QAC5B,qIAAA,CAAA,aAAU;QAGb,sIAAA,CAAA,kBAAe;QAChB,sIAAA,CAAA,6BAA0B;QACvB,sIAAA,CAAA,oBAAiB;;;KAR1B", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-8 w-8\", \n    lg: \"h-12 w-12\"\n  }\n\n  return (\n    <div className={cn(\"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\", sizeClasses[size], className)} />\n  )\n}\n\ninterface LoadingCardProps {\n  className?: string\n}\n\nexport function LoadingCard({ className }: LoadingCardProps) {\n  return (\n    <div className={cn(\"rounded-xl border bg-card text-card-foreground shadow animate-pulse\", className)}>\n      <div className=\"p-6\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-4\"></div>\n        <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-2\"></div>\n        <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n      </div>\n    </div>\n  )\n}\n\ninterface LoadingPageProps {\n  title?: string\n  description?: string\n}\n\nexport function LoadingPage({ title = \"Loading...\", description = \"Please wait while we load your content\" }: LoadingPageProps) {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <div className=\"h-12 bg-gray-200 rounded w-1/3 mx-auto mb-4 animate-pulse\"></div>\n            <div className=\"h-6 bg-gray-200 rounded w-1/2 mx-auto animate-pulse\"></div>\n          </div>\n        </div>\n      </section>\n      \n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\n          <LoadingSpinner size=\"lg\" />\n          <h2 className=\"text-xl font-semibold text-gray-900\">{title}</h2>\n          <p className=\"text-gray-600 text-center max-w-md\">{description}</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface LoadingSectionProps {\n  rows?: number\n  className?: string\n}\n\nexport function LoadingSection({ rows = 3, className }: LoadingSectionProps) {\n  return (\n    <div className={cn(\"space-y-4\", className)}>\n      {Array.from({ length: rows }).map((_, i) => (\n        <div key={i} className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-full mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\ninterface LoadingGridProps {\n  items?: number\n  className?: string\n}\n\nexport function LoadingGrid({ items = 6, className }: LoadingGridProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", className)}>\n      {Array.from({ length: items }).map((_, i) => (\n        <LoadingCard key={i} />\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wEAAwE,WAAW,CAAC,KAAK,EAAE;;;;;;AAElH;KAVgB;AAgBT,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uEAAuE;kBACxF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;MAVgB;AAiBT,SAAS,YAAY,EAAE,QAAQ,YAAY,EAAE,cAAc,wCAAwC,EAAoB;IAC5H,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAe,MAAK;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;;;;;;;;AAK7D;MArBgB;AA4BT,SAAS,eAAe,EAAE,OAAO,CAAC,EAAE,SAAS,EAAuB;IACzE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;gBAAY,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;eAFP;;;;;;;;;;AAOlB;MAXgB;AAkBT,SAAS,YAAY,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAoB;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;kBACxE,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,6LAAC,iBAAiB;;;;;;;;;;AAI1B;MARgB", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/error.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Home, ArrowLeft } from \"lucide-react\"\nimport { Button } from \"./button\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ErrorMessageProps {\n  title?: string\n  message?: string\n  onRetry?: () => void\n  className?: string\n}\n\nexport function ErrorMessage({ \n  title = \"Something went wrong\", \n  message = \"An unexpected error occurred. Please try again.\",\n  onRetry,\n  className \n}: ErrorMessageProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center p-8 text-center\", className)}>\n      <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n        <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">{message}</p>\n      {onRetry && (\n        <Button onClick={onRetry} className=\"flex items-center gap-2\">\n          <RefreshCw className=\"h-4 w-4\" />\n          Try Again\n        </Button>\n      )}\n    </div>\n  )\n}\n\ninterface ErrorPageProps {\n  title?: string\n  message?: string\n  onRetry?: () => void\n  showHomeButton?: boolean\n}\n\nexport function ErrorPage({ \n  title = \"Oops! Something went wrong\", \n  message = \"We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.\",\n  onRetry,\n  showHomeButton = true\n}: ErrorPageProps) {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <section className=\"bg-gradient-to-br from-red-50 to-orange-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\">Error</h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">Something unexpected happened</p>\n          </div>\n        </div>\n      </section>\n      \n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex flex-col items-center justify-center min-h-[400px]\">\n          <div className=\"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-6\">\n            <AlertTriangle className=\"h-12 w-12 text-red-600\" />\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">{title}</h2>\n          <p className=\"text-gray-600 text-center max-w-2xl mb-8\">{message}</p>\n          \n          <div className=\"flex gap-4\">\n            {onRetry && (\n              <Button onClick={onRetry} className=\"flex items-center gap-2\">\n                <RefreshCw className=\"h-4 w-4\" />\n                Try Again\n              </Button>\n            )}\n            {showHomeButton && (\n              <Button variant=\"outline\" onClick={() => window.location.href = '/'} className=\"flex items-center gap-2\">\n                <Home className=\"h-4 w-4\" />\n                Go Home\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ErrorBoundaryFallbackProps {\n  error: Error\n  resetError: () => void\n}\n\nexport function ErrorBoundaryFallback({ error, resetError }: ErrorBoundaryFallbackProps) {\n  return (\n    <div className=\"min-h-screen bg-white flex items-center justify-center\">\n      <div className=\"text-center p-8\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <AlertTriangle className=\"h-10 w-10 text-red-600\" />\n        </div>\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Application Error</h1>\n        <p className=\"text-gray-600 mb-6 max-w-md\">\n          The application encountered an unexpected error. This has been logged and our team has been notified.\n        </p>\n        <details className=\"text-left mb-6 p-4 bg-gray-50 rounded-lg\">\n          <summary className=\"cursor-pointer font-medium text-gray-700 mb-2\">Error Details</summary>\n          <pre className=\"text-sm text-red-600 whitespace-pre-wrap\">{error.message}</pre>\n        </details>\n        <div className=\"flex gap-4 justify-center\">\n          <Button onClick={resetError} className=\"flex items-center gap-2\">\n            <RefreshCw className=\"h-4 w-4\" />\n            Try Again\n          </Button>\n          <Button variant=\"outline\" onClick={() => window.location.href = '/'} className=\"flex items-center gap-2\">\n            <Home className=\"h-4 w-4\" />\n            Go Home\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface NetworkErrorProps {\n  onRetry?: () => void\n  className?: string\n}\n\nexport function NetworkError({ onRetry, className }: NetworkErrorProps) {\n  return (\n    <ErrorMessage\n      title=\"Connection Error\"\n      message=\"Unable to connect to the server. Please check your internet connection and try again.\"\n      onRetry={onRetry}\n      className={className}\n    />\n  )\n}\n\ninterface NotFoundErrorProps {\n  resource?: string\n  onGoBack?: () => void\n  className?: string\n}\n\nexport function NotFoundError({ resource = \"page\", onGoBack, className }: NotFoundErrorProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center p-8 text-center\", className)}>\n      <div className=\"text-6xl font-bold text-gray-300 mb-4\">404</div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n        {resource.charAt(0).toUpperCase() + resource.slice(1)} Not Found\n      </h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">\n        The {resource} you're looking for doesn't exist or has been moved.\n      </p>\n      <div className=\"flex gap-4\">\n        {onGoBack && (\n          <Button variant=\"outline\" onClick={onGoBack} className=\"flex items-center gap-2\">\n            <ArrowLeft className=\"h-4 w-4\" />\n            Go Back\n          </Button>\n        )}\n        <Button onClick={() => window.location.href = '/'} className=\"flex items-center gap-2\">\n          <Home className=\"h-4 w-4\" />\n          Go Home\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AASO,SAAS,aAAa,EAC3B,QAAQ,sBAAsB,EAC9B,UAAU,iDAAiD,EAC3D,OAAO,EACP,SAAS,EACS;IAClB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;;0BAC9E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;0BAE3B,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAA+B;;;;;;YAC3C,yBACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS;gBAAS,WAAU;;kCAClC,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAY;;;;;;;;;;;;;AAM3C;KArBgB;AA8BT,SAAS,UAAU,EACxB,QAAQ,4BAA4B,EACpC,UAAU,gHAAgH,EAC1H,OAAO,EACP,iBAAiB,IAAI,EACN;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAEzD,6LAAC;4BAAI,WAAU;;gCACZ,yBACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAS,WAAU;;sDAClC,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;gCAIpC,gCACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCAAK,WAAU;;sDAC7E,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;MA3CgB;AAkDT,SAAS,sBAAsB,EAAE,KAAK,EAAE,UAAU,EAA8B;IACrF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;8BAE3B,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;8BAG3C,6LAAC;oBAAQ,WAAU;;sCACjB,6LAAC;4BAAQ,WAAU;sCAAgD;;;;;;sCACnE,6LAAC;4BAAI,WAAU;sCAA4C,MAAM,OAAO;;;;;;;;;;;;8BAE1E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,WAAU;;8CACrC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGnC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BAAK,WAAU;;8CAC7E,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;MA5BgB;AAmCT,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;IACpE,qBACE,6LAAC;QACC,OAAM;QACN,SAAQ;QACR,SAAS;QACT,WAAW;;;;;;AAGjB;MATgB;AAiBT,SAAS,cAAc,EAAE,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAsB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;;0BAC9E,6LAAC;gBAAI,WAAU;0BAAwC;;;;;;0BACvD,6LAAC;gBAAG,WAAU;;oBACX,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;oBAAG;;;;;;;0BAExD,6LAAC;gBAAE,WAAU;;oBAA8B;oBACpC;oBAAS;;;;;;;0BAEhB,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAU,WAAU;;0CACrD,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIrC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wBAAK,WAAU;;0CAC3D,6LAAC,sMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAMtC;MAxBgB", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/empty-state.tsx"], "sourcesContent": ["import { Plus, Search, Target, Activity, Dumbbell, Calendar, TrendingUp } from \"lucide-react\"\nimport { Button } from \"./button\"\nimport { cn } from \"@/lib/utils\"\n\ninterface EmptyStateProps {\n  icon?: React.ReactNode\n  title: string\n  description: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n  className?: string\n}\n\nexport function EmptyState({ icon, title, description, action, className }: EmptyStateProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center p-12 text-center\", className)}>\n      <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6\">\n        {icon || <Search className=\"h-8 w-8 text-gray-400\" />}\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">{description}</p>\n      {action && (\n        <Button onClick={action.onClick} className=\"flex items-center gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          {action.label}\n        </Button>\n      )}\n    </div>\n  )\n}\n\nexport function EmptyWorkouts({ onCreateWorkout }: { onCreateWorkout?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Dumbbell className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No workouts yet\"\n      description=\"Start your fitness journey by creating your first workout plan. Choose from our templates or build your own custom routine.\"\n      action={onCreateWorkout ? {\n        label: \"Create First Workout\",\n        onClick: onCreateWorkout\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyExercises({ onAddExercise }: { onAddExercise?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Activity className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No exercises found\"\n      description=\"We couldn't find any exercises matching your criteria. Try adjusting your filters or browse our complete exercise database.\"\n      action={onAddExercise ? {\n        label: \"Browse All Exercises\",\n        onClick: onAddExercise\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyProgress({ onStartWorkout }: { onStartWorkout?: () => void }) {\n  return (\n    <EmptyState\n      icon={<TrendingUp className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No progress data yet\"\n      description=\"Complete your first workout to start tracking your fitness progress. We'll show you detailed analytics and insights as you build your routine.\"\n      action={onStartWorkout ? {\n        label: \"Start First Workout\",\n        onClick: onStartWorkout\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyGoals({ onCreateGoal }: { onCreateGoal?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Target className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No goals set\"\n      description=\"Set your first fitness goal to stay motivated and track your progress. Choose from weekly workouts, monthly targets, or create custom goals.\"\n      action={onCreateGoal ? {\n        label: \"Set First Goal\",\n        onClick: onCreateGoal\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyHistory({ onStartWorkout }: { onStartWorkout?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Calendar className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No workout history\"\n      description=\"Your workout history will appear here once you complete your first session. Start working out to build your fitness timeline.\"\n      action={onStartWorkout ? {\n        label: \"Start Working Out\",\n        onClick: onStartWorkout\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptySearchResults({ searchTerm, onClearSearch }: { searchTerm: string, onClearSearch?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Search className=\"h-8 w-8 text-gray-400\" />}\n      title={`No results for \"${searchTerm}\"`}\n      description=\"We couldn't find anything matching your search. Try different keywords or browse our categories to discover new content.\"\n      action={onClearSearch ? {\n        label: \"Clear Search\",\n        onClick: onClearSearch\n      } : undefined}\n    />\n  )\n}\n\ninterface EmptyCardProps {\n  title: string\n  description: string\n  className?: string\n}\n\nexport function EmptyCard({ title, description, className }: EmptyCardProps) {\n  return (\n    <div className={cn(\"rounded-xl border bg-card text-card-foreground shadow\", className)}>\n      <div className=\"p-6\">\n        <div className=\"flex flex-col items-center text-center py-8\">\n          <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n            <Search className=\"h-6 w-6 text-gray-400\" />\n          </div>\n          <h4 className=\"font-medium text-gray-900 mb-2\">{title}</h4>\n          <p className=\"text-sm text-gray-600\">{description}</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface EmptyListProps {\n  icon?: React.ReactNode\n  title: string\n  description: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n  className?: string\n}\n\nexport function EmptyList({ icon, title, description, action, className }: EmptyListProps) {\n  return (\n    <div className={cn(\"border-2 border-dashed border-gray-200 rounded-lg p-8\", className)}>\n      <div className=\"text-center\">\n        <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          {icon || <Plus className=\"h-6 w-6 text-gray-400\" />}\n        </div>\n        <h3 className=\"text-sm font-medium text-gray-900 mb-2\">{title}</h3>\n        <p className=\"text-sm text-gray-600 mb-4\">{description}</p>\n        {action && (\n          <Button variant=\"outline\" size=\"sm\" onClick={action.onClick}>\n            {action.label}\n          </Button>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAaO,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAmB;IACzF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAC/E,6LAAC;gBAAI,WAAU;0BACZ,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAE7B,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAA+B;;;;;;YAC3C,wBACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS,OAAO,OAAO;gBAAE,WAAU;;kCACzC,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,OAAO,KAAK;;;;;;;;;;;;;AAKvB;KAhBgB;AAkBT,SAAS,cAAc,EAAE,eAAe,EAAoC;IACjF,qBACE,6LAAC;QACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,aAAY;QACZ,QAAQ,kBAAkB;YACxB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,eAAe,EAAE,aAAa,EAAkC;IAC9E,qBACE,6LAAC;QACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,aAAY;QACZ,QAAQ,gBAAgB;YACtB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,cAAc,EAAE,cAAc,EAAmC;IAC/E,qBACE,6LAAC;QACC,oBAAM,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,OAAM;QACN,aAAY;QACZ,QAAQ,iBAAiB;YACvB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,WAAW,EAAE,YAAY,EAAiC;IACxE,qBACE,6LAAC;QACC,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,OAAM;QACN,aAAY;QACZ,QAAQ,eAAe;YACrB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,aAAa,EAAE,cAAc,EAAmC;IAC9E,qBACE,6LAAC;QACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,aAAY;QACZ,QAAQ,iBAAiB;YACvB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,mBAAmB,EAAE,UAAU,EAAE,aAAa,EAAsD;IAClH,qBACE,6LAAC;QACC,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;QACvC,aAAY;QACZ,QAAQ,gBAAgB;YACtB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAoBT,SAAS,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAkB;IACzE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;kBAC1E,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;;;;;AAKhD;MAdgB;AA2BT,SAAS,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAkB;IACvF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;kBAC1E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,sBAAQ,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;8BAE3B,6LAAC;oBAAG,WAAU;8BAA0C;;;;;;8BACxD,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;gBAC1C,wBACC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS,OAAO,OAAO;8BACxD,OAAO,KAAK;;;;;;;;;;;;;;;;;AAMzB;MAjBgB", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-workouts.ts"], "sourcesContent": ["/**\n * React Query hooks for workout data\n */\n\nimport { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';\nimport { WorkoutService } from '../api/services/workouts';\nimport type {\n  CreateWorkoutSessionData\n} from '../api/types';\n\n// Query keys for consistent caching\nexport const workoutKeys = {\n  all: ['workouts'] as const,\n  sessions: () => [...workoutKeys.all, 'sessions'] as const,\n  session: (id: string) => [...workoutKeys.sessions(), id] as const,\n  sessionsList: (params: any) => [...workoutKeys.sessions(), 'list', params] as const,\n  programs: () => [...workoutKeys.all, 'programs'] as const,\n  program: (id: string) => [...workoutKeys.programs(), id] as const,\n  programsList: (params: any) => [...workoutKeys.programs(), 'list', params] as const,\n  userPrograms: () => [...workoutKeys.programs(), 'user'] as const,\n  popular: (limit: number) => [...workoutKeys.programs(), 'popular', limit] as const,\n  recommended: (limit: number) => [...workoutKeys.programs(), 'recommended', limit] as const,\n  stats: (period: string) => [...workoutKeys.all, 'stats', period] as const,\n  history: (params: any) => [...workoutKeys.all, 'history', params] as const,\n};\n\n/**\n * Hook to get workout sessions\n */\nexport function useWorkoutSessions(params: Parameters<typeof WorkoutService.getWorkoutSessions>[0] = {}) {\n  return useQuery({\n    queryKey: workoutKeys.sessionsList(params),\n    queryFn: () => WorkoutService.getWorkoutSessions(params),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Hook to get a specific workout session\n */\nexport function useWorkoutSession(id: string, enabled = true) {\n  return useQuery({\n    queryKey: workoutKeys.session(id),\n    queryFn: () => WorkoutService.getWorkoutSession(id),\n    enabled: enabled && !!id,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to create a workout session\n */\nexport function useCreateWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateWorkoutSessionData) => WorkoutService.createWorkoutSession(data),\n    onSuccess: () => {\n      // Invalidate sessions list to refetch\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to update a workout session\n */\nexport function useUpdateWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: Partial<CreateWorkoutSessionData> }) =>\n      WorkoutService.updateWorkoutSession(id, data),\n    onSuccess: (updatedSession) => {\n      // Update the specific session cache\n      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);\n      \n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to delete a workout session\n */\nexport function useDeleteWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.deleteWorkoutSession(id),\n    onSuccess: (_, deletedId) => {\n      // Remove from cache\n      queryClient.removeQueries({ queryKey: workoutKeys.session(deletedId) });\n      \n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to start a workout session\n */\nexport function useStartWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.startWorkoutSession(id),\n    onSuccess: (updatedSession) => {\n      // Update the specific session cache\n      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);\n      \n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to complete a workout session\n */\nexport function useCompleteWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: { duration: number; notes?: string } }) =>\n      WorkoutService.completeWorkoutSession(id, data),\n    onSuccess: (updatedSession) => {\n      // Update the specific session cache\n      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);\n      \n      // Invalidate sessions list and stats\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n      queryClient.invalidateQueries({ queryKey: workoutKeys.stats('month') });\n    },\n  });\n}\n\n/**\n * Hook to get workout programs\n */\nexport function useWorkoutPrograms(params: Parameters<typeof WorkoutService.getWorkoutPrograms>[0] = {}) {\n  return useQuery({\n    queryKey: workoutKeys.programsList(params),\n    queryFn: () => WorkoutService.getWorkoutPrograms(params),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get a specific workout program\n */\nexport function useWorkoutProgram(id: string, enabled = true) {\n  return useQuery({\n    queryKey: workoutKeys.program(id),\n    queryFn: () => WorkoutService.getWorkoutProgram(id),\n    enabled: enabled && !!id,\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get user's joined programs\n */\nexport function useUserPrograms() {\n  return useQuery({\n    queryKey: workoutKeys.userPrograms(),\n    queryFn: () => WorkoutService.getUserPrograms(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to get popular programs\n */\nexport function usePopularPrograms(limit = 10) {\n  return useQuery({\n    queryKey: workoutKeys.popular(limit),\n    queryFn: () => WorkoutService.getPopularPrograms(limit),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  });\n}\n\n/**\n * Hook to get recommended programs\n */\nexport function useRecommendedPrograms(limit = 6) {\n  return useQuery({\n    queryKey: workoutKeys.recommended(limit),\n    queryFn: () => WorkoutService.getRecommendedPrograms(limit),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to create a workout program\n */\nexport function useCreateWorkoutProgram() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: any) => WorkoutService.createWorkoutProgram(data),\n    onSuccess: () => {\n      // Invalidate programs list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.programs() });\n    },\n  });\n}\n\n/**\n * Hook to join a workout program\n */\nexport function useJoinWorkoutProgram() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.joinWorkoutProgram(id),\n    onSuccess: () => {\n      // Invalidate user programs\n      queryClient.invalidateQueries({ queryKey: workoutKeys.userPrograms() });\n    },\n  });\n}\n\n/**\n * Hook to leave a workout program\n */\nexport function useLeaveWorkoutProgram() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.leaveWorkoutProgram(id),\n    onSuccess: () => {\n      // Invalidate user programs\n      queryClient.invalidateQueries({ queryKey: workoutKeys.userPrograms() });\n    },\n  });\n}\n\n/**\n * Hook to generate AI workout\n */\nexport function useGenerateAIWorkout() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (preferences: Parameters<typeof WorkoutService.generateAIWorkout>[0]) =>\n      WorkoutService.generateAIWorkout(preferences),\n    onSuccess: () => {\n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to get workout statistics\n */\nexport function useWorkoutStats(period: 'week' | 'month' | 'year' = 'month') {\n  return useQuery({\n    queryKey: workoutKeys.stats(period),\n    queryFn: () => WorkoutService.getWorkoutStats(period),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to get workout history with infinite scrolling\n */\nexport function useWorkoutHistory(params: Omit<Parameters<typeof WorkoutService.getWorkoutHistory>[0], 'offset'> = {}) {\n  return useInfiniteQuery({\n    queryKey: workoutKeys.history(params),\n    queryFn: ({ pageParam = 0 }) => \n      WorkoutService.getWorkoutHistory({ ...params, offset: pageParam }),\n    initialPageParam: 0,\n    getNextPageParam: (lastPage) => {\n      const { pagination } = lastPage;\n      return pagination.hasNext ? pagination.page * pagination.limit : undefined;\n    },\n    staleTime: 5 * 60 * 1000,\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;AAED;AAAA;AAAA;AAAA;AACA;;;;AAMO,MAAM,cAAc;IACzB,KAAK;QAAC;KAAW;IACjB,UAAU,IAAM;eAAI,YAAY,GAAG;YAAE;SAAW;IAChD,SAAS,CAAC,KAAe;eAAI,YAAY,QAAQ;YAAI;SAAG;IACxD,cAAc,CAAC,SAAgB;eAAI,YAAY,QAAQ;YAAI;YAAQ;SAAO;IAC1E,UAAU,IAAM;eAAI,YAAY,GAAG;YAAE;SAAW;IAChD,SAAS,CAAC,KAAe;eAAI,YAAY,QAAQ;YAAI;SAAG;IACxD,cAAc,CAAC,SAAgB;eAAI,YAAY,QAAQ;YAAI;YAAQ;SAAO;IAC1E,cAAc,IAAM;eAAI,YAAY,QAAQ;YAAI;SAAO;IACvD,SAAS,CAAC,QAAkB;eAAI,YAAY,QAAQ;YAAI;YAAW;SAAM;IACzE,aAAa,CAAC,QAAkB;eAAI,YAAY,QAAQ;YAAI;YAAe;SAAM;IACjF,OAAO,CAAC,SAAmB;eAAI,YAAY,GAAG;YAAE;YAAS;SAAO;IAChE,SAAS,CAAC,SAAgB;eAAI,YAAY,GAAG;YAAE;YAAW;SAAO;AACnE;AAKO,SAAS,mBAAmB,SAAkE,CAAC,CAAC;;IACrG,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,YAAY,CAAC;QACnC,OAAO;2CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QACjD,WAAW,IAAI,KAAK;IACtB;AACF;GANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,EAAU,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;0CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;;QAChD,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,OAAmC,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;;QACpF,SAAS;mDAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2D,GAChF,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,IAAI;;QAC1C,SAAS;mDAAE,CAAC;gBACV,oCAAoC;gBACpC,YAAY,YAAY,CAAC,YAAY,OAAO,CAAC,eAAe,EAAE,GAAG;gBAEjE,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;;QAChE,SAAS;mDAAE,CAAC,GAAG;gBACb,oBAAoB;gBACpB,YAAY,aAAa,CAAC;oBAAE,UAAU,YAAY,OAAO,CAAC;gBAAW;gBAErE,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAbgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAeb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC;;QAC/D,SAAS;kDAAE,CAAC;gBACV,oCAAoC;gBACpC,YAAY,YAAY,CAAC,YAAY,OAAO,CAAC,eAAe,EAAE,GAAG;gBAEjE,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAbgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAeb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA8D,GACnF,4IAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC,IAAI;;QAC5C,SAAS;qDAAE,CAAC;gBACV,oCAAoC;gBACpC,YAAY,YAAY,CAAC,YAAY,OAAO,CAAC,eAAe,EAAE,GAAG;gBAEjE,qCAAqC;gBACrC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK,CAAC;gBAAS;YACvE;;IACF;AACF;IAfgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,mBAAmB,SAAkE,CAAC,CAAC;;IACrG,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,YAAY,CAAC;QACnC,OAAO;2CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QACjD,WAAW,KAAK,KAAK;IACvB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,EAAU,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;0CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;;QAChD,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,YAAY;QAClC,OAAO;wCAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,eAAe;;QAC7C,WAAW,IAAI,KAAK;IACtB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,mBAAmB,QAAQ,EAAE;;IAC3C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;2CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QACjD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,uBAAuB,QAAQ,CAAC;;IAC9C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,WAAW,CAAC;QAClC,OAAO;+CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC;;QACrD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,OAAc,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;;QAC/D,SAAS;mDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;KAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QAC9D,SAAS;iDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,YAAY;gBAAG;YACvE;;IACF;AACF;KAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC;;QAC/D,SAAS;kDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,YAAY;gBAAG;YACvE;;IACF;AACF;KAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,CAAC,cACX,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;;QACnC,SAAS;gDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;KAXgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,SAAS,gBAAgB,SAAoC,OAAO;;IACzE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,KAAK,CAAC;QAC5B,OAAO;wCAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,eAAe,CAAC;;QAC9C,WAAW,IAAI,KAAK;IACtB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,SAAiF,CAAC,CAAC;;IACnH,OAAO,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;kDAAE,CAAC,EAAE,YAAY,CAAC,EAAE,GACzB,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU;;QAClE,kBAAkB;QAClB,gBAAgB;kDAAE,CAAC;gBACjB,MAAM,EAAE,UAAU,EAAE,GAAG;gBACvB,OAAO,WAAW,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,KAAK,GAAG;YACnE;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;KAZgB;;QACP,sLAAA,CAAA,mBAAgB", "debugId": null}}]}