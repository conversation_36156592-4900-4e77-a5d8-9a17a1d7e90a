import Image from 'next/image';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  fill = false,
  sizes,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  loading = 'lazy',
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  // Generate blur placeholder for better UX
  const generateBlurDataURL = (w: number, h: number) => {
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#f3f4f6';
      ctx.fillRect(0, 0, w, h);
    }
    return canvas.toDataURL();
  };

  if (hasError) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-gray-100 text-gray-400",
          className
        )}
        style={{ width, height }}
      >
        <span className="text-sm">Image not available</span>
      </div>
    );
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {isLoading && (
        <div 
          className="absolute inset-0 bg-gray-100 animate-pulse"
          style={{ width, height }}
        />
      )}
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        sizes={sizes}
        quality={quality}
        priority={priority}
        placeholder={placeholder}
        blurDataURL={blurDataURL || (width && height ? generateBlurDataURL(width, height) : undefined)}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      />
    </div>
  );
}

// Exercise image component with specific optimizations
export function ExerciseImage({
  exerciseName,
  src,
  className,
  ...props
}: Omit<OptimizedImageProps, 'alt'> & { exerciseName: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={`${exerciseName} exercise demonstration`}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      quality={90}
      className={cn("rounded-lg", className)}
      {...props}
    />
  );
}

// Avatar image component
export function AvatarImage({
  userName,
  src,
  size = 40,
  className,
  ...props
}: Omit<OptimizedImageProps, 'alt' | 'width' | 'height'> & { 
  userName: string; 
  size?: number;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={`${userName} profile picture`}
      width={size}
      height={size}
      quality={95}
      className={cn("rounded-full", className)}
      {...props}
    />
  );
}

// Hero image component with specific optimizations
export function HeroImage({
  title,
  src,
  className,
  ...props
}: Omit<OptimizedImageProps, 'alt'> & { title: string }) {
  return (
    <OptimizedImage
      src={src}
      alt={title}
      priority={true}
      sizes="100vw"
      quality={90}
      placeholder="blur"
      className={cn("w-full h-auto", className)}
      {...props}
    />
  );
}
