{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/api-test/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Container,\n  Ty<PERSON><PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Alert,\n  CircularProgress,\n  Divider,\n} from '@mui/material';\n\ninterface Program {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  level: string;\n  durationWeeks: number;\n  sessionsPerWeek: number;\n  equipment: string[];\n  isPremium: boolean;\n}\n\ninterface Exercise {\n  id: string;\n  name: string;\n  description: string;\n  instructions: string;\n}\n\nexport default function ApiTestPage() {\n  const [programs, setPrograms] = useState<Program[]>([]);\n  const [exercises, setExercises] = useState<Exercise[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const testProgramsAPI = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('/api/programs/public');\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      setPrograms(data);\n    } catch (err) {\n      setError(`Programs API Error: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testExercisesAPI = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('/api/exercises/public');\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      setExercises(data);\n    } catch (err) {\n      setError(`Exercises API Error: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // Auto-test APIs on page load\n    testProgramsAPI();\n    testExercisesAPI();\n  }, []);\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      <Typography variant=\"h3\" component=\"h1\" gutterBottom align=\"center\">\n        API Integration Test\n      </Typography>\n      \n      <Typography variant=\"h6\" color=\"text.secondary\" align=\"center\" sx={{ mb: 4 }}>\n        Testing workout-cool API integration\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      <Grid container spacing={4}>\n        {/* Programs Section */}\n        <Grid item xs={12}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h5\" component=\"h2\">\n                  Programs API Test\n                </Typography>\n                <Button \n                  variant=\"outlined\" \n                  onClick={testProgramsAPI}\n                  disabled={loading}\n                  startIcon={loading ? <CircularProgress size={20} /> : null}\n                >\n                  {loading ? 'Testing...' : 'Test Programs API'}\n                </Button>\n              </Box>\n              \n              <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                Endpoint: /api/programs/public\n              </Typography>\n              \n              <Divider sx={{ my: 2 }} />\n              \n              {programs.length > 0 ? (\n                <Box>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Found {programs.length} programs:\n                  </Typography>\n                  {programs.map((program) => (\n                    <Card key={program.id} variant=\"outlined\" sx={{ mb: 2 }}>\n                      <CardContent>\n                        <Typography variant=\"h6\">{program.title}</Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          {program.description}\n                        </Typography>\n                        <Box display=\"flex\" gap={1} flexWrap=\"wrap\" mt={1}>\n                          <Typography variant=\"caption\" sx={{ \n                            px: 1, \n                            py: 0.5, \n                            bgcolor: 'primary.light', \n                            color: 'primary.contrastText',\n                            borderRadius: 1 \n                          }}>\n                            {program.category}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ \n                            px: 1, \n                            py: 0.5, \n                            bgcolor: 'secondary.light', \n                            color: 'secondary.contrastText',\n                            borderRadius: 1 \n                          }}>\n                            {program.level}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ \n                            px: 1, \n                            py: 0.5, \n                            bgcolor: 'info.light', \n                            color: 'info.contrastText',\n                            borderRadius: 1 \n                          }}>\n                            {program.durationWeeks} weeks\n                          </Typography>\n                        </Box>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </Box>\n              ) : (\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  No programs loaded yet. Click \"Test Programs API\" to load data.\n                </Typography>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Exercises Section */}\n        <Grid item xs={12}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h5\" component=\"h2\">\n                  Exercises API Test\n                </Typography>\n                <Button \n                  variant=\"outlined\" \n                  onClick={testExercisesAPI}\n                  disabled={loading}\n                  startIcon={loading ? <CircularProgress size={20} /> : null}\n                >\n                  {loading ? 'Testing...' : 'Test Exercises API'}\n                </Button>\n              </Box>\n              \n              <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                Endpoint: /api/exercises/public\n              </Typography>\n              \n              <Divider sx={{ my: 2 }} />\n              \n              {exercises.length > 0 ? (\n                <Box>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Found {exercises.length} exercises:\n                  </Typography>\n                  {exercises.map((exercise) => (\n                    <Card key={exercise.id} variant=\"outlined\" sx={{ mb: 2 }}>\n                      <CardContent>\n                        <Typography variant=\"h6\">{exercise.name}</Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          {exercise.description}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                          <strong>Instructions:</strong> {exercise.instructions}\n                        </Typography>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </Box>\n              ) : (\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  No exercises loaded yet. Click \"Test Exercises API\" to load data.\n                </Typography>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Container>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAmCe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,CAAC,oBAAoB,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;QACxF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS,CAAC,qBAAqB,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;QACzF,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,8BAA8B;YAC9B;YACA;QACF;gCAAG,EAAE;IAEL,qBACE,6LAAC,6MAAA,CAAA,YAAS;QAAC,UAAS;QAAK,IAAI;YAAE,IAAI;QAAE;;0BACnC,6LAAC,gNAAA,CAAA,aAAU;gBAAC,SAAQ;gBAAK,WAAU;gBAAK,YAAY;gBAAC,OAAM;0BAAS;;;;;;0BAIpE,6LAAC,gNAAA,CAAA,aAAU;gBAAC,SAAQ;gBAAK,OAAM;gBAAiB,OAAM;gBAAS,IAAI;oBAAE,IAAI;gBAAE;0BAAG;;;;;;YAI7E,uBACC,6LAAC,iMAAA,CAAA,QAAK;gBAAC,UAAS;gBAAQ,IAAI;oBAAE,IAAI;gBAAE;0BACjC;;;;;;0BAIL,6LAAC,8LAAA,CAAA,OAAI;gBAAC,SAAS;gBAAC,SAAS;;kCAEvB,6LAAC,8LAAA,CAAA,OAAI;wBAAC,IAAI;wBAAC,IAAI;kCACb,cAAA,6LAAC,8LAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mNAAA,CAAA,cAAW;;kDACV,6LAAC,2LAAA,CAAA,MAAG;wCAAC,SAAQ;wCAAO,gBAAe;wCAAgB,YAAW;wCAAS,IAAI;;0DACzE,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,WAAU;0DAAK;;;;;;0DAGxC,6LAAC,oMAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,WAAW,wBAAU,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,MAAM;;;;;6DAAS;0DAErD,UAAU,eAAe;;;;;;;;;;;;kDAI9B,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,OAAM;wCAAiB,YAAY;kDAAC;;;;;;kDAIhE,6LAAC,uMAAA,CAAA,UAAO;wCAAC,IAAI;4CAAE,IAAI;wCAAE;;;;;;oCAEpB,SAAS,MAAM,GAAG,kBACjB,6LAAC,2LAAA,CAAA,MAAG;;0DACF,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,YAAY;;oDAAC;oDAC7B,SAAS,MAAM;oDAAC;;;;;;;4CAExB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8LAAA,CAAA,OAAI;oDAAkB,SAAQ;oDAAW,IAAI;wDAAE,IAAI;oDAAE;8DACpD,cAAA,6LAAC,mNAAA,CAAA,cAAW;;0EACV,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;0EAAM,QAAQ,KAAK;;;;;;0EACvC,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,OAAM;gEAAiB,YAAY;0EAC5D,QAAQ,WAAW;;;;;;0EAEtB,6LAAC,2LAAA,CAAA,MAAG;gEAAC,SAAQ;gEAAO,KAAK;gEAAG,UAAS;gEAAO,IAAI;;kFAC9C,6LAAC,gNAAA,CAAA,aAAU;wEAAC,SAAQ;wEAAU,IAAI;4EAChC,IAAI;4EACJ,IAAI;4EACJ,SAAS;4EACT,OAAO;4EACP,cAAc;wEAChB;kFACG,QAAQ,QAAQ;;;;;;kFAEnB,6LAAC,gNAAA,CAAA,aAAU;wEAAC,SAAQ;wEAAU,IAAI;4EAChC,IAAI;4EACJ,IAAI;4EACJ,SAAS;4EACT,OAAO;4EACP,cAAc;wEAChB;kFACG,QAAQ,KAAK;;;;;;kFAEhB,6LAAC,gNAAA,CAAA,aAAU;wEAAC,SAAQ;wEAAU,IAAI;4EAChC,IAAI;4EACJ,IAAI;4EACJ,SAAS;4EACT,OAAO;4EACP,cAAc;wEAChB;;4EACG,QAAQ,aAAa;4EAAC;;;;;;;;;;;;;;;;;;;mDAhCpB,QAAQ,EAAE;;;;;;;;;;6DAwCzB,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,OAAM;kDAAiB;;;;;;;;;;;;;;;;;;;;;;kCAS3D,6LAAC,8LAAA,CAAA,OAAI;wBAAC,IAAI;wBAAC,IAAI;kCACb,cAAA,6LAAC,8LAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mNAAA,CAAA,cAAW;;kDACV,6LAAC,2LAAA,CAAA,MAAG;wCAAC,SAAQ;wCAAO,gBAAe;wCAAgB,YAAW;wCAAS,IAAI;;0DACzE,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,WAAU;0DAAK;;;;;;0DAGxC,6LAAC,oMAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,WAAW,wBAAU,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,MAAM;;;;;6DAAS;0DAErD,UAAU,eAAe;;;;;;;;;;;;kDAI9B,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,OAAM;wCAAiB,YAAY;kDAAC;;;;;;kDAIhE,6LAAC,uMAAA,CAAA,UAAO;wCAAC,IAAI;4CAAE,IAAI;wCAAE;;;;;;oCAEpB,UAAU,MAAM,GAAG,kBAClB,6LAAC,2LAAA,CAAA,MAAG;;0DACF,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,YAAY;;oDAAC;oDAC7B,UAAU,MAAM;oDAAC;;;;;;;4CAEzB,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,8LAAA,CAAA,OAAI;oDAAmB,SAAQ;oDAAW,IAAI;wDAAE,IAAI;oDAAE;8DACrD,cAAA,6LAAC,mNAAA,CAAA,cAAW;;0EACV,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;0EAAM,SAAS,IAAI;;;;;;0EACvC,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,OAAM;gEAAiB,YAAY;0EAC5D,SAAS,WAAW;;;;;;0EAEvB,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,IAAI;oEAAE,IAAI;gEAAE;;kFACtC,6LAAC;kFAAO;;;;;;oEAAsB;oEAAE,SAAS,YAAY;;;;;;;;;;;;;mDAPhD,SAAS,EAAE;;;;;;;;;;6DAc1B,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,OAAM;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE;GAnMwB;KAAA", "debugId": null}}]}