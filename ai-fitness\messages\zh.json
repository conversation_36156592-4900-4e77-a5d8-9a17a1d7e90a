{"common": {"loading": "加载中...", "error": "错误", "retry": "重试", "cancel": "取消", "save": "保存", "delete": "删除", "edit": "编辑", "view": "查看", "search": "搜索", "filter": "筛选", "clear": "清除", "submit": "提交", "back": "返回", "next": "下一步", "previous": "上一步", "close": "关闭"}, "navigation": {"home": "首页", "workouts": "训练计划", "exercises": "运动库", "progress": "进度追踪", "profile": "个人资料", "settings": "设置", "logout": "退出登录"}, "homepage": {"title": "AI-fitness-singles", "subtitle": "专为单身人士设计的智能健身平台", "description": "AI驱动的健身平台，专为单身人士设计。创建个性化训练计划，访问全面的运动数据库，通过详细分析追踪进度，实现您的健身目标。", "getStarted": "开始使用", "learnMore": "了解更多", "features": {"title": "核心功能", "workouts": {"title": "智能训练计划", "description": "AI生成的个性化训练方案"}, "exercises": {"title": "运动库", "description": "包含详细指导的全面运动数据库"}, "progress": {"title": "进度分析", "description": "通过详细洞察追踪您的健身之旅"}}, "stats": {"title": "快速统计", "totalWorkouts": "总训练次数", "completedSessions": "完成会话", "totalExercises": "总运动数", "progressScore": "进度评分"}}, "workouts": {"title": "训练计划", "subtitle": "发现并创建个性化训练方案", "createNew": "创建新训练", "searchPlaceholder": "搜索训练计划...", "filters": {"all": "所有级别", "beginner": "初级", "intermediate": "中级", "advanced": "高级"}, "card": {"level": "级别", "duration": "时长", "exercises": "运动项目", "startWorkout": "开始训练", "viewDetails": "查看详情"}, "empty": {"title": "未找到训练计划", "description": "尝试调整您的搜索或筛选条件"}}, "exercises": {"title": "运动库", "subtitle": "包含详细指导的全面运动库", "searchPlaceholder": "搜索运动...", "filters": {"all": "所有类别", "strength": "力量训练", "cardio": "有氧运动", "flexibility": "柔韧性", "balance": "平衡训练"}, "card": {"muscleGroup": "肌肉群", "equipment": "器械", "difficulty": "难度", "viewExercise": "查看运动", "addToWorkout": "添加到训练"}, "attributes": {"muscleGroup": "肌肉群", "equipment": "器械", "difficulty": "难度"}, "empty": {"title": "未找到运动", "description": "尝试调整您的搜索或筛选条件"}}, "progress": {"title": "进度追踪", "subtitle": "通过详细分析监控您的健身之旅", "overview": {"title": "进度概览", "thisWeek": "本周", "thisMonth": "本月", "allTime": "全部时间"}, "charts": {"workoutFrequency": "训练频率", "progressTrend": "进度趋势", "exerciseDistribution": "运动分布"}, "stats": {"totalWorkouts": "总训练次数", "totalTime": "总时长", "averageSession": "平均会话", "caloriesBurned": "消耗卡路里"}, "empty": {"title": "暂无进度数据", "description": "开始训练以查看您的进度"}}, "apiTest": {"title": "API 集成测试", "subtitle": "测试 workout-cool API 集成", "programs": {"title": "训练计划 API 测试", "test": "测试训练计划 API", "success": "训练计划 API 工作正常", "error": "训练计划 API 错误"}, "exercises": {"title": "运动 API 测试", "test": "测试运动 API", "success": "运动 API 工作正常", "error": "运动 API 错误"}, "progress": {"title": "进度 API 测试", "test": "测试进度 API", "success": "进度 API 工作正常", "error": "进度 API 错误"}}, "language": {"switch": "切换语言", "english": "English", "chinese": "中文"}}