// src/retryStrategies.ts
var removeOldestQuery = ({ persistedClient }) => {
  const mutations = [...persistedClient.clientState.mutations];
  const queries = [...persistedClient.clientState.queries];
  const client = {
    ...persistedClient,
    clientState: { mutations, queries }
  };
  const sortedQueries = [...queries].sort(
    (a, b) => a.state.dataUpdatedAt - b.state.dataUpdatedAt
  );
  if (sortedQueries.length > 0) {
    const oldestData = sortedQueries.shift();
    client.clientState.queries = queries.filter((q) => q !== oldestData);
    return client;
  }
  return void 0;
};
export {
  removeOldestQuery
};
//# sourceMappingURL=retryStrategies.js.map