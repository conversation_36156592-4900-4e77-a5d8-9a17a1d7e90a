/**
 * React Query Configuration and Cache Management
 * Centralized configuration for data fetching, caching, and synchronization
 */

import { QueryClient, DefaultOptions } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { appActions } from '../store/app-store';

// ============================================================================
// QUERY CLIENT CONFIGURATION
// ============================================================================

const queryConfig: DefaultOptions = {
  queries: {
    // Global defaults for all queries
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
  },
  mutations: {
    // Global defaults for all mutations
    retry: (failureCount, error: any) => {
      // Don't retry mutations on client errors
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      // Retry once for server errors
      return failureCount < 1;
    },
    onError: (error: any) => {
      // Global error handling for mutations
      console.error('Mutation error:', error);
      
      // Add error notification
      appActions.addNotification({
        type: 'error',
        title: 'Operation Failed',
        message: error?.message || 'An unexpected error occurred',
      });
    },
  },
};

// Create the query client
export const queryClient = new QueryClient({
  defaultOptions: queryConfig,
});

// ============================================================================
// PERSISTENCE CONFIGURATION
// ============================================================================

const persister = createSyncStoragePersister({
  storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  key: 'ai-fitness-query-cache',
  serialize: JSON.stringify,
  deserialize: JSON.parse,
});

// Persist query client (only in browser)
if (typeof window !== 'undefined') {
  persistQueryClient({
    queryClient,
    persister,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    buster: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  });
}

// ============================================================================
// CACHE MANAGEMENT UTILITIES
// ============================================================================

export const cacheUtils = {
  /**
   * Invalidate all queries for a specific entity type
   */
  invalidateEntity: (entityType: string) => {
    queryClient.invalidateQueries({ queryKey: [entityType] });
  },

  /**
   * Remove all cached data for a specific entity
   */
  removeEntity: (entityType: string, id?: string) => {
    if (id) {
      queryClient.removeQueries({ queryKey: [entityType, id] });
    } else {
      queryClient.removeQueries({ queryKey: [entityType] });
    }
  },

  /**
   * Prefetch data for better UX
   */
  prefetch: async (queryKey: any[], queryFn: () => Promise<any>) => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  },

  /**
   * Set query data manually (for optimistic updates)
   */
  setQueryData: <T>(queryKey: any[], data: T) => {
    queryClient.setQueryData(queryKey, data);
  },

  /**
   * Get cached query data
   */
  getQueryData: <T>(queryKey: any[]): T | undefined => {
    return queryClient.getQueryData(queryKey);
  },

  /**
   * Clear all cached data
   */
  clearAll: () => {
    queryClient.clear();
  },

  /**
   * Reset queries to refetch fresh data
   */
  resetQueries: (queryKey?: any[]) => {
    if (queryKey) {
      queryClient.resetQueries({ queryKey });
    } else {
      queryClient.resetQueries();
    }
  },

  /**
   * Cancel ongoing queries
   */
  cancelQueries: (queryKey?: any[]) => {
    if (queryKey) {
      queryClient.cancelQueries({ queryKey });
    } else {
      queryClient.cancelQueries();
    }
  },
};

// ============================================================================
// QUERY KEY FACTORIES
// ============================================================================

/**
 * Centralized query key management for consistency
 */
export const queryKeys = {
  // Auth
  auth: {
    all: ['auth'] as const,
    session: () => [...queryKeys.auth.all, 'session'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
  },

  // Exercises
  exercises: {
    all: ['exercises'] as const,
    lists: () => [...queryKeys.exercises.all, 'list'] as const,
    list: (filters: any) => [...queryKeys.exercises.lists(), filters] as const,
    details: () => [...queryKeys.exercises.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.exercises.details(), id] as const,
    search: (query: string) => [...queryKeys.exercises.all, 'search', query] as const,
    attributes: () => [...queryKeys.exercises.all, 'attributes'] as const,
  },

  // Workouts
  workouts: {
    all: ['workouts'] as const,
    sessions: () => [...queryKeys.workouts.all, 'sessions'] as const,
    session: (id: string) => [...queryKeys.workouts.sessions(), id] as const,
    programs: () => [...queryKeys.workouts.all, 'programs'] as const,
    program: (id: string) => [...queryKeys.workouts.programs(), id] as const,
    history: (filters: any) => [...queryKeys.workouts.all, 'history', filters] as const,
    stats: (period: string) => [...queryKeys.workouts.all, 'stats', period] as const,
  },

  // Progress
  progress: {
    all: ['progress'] as const,
    records: () => [...queryKeys.progress.all, 'records'] as const,
    record: (id: string) => [...queryKeys.progress.records(), id] as const,
    stats: (period: string) => [...queryKeys.progress.all, 'stats', period] as const,
    goals: () => [...queryKeys.progress.all, 'goals'] as const,
    achievements: () => [...queryKeys.progress.all, 'achievements'] as const,
    calendar: (year: number, month: number) => [...queryKeys.progress.all, 'calendar', year, month] as const,
  },

  // User
  user: {
    all: ['user'] as const,
    profile: () => [...queryKeys.user.all, 'profile'] as const,
    preferences: () => [...queryKeys.user.all, 'preferences'] as const,
    subscription: () => [...queryKeys.user.all, 'subscription'] as const,
  },
};

// ============================================================================
// OFFLINE SUPPORT
// ============================================================================

export const offlineUtils = {
  /**
   * Check if we're online
   */
  isOnline: () => {
    return typeof navigator !== 'undefined' ? navigator.onLine : true;
  },

  /**
   * Setup online/offline event listeners
   */
  setupNetworkListeners: () => {
    if (typeof window === 'undefined') return;

    const handleOnline = () => {
      appActions.setOnlineStatus(true);
      // Refetch all queries when coming back online
      queryClient.refetchQueries();
    };

    const handleOffline = () => {
      appActions.setOnlineStatus(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial status
    appActions.setOnlineStatus(navigator.onLine);

    // Return cleanup function
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  },

  /**
   * Queue mutation for offline sync
   */
  queueOfflineMutation: (type: string, action: string, data: any) => {
    appActions.addPendingSync({
      type: type as any,
      action: action as any,
      data,
    });
  },
};

// ============================================================================
// QUERY CLIENT EVENTS
// ============================================================================

// Setup global query client event listeners
queryClient.getQueryCache().subscribe((event) => {
  // Log query events in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Query event:', event);
  }

  // Handle specific events
  switch (event.type) {
    case 'added':
      // Query was added to cache
      break;
    case 'removed':
      // Query was removed from cache
      break;
    case 'updated':
      // Query data was updated
      break;
  }
});

queryClient.getMutationCache().subscribe((event) => {
  // Log mutation events in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Mutation event:', event);
  }

  // Handle mutation success/error globally
  if (event.type === 'updated') {
    const mutation = event.mutation;
    
    if (mutation.state.status === 'success') {
      // Global success handling
      appActions.addNotification({
        type: 'success',
        title: 'Success',
        message: 'Operation completed successfully',
      });
    }
  }
});

// ============================================================================
// EXPORTS
// ============================================================================

export { queryClient as default };
export type { QueryClient };
