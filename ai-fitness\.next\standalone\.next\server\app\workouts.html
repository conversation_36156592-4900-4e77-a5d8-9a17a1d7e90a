<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/89f2b2231ada20b1.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-9b16ec72bdacc159.js"/><script src="/_next/static/chunks/common-115fb90c12ff9b32.js" async=""></script><script src="/_next/static/chunks/vendors-59395308dd53cbb3.js" async=""></script><script src="/_next/static/chunks/main-app-4e781ab3fe38e393.js" async=""></script><script src="/_next/static/chunks/app/layout-6606529bb353912a.js" async=""></script><script src="/_next/static/chunks/app/workouts/page-b1f7369cb99190d1.js" async=""></script><title>AI-fitness-singles - Smart Fitness Platform</title><meta name="description" content="AI-powered fitness platform for singles. Create custom workout plans, access comprehensive exercise database, and track your fitness progress with detailed analytics."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><nav class="bg-white shadow-sm border-b"><div class="container mx-auto px-4"><div class="flex justify-between items-center h-16"><a class="flex items-center space-x-2" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-dumbbell h-8 w-8 text-blue-600" aria-hidden="true"><path d="M17.596 12.768a2 2 0 1 0 2.829-2.829l-1.768-1.767a2 2 0 0 0 2.828-2.829l-2.828-2.828a2 2 0 0 0-2.829 2.828l-1.767-1.768a2 2 0 1 0-2.829 2.829z"></path><path d="m2.5 21.5 1.4-1.4"></path><path d="m20.1 3.9 1.4-1.4"></path><path d="M5.343 21.485a2 2 0 1 0 2.829-2.828l1.767 1.768a2 2 0 1 0 2.829-2.829l-6.364-6.364a2 2 0 1 0-2.829 2.829l1.768 1.767a2 2 0 0 0-2.828 2.829z"></path><path d="m9.6 14.4 4.8-4.8"></path></svg><span class="text-xl font-bold text-gray-900">AI-fitness-singles</span></a><div class="hidden md:flex items-center space-x-8"><a class="text-gray-600 hover:text-blue-600 transition-colors" href="/workouts">Workouts</a><a class="text-gray-600 hover:text-blue-600 transition-colors" href="/exercises">Exercises</a><a class="text-gray-600 hover:text-blue-600 transition-colors" href="/progress">Progress</a><div class="flex items-center space-x-4"><div class="flex items-center space-x-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wifi h-4 w-4 text-green-600" aria-hidden="true"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg></div><div class="relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-4 w-4 text-gray-600" aria-hidden="true"><path d="M10.268 21a2 2 0 0 0 3.464 0"></path><path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"></path></svg></div></div><div class="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div></div><div class="md:hidden"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 rounded-md text-xs p-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-6 w-6" aria-hidden="true"><path d="M4 12h16"></path><path d="M4 18h16"></path><path d="M4 6h16"></path></svg></button></div></div></div></nav><div class="container mx-auto px-4 py-8"><div class="text-center mb-8"><h1 class="text-4xl font-bold text-gray-900 mb-4">Workout Programs</h1><p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover personalized workout programs designed to help you reach your fitness goals</p></div><div class="flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm"><button class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors bg-blue-600 text-white">All Programs</button></div><div class="bg-white rounded-lg shadow-sm p-6 mb-8"><div class="flex flex-col md:flex-row gap-4 mb-4"><div class="flex-1 relative"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg><input type="text" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-10" placeholder="Search workout programs..." value=""/></div><button class="justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-funnel h-4 w-4" aria-hidden="true"><path d="M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z"></path></svg>Filters</button></div></div><div class="mb-8"><h2 class="text-2xl font-bold text-gray-900 mb-4">Popular Programs</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div></div></div><div><h2 class="text-2xl font-bold text-gray-900 mb-4">All Programs</h2><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div><div class="rounded-xl border bg-card text-card-foreground shadow animate-pulse"><div class="p-6"><div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div><div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-3 bg-gray-200 rounded w-2/3"></div></div></div></div></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-9b16ec72bdacc159.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7080,[\"177\",\"static/chunks/app/layout-6606529bb353912a.js\"],\"AppProviders\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[4281,[\"347\",\"static/chunks/app/workouts/page-b1f7369cb99190d1.js\"],\"default\"]\n9:I[9665,[],\"OutletBoundary\"]\nc:I[4911,[],\"AsyncMetadataOutlet\"]\ne:I[9665,[],\"ViewportBoundary\"]\n10:I[9665,[],\"MetadataBoundary\"]\n12:I[6614,[],\"\"]\n:HL[\"/_next/static/css/89f2b2231ada20b1.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"6wSPL4KoFigmVn13lZNXC\",\"p\":\"\",\"c\":[\"\",\"workouts\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"workouts\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/89f2b2231ada20b1.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c antialiased\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"workouts\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"ALf9uvIZENLGGPLaAoCF3v\",{\"children\":[[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],null]}],[\"$\",\"$L10\",null,{\"children\":\"$L11\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\n11:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]}]\n"])</script><script>self.__next_f.push([1,"b:null\n"])</script><script>self.__next_f.push([1,"f:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"d:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"AI-fitness-singles - Smart Fitness Platform\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"AI-powered fitness platform for singles. Create custom workout plans, access comprehensive exercise database, and track your fitness progress with detailed analytics.\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n15:{\"metadata\":\"$d:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>