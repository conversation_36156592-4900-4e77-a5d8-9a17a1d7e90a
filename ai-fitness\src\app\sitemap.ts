import { MetadataRoute } from 'next';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app';
  const currentDate = new Date();

  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/exercises`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/workouts`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/progress`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/dashboard`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/auth/signin`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/auth/signup`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
  ];

  // Dynamic exercise pages
  const exercisePages: MetadataRoute.Sitemap = [];
  try {
    // In a real application, you would fetch exercise IDs from your database
    // For now, we'll create some example exercise pages
    const sampleExerciseIds = [
      'push-ups',
      'squats',
      'deadlifts',
      'bench-press',
      'pull-ups',
      'lunges',
      'planks',
      'burpees',
      'mountain-climbers',
      'jumping-jacks'
    ];

    for (const exerciseId of sampleExerciseIds) {
      exercisePages.push({
        url: `${baseUrl}/exercises/${exerciseId}`,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.8,
      });
    }
  } catch (error) {
    console.error('Error generating exercise sitemap:', error);
  }

  // Dynamic workout pages
  const workoutPages: MetadataRoute.Sitemap = [];
  try {
    // In a real application, you would fetch workout program IDs from your database
    // For now, we'll create some example workout pages
    const sampleWorkoutIds = [
      'beginner-full-body',
      'intermediate-strength',
      'advanced-powerlifting',
      'cardio-hiit',
      'yoga-flexibility',
      'bodyweight-home',
      'weight-loss-program',
      'muscle-building',
      'endurance-training',
      'functional-fitness'
    ];

    for (const workoutId of sampleWorkoutIds) {
      workoutPages.push({
        url: `${baseUrl}/workouts/${workoutId}`,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.8,
      });
    }
  } catch (error) {
    console.error('Error generating workout sitemap:', error);
  }

  // Combine all pages
  return [...staticPages, ...exercisePages, ...workoutPages];
}

// Generate additional sitemaps for large datasets
export async function generateSitemaps() {
  // Return an array of sitemap IDs if you have multiple sitemaps
  // For example: ['exercises', 'workouts', 'users']
  return [{ id: 'main' }];
}
