version: '3.8'

services:
  # AI-fitness-singles Frontend Application
  ai-fitness-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-fitness-singles
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_NAME=AI-fitness-singles
      - NEXT_PUBLIC_API_BASE_URL=${API_BASE_URL:-http://localhost:8000}
      - AUTH_SECRET=${AUTH_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    env_file:
      - .env.production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-fitness-network
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: ai-fitness-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - ai-fitness-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: ai-fitness-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - ai-fitness-app
    restart: unless-stopped
    networks:
      - ai-fitness-network

volumes:
  redis_data:
    driver: local

networks:
  ai-fitness-network:
    driver: bridge
