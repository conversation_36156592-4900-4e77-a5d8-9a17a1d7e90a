{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/providers/query-provider.tsx"], "sourcesContent": ["'use client';\n\n/**\n * React Query Provider for AI-fitness application\n * Provides global state management and caching for API requests\n */\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { useState } from 'react';\n\ninterface QueryProviderProps {\n  children: React.ReactNode;\n}\n\nexport function QueryProvider({ children }: QueryProviderProps) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            // Stale time: how long data is considered fresh\n            staleTime: 5 * 60 * 1000, // 5 minutes\n            \n            // Cache time: how long data stays in cache after component unmounts\n            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n            \n            // Retry configuration\n            retry: (failureCount, error: any) => {\n              // Don't retry on authentication errors\n              if (error?.status === 401 || error?.status === 403) {\n                return false;\n              }\n              \n              // Don't retry on validation errors\n              if (error?.status === 422) {\n                return false;\n              }\n              \n              // Retry up to 3 times for other errors\n              return failureCount < 3;\n            },\n            \n            // Retry delay with exponential backoff\n            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n            \n            // Refetch on window focus (useful for real-time data)\n            refetchOnWindowFocus: false,\n            \n            // Refetch on reconnect\n            refetchOnReconnect: true,\n            \n            // Background refetch interval (disabled by default)\n            refetchInterval: false,\n          },\n          mutations: {\n            // Retry mutations on network errors\n            retry: (failureCount, error: any) => {\n              // Don't retry on client errors (4xx)\n              if (error?.status >= 400 && error?.status < 500) {\n                return false;\n              }\n              \n              // Retry up to 2 times for server errors\n              return failureCount < 2;\n            },\n            \n            // Retry delay for mutations\n            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      {/* Show React Query DevTools in development */}\n      {process.env.NODE_ENV === 'development' && (\n        <ReactQueryDevtools\n          initialIsOpen={false}\n          buttonPosition=\"bottom-right\"\n        />\n      )}\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;CAGC,GAED;AAAA;AACA;AACA;AATA;;;;;AAeO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,6KAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,gDAAgD;oBAChD,WAAW,IAAI,KAAK;oBAEpB,oEAAoE;oBACpE,QAAQ,KAAK,KAAK;oBAElB,sBAAsB;oBACtB,OAAO,CAAC,cAAc;wBACpB,uCAAuC;wBACvC,IAAI,OAAO,WAAW,OAAO,OAAO,WAAW,KAAK;4BAClD,OAAO;wBACT;wBAEA,mCAAmC;wBACnC,IAAI,OAAO,WAAW,KAAK;4BACzB,OAAO;wBACT;wBAEA,uCAAuC;wBACvC,OAAO,eAAe;oBACxB;oBAEA,uCAAuC;oBACvC,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;oBAEjE,sDAAsD;oBACtD,sBAAsB;oBAEtB,uBAAuB;oBACvB,oBAAoB;oBAEpB,oDAAoD;oBACpD,iBAAiB;gBACnB;gBACA,WAAW;oBACT,oCAAoC;oBACpC,OAAO,CAAC,cAAc;wBACpB,qCAAqC;wBACrC,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;4BAC/C,OAAO;wBACT;wBAEA,wCAAwC;wBACxC,OAAO,eAAe;oBACxB;oBAEA,4BAA4B;oBAC5B,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;gBACnE;YACF;QACF;IAGJ,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;YAEA,oDAAyB,+BACxB,8OAAC,oLAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,gBAAe;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/config.ts"], "sourcesContent": ["/**\n * API Configuration for AI-fitness application\n * Connects to workout-cool backend API\n */\n\nexport const API_CONFIG = {\n  // Base URL for the workout-cool backend API\n  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',\n  \n  // API endpoints\n  ENDPOINTS: {\n    // Authentication\n    AUTH: {\n      SIGNIN: '/api/auth/signin',\n      SIGNUP: '/api/auth/signup',\n      SIGNOUT: '/api/auth/signout',\n      SESSION: '/api/auth/session',\n      RESET_PASSWORD: '/api/auth/reset-password',\n    },\n    \n    // Exercises\n    EXERCISES: {\n      LIST: '/api/exercises',\n      SEARCH: '/api/exercises/search',\n      DETAILS: (id: string) => `/api/exercises/${id}`,\n      ATTRIBUTES: '/api/exercises/attributes',\n    },\n    \n    // Workout Sessions\n    WORKOUTS: {\n      LIST: '/api/workout-sessions',\n      CREATE: '/api/workout-sessions',\n      DETAILS: (id: string) => `/api/workout-sessions/${id}`,\n      UPDATE: (id: string) => `/api/workout-sessions/${id}`,\n      DELETE: (id: string) => `/api/workout-sessions/${id}`,\n      COMPLETE: (id: string) => `/api/workout-sessions/${id}/complete`,\n      SYNC: '/api/workout-sessions/sync',\n    },\n    \n    // Programs\n    PROGRAMS: {\n      LIST: '/api/programs',\n      CREATE: '/api/programs',\n      DETAILS: (slug: string) => `/api/programs/${slug}`,\n      UPDATE: (id: string) => `/api/programs/${id}`,\n      DELETE: (id: string) => `/api/programs/${id}`,\n      ENROLL: (id: string) => `/api/programs/${id}/enroll`,\n      SESSIONS: (programId: string) => `/api/programs/${programId}/sessions`,\n      START_SESSION: '/api/programs/sessions/start',\n      COMPLETE_SESSION: '/api/programs/sessions/complete',\n    },\n    \n    // User Progress\n    PROGRESS: {\n      LIST: '/api/progress',\n      DETAILS: (id: string) => `/api/progress/${id}`,\n      CREATE: '/api/progress',\n      UPDATE: (id: string) => `/api/progress/${id}`,\n      DELETE: (id: string) => `/api/progress/${id}`,\n      OVERVIEW: '/api/progress/overview',\n      STATS: '/api/progress/stats',\n      HISTORY: '/api/progress/history',\n      GOALS: '/api/progress/goals',\n      EXPORT: '/api/progress/export',\n    },\n    \n    // Premium\n    PREMIUM: {\n      PLANS: '/api/premium/plans',\n      SUBSCRIPTION: '/api/premium/subscription',\n      CHECKOUT: '/api/premium/checkout',\n      BILLING_PORTAL: '/api/premium/billing-portal',\n    },\n    \n    // User Management\n    USERS: {\n      PROFILE: '/api/users/profile',\n      UPDATE_PROFILE: '/api/users/profile',\n      PREFERENCES: '/api/users/preferences',\n    },\n  },\n  \n  // Request timeouts\n  TIMEOUT: {\n    DEFAULT: 10000, // 10 seconds\n    UPLOAD: 30000,  // 30 seconds\n    DOWNLOAD: 60000, // 60 seconds\n  },\n  \n  // Retry configuration\n  RETRY: {\n    ATTEMPTS: 3,\n    DELAY: 1000, // 1 second\n    BACKOFF_FACTOR: 2,\n  },\n} as const;\n\nexport type ApiEndpoints = typeof API_CONFIG.ENDPOINTS;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAEM,MAAM,aAAa;IACxB,4CAA4C;IAC5C,UAAU,6DAAwC;IAElD,gBAAgB;IAChB,WAAW;QACT,iBAAiB;QACjB,MAAM;YACJ,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,SAAS;YACT,gBAAgB;QAClB;QAEA,YAAY;QACZ,WAAW;YACT,MAAM;YACN,QAAQ;YACR,SAAS,CAAC,KAAe,CAAC,eAAe,EAAE,IAAI;YAC/C,YAAY;QACd;QAEA,mBAAmB;QACnB,UAAU;YACR,MAAM;YACN,QAAQ;YACR,SAAS,CAAC,KAAe,CAAC,sBAAsB,EAAE,IAAI;YACtD,QAAQ,CAAC,KAAe,CAAC,sBAAsB,EAAE,IAAI;YACrD,QAAQ,CAAC,KAAe,CAAC,sBAAsB,EAAE,IAAI;YACrD,UAAU,CAAC,KAAe,CAAC,sBAAsB,EAAE,GAAG,SAAS,CAAC;YAChE,MAAM;QACR;QAEA,WAAW;QACX,UAAU;YACR,MAAM;YACN,QAAQ;YACR,SAAS,CAAC,OAAiB,CAAC,cAAc,EAAE,MAAM;YAClD,QAAQ,CAAC,KAAe,CAAC,cAAc,EAAE,IAAI;YAC7C,QAAQ,CAAC,KAAe,CAAC,cAAc,EAAE,IAAI;YAC7C,QAAQ,CAAC,KAAe,CAAC,cAAc,EAAE,GAAG,OAAO,CAAC;YACpD,UAAU,CAAC,YAAsB,CAAC,cAAc,EAAE,UAAU,SAAS,CAAC;YACtE,eAAe;YACf,kBAAkB;QACpB;QAEA,gBAAgB;QAChB,UAAU;YACR,MAAM;YACN,SAAS,CAAC,KAAe,CAAC,cAAc,EAAE,IAAI;YAC9C,QAAQ;YACR,QAAQ,CAAC,KAAe,CAAC,cAAc,EAAE,IAAI;YAC7C,QAAQ,CAAC,KAAe,CAAC,cAAc,EAAE,IAAI;YAC7C,UAAU;YACV,OAAO;YACP,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QAEA,UAAU;QACV,SAAS;YACP,OAAO;YACP,cAAc;YACd,UAAU;YACV,gBAAgB;QAClB;QAEA,kBAAkB;QAClB,OAAO;YACL,SAAS;YACT,gBAAgB;YAChB,aAAa;QACf;IACF;IAEA,mBAAmB;IACnB,SAAS;QACP,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IAEA,sBAAsB;IACtB,OAAO;QACL,UAAU;QACV,OAAO;QACP,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/errors.ts"], "sourcesContent": ["/**\n * API Error Classes for AI-fitness application\n * Provides structured error handling for different types of API errors\n */\n\nexport class ApiError extends Error {\n  public readonly status: number;\n  public readonly code?: string;\n  public readonly details?: any;\n\n  constructor(message: string, status: number = 500, code?: string, details?: any) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.code = code;\n    this.details = details;\n\n    // Maintains proper stack trace for where our error was thrown (only available on V8)\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ApiError);\n    }\n  }\n\n  /**\n   * Check if error is a client error (4xx)\n   */\n  get isClientError(): boolean {\n    return this.status >= 400 && this.status < 500;\n  }\n\n  /**\n   * Check if error is a server error (5xx)\n   */\n  get isServerError(): boolean {\n    return this.status >= 500;\n  }\n\n  /**\n   * Convert error to JSON for logging\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      code: this.code,\n      details: this.details,\n      stack: this.stack,\n    };\n  }\n}\n\nexport class NetworkError extends ApiError {\n  constructor(message: string = 'Network error occurred') {\n    super(message, 0, 'NETWORK_ERROR');\n    this.name = 'NetworkError';\n  }\n}\n\nexport class AuthenticationError extends ApiError {\n  constructor(message: string = 'Authentication required') {\n    super(message, 401, 'AUTHENTICATION_ERROR');\n    this.name = 'AuthenticationError';\n  }\n}\n\nexport class AuthorizationError extends ApiError {\n  constructor(message: string = 'Access denied') {\n    super(message, 403, 'AUTHORIZATION_ERROR');\n    this.name = 'AuthorizationError';\n  }\n}\n\nexport class ValidationError extends ApiError {\n  public readonly fieldErrors: Record<string, string[]>;\n\n  constructor(message: string = 'Validation failed', fieldErrors: Record<string, string[]> = {}) {\n    super(message, 422, 'VALIDATION_ERROR', fieldErrors);\n    this.name = 'ValidationError';\n    this.fieldErrors = fieldErrors;\n  }\n\n  /**\n   * Get error message for a specific field\n   */\n  getFieldError(field: string): string | null {\n    const errors = this.fieldErrors[field];\n    return errors && errors.length > 0 ? errors[0] : null;\n  }\n\n  /**\n   * Check if a specific field has errors\n   */\n  hasFieldError(field: string): boolean {\n    return Boolean(this.fieldErrors[field]?.length);\n  }\n\n  /**\n   * Get all field error messages as a flat array\n   */\n  getAllFieldErrors(): string[] {\n    return Object.values(this.fieldErrors).flat();\n  }\n}\n\nexport class NotFoundError extends ApiError {\n  constructor(message: string = 'Resource not found') {\n    super(message, 404, 'NOT_FOUND_ERROR');\n    this.name = 'NotFoundError';\n  }\n}\n\nexport class ConflictError extends ApiError {\n  constructor(message: string = 'Resource conflict') {\n    super(message, 409, 'CONFLICT_ERROR');\n    this.name = 'ConflictError';\n  }\n}\n\nexport class RateLimitError extends ApiError {\n  public readonly retryAfter?: number;\n\n  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {\n    super(message, 429, 'RATE_LIMIT_ERROR', { retryAfter });\n    this.name = 'RateLimitError';\n    this.retryAfter = retryAfter;\n  }\n}\n\n/**\n * Type guard to check if an error is an ApiError\n */\nexport function isApiError(error: any): error is ApiError {\n  return error instanceof ApiError;\n}\n\n/**\n * Type guard to check if an error is a NetworkError\n */\nexport function isNetworkError(error: any): error is NetworkError {\n  return error instanceof NetworkError;\n}\n\n/**\n * Type guard to check if an error is an AuthenticationError\n */\nexport function isAuthenticationError(error: any): error is AuthenticationError {\n  return error instanceof AuthenticationError;\n}\n\n/**\n * Type guard to check if an error is a ValidationError\n */\nexport function isValidationError(error: any): error is ValidationError {\n  return error instanceof ValidationError;\n}\n\n/**\n * Get user-friendly error message from any error\n */\nexport function getErrorMessage(error: any): string {\n  if (isApiError(error)) {\n    return error.message;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  return 'An unexpected error occurred';\n}\n\n/**\n * Get error code from any error\n */\nexport function getErrorCode(error: any): string | null {\n  if (isApiError(error)) {\n    return error.code || null;\n  }\n\n  return null;\n}\n\n/**\n * Check if error should trigger a retry\n */\nexport function shouldRetry(error: any): boolean {\n  if (isNetworkError(error)) {\n    return true;\n  }\n\n  if (isApiError(error)) {\n    // Retry on server errors but not client errors\n    return error.isServerError;\n  }\n\n  return false;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;AAEM,MAAM,iBAAiB;IACZ,OAAe;IACf,KAAc;IACd,QAAc;IAE9B,YAAY,OAAe,EAAE,SAAiB,GAAG,EAAE,IAAa,EAAE,OAAa,CAAE;QAC/E,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QAEf,qFAAqF;QACrF,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE;QAChC;IACF;IAEA;;GAEC,GACD,IAAI,gBAAyB;QAC3B,OAAO,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG;IAC7C;IAEA;;GAEC,GACD,IAAI,gBAAyB;QAC3B,OAAO,IAAI,CAAC,MAAM,IAAI;IACxB;IAEA;;GAEC,GACD,SAAS;QACP,OAAO;YACL,MAAM,IAAI,CAAC,IAAI;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,IAAI,CAAC,MAAM;YACnB,MAAM,IAAI,CAAC,IAAI;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,OAAO,IAAI,CAAC,KAAK;QACnB;IACF;AACF;AAEO,MAAM,qBAAqB;IAChC,YAAY,UAAkB,wBAAwB,CAAE;QACtD,KAAK,CAAC,SAAS,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,4BAA4B;IACvC,YAAY,UAAkB,yBAAyB,CAAE;QACvD,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,2BAA2B;IACtC,YAAY,UAAkB,eAAe,CAAE;QAC7C,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,wBAAwB;IACnB,YAAsC;IAEtD,YAAY,UAAkB,mBAAmB,EAAE,cAAwC,CAAC,CAAC,CAAE;QAC7F,KAAK,CAAC,SAAS,KAAK,oBAAoB;QACxC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;;GAEC,GACD,cAAc,KAAa,EAAiB;QAC1C,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM;QACtC,OAAO,UAAU,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG;IACnD;IAEA;;GAEC,GACD,cAAc,KAAa,EAAW;QACpC,OAAO,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;IAC1C;IAEA;;GAEC,GACD,oBAA8B;QAC5B,OAAO,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI;IAC7C;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,UAAkB,oBAAoB,CAAE;QAClD,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,UAAkB,mBAAmB,CAAE;QACjD,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,uBAAuB;IAClB,WAAoB;IAEpC,YAAY,UAAkB,qBAAqB,EAAE,UAAmB,CAAE;QACxE,KAAK,CAAC,SAAS,KAAK,oBAAoB;YAAE;QAAW;QACrD,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG;IACpB;AACF;AAKO,SAAS,WAAW,KAAU;IACnC,OAAO,iBAAiB;AAC1B;AAKO,SAAS,eAAe,KAAU;IACvC,OAAO,iBAAiB;AAC1B;AAKO,SAAS,sBAAsB,KAAU;IAC9C,OAAO,iBAAiB;AAC1B;AAKO,SAAS,kBAAkB,KAAU;IAC1C,OAAO,iBAAiB;AAC1B;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,WAAW,QAAQ;QACrB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,aAAa,KAAU;IACrC,IAAI,WAAW,QAAQ;QACrB,OAAO,MAAM,IAAI,IAAI;IACvB;IAEA,OAAO;AACT;AAKO,SAAS,YAAY,KAAU;IACpC,IAAI,eAAe,QAAQ;QACzB,OAAO;IACT;IAEA,IAAI,WAAW,QAAQ;QACrB,+CAA+C;QAC/C,OAAO,MAAM,aAAa;IAC5B;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/client.ts"], "sourcesContent": ["/**\n * API Client for AI-fitness application\n * Handles HTTP requests, authentication, and error handling\n */\n\nimport { API_CONFIG } from './config';\nimport { ApiError, NetworkError, AuthenticationError, ValidationError } from './errors';\n\nexport interface ApiRequestOptions extends RequestInit {\n  timeout?: number;\n  retries?: number;\n  requireAuth?: boolean;\n}\n\nexport interface ApiResponse<T = any> {\n  data: T;\n  success: boolean;\n  message?: string;\n  errors?: Record<string, string[]>;\n}\n\nclass ApiClient {\n  private baseUrl: string;\n  private defaultHeaders: HeadersInit;\n\n  constructor() {\n    this.baseUrl = API_CONFIG.BASE_URL;\n    this.defaultHeaders = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n    };\n  }\n\n  /**\n   * Get authentication token from session storage or cookies\n   */\n  private getAuthToken(): string | null {\n    if (typeof window !== 'undefined') {\n      // Try to get token from localStorage first\n      const token = localStorage.getItem('auth-token');\n      if (token) {\n        return token;\n      }\n\n      // Try to get user data and extract token\n      const userData = localStorage.getItem('auth-user');\n      if (userData) {\n        try {\n          const user = JSON.parse(userData);\n          return user.token || null;\n        } catch {\n          return null;\n        }\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Build request headers with authentication if available\n   */\n  private buildHeaders(options: ApiRequestOptions = {}): HeadersInit {\n    const headers = { ...this.defaultHeaders };\n\n    // Add authentication header if required and available\n    if (options.requireAuth !== false) {\n      const token = this.getAuthToken();\n      if (token) {\n        (headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;\n      }\n    }\n\n    // Merge with custom headers\n    if (options.headers) {\n      Object.assign(headers, options.headers);\n    }\n\n    return headers;\n  }\n\n  /**\n   * Handle API response and errors\n   */\n  private async handleResponse<T>(response: Response): Promise<T> {\n    const contentType = response.headers.get('content-type');\n    const isJson = contentType?.includes('application/json');\n\n    let data: any;\n    try {\n      data = isJson ? await response.json() : await response.text();\n    } catch (error) {\n      throw new ApiError('Failed to parse response', response.status);\n    }\n\n    if (!response.ok) {\n      switch (response.status) {\n        case 401:\n          throw new AuthenticationError(data.message || 'Authentication required');\n        case 422:\n          throw new ValidationError(data.message || 'Validation failed', data.errors);\n        case 404:\n          throw new ApiError(data.message || 'Resource not found', 404);\n        case 500:\n          throw new ApiError(data.message || 'Internal server error', 500);\n        default:\n          throw new ApiError(data.message || 'Request failed', response.status);\n      }\n    }\n\n    return data;\n  }\n\n  /**\n   * Make HTTP request with retry logic\n   */\n  private async makeRequest<T>(\n    url: string,\n    options: ApiRequestOptions = {}\n  ): Promise<T> {\n    const {\n      timeout = API_CONFIG.TIMEOUT.DEFAULT,\n      retries = API_CONFIG.RETRY.ATTEMPTS,\n      ...requestOptions\n    } = options;\n\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n    const requestConfig: RequestInit = {\n      ...requestOptions,\n      headers: this.buildHeaders(options),\n      signal: controller.signal,\n    };\n\n    let lastError: Error | null = null;\n\n    for (let attempt = 0; attempt <= retries; attempt++) {\n      try {\n        const response = await fetch(`${this.baseUrl}${url}`, requestConfig);\n        clearTimeout(timeoutId);\n        return await this.handleResponse<T>(response);\n      } catch (error) {\n        lastError = error as Error;\n\n        // Don't retry on authentication or validation errors\n        if (error instanceof AuthenticationError || error instanceof ValidationError) {\n          throw error;\n        }\n\n        // Don't retry on the last attempt\n        if (attempt === retries) {\n          break;\n        }\n\n        // Wait before retrying\n        const delay = API_CONFIG.RETRY.DELAY * Math.pow(API_CONFIG.RETRY.BACKOFF_FACTOR, attempt);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n\n    clearTimeout(timeoutId);\n\n    // Handle network errors\n    if (lastError) {\n      if (lastError.name === 'AbortError') {\n        throw new NetworkError('Request timeout');\n      }\n\n      if (lastError.name === 'TypeError') {\n        throw new NetworkError('Network error - please check your connection');\n      }\n\n      throw lastError;\n    }\n\n    throw new Error('Request failed after all retries');\n  }\n\n  /**\n   * GET request\n   */\n  async get<T>(url: string, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, { ...options, method: 'GET' });\n  }\n\n  /**\n   * POST request\n   */\n  async post<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, {\n      ...options,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  /**\n   * PUT request\n   */\n  async put<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, {\n      ...options,\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  /**\n   * PATCH request\n   */\n  async patch<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, {\n      ...options,\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  /**\n   * DELETE request\n   */\n  async delete<T>(url: string, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, { ...options, method: 'DELETE' });\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAeA,MAAM;IACI,QAAgB;IAChB,eAA4B;IAEpC,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,2HAAA,CAAA,aAAU,CAAC,QAAQ;QAClC,IAAI,CAAC,cAAc,GAAG;YACpB,gBAAgB;YAChB,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,AAAQ,eAA8B;QACpC,uCAAmC;;QAiBnC;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,aAAa,UAA6B,CAAC,CAAC,EAAe;QACjE,MAAM,UAAU;YAAE,GAAG,IAAI,CAAC,cAAc;QAAC;QAEzC,sDAAsD;QACtD,IAAI,QAAQ,WAAW,KAAK,OAAO;YACjC,MAAM,QAAQ,IAAI,CAAC,YAAY;YAC/B,IAAI,OAAO;gBACR,OAAkC,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;YAC1E;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,OAAO,EAAE;YACnB,OAAO,MAAM,CAAC,SAAS,QAAQ,OAAO;QACxC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,eAAkB,QAAkB,EAAc;QAC9D,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,aAAa,SAAS;QAErC,IAAI;QACJ,IAAI;YACF,OAAO,SAAS,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI;QAC7D,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,2HAAA,CAAA,WAAQ,CAAC,4BAA4B,SAAS,MAAM;QAChE;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAQ,SAAS,MAAM;gBACrB,KAAK;oBACH,MAAM,IAAI,2HAAA,CAAA,sBAAmB,CAAC,KAAK,OAAO,IAAI;gBAChD,KAAK;oBACH,MAAM,IAAI,2HAAA,CAAA,kBAAe,CAAC,KAAK,OAAO,IAAI,qBAAqB,KAAK,MAAM;gBAC5E,KAAK;oBACH,MAAM,IAAI,2HAAA,CAAA,WAAQ,CAAC,KAAK,OAAO,IAAI,sBAAsB;gBAC3D,KAAK;oBACH,MAAM,IAAI,2HAAA,CAAA,WAAQ,CAAC,KAAK,OAAO,IAAI,yBAAyB;gBAC9D;oBACE,MAAM,IAAI,2HAAA,CAAA,WAAQ,CAAC,KAAK,OAAO,IAAI,kBAAkB,SAAS,MAAM;YACxE;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,YACZ,GAAW,EACX,UAA6B,CAAC,CAAC,EACnB;QACZ,MAAM,EACJ,UAAU,2HAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,EACpC,UAAU,2HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,QAAQ,EACnC,GAAG,gBACJ,GAAG;QAEJ,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;QAEvD,MAAM,gBAA6B;YACjC,GAAG,cAAc;YACjB,SAAS,IAAI,CAAC,YAAY,CAAC;YAC3B,QAAQ,WAAW,MAAM;QAC3B;QAEA,IAAI,YAA0B;QAE9B,IAAK,IAAI,UAAU,GAAG,WAAW,SAAS,UAAW;YACnD,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE;gBACtD,aAAa;gBACb,OAAO,MAAM,IAAI,CAAC,cAAc,CAAI;YACtC,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,qDAAqD;gBACrD,IAAI,iBAAiB,2HAAA,CAAA,sBAAmB,IAAI,iBAAiB,2HAAA,CAAA,kBAAe,EAAE;oBAC5E,MAAM;gBACR;gBAEA,kCAAkC;gBAClC,IAAI,YAAY,SAAS;oBACvB;gBACF;gBAEA,uBAAuB;gBACvB,MAAM,QAAQ,2HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,2HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,cAAc,EAAE;gBACjF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,aAAa;QAEb,wBAAwB;QACxB,IAAI,WAAW;YACb,IAAI,UAAU,IAAI,KAAK,cAAc;gBACnC,MAAM,IAAI,2HAAA,CAAA,eAAY,CAAC;YACzB;YAEA,IAAI,UAAU,IAAI,KAAK,aAAa;gBAClC,MAAM,IAAI,2HAAA,CAAA,eAAY,CAAC;YACzB;YAEA,MAAM;QACR;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA;;GAEC,GACD,MAAM,IAAO,GAAW,EAAE,UAA6B,CAAC,CAAC,EAAc;QACrE,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAC9D;IAEA;;GAEC,GACD,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAE,UAA6B,CAAC,CAAC,EAAc;QAClF,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAC9B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,IAAO,GAAW,EAAE,IAAU,EAAE,UAA6B,CAAC,CAAC,EAAc;QACjF,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAC9B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,MAAS,GAAW,EAAE,IAAU,EAAE,UAA6B,CAAC,CAAC,EAAc;QACnF,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAC9B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,OAAU,GAAW,EAAE,UAA6B,CAAC,CAAC,EAAc;QACxE,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IACjE;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/auth.ts"], "sourcesContent": ["/**\n * Authentication API Service\n * Handles authentication-related API calls to workout-cool backend\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport type { User, Session, AuthCredentials, SignUpData } from '../types';\n\nexport class AuthService {\n  /**\n   * Sign in with email and password\n   */\n  static async signIn(credentials: AuthCredentials): Promise<Session> {\n    return apiClient.post<Session>(API_CONFIG.ENDPOINTS.AUTH.SIGNIN, credentials, {\n      requireAuth: false,\n    });\n  }\n\n  /**\n   * Sign up with user data\n   */\n  static async signUp(userData: SignUpData): Promise<Session> {\n    return apiClient.post<Session>(API_CONFIG.ENDPOINTS.AUTH.SIGNUP, userData, {\n      requireAuth: false,\n    });\n  }\n\n  /**\n   * Sign out current user\n   */\n  static async signOut(): Promise<void> {\n    return apiClient.post<void>(API_CONFIG.ENDPOINTS.AUTH.SIGNOUT);\n  }\n\n  /**\n   * Get current session\n   */\n  static async getSession(): Promise<Session | null> {\n    try {\n      return await apiClient.get<Session>(API_CONFIG.ENDPOINTS.AUTH.SESSION);\n    } catch (error: any) {\n      // Return null if not authenticated\n      if (error.status === 401) {\n        return null;\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * Request password reset\n   */\n  static async requestPasswordReset(email: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(\n      API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD,\n      { email },\n      { requireAuth: false }\n    );\n  }\n\n  /**\n   * Reset password with token\n   */\n  static async resetPassword(\n    token: string,\n    newPassword: string\n  ): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(\n      API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD,\n      { token, password: newPassword },\n      { requireAuth: false }\n    );\n  }\n\n  /**\n   * Verify email with token\n   */\n  static async verifyEmail(token: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(\n      '/api/auth/verify-email',\n      { token },\n      { requireAuth: false }\n    );\n  }\n\n  /**\n   * Refresh authentication token\n   */\n  static async refreshToken(): Promise<Session> {\n    return apiClient.post<Session>('/api/auth/refresh');\n  }\n\n  /**\n   * Update user profile\n   */\n  static async updateProfile(updates: Partial<User>): Promise<User> {\n    return apiClient.patch<User>('/api/auth/profile', updates);\n  }\n\n  /**\n   * Change password\n   */\n  static async changePassword(\n    currentPassword: string,\n    newPassword: string\n  ): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>('/api/auth/change-password', {\n      currentPassword,\n      newPassword,\n    });\n  }\n\n  /**\n   * Delete account\n   */\n  static async deleteAccount(password: string): Promise<{ message: string }> {\n    return apiClient.delete<{ message: string }>('/api/auth/account', {\n      body: JSON.stringify({ password }),\n    });\n  }\n\n  /**\n   * Get user sessions\n   */\n  static async getUserSessions(): Promise<Array<{\n    id: string;\n    userAgent: string;\n    ip: string;\n    createdAt: string;\n    lastUsed: string;\n    isCurrent: boolean;\n  }>> {\n    return apiClient.get<Array<{\n      id: string;\n      userAgent: string;\n      ip: string;\n      createdAt: string;\n      lastUsed: string;\n      isCurrent: boolean;\n    }>>('/api/auth/sessions');\n  }\n\n  /**\n   * Revoke a specific session\n   */\n  static async revokeSession(sessionId: string): Promise<{ message: string }> {\n    return apiClient.delete<{ message: string }>(`/api/auth/sessions/${sessionId}`);\n  }\n\n  /**\n   * Revoke all other sessions\n   */\n  static async revokeAllOtherSessions(): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>('/api/auth/sessions/revoke-all');\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAGO,MAAM;IACX;;GAEC,GACD,aAAa,OAAO,WAA4B,EAAoB;QAClE,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAU,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa;YAC5E,aAAa;QACf;IACF;IAEA;;GAEC,GACD,aAAa,OAAO,QAAoB,EAAoB;QAC1D,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAU,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU;YACzE,aAAa;QACf;IACF;IAEA;;GAEC,GACD,aAAa,UAAyB;QACpC,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAO,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;IAC/D;IAEA;;GAEC,GACD,aAAa,aAAsC;QACjD,IAAI;YACF,OAAO,MAAM,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAU,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;QACvE,EAAE,OAAO,OAAY;YACnB,mCAAmC;YACnC,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,qBAAqB,KAAa,EAAgC;QAC7E,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EACxC;YAAE;QAAM,GACR;YAAE,aAAa;QAAM;IAEzB;IAEA;;GAEC,GACD,aAAa,cACX,KAAa,EACb,WAAmB,EACW;QAC9B,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EACxC;YAAE;YAAO,UAAU;QAAY,GAC/B;YAAE,aAAa;QAAM;IAEzB;IAEA;;GAEC,GACD,aAAa,YAAY,KAAa,EAAgC;QACpE,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,0BACA;YAAE;QAAM,GACR;YAAE,aAAa;QAAM;IAEzB;IAEA;;GAEC,GACD,aAAa,eAAiC;QAC5C,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAU;IACjC;IAEA;;GAEC,GACD,aAAa,cAAc,OAAsB,EAAiB;QAChE,OAAO,2HAAA,CAAA,YAAS,CAAC,KAAK,CAAO,qBAAqB;IACpD;IAEA;;GAEC,GACD,aAAa,eACX,eAAuB,EACvB,WAAmB,EACW;QAC9B,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB,6BAA6B;YACtE;YACA;QACF;IACF;IAEA;;GAEC,GACD,aAAa,cAAc,QAAgB,EAAgC;QACzE,OAAO,2HAAA,CAAA,YAAS,CAAC,MAAM,CAAsB,qBAAqB;YAChE,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;IACF;IAEA;;GAEC,GACD,aAAa,kBAOT;QACF,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAOhB;IACN;IAEA;;GAEC,GACD,aAAa,cAAc,SAAiB,EAAgC;QAC1E,OAAO,2HAAA,CAAA,YAAS,CAAC,MAAM,CAAsB,CAAC,mBAAmB,EAAE,WAAW;IAChF;IAEA;;GAEC,GACD,aAAa,yBAAuD;QAClE,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-auth.ts"], "sourcesContent": ["/**\n * Authentication React Query hooks\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { AuthService } from '../api/services/auth';\nimport type { AuthCredentials, SignUpData } from '../api/types';\n\n// Query keys for authentication\nexport const authKeys = {\n  all: ['auth'] as const,\n  session: () => [...authKeys.all, 'session'] as const,\n  sessions: () => [...authKeys.all, 'sessions'] as const,\n};\n\n/**\n * Hook to get current session\n */\nexport function useSession() {\n  return useQuery({\n    queryKey: authKeys.session(),\n    queryFn: () => AuthService.getSession(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    retry: false, // Don't retry on auth failures\n  });\n}\n\n/**\n * Hook to get authentication state\n */\nexport function useAuth() {\n  const { data: session, isLoading, error } = useSession();\n  \n  return {\n    user: session?.user || null,\n    isAuthenticated: !!session?.user,\n    isLoading,\n    error,\n    session,\n  };\n}\n\n/**\n * Hook to check if user is admin\n */\nexport function useIsAdmin() {\n  const { user, isAuthenticated } = useAuth();\n  return isAuthenticated && user?.role === 'admin';\n}\n\n/**\n * Hook to check if user is premium\n */\nexport function useIsPremium() {\n  const { user, isAuthenticated } = useAuth();\n  return isAuthenticated && user?.isPremium === true;\n}\n\n/**\n * Hook for sign in mutation\n */\nexport function useSignIn() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (credentials: AuthCredentials) => AuthService.signIn(credentials),\n    onSuccess: (session) => {\n      // Update session cache\n      queryClient.setQueryData(authKeys.session(), session);\n      \n      // Invalidate all queries to refetch with new auth state\n      queryClient.invalidateQueries();\n    },\n    onError: (error) => {\n      console.error('Sign in failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for sign up mutation\n */\nexport function useSignUp() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (userData: SignUpData) => AuthService.signUp(userData),\n    onSuccess: (session) => {\n      // Update session cache\n      queryClient.setQueryData(authKeys.session(), session);\n      \n      // Invalidate all queries to refetch with new auth state\n      queryClient.invalidateQueries();\n    },\n    onError: (error) => {\n      console.error('Sign up failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for sign out mutation\n */\nexport function useSignOut() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: () => AuthService.signOut(),\n    onSuccess: () => {\n      // Clear session cache\n      queryClient.setQueryData(authKeys.session(), null);\n      \n      // Clear all cached data\n      queryClient.clear();\n    },\n    onError: (error) => {\n      console.error('Sign out failed:', error);\n      \n      // Even if sign out fails on server, clear local cache\n      queryClient.setQueryData(authKeys.session(), null);\n      queryClient.clear();\n    },\n  });\n}\n\n/**\n * Hook for password reset request\n */\nexport function useRequestPasswordReset() {\n  return useMutation({\n    mutationFn: (email: string) => AuthService.requestPasswordReset(email),\n    onError: (error) => {\n      console.error('Password reset request failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for password reset\n */\nexport function useResetPassword() {\n  return useMutation({\n    mutationFn: ({ token, newPassword }: { token: string; newPassword: string }) =>\n      AuthService.resetPassword(token, newPassword),\n    onError: (error) => {\n      console.error('Password reset failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for email verification\n */\nexport function useVerifyEmail() {\n  return useMutation({\n    mutationFn: (token: string) => AuthService.verifyEmail(token),\n    onError: (error) => {\n      console.error('Email verification failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for profile update\n */\nexport function useUpdateProfile() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (updates: Parameters<typeof AuthService.updateProfile>[0]) =>\n      AuthService.updateProfile(updates),\n    onSuccess: (updatedUser) => {\n      // Update session cache with new user data\n      const currentSession = queryClient.getQueryData(authKeys.session());\n      if (currentSession) {\n        queryClient.setQueryData(authKeys.session(), {\n          ...currentSession,\n          user: updatedUser,\n        });\n      }\n    },\n    onError: (error) => {\n      console.error('Profile update failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for password change\n */\nexport function useChangePassword() {\n  return useMutation({\n    mutationFn: ({ currentPassword, newPassword }: {\n      currentPassword: string;\n      newPassword: string;\n    }) => AuthService.changePassword(currentPassword, newPassword),\n    onError: (error) => {\n      console.error('Password change failed:', error);\n    },\n  });\n}\n\n/**\n * Hook to get user sessions\n */\nexport function useUserSessions() {\n  return useQuery({\n    queryKey: authKeys.sessions(),\n    queryFn: () => AuthService.getUserSessions(),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Hook for session revocation\n */\nexport function useRevokeSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (sessionId: string) => AuthService.revokeSession(sessionId),\n    onSuccess: () => {\n      // Refetch sessions list\n      queryClient.invalidateQueries({ queryKey: authKeys.sessions() });\n    },\n    onError: (error) => {\n      console.error('Session revocation failed:', error);\n    },\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;AAED;AAAA;AAAA;AACA;;;AAIO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAO;IACb,SAAS,IAAM;eAAI,SAAS,GAAG;YAAE;SAAU;IAC3C,UAAU,IAAM;eAAI,SAAS,GAAG;YAAE;SAAW;AAC/C;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,OAAO;QAC1B,SAAS,IAAM,qIAAA,CAAA,cAAW,CAAC,UAAU;QACrC,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;AACF;AAKO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAE5C,OAAO;QACL,MAAM,SAAS,QAAQ;QACvB,iBAAiB,CAAC,CAAC,SAAS;QAC5B;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;IAClC,OAAO,mBAAmB,MAAM,SAAS;AAC3C;AAKO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;IAClC,OAAO,mBAAmB,MAAM,cAAc;AAChD;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,cAAiC,qIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;QACjE,WAAW,CAAC;YACV,uBAAuB;YACvB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;YAE7C,wDAAwD;YACxD,YAAY,iBAAiB;QAC/B;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,WAAyB,qIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;QACzD,WAAW,CAAC;YACV,uBAAuB;YACvB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;YAE7C,wDAAwD;YACxD,YAAY,iBAAiB;QAC/B;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,IAAM,qIAAA,CAAA,cAAW,CAAC,OAAO;QACrC,WAAW;YACT,sBAAsB;YACtB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;YAE7C,wBAAwB;YACxB,YAAY,KAAK;QACnB;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,oBAAoB;YAElC,sDAAsD;YACtD,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;YAC7C,YAAY,KAAK;QACnB;IACF;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,QAAkB,qIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC;QAChE,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,KAAK,EAAE,WAAW,EAA0C,GACzE,qIAAA,CAAA,cAAW,CAAC,aAAa,CAAC,OAAO;QACnC,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,QAAkB,qIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;QACvD,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,UACX,qIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;QAC5B,WAAW,CAAC;YACV,0CAA0C;YAC1C,MAAM,iBAAiB,YAAY,YAAY,CAAC,SAAS,OAAO;YAChE,IAAI,gBAAgB;gBAClB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;oBAC3C,GAAG,cAAc;oBACjB,MAAM;gBACR;YACF;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,eAAe,EAAE,WAAW,EAG1C,GAAK,qIAAA,CAAA,cAAW,CAAC,cAAc,CAAC,iBAAiB;QAClD,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,QAAQ;QAC3B,SAAS,IAAM,qIAAA,CAAA,cAAW,CAAC,eAAe;QAC1C,WAAW,IAAI,KAAK;IACtB;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,YAAsB,qIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;QAC7D,WAAW;YACT,wBAAwB;YACxB,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,QAAQ;YAAG;QAChE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;AACF", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/providers/auth-provider.tsx"], "sourcesContent": ["'use client';\n\n/**\n * Authentication Provider for AI-fitness application\n * Provides authentication context and session management\n */\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { useAuth as useAuthQuery } from '../hooks/use-auth';\nimport type { User } from '../api/types';\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: Error | null;\n  refreshAuth: () => void;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const { user, isAuthenticated, isLoading, error } = useAuthQuery();\n  const [refreshKey, setRefreshKey] = useState(0);\n\n  const refreshAuth = () => {\n    setRefreshKey(prev => prev + 1);\n  };\n\n  // Update API client with auth token when user changes\n  useEffect(() => {\n    if (user && typeof window !== 'undefined') {\n      // Store auth state in localStorage for API client\n      localStorage.setItem('auth-user', JSON.stringify(user));\n    } else if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth-user');\n    }\n  }, [user]);\n\n  const contextValue: AuthContextType = {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    refreshAuth,\n  };\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\n/**\n * Hook to use authentication context\n */\nexport function useAuthContext() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuthContext must be used within an AuthProvider');\n  }\n  return context;\n}\n\n/**\n * Higher-order component to protect routes that require authentication\n */\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>\n): React.ComponentType<P> {\n  return function AuthenticatedComponent(props: P) {\n    const { isAuthenticated, isLoading } = useAuthContext();\n\n    if (isLoading) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n      );\n    }\n\n    if (!isAuthenticated) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-2xl font-bold text-center text-gray-900 mb-4\">\n              Authentication Required\n            </h2>\n            <p className=\"text-gray-600 text-center mb-6\">\n              Please sign in to access this page.\n            </p>\n            <div className=\"space-y-3\">\n              <button\n                onClick={() => window.location.href = '/auth/signin'}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Sign In\n              </button>\n              <button\n                onClick={() => window.location.href = '/auth/signup'}\n                className=\"w-full bg-gray-200 text-gray-900 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\"\n              >\n                Sign Up\n              </button>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return <Component {...props} />;\n  };\n}\n\n/**\n * Higher-order component to protect routes that require admin access\n */\nexport function withAdminAuth<P extends object>(\n  Component: React.ComponentType<P>\n): React.ComponentType<P> {\n  return function AdminAuthenticatedComponent(props: P) {\n    const { user, isAuthenticated, isLoading } = useAuthContext();\n\n    if (isLoading) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n      );\n    }\n\n    if (!isAuthenticated || user?.role !== 'admin') {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-2xl font-bold text-center text-gray-900 mb-4\">\n              Admin Access Required\n            </h2>\n            <p className=\"text-gray-600 text-center mb-6\">\n              You need administrator privileges to access this page.\n            </p>\n            <button\n              onClick={() => window.location.href = '/'}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              Go Home\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return <Component {...props} />;\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;CAGC,GAED;AACA;AARA;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAY,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,cAAc;QAClB,cAAc,CAAA,OAAQ,OAAO;IAC/B;IAEA,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAA2C;;QAG3C,OAAO,uCAAmC;;QAE1C;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAgC;QACpC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAKO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAKO,SAAS,SACd,SAAiC;IAEjC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;QAEvC,IAAI,WAAW;YACb,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,CAAC,iBAAiB;YACpB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;QAOX;QAEA,qBAAO,8OAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;AACF;AAKO,SAAS,cACd,SAAiC;IAEjC,OAAO,SAAS,4BAA4B,KAAQ;QAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;QAE7C,IAAI,WAAW;YACb,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,CAAC,mBAAmB,MAAM,SAAS,SAAS;YAC9C,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,qBAAO,8OAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/store/app-store.ts"], "sourcesContent": ["/**\n * Global Application State Management with Zustand\n * Handles app-wide state that needs to persist across components\n */\n\nimport { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport { immer } from 'zustand/middleware/immer';\nimport type { User } from '../api/types';\n\n// ============================================================================\n// APP STATE INTERFACES\n// ============================================================================\n\ninterface AppSettings {\n  theme: 'light' | 'dark' | 'system';\n  language: 'en' | 'zh';\n  units: 'metric' | 'imperial';\n  notifications: {\n    workoutReminders: boolean;\n    progressUpdates: boolean;\n    achievements: boolean;\n    marketing: boolean;\n  };\n  privacy: {\n    shareProgress: boolean;\n    showInLeaderboards: boolean;\n    allowDataCollection: boolean;\n  };\n}\n\ninterface WorkoutPreferences {\n  defaultDuration: number; // minutes\n  preferredDifficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';\n  favoriteCategories: string[];\n  excludedEquipment: string[];\n  restTimeBetweenSets: number; // seconds\n  autoStartNextExercise: boolean;\n  playWorkoutMusic: boolean;\n  voiceInstructions: boolean;\n}\n\ninterface UIState {\n  sidebarCollapsed: boolean;\n  activeWorkoutSession: string | null;\n  currentPage: string;\n  breadcrumbs: Array<{ label: string; href: string }>;\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    title: string;\n    message: string;\n    timestamp: number;\n    read: boolean;\n  }>;\n  modals: {\n    workoutComplete: boolean;\n    goalAchieved: boolean;\n    subscriptionPrompt: boolean;\n  };\n}\n\ninterface OfflineState {\n  isOnline: boolean;\n  pendingSync: Array<{\n    id: string;\n    type: 'workout' | 'progress' | 'goal';\n    action: 'create' | 'update' | 'delete';\n    data: any;\n    timestamp: number;\n  }>;\n  lastSyncTime: number | null;\n}\n\ninterface AppState {\n  // User & Auth\n  user: User | null;\n  isAuthenticated: boolean;\n  \n  // App Settings\n  settings: AppSettings;\n  workoutPreferences: WorkoutPreferences;\n  \n  // UI State\n  ui: UIState;\n  \n  // Offline Support\n  offline: OfflineState;\n  \n  // Actions\n  setUser: (user: User | null) => void;\n  setAuthenticated: (authenticated: boolean) => void;\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  updateWorkoutPreferences: (preferences: Partial<WorkoutPreferences>) => void;\n  updateUIState: (ui: Partial<UIState>) => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n  setOnlineStatus: (isOnline: boolean) => void;\n  addPendingSync: (item: Omit<OfflineState['pendingSync'][0], 'id' | 'timestamp'>) => void;\n  removePendingSync: (id: string) => void;\n  clearPendingSync: () => void;\n  updateLastSyncTime: () => void;\n  reset: () => void;\n}\n\n// ============================================================================\n// DEFAULT VALUES\n// ============================================================================\n\nconst defaultSettings: AppSettings = {\n  theme: 'system',\n  language: 'en',\n  units: 'metric',\n  notifications: {\n    workoutReminders: true,\n    progressUpdates: true,\n    achievements: true,\n    marketing: false,\n  },\n  privacy: {\n    shareProgress: false,\n    showInLeaderboards: true,\n    allowDataCollection: true,\n  },\n};\n\nconst defaultWorkoutPreferences: WorkoutPreferences = {\n  defaultDuration: 45,\n  preferredDifficulty: 'INTERMEDIATE',\n  favoriteCategories: [],\n  excludedEquipment: [],\n  restTimeBetweenSets: 60,\n  autoStartNextExercise: false,\n  playWorkoutMusic: true,\n  voiceInstructions: false,\n};\n\nconst defaultUIState: UIState = {\n  sidebarCollapsed: false,\n  activeWorkoutSession: null,\n  currentPage: '/',\n  breadcrumbs: [],\n  notifications: [],\n  modals: {\n    workoutComplete: false,\n    goalAchieved: false,\n    subscriptionPrompt: false,\n  },\n};\n\nconst defaultOfflineState: OfflineState = {\n  isOnline: true,\n  pendingSync: [],\n  lastSyncTime: null,\n};\n\n// ============================================================================\n// ZUSTAND STORE\n// ============================================================================\n\nexport const useAppStore = create<AppState>()(\n  persist(\n    immer((set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      settings: defaultSettings,\n      workoutPreferences: defaultWorkoutPreferences,\n      ui: defaultUIState,\n      offline: defaultOfflineState,\n\n      // Actions\n      setUser: (user) => set((state) => {\n        state.user = user;\n      }),\n\n      setAuthenticated: (authenticated) => set((state) => {\n        state.isAuthenticated = authenticated;\n        if (!authenticated) {\n          state.user = null;\n        }\n      }),\n\n      updateSettings: (newSettings) => set((state) => {\n        Object.assign(state.settings, newSettings);\n      }),\n\n      updateWorkoutPreferences: (newPreferences) => set((state) => {\n        Object.assign(state.workoutPreferences, newPreferences);\n      }),\n\n      updateUIState: (newUIState) => set((state) => {\n        Object.assign(state.ui, newUIState);\n      }),\n\n      addNotification: (notification) => set((state) => {\n        // Ensure notifications array exists\n        if (!state.ui.notifications) {\n          state.ui.notifications = [];\n        }\n\n        const newNotification = {\n          ...notification,\n          id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          timestamp: Date.now(),\n          read: false,\n        };\n        state.ui.notifications.unshift(newNotification);\n\n        // Keep only last 50 notifications\n        if (state.ui.notifications.length > 50) {\n          state.ui.notifications = state.ui.notifications.slice(0, 50);\n        }\n      }),\n\n      markNotificationRead: (id) => set((state) => {\n        // Ensure notifications array exists\n        if (!state.ui.notifications) {\n          state.ui.notifications = [];\n          return;\n        }\n\n        const notification = state.ui.notifications.find(n => n.id === id);\n        if (notification) {\n          notification.read = true;\n        }\n      }),\n\n      clearNotifications: () => set((state) => {\n        state.ui.notifications = [];\n      }),\n\n      setOnlineStatus: (isOnline) => set((state) => {\n        state.offline.isOnline = isOnline;\n      }),\n\n      addPendingSync: (item) => set((state) => {\n        const newItem = {\n          ...item,\n          id: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          timestamp: Date.now(),\n        };\n        state.offline.pendingSync.push(newItem);\n      }),\n\n      removePendingSync: (id) => set((state) => {\n        state.offline.pendingSync = state.offline.pendingSync.filter(item => item.id !== id);\n      }),\n\n      clearPendingSync: () => set((state) => {\n        state.offline.pendingSync = [];\n      }),\n\n      updateLastSyncTime: () => set((state) => {\n        state.offline.lastSyncTime = Date.now();\n      }),\n\n      reset: () => set(() => ({\n        user: null,\n        isAuthenticated: false,\n        settings: defaultSettings,\n        workoutPreferences: defaultWorkoutPreferences,\n        ui: defaultUIState,\n        offline: defaultOfflineState,\n      })),\n    })),\n    {\n      name: 'ai-fitness-app-store',\n      storage: createJSONStorage(() => localStorage),\n      partialize: (state) => ({\n        // Only persist certain parts of the state\n        settings: state.settings,\n        workoutPreferences: state.workoutPreferences,\n        ui: {\n          sidebarCollapsed: state.ui.sidebarCollapsed,\n          // Don't persist notifications and modals\n          notifications: [], // Always initialize as empty array\n        },\n        offline: {\n          pendingSync: state.offline.pendingSync,\n          lastSyncTime: state.offline.lastSyncTime,\n          // Don't persist online status\n        },\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// SELECTORS\n// ============================================================================\n\n// Convenience selectors for commonly used state\nexport const useUser = () => useAppStore((state) => state.user);\nexport const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);\nexport const useSettings = () => useAppStore((state) => state.settings);\nexport const useWorkoutPreferences = () => useAppStore((state) => state.workoutPreferences);\nexport const useUIState = () => useAppStore((state) => state.ui);\nexport const useNotifications = () => useAppStore((state) => state.ui.notifications || []);\nexport const useOfflineState = () => useAppStore((state) => state.offline);\n\n// Computed selectors\nexport const useUnreadNotificationCount = () =>\n  useAppStore((state) => (state.ui.notifications || []).filter(n => !n.read).length);\n\nexport const useHasPendingSync = () => \n  useAppStore((state) => state.offline.pendingSync.length > 0);\n\nexport const useIsOffline = () => \n  useAppStore((state) => !state.offline.isOnline);\n\n// ============================================================================\n// STORE ACTIONS\n// ============================================================================\n\n// Export actions for use outside of components\nexport const appActions = {\n  setUser: (user: User | null) => useAppStore.getState().setUser(user),\n  setAuthenticated: (authenticated: boolean) => useAppStore.getState().setAuthenticated(authenticated),\n  updateSettings: (settings: Partial<AppSettings>) => useAppStore.getState().updateSettings(settings),\n  updateWorkoutPreferences: (preferences: Partial<WorkoutPreferences>) => useAppStore.getState().updateWorkoutPreferences(preferences),\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => useAppStore.getState().addNotification(notification),\n  setOnlineStatus: (isOnline: boolean) => useAppStore.getState().setOnlineStatus(isOnline),\n  addPendingSync: (item: Omit<OfflineState['pendingSync'][0], 'id' | 'timestamp'>) => useAppStore.getState().addPendingSync(item),\n  removePendingSync: (id: string) => useAppStore.getState().removePendingSync(id),\n  clearPendingSync: () => useAppStore.getState().clearPendingSync(),\n  updateLastSyncTime: () => useAppStore.getState().updateLastSyncTime(),\n  reset: () => useAppStore.getState().reset(),\n};\n\n// ============================================================================\n// TYPES EXPORT\n// ============================================================================\n\nexport type { AppSettings, WorkoutPreferences, UIState, OfflineState };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAED;AACA;AACA;;;;AAmGA,+EAA+E;AAC/E,iBAAiB;AACjB,+EAA+E;AAE/E,MAAM,kBAA+B;IACnC,OAAO;IACP,UAAU;IACV,OAAO;IACP,eAAe;QACb,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,WAAW;IACb;IACA,SAAS;QACP,eAAe;QACf,oBAAoB;QACpB,qBAAqB;IACvB;AACF;AAEA,MAAM,4BAAgD;IACpD,iBAAiB;IACjB,qBAAqB;IACrB,oBAAoB,EAAE;IACtB,mBAAmB,EAAE;IACrB,qBAAqB;IACrB,uBAAuB;IACvB,kBAAkB;IAClB,mBAAmB;AACrB;AAEA,MAAM,iBAA0B;IAC9B,kBAAkB;IAClB,sBAAsB;IACtB,aAAa;IACb,aAAa,EAAE;IACf,eAAe,EAAE;IACjB,QAAQ;QACN,iBAAiB;QACjB,cAAc;QACd,oBAAoB;IACtB;AACF;AAEA,MAAM,sBAAoC;IACxC,UAAU;IACV,aAAa,EAAE;IACf,cAAc;AAChB;AAMO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnB,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU;QACV,oBAAoB;QACpB,IAAI;QACJ,SAAS;QAET,UAAU;QACV,SAAS,CAAC,OAAS,IAAI,CAAC;gBACtB,MAAM,IAAI,GAAG;YACf;QAEA,kBAAkB,CAAC,gBAAkB,IAAI,CAAC;gBACxC,MAAM,eAAe,GAAG;gBACxB,IAAI,CAAC,eAAe;oBAClB,MAAM,IAAI,GAAG;gBACf;YACF;QAEA,gBAAgB,CAAC,cAAgB,IAAI,CAAC;gBACpC,OAAO,MAAM,CAAC,MAAM,QAAQ,EAAE;YAChC;QAEA,0BAA0B,CAAC,iBAAmB,IAAI,CAAC;gBACjD,OAAO,MAAM,CAAC,MAAM,kBAAkB,EAAE;YAC1C;QAEA,eAAe,CAAC,aAAe,IAAI,CAAC;gBAClC,OAAO,MAAM,CAAC,MAAM,EAAE,EAAE;YAC1B;QAEA,iBAAiB,CAAC,eAAiB,IAAI,CAAC;gBACtC,oCAAoC;gBACpC,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE;oBAC3B,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE;gBAC7B;gBAEA,MAAM,kBAAkB;oBACtB,GAAG,YAAY;oBACf,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBAC3E,WAAW,KAAK,GAAG;oBACnB,MAAM;gBACR;gBACA,MAAM,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;gBAE/B,kCAAkC;gBAClC,IAAI,MAAM,EAAE,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI;oBACtC,MAAM,EAAE,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG;gBAC3D;YACF;QAEA,sBAAsB,CAAC,KAAO,IAAI,CAAC;gBACjC,oCAAoC;gBACpC,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE;oBAC3B,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE;oBAC3B;gBACF;gBAEA,MAAM,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC/D,IAAI,cAAc;oBAChB,aAAa,IAAI,GAAG;gBACtB;YACF;QAEA,oBAAoB,IAAM,IAAI,CAAC;gBAC7B,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE;YAC7B;QAEA,iBAAiB,CAAC,WAAa,IAAI,CAAC;gBAClC,MAAM,OAAO,CAAC,QAAQ,GAAG;YAC3B;QAEA,gBAAgB,CAAC,OAAS,IAAI,CAAC;gBAC7B,MAAM,UAAU;oBACd,GAAG,IAAI;oBACP,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACnE,WAAW,KAAK,GAAG;gBACrB;gBACA,MAAM,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;YACjC;QAEA,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,OAAO,CAAC,WAAW,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACnF;QAEA,kBAAkB,IAAM,IAAI,CAAC;gBAC3B,MAAM,OAAO,CAAC,WAAW,GAAG,EAAE;YAChC;QAEA,oBAAoB,IAAM,IAAI,CAAC;gBAC7B,MAAM,OAAO,CAAC,YAAY,GAAG,KAAK,GAAG;YACvC;QAEA,OAAO,IAAM,IAAI,IAAM,CAAC;oBACtB,MAAM;oBACN,iBAAiB;oBACjB,UAAU;oBACV,oBAAoB;oBACpB,IAAI;oBACJ,SAAS;gBACX,CAAC;IACH,CAAC,IACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,0CAA0C;YAC1C,UAAU,MAAM,QAAQ;YACxB,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;gBAC3C,yCAAyC;gBACzC,eAAe,EAAE;YACnB;YACA,SAAS;gBACP,aAAa,MAAM,OAAO,CAAC,WAAW;gBACtC,cAAc,MAAM,OAAO,CAAC,YAAY;YAE1C;QACF,CAAC;AACH;AASG,MAAM,UAAU,IAAM,YAAY,CAAC,QAAU,MAAM,IAAI;AACvD,MAAM,qBAAqB,IAAM,YAAY,CAAC,QAAU,MAAM,eAAe;AAC7E,MAAM,cAAc,IAAM,YAAY,CAAC,QAAU,MAAM,QAAQ;AAC/D,MAAM,wBAAwB,IAAM,YAAY,CAAC,QAAU,MAAM,kBAAkB;AACnF,MAAM,aAAa,IAAM,YAAY,CAAC,QAAU,MAAM,EAAE;AACxD,MAAM,mBAAmB,IAAM,YAAY,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa,IAAI,EAAE;AAClF,MAAM,kBAAkB,IAAM,YAAY,CAAC,QAAU,MAAM,OAAO;AAGlE,MAAM,6BAA6B,IACxC,YAAY,CAAC,QAAU,CAAC,MAAM,EAAE,CAAC,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;AAE5E,MAAM,oBAAoB,IAC/B,YAAY,CAAC,QAAU,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG;AAErD,MAAM,eAAe,IAC1B,YAAY,CAAC,QAAU,CAAC,MAAM,OAAO,CAAC,QAAQ;AAOzC,MAAM,aAAa;IACxB,SAAS,CAAC,OAAsB,YAAY,QAAQ,GAAG,OAAO,CAAC;IAC/D,kBAAkB,CAAC,gBAA2B,YAAY,QAAQ,GAAG,gBAAgB,CAAC;IACtF,gBAAgB,CAAC,WAAmC,YAAY,QAAQ,GAAG,cAAc,CAAC;IAC1F,0BAA0B,CAAC,cAA6C,YAAY,QAAQ,GAAG,wBAAwB,CAAC;IACxH,iBAAiB,CAAC,eAAiF,YAAY,QAAQ,GAAG,eAAe,CAAC;IAC1I,iBAAiB,CAAC,WAAsB,YAAY,QAAQ,GAAG,eAAe,CAAC;IAC/E,gBAAgB,CAAC,OAAmE,YAAY,QAAQ,GAAG,cAAc,CAAC;IAC1H,mBAAmB,CAAC,KAAe,YAAY,QAAQ,GAAG,iBAAiB,CAAC;IAC5E,kBAAkB,IAAM,YAAY,QAAQ,GAAG,gBAAgB;IAC/D,oBAAoB,IAAM,YAAY,QAAQ,GAAG,kBAAkB;IACnE,OAAO,IAAM,YAAY,QAAQ,GAAG,KAAK;AAC3C", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/workouts.ts"], "sourcesContent": ["/**\n * Workout API Service\n * Handles all workout-related API calls\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport type {\n  WorkoutSession,\n  Program,\n  CreateWorkoutSessionData,\n  PaginatedResponse\n} from '../types';\n\nexport class WorkoutService {\n  /**\n   * Get user's workout sessions with optional filtering\n   */\n  static async getWorkoutSessions(params: {\n    limit?: number;\n    offset?: number;\n    status?: string;\n    programId?: string;\n    startDate?: string;\n    endDate?: string;\n  } = {}): Promise<PaginatedResponse<WorkoutSession>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.status) searchParams.append('status', params.status);\n    if (params.programId) searchParams.append('programId', params.programId);\n    if (params.startDate) searchParams.append('startDate', params.startDate);\n    if (params.endDate) searchParams.append('endDate', params.endDate);\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `${API_CONFIG.ENDPOINTS.WORKOUTS.LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.WORKOUTS.LIST;\n\n    return apiClient.get<PaginatedResponse<WorkoutSession>>(url);\n  }\n\n  /**\n   * Get workout session by ID\n   */\n  static async getWorkoutSession(id: string): Promise<WorkoutSession> {\n    return apiClient.get<WorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id));\n  }\n\n  /**\n   * Create a new workout session\n   */\n  static async createWorkoutSession(data: CreateWorkoutSessionData): Promise<WorkoutSession> {\n    return apiClient.post<WorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.CREATE, data);\n  }\n\n  /**\n   * Update workout session\n   */\n  static async updateWorkoutSession(\n    id: string,\n    data: Partial<CreateWorkoutSessionData>\n  ): Promise<WorkoutSession> {\n    return apiClient.patch<WorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.UPDATE(id), data);\n  }\n\n  /**\n   * Delete workout session\n   */\n  static async deleteWorkoutSession(id: string): Promise<void> {\n    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.WORKOUTS.DELETE(id));\n  }\n\n  /**\n   * Start a workout session\n   */\n  static async startWorkoutSession(id: string): Promise<WorkoutSession> {\n    return apiClient.post<WorkoutSession>(`${API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id)}/start`);\n  }\n\n  /**\n   * Complete a workout session\n   */\n  static async completeWorkoutSession(\n    id: string, \n    data: { duration: number; notes?: string }\n  ): Promise<WorkoutSession> {\n    return apiClient.post<WorkoutSession>(\n      `${API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id)}/complete`, \n      data\n    );\n  }\n\n  /**\n   * Get workout programs\n   */\n  static async getWorkoutPrograms(params: {\n    limit?: number;\n    offset?: number;\n    category?: string;\n    difficulty?: string;\n    duration?: string;\n  } = {}): Promise<PaginatedResponse<Program>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.category) searchParams.append('category', params.category);\n    if (params.difficulty) searchParams.append('difficulty', params.difficulty);\n    if (params.duration) searchParams.append('duration', params.duration);\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.PROGRAMS.LIST;\n\n    return apiClient.get<PaginatedResponse<Program>>(url);\n  }\n\n  /**\n   * Get workout program by ID\n   */\n  static async getWorkoutProgram(id: string): Promise<Program> {\n    return apiClient.get<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id));\n  }\n\n  /**\n   * Create a new workout program\n   */\n  static async createWorkoutProgram(data: any): Promise<Program> {\n    return apiClient.post<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.CREATE, data);\n  }\n\n  /**\n   * Update workout program\n   */\n  static async updateWorkoutProgram(\n    id: string,\n    data: any\n  ): Promise<Program> {\n    return apiClient.patch<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.UPDATE(id), data);\n  }\n\n  /**\n   * Delete workout program\n   */\n  static async deleteWorkoutProgram(id: string): Promise<void> {\n    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRAMS.DELETE(id));\n  }\n\n  /**\n   * Join a workout program\n   */\n  static async joinWorkoutProgram(id: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(`${API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id)}/join`);\n  }\n\n  /**\n   * Leave a workout program\n   */\n  static async leaveWorkoutProgram(id: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(`${API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id)}/leave`);\n  }\n\n  /**\n   * Get user's joined programs\n   */\n  static async getUserPrograms(): Promise<Program[]> {\n    const response = await apiClient.get<{ programs: Program[] }>('/api/user/programs');\n    return response.programs || [];\n  }\n\n  /**\n   * Get popular workout programs\n   */\n  static async getPopularPrograms(limit = 10): Promise<Program[]> {\n    const searchParams = new URLSearchParams({\n      sort: 'popular',\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;\n    const response = await apiClient.get<PaginatedResponse<Program>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get recommended workout programs for user\n   */\n  static async getRecommendedPrograms(limit = 6): Promise<Program[]> {\n    const searchParams = new URLSearchParams({\n      recommended: 'true',\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;\n    const response = await apiClient.get<PaginatedResponse<Program>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Generate AI workout plan\n   */\n  static async generateAIWorkout(preferences: {\n    goals: string[];\n    equipment: string[];\n    duration: number;\n    difficulty: string;\n    muscleGroups: string[];\n  }): Promise<WorkoutSession> {\n    return apiClient.post<WorkoutSession>('/api/workouts/generate', preferences);\n  }\n\n  /**\n   * Get workout statistics\n   */\n  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    totalWorkouts: number;\n    totalDuration: number;\n    averageDuration: number;\n    caloriesBurned: number;\n    streakDays: number;\n    completionRate: number;\n    favoriteExercises: Array<{ name: string; count: number }>;\n    weeklyProgress: Array<{ date: string; workouts: number; duration: number }>;\n  }> {\n    return apiClient.get(`/api/workouts/stats?period=${period}`);\n  }\n\n  /**\n   * Get workout history\n   */\n  static async getWorkoutHistory(params: {\n    limit?: number;\n    offset?: number;\n    startDate?: string;\n    endDate?: string;\n  } = {}): Promise<PaginatedResponse<WorkoutSession>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.startDate) searchParams.append('startDate', params.startDate);\n    if (params.endDate) searchParams.append('endDate', params.endDate);\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `/api/workouts/history?${queryString}`\n      : '/api/workouts/history';\n\n    return apiClient.get<PaginatedResponse<WorkoutSession>>(url);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAQO,MAAM;IACX;;GAEC,GACD,aAAa,mBAAmB,SAO5B,CAAC,CAAC,EAA8C;QAClD,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAEjE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,GACtD,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI;QAEtC,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAoC;IAC1D;IAEA;;GAEC,GACD,aAAa,kBAAkB,EAAU,EAA2B;QAClE,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAiB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC7E;IAEA;;GAEC,GACD,aAAa,qBAAqB,IAA8B,EAA2B;QACzF,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAiB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;IAC9E;IAEA;;GAEC,GACD,aAAa,qBACX,EAAU,EACV,IAAuC,EACd;QACzB,OAAO,2HAAA,CAAA,YAAS,CAAC,KAAK,CAAiB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK;IACnF;IAEA;;GAEC,GACD,aAAa,qBAAqB,EAAU,EAAiB;QAC3D,OAAO,2HAAA,CAAA,YAAS,CAAC,MAAM,CAAO,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,aAAa,oBAAoB,EAAU,EAA2B;QACpE,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAiB,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC;IAC5F;IAEA;;GAEC,GACD,aAAa,uBACX,EAAU,EACV,IAA0C,EACjB;QACzB,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,EACvD;IAEJ;IAEA;;GAEC,GACD,aAAa,mBAAmB,SAM5B,CAAC,CAAC,EAAuC;QAC3C,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,QAAQ,EAAE,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACpE,IAAI,OAAO,UAAU,EAAE,aAAa,MAAM,CAAC,cAAc,OAAO,UAAU;QAC1E,IAAI,OAAO,QAAQ,EAAE,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAEpE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,GACtD,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI;QAEtC,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAA6B;IACnD;IAEA;;GAEC,GACD,aAAa,kBAAkB,EAAU,EAAoB;QAC3D,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAU,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;IACtE;IAEA;;GAEC,GACD,aAAa,qBAAqB,IAAS,EAAoB;QAC7D,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAU,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;IACvE;IAEA;;GAEC,GACD,aAAa,qBACX,EAAU,EACV,IAAS,EACS;QAClB,OAAO,2HAAA,CAAA,YAAS,CAAC,KAAK,CAAU,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK;IAC5E;IAEA;;GAEC,GACD,aAAa,qBAAqB,EAAU,EAAiB;QAC3D,OAAO,2HAAA,CAAA,YAAS,CAAC,MAAM,CAAO,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,aAAa,mBAAmB,EAAU,EAAgC;QACxE,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;IAChG;IAEA;;GAEC,GACD,aAAa,oBAAoB,EAAU,EAAgC;QACzE,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC;IACjG;IAEA;;GAEC,GACD,aAAa,kBAAsC;QACjD,MAAM,WAAW,MAAM,2HAAA,CAAA,YAAS,CAAC,GAAG,CAA0B;QAC9D,OAAO,SAAS,QAAQ,IAAI,EAAE;IAChC;IAEA;;GAEC,GACD,aAAa,mBAAmB,QAAQ,EAAE,EAAsB;QAC9D,MAAM,eAAe,IAAI,gBAAgB;YACvC,MAAM;YACN,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAC9E,MAAM,WAAW,MAAM,2HAAA,CAAA,YAAS,CAAC,GAAG,CAA6B;QACjE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,uBAAuB,QAAQ,CAAC,EAAsB;QACjE,MAAM,eAAe,IAAI,gBAAgB;YACvC,aAAa;YACb,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAC9E,MAAM,WAAW,MAAM,2HAAA,CAAA,YAAS,CAAC,GAAG,CAA6B;QACjE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,kBAAkB,WAM9B,EAA2B;QAC1B,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAiB,0BAA0B;IAClE;IAEA;;GAEC,GACD,aAAa,gBAAgB,SAAoC,OAAO,EASrE;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ;IAC7D;IAEA;;GAEC,GACD,aAAa,kBAAkB,SAK3B,CAAC,CAAC,EAA8C;QAClD,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAEjE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,CAAC,sBAAsB,EAAE,aAAa,GACtC;QAEJ,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAoC;IAC1D;AACF", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/progress.ts"], "sourcesContent": ["/**\n * Progress Tracking API Service\n * Handles all progress and analytics related API calls\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport type { \n  ProgressRecord, \n  ProgressStats,\n  CreateProgressRecordData,\n  UpdateProgressRecordData,\n  PaginatedResponse \n} from '../types';\n\nexport class ProgressService {\n  /**\n   * Get user's progress records with optional filtering\n   */\n  static async getProgressRecords(params: {\n    limit?: number;\n    offset?: number;\n    type?: string;\n    startDate?: string;\n    endDate?: string;\n    exerciseId?: string;\n    workoutId?: string;\n  } = {}): Promise<PaginatedResponse<ProgressRecord>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.type) searchParams.append('type', params.type);\n    if (params.startDate) searchParams.append('startDate', params.startDate);\n    if (params.endDate) searchParams.append('endDate', params.endDate);\n    if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);\n    if (params.workoutId) searchParams.append('workoutId', params.workoutId);\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `${API_CONFIG.ENDPOINTS.PROGRESS.LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.PROGRESS.LIST;\n\n    return apiClient.get<PaginatedResponse<ProgressRecord>>(url);\n  }\n\n  /**\n   * Get progress record by ID\n   */\n  static async getProgressRecord(id: string): Promise<ProgressRecord> {\n    return apiClient.get<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.DETAILS(id));\n  }\n\n  /**\n   * Create a new progress record\n   */\n  static async createProgressRecord(data: CreateProgressRecordData): Promise<ProgressRecord> {\n    return apiClient.post<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.CREATE, data);\n  }\n\n  /**\n   * Update progress record\n   */\n  static async updateProgressRecord(\n    id: string, \n    data: UpdateProgressRecordData\n  ): Promise<ProgressRecord> {\n    return apiClient.patch<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.UPDATE(id), data);\n  }\n\n  /**\n   * Delete progress record\n   */\n  static async deleteProgressRecord(id: string): Promise<void> {\n    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRESS.DELETE(id));\n  }\n\n  /**\n   * Get comprehensive progress statistics\n   */\n  static async getProgressStats(period: 'week' | 'month' | 'year' | 'all' = 'month'): Promise<ProgressStats> {\n    return apiClient.get<ProgressStats>(`${API_CONFIG.ENDPOINTS.PROGRESS.STATS}?period=${period}`);\n  }\n\n  /**\n   * Get workout completion statistics\n   */\n  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    totalWorkouts: number;\n    totalDuration: number;\n    averageDuration: number;\n    caloriesBurned: number;\n    streakDays: number;\n    completionRate: number;\n    favoriteExercises: Array<{ name: string; count: number }>;\n    weeklyProgress: Array<{ date: string; workouts: number; duration: number; calories: number }>;\n    monthlyProgress: Array<{ month: string; workouts: number; duration: number; calories: number }>;\n  }> {\n    return apiClient.get(`/api/progress/workout-stats?period=${period}`);\n  }\n\n  /**\n   * Get exercise performance data\n   */\n  static async getExerciseProgress(exerciseId: string, period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    exerciseId: string;\n    exerciseName: string;\n    totalSessions: number;\n    bestPerformance: {\n      weight?: number;\n      reps?: number;\n      duration?: number;\n      distance?: number;\n      date: string;\n    };\n    averagePerformance: {\n      weight?: number;\n      reps?: number;\n      duration?: number;\n      distance?: number;\n    };\n    progressData: Array<{\n      date: string;\n      weight?: number;\n      reps?: number;\n      duration?: number;\n      distance?: number;\n      volume?: number;\n    }>;\n    improvements: {\n      weightIncrease?: number;\n      repsIncrease?: number;\n      durationIncrease?: number;\n      distanceIncrease?: number;\n    };\n  }> {\n    return apiClient.get(`/api/progress/exercise/${exerciseId}?period=${period}`);\n  }\n\n  /**\n   * Get body measurements progress\n   */\n  static async getBodyMeasurements(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    measurements: Array<{\n      date: string;\n      weight?: number;\n      bodyFat?: number;\n      muscleMass?: number;\n      chest?: number;\n      waist?: number;\n      hips?: number;\n      arms?: number;\n      thighs?: number;\n    }>;\n    trends: {\n      weight?: { change: number; percentage: number };\n      bodyFat?: { change: number; percentage: number };\n      muscleMass?: { change: number; percentage: number };\n    };\n  }> {\n    return apiClient.get(`/api/progress/body-measurements?period=${period}`);\n  }\n\n  /**\n   * Add body measurement record\n   */\n  static async addBodyMeasurement(data: {\n    date: string;\n    weight?: number;\n    bodyFat?: number;\n    muscleMass?: number;\n    chest?: number;\n    waist?: number;\n    hips?: number;\n    arms?: number;\n    thighs?: number;\n    notes?: string;\n  }): Promise<{ message: string }> {\n    return apiClient.post('/api/progress/body-measurements', data);\n  }\n\n  /**\n   * Get fitness goals and progress\n   */\n  static async getFitnessGoals(): Promise<{\n    goals: Array<{\n      id: string;\n      title: string;\n      description: string;\n      targetValue: number;\n      currentValue: number;\n      unit: string;\n      deadline: string;\n      category: string;\n      progress: number;\n      status: 'active' | 'completed' | 'paused';\n    }>;\n  }> {\n    return apiClient.get('/api/progress/goals');\n  }\n\n  /**\n   * Create a new fitness goal\n   */\n  static async createFitnessGoal(data: {\n    title: string;\n    description: string;\n    targetValue: number;\n    unit: string;\n    deadline: string;\n    category: string;\n  }): Promise<{ message: string; goalId: string }> {\n    return apiClient.post('/api/progress/goals', data);\n  }\n\n  /**\n   * Update fitness goal progress\n   */\n  static async updateGoalProgress(goalId: string, currentValue: number): Promise<{ message: string }> {\n    return apiClient.patch(`/api/progress/goals/${goalId}`, { currentValue });\n  }\n\n  /**\n   * Get achievement badges and milestones\n   */\n  static async getAchievements(): Promise<{\n    badges: Array<{\n      id: string;\n      name: string;\n      description: string;\n      icon: string;\n      category: string;\n      earnedDate?: string;\n      progress?: number;\n      requirement: number;\n    }>;\n    milestones: Array<{\n      id: string;\n      title: string;\n      description: string;\n      achievedDate: string;\n      category: string;\n    }>;\n  }> {\n    return apiClient.get('/api/progress/achievements');\n  }\n\n  /**\n   * Get workout calendar data\n   */\n  static async getWorkoutCalendar(year: number, month: number): Promise<{\n    calendar: Array<{\n      date: string;\n      workouts: number;\n      duration: number;\n      calories: number;\n      hasWorkout: boolean;\n    }>;\n    monthStats: {\n      totalWorkouts: number;\n      totalDuration: number;\n      totalCalories: number;\n      activeDays: number;\n    };\n  }> {\n    return apiClient.get(`/api/progress/calendar?year=${year}&month=${month}`);\n  }\n\n  /**\n   * Get personal records (PRs)\n   */\n  static async getPersonalRecords(): Promise<{\n    records: Array<{\n      exerciseId: string;\n      exerciseName: string;\n      recordType: 'weight' | 'reps' | 'duration' | 'distance';\n      value: number;\n      unit: string;\n      achievedDate: string;\n      workoutId?: string;\n    }>;\n    recentPRs: Array<{\n      exerciseId: string;\n      exerciseName: string;\n      recordType: string;\n      value: number;\n      unit: string;\n      achievedDate: string;\n      improvement: number;\n    }>;\n  }> {\n    return apiClient.get('/api/progress/personal-records');\n  }\n\n  /**\n   * Get strength progression data\n   */\n  static async getStrengthProgression(exerciseIds?: string[]): Promise<{\n    exercises: Array<{\n      exerciseId: string;\n      exerciseName: string;\n      progressData: Array<{\n        date: string;\n        maxWeight: number;\n        totalVolume: number;\n        oneRepMax: number;\n      }>;\n      trends: {\n        weightTrend: number;\n        volumeTrend: number;\n        oneRepMaxTrend: number;\n      };\n    }>;\n  }> {\n    const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';\n    return apiClient.get(`/api/progress/strength-progression${params}`);\n  }\n\n  /**\n   * Export progress data\n   */\n  static async exportProgressData(format: 'csv' | 'json' = 'csv', period: 'month' | 'year' | 'all' = 'all'): Promise<Blob> {\n    const response = await fetch(`${API_CONFIG.BASE_URL}/api/progress/export?format=${format}&period=${period}`, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error('Failed to export data');\n    }\n\n    return response.blob();\n  }\n\n  /**\n   * Get workout intensity analysis\n   */\n  static async getWorkoutIntensity(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    averageIntensity: number;\n    intensityDistribution: Array<{\n      level: 'low' | 'moderate' | 'high' | 'very_high';\n      count: number;\n      percentage: number;\n    }>;\n    weeklyIntensity: Array<{\n      week: string;\n      averageIntensity: number;\n      workoutCount: number;\n    }>;\n  }> {\n    return apiClient.get(`/api/progress/workout-intensity?period=${period}`);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,mBAAmB,SAQ5B,CAAC,CAAC,EAA8C;QAClD,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI;QACxD,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QACjE,IAAI,OAAO,UAAU,EAAE,aAAa,MAAM,CAAC,cAAc,OAAO,UAAU;QAC1E,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,GACtD,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI;QAEtC,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAoC;IAC1D;IAEA;;GAEC,GACD,aAAa,kBAAkB,EAAU,EAA2B;QAClE,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAiB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC7E;IAEA;;GAEC,GACD,aAAa,qBAAqB,IAA8B,EAA2B;QACzF,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAiB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;IAC9E;IAEA;;GAEC,GACD,aAAa,qBACX,EAAU,EACV,IAA8B,EACL;QACzB,OAAO,2HAAA,CAAA,YAAS,CAAC,KAAK,CAAiB,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK;IACnF;IAEA;;GAEC,GACD,aAAa,qBAAqB,EAAU,EAAiB;QAC3D,OAAO,2HAAA,CAAA,YAAS,CAAC,MAAM,CAAO,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,aAAa,iBAAiB,SAA4C,OAAO,EAA0B;QACzG,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAgB,GAAG,2HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ;IAC/F;IAEA;;GAEC,GACD,aAAa,gBAAgB,SAAoC,OAAO,EAUrE;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ;IACrE;IAEA;;GAEC,GACD,aAAa,oBAAoB,UAAkB,EAAE,SAAoC,OAAO,EA+B7F;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,QAAQ,EAAE,QAAQ;IAC9E;IAEA;;GAEC,GACD,aAAa,oBAAoB,SAAoC,OAAO,EAiBzE;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,QAAQ;IACzE;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAW/B,EAAgC;QAC/B,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,mCAAmC;IAC3D;IAEA;;GAEC,GACD,aAAa,kBAaV;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;IACvB;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAO9B,EAAgD;QAC/C,OAAO,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;IAC/C;IAEA;;GAEC,GACD,aAAa,mBAAmB,MAAc,EAAE,YAAoB,EAAgC;QAClG,OAAO,2HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAAE;QAAa;IACzE;IAEA;;GAEC,GACD,aAAa,kBAkBV;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;IACvB;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAAE,KAAa,EAcxD;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC3E;IAEA;;GAEC,GACD,aAAa,qBAmBV;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;IACvB;IAEA;;GAEC,GACD,aAAa,uBAAuB,WAAsB,EAgBvD;QACD,MAAM,SAAS,cAAc,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,MAAM,GAAG;QACrE,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,kCAAkC,EAAE,QAAQ;IACpE;IAEA;;GAEC,GACD,aAAa,mBAAmB,SAAyB,KAAK,EAAE,SAAmC,KAAK,EAAiB;QACvH,MAAM,WAAW,MAAM,MAAM,GAAG,2HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,QAAQ,EAAE,QAAQ,EAAE;YAC3G,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;YACjE;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,oBAAoB,SAAoC,OAAO,EAYzE;QACD,OAAO,2HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,QAAQ;IACzE;AACF", "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/query/config.ts"], "sourcesContent": ["/**\n * React Query Configuration and Cache Management\n * Centralized configuration for data fetching, caching, and synchronization\n */\n\nimport { QueryClient, DefaultOptions } from '@tanstack/react-query';\nimport { persistQueryClient } from '@tanstack/react-query-persist-client';\nimport { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';\nimport { appActions } from '../store/app-store';\n\n// ============================================================================\n// QUERY CLIENT CONFIGURATION\n// ============================================================================\n\nconst queryConfig: DefaultOptions = {\n  queries: {\n    // Global defaults for all queries\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n    retry: (failureCount, error: any) => {\n      // Don't retry on 4xx errors (client errors)\n      if (error?.status >= 400 && error?.status < 500) {\n        return false;\n      }\n      // Retry up to 3 times for other errors\n      return failureCount < 3;\n    },\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n    refetchOnWindowFocus: false,\n    refetchOnReconnect: true,\n    refetchOnMount: true,\n  },\n  mutations: {\n    // Global defaults for all mutations\n    retry: (failureCount, error: any) => {\n      // Don't retry mutations on client errors\n      if (error?.status >= 400 && error?.status < 500) {\n        return false;\n      }\n      // Retry once for server errors\n      return failureCount < 1;\n    },\n    onError: (error: any) => {\n      // Global error handling for mutations\n      console.error('Mutation error:', error);\n      \n      // Add error notification\n      appActions.addNotification({\n        type: 'error',\n        title: 'Operation Failed',\n        message: error?.message || 'An unexpected error occurred',\n      });\n    },\n  },\n};\n\n// Create the query client\nexport const queryClient = new QueryClient({\n  defaultOptions: queryConfig,\n});\n\n// ============================================================================\n// PERSISTENCE CONFIGURATION\n// ============================================================================\n\nconst persister = createSyncStoragePersister({\n  storage: typeof window !== 'undefined' ? window.localStorage : undefined,\n  key: 'ai-fitness-query-cache',\n  serialize: JSON.stringify,\n  deserialize: JSON.parse,\n});\n\n// Persist query client (only in browser)\nif (typeof window !== 'undefined') {\n  persistQueryClient({\n    queryClient,\n    persister,\n    maxAge: 24 * 60 * 60 * 1000, // 24 hours\n    buster: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n  });\n}\n\n// ============================================================================\n// CACHE MANAGEMENT UTILITIES\n// ============================================================================\n\nexport const cacheUtils = {\n  /**\n   * Invalidate all queries for a specific entity type\n   */\n  invalidateEntity: (entityType: string) => {\n    queryClient.invalidateQueries({ queryKey: [entityType] });\n  },\n\n  /**\n   * Remove all cached data for a specific entity\n   */\n  removeEntity: (entityType: string, id?: string) => {\n    if (id) {\n      queryClient.removeQueries({ queryKey: [entityType, id] });\n    } else {\n      queryClient.removeQueries({ queryKey: [entityType] });\n    }\n  },\n\n  /**\n   * Prefetch data for better UX\n   */\n  prefetch: async (queryKey: any[], queryFn: () => Promise<any>) => {\n    await queryClient.prefetchQuery({\n      queryKey,\n      queryFn,\n      staleTime: 10 * 60 * 1000, // 10 minutes\n    });\n  },\n\n  /**\n   * Set query data manually (for optimistic updates)\n   */\n  setQueryData: <T>(queryKey: any[], data: T) => {\n    queryClient.setQueryData(queryKey, data);\n  },\n\n  /**\n   * Get cached query data\n   */\n  getQueryData: <T>(queryKey: any[]): T | undefined => {\n    return queryClient.getQueryData(queryKey);\n  },\n\n  /**\n   * Clear all cached data\n   */\n  clearAll: () => {\n    queryClient.clear();\n  },\n\n  /**\n   * Reset queries to refetch fresh data\n   */\n  resetQueries: (queryKey?: any[]) => {\n    if (queryKey) {\n      queryClient.resetQueries({ queryKey });\n    } else {\n      queryClient.resetQueries();\n    }\n  },\n\n  /**\n   * Cancel ongoing queries\n   */\n  cancelQueries: (queryKey?: any[]) => {\n    if (queryKey) {\n      queryClient.cancelQueries({ queryKey });\n    } else {\n      queryClient.cancelQueries();\n    }\n  },\n};\n\n// ============================================================================\n// QUERY KEY FACTORIES\n// ============================================================================\n\n/**\n * Centralized query key management for consistency\n */\nexport const queryKeys = {\n  // Auth\n  auth: {\n    all: ['auth'] as const,\n    session: () => [...queryKeys.auth.all, 'session'] as const,\n    user: () => [...queryKeys.auth.all, 'user'] as const,\n  },\n\n  // Exercises\n  exercises: {\n    all: ['exercises'] as const,\n    lists: () => [...queryKeys.exercises.all, 'list'] as const,\n    list: (filters: any) => [...queryKeys.exercises.lists(), filters] as const,\n    details: () => [...queryKeys.exercises.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.exercises.details(), id] as const,\n    search: (query: string) => [...queryKeys.exercises.all, 'search', query] as const,\n    attributes: () => [...queryKeys.exercises.all, 'attributes'] as const,\n  },\n\n  // Workouts\n  workouts: {\n    all: ['workouts'] as const,\n    sessions: () => [...queryKeys.workouts.all, 'sessions'] as const,\n    session: (id: string) => [...queryKeys.workouts.sessions(), id] as const,\n    programs: () => [...queryKeys.workouts.all, 'programs'] as const,\n    program: (id: string) => [...queryKeys.workouts.programs(), id] as const,\n    history: (filters: any) => [...queryKeys.workouts.all, 'history', filters] as const,\n    stats: (period: string) => [...queryKeys.workouts.all, 'stats', period] as const,\n  },\n\n  // Progress\n  progress: {\n    all: ['progress'] as const,\n    records: () => [...queryKeys.progress.all, 'records'] as const,\n    record: (id: string) => [...queryKeys.progress.records(), id] as const,\n    stats: (period: string) => [...queryKeys.progress.all, 'stats', period] as const,\n    goals: () => [...queryKeys.progress.all, 'goals'] as const,\n    achievements: () => [...queryKeys.progress.all, 'achievements'] as const,\n    calendar: (year: number, month: number) => [...queryKeys.progress.all, 'calendar', year, month] as const,\n  },\n\n  // User\n  user: {\n    all: ['user'] as const,\n    profile: () => [...queryKeys.user.all, 'profile'] as const,\n    preferences: () => [...queryKeys.user.all, 'preferences'] as const,\n    subscription: () => [...queryKeys.user.all, 'subscription'] as const,\n  },\n};\n\n// ============================================================================\n// OFFLINE SUPPORT\n// ============================================================================\n\nexport const offlineUtils = {\n  /**\n   * Check if we're online\n   */\n  isOnline: () => {\n    return typeof navigator !== 'undefined' ? navigator.onLine : true;\n  },\n\n  /**\n   * Setup online/offline event listeners\n   */\n  setupNetworkListeners: () => {\n    if (typeof window === 'undefined') return;\n\n    const handleOnline = () => {\n      appActions.setOnlineStatus(true);\n      // Refetch all queries when coming back online\n      queryClient.refetchQueries();\n    };\n\n    const handleOffline = () => {\n      appActions.setOnlineStatus(false);\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    // Set initial status\n    appActions.setOnlineStatus(navigator.onLine);\n\n    // Return cleanup function\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  },\n\n  /**\n   * Queue mutation for offline sync\n   */\n  queueOfflineMutation: (type: string, action: string, data: any) => {\n    appActions.addPendingSync({\n      type: type as any,\n      action: action as any,\n      data,\n    });\n  },\n};\n\n// ============================================================================\n// QUERY CLIENT EVENTS\n// ============================================================================\n\n// Setup global query client event listeners\nqueryClient.getQueryCache().subscribe((event) => {\n  // Log query events in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('Query event:', event);\n  }\n\n  // Handle specific events\n  switch (event.type) {\n    case 'added':\n      // Query was added to cache\n      break;\n    case 'removed':\n      // Query was removed from cache\n      break;\n    case 'updated':\n      // Query data was updated\n      break;\n  }\n});\n\nqueryClient.getMutationCache().subscribe((event) => {\n  // Log mutation events in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('Mutation event:', event);\n  }\n\n  // Handle mutation success/error globally\n  if (event.type === 'updated') {\n    const mutation = event.mutation;\n    \n    if (mutation.state.status === 'success') {\n      // Global success handling\n      appActions.addNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Operation completed successfully',\n      });\n    }\n  }\n});\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport { queryClient as default };\nexport type { QueryClient };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;AAEA;AACA;;;;;AAEA,+EAA+E;AAC/E,6BAA6B;AAC7B,+EAA+E;AAE/E,MAAM,cAA8B;IAClC,SAAS;QACP,kCAAkC;QAClC,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO,CAAC,cAAc;YACpB,4CAA4C;YAC5C,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;gBAC/C,OAAO;YACT;YACA,uCAAuC;YACvC,OAAO,eAAe;QACxB;QACA,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QACjE,sBAAsB;QACtB,oBAAoB;QACpB,gBAAgB;IAClB;IACA,WAAW;QACT,oCAAoC;QACpC,OAAO,CAAC,cAAc;YACpB,yCAAyC;YACzC,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;gBAC/C,OAAO;YACT;YACA,+BAA+B;YAC/B,OAAO,eAAe;QACxB;QACA,SAAS,CAAC;YACR,sCAAsC;YACtC,QAAQ,KAAK,CAAC,mBAAmB;YAEjC,yBAAyB;YACzB,mIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS,OAAO,WAAW;YAC7B;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW,CAAC;IACzC,gBAAgB;AAClB;AAEA,+EAA+E;AAC/E,4BAA4B;AAC5B,+EAA+E;AAE/E,MAAM,YAAY,CAAA,GAAA,+LAAA,CAAA,6BAA0B,AAAD,EAAE;IAC3C,SAAS,6EAAsD;IAC/D,KAAK;IACL,WAAW,KAAK,SAAS;IACzB,aAAa,KAAK,KAAK;AACzB;AAEA,yCAAyC;AACzC,uCAAmC;;AAOnC;AAMO,MAAM,aAAa;IACxB;;GAEC,GACD,kBAAkB,CAAC;QACjB,YAAY,iBAAiB,CAAC;YAAE,UAAU;gBAAC;aAAW;QAAC;IACzD;IAEA;;GAEC,GACD,cAAc,CAAC,YAAoB;QACjC,IAAI,IAAI;YACN,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;QACzD,OAAO;YACL,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACrD;IACF;IAEA;;GAEC,GACD,UAAU,OAAO,UAAiB;QAChC,MAAM,YAAY,aAAa,CAAC;YAC9B;YACA;YACA,WAAW,KAAK,KAAK;QACvB;IACF;IAEA;;GAEC,GACD,cAAc,CAAI,UAAiB;QACjC,YAAY,YAAY,CAAC,UAAU;IACrC;IAEA;;GAEC,GACD,cAAc,CAAI;QAChB,OAAO,YAAY,YAAY,CAAC;IAClC;IAEA;;GAEC,GACD,UAAU;QACR,YAAY,KAAK;IACnB;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,IAAI,UAAU;YACZ,YAAY,YAAY,CAAC;gBAAE;YAAS;QACtC,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,IAAI,UAAU;YACZ,YAAY,aAAa,CAAC;gBAAE;YAAS;QACvC,OAAO;YACL,YAAY,aAAa;QAC3B;IACF;AACF;AASO,MAAM,YAAY;IACvB,OAAO;IACP,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,SAAS,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAU;QACjD,MAAM,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAO;IAC7C;IAEA,YAAY;IACZ,WAAW;QACT,KAAK;YAAC;SAAY;QAClB,OAAO,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAO;QACjD,MAAM,CAAC,UAAiB;mBAAI,UAAU,SAAS,CAAC,KAAK;gBAAI;aAAQ;QACjE,SAAS,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAS;QACrD,QAAQ,CAAC,KAAe;mBAAI,UAAU,SAAS,CAAC,OAAO;gBAAI;aAAG;QAC9D,QAAQ,CAAC,QAAkB;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;gBAAU;aAAM;QACxE,YAAY,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAa;IAC9D;IAEA,WAAW;IACX,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,SAAS,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,QAAQ;gBAAI;aAAG;QAC/D,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,SAAS,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,QAAQ;gBAAI;aAAG;QAC/D,SAAS,CAAC,UAAiB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAW;aAAQ;QAC1E,OAAO,CAAC,SAAmB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAS;aAAO;IACzE;IAEA,WAAW;IACX,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAU;QACrD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,OAAO,CAAC,SAAmB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAS;aAAO;QACvE,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAQ;QACjD,cAAc,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAe;QAC/D,UAAU,CAAC,MAAc,QAAkB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAY;gBAAM;aAAM;IACjG;IAEA,OAAO;IACP,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,SAAS,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAU;QACjD,aAAa,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAc;QACzD,cAAc,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAe;IAC7D;AACF;AAMO,MAAM,eAAe;IAC1B;;GAEC,GACD,UAAU;QACR,OAAO,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;IAC/D;IAEA;;GAEC,GACD,uBAAuB;QACrB,wCAAmC;;QAEnC,MAAM;QAMN,MAAM;IAeR;IAEA;;GAEC,GACD,sBAAsB,CAAC,MAAc,QAAgB;QACnD,mIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACxB,MAAM;YACN,QAAQ;YACR;QACF;IACF;AACF;AAEA,+EAA+E;AAC/E,sBAAsB;AACtB,+EAA+E;AAE/E,4CAA4C;AAC5C,YAAY,aAAa,GAAG,SAAS,CAAC,CAAC;IACrC,kCAAkC;IAClC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,yBAAyB;IACzB,OAAQ,MAAM,IAAI;QAChB,KAAK;YAEH;QACF,KAAK;YAEH;QACF,KAAK;YAEH;IACJ;AACF;AAEA,YAAY,gBAAgB,GAAG,SAAS,CAAC,CAAC;IACxC,qCAAqC;IACrC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,yCAAyC;IACzC,IAAI,MAAM,IAAI,KAAK,WAAW;QAC5B,MAAM,WAAW,MAAM,QAAQ;QAE/B,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,WAAW;YACvC,0BAA0B;YAC1B,mIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/sync/sync-manager.ts"], "sourcesContent": ["/**\n * Sync Manager for Offline Data Synchronization\n * Handles syncing offline data when connection is restored\n */\n\nimport { useAppStore, appActions } from '../store/app-store';\nimport { WorkoutService } from '../api/services/workouts';\nimport { ProgressService } from '../api/services/progress';\nimport { queryClient, cacheUtils } from '../query/config';\n\n// ============================================================================\n// SYNC MANAGER CLASS\n// ============================================================================\n\nexport class SyncManager {\n  private static instance: SyncManager;\n  private syncInProgress = false;\n  private syncQueue: Array<any> = [];\n\n  private constructor() {\n    this.setupNetworkListeners();\n  }\n\n  static getInstance(): SyncManager {\n    if (!SyncManager.instance) {\n      SyncManager.instance = new SyncManager();\n    }\n    return SyncManager.instance;\n  }\n\n  /**\n   * Setup network event listeners\n   */\n  private setupNetworkListeners() {\n    if (typeof window === 'undefined') return;\n\n    const handleOnline = () => {\n      appActions.setOnlineStatus(true);\n      this.syncPendingData();\n    };\n\n    const handleOffline = () => {\n      appActions.setOnlineStatus(false);\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    // Set initial status\n    appActions.setOnlineStatus(navigator.onLine);\n  }\n\n  /**\n   * Add data to sync queue for offline processing\n   */\n  addToSyncQueue(item: {\n    type: 'workout' | 'progress' | 'goal';\n    action: 'create' | 'update' | 'delete';\n    data: any;\n    localId?: string;\n  }) {\n    appActions.addPendingSync(item);\n    \n    // Try to sync immediately if online\n    if (navigator.onLine) {\n      this.syncPendingData();\n    }\n  }\n\n  /**\n   * Sync all pending data\n   */\n  async syncPendingData() {\n    if (this.syncInProgress || !navigator.onLine) {\n      return;\n    }\n\n    const state = useAppStore.getState();\n    const pendingItems = state.offline.pendingSync;\n\n    if (pendingItems.length === 0) {\n      return;\n    }\n\n    this.syncInProgress = true;\n\n    try {\n      appActions.addNotification({\n        type: 'info',\n        title: 'Syncing Data',\n        message: `Syncing ${pendingItems.length} pending items...`,\n      });\n\n      const syncResults = await Promise.allSettled(\n        pendingItems.map(item => this.syncSingleItem(item))\n      );\n\n      // Process results\n      let successCount = 0;\n      let failureCount = 0;\n\n      syncResults.forEach((result, index) => {\n        const item = pendingItems[index];\n        \n        if (result.status === 'fulfilled') {\n          successCount++;\n          appActions.removePendingSync(item.id);\n        } else {\n          failureCount++;\n          console.error(`Failed to sync item ${item.id}:`, result.reason);\n        }\n      });\n\n      // Update last sync time\n      appActions.updateLastSyncTime();\n\n      // Show result notification\n      if (failureCount === 0) {\n        appActions.addNotification({\n          type: 'success',\n          title: 'Sync Complete',\n          message: `Successfully synced ${successCount} items`,\n        });\n      } else {\n        appActions.addNotification({\n          type: 'warning',\n          title: 'Sync Partially Complete',\n          message: `Synced ${successCount} items, ${failureCount} failed`,\n        });\n      }\n\n      // Invalidate relevant queries to refetch fresh data\n      cacheUtils.invalidateEntity('workouts');\n      cacheUtils.invalidateEntity('progress');\n\n    } catch (error) {\n      console.error('Sync failed:', error);\n      appActions.addNotification({\n        type: 'error',\n        title: 'Sync Failed',\n        message: 'Failed to sync offline data. Will retry later.',\n      });\n    } finally {\n      this.syncInProgress = false;\n    }\n  }\n\n  /**\n   * Sync a single item\n   */\n  private async syncSingleItem(item: any): Promise<any> {\n    switch (item.type) {\n      case 'workout':\n        return this.syncWorkoutItem(item);\n      case 'progress':\n        return this.syncProgressItem(item);\n      case 'goal':\n        return this.syncGoalItem(item);\n      default:\n        throw new Error(`Unknown sync item type: ${item.type}`);\n    }\n  }\n\n  /**\n   * Sync workout-related items\n   */\n  private async syncWorkoutItem(item: any): Promise<any> {\n    switch (item.action) {\n      case 'create':\n        if (item.data.type === 'session') {\n          return WorkoutService.createWorkoutSession(item.data);\n        } else if (item.data.type === 'program') {\n          return WorkoutService.createWorkoutProgram(item.data);\n        }\n        break;\n      \n      case 'update':\n        if (item.data.type === 'session') {\n          return WorkoutService.updateWorkoutSession(item.data.id, item.data);\n        } else if (item.data.type === 'program') {\n          return WorkoutService.updateWorkoutProgram(item.data.id, item.data);\n        }\n        break;\n      \n      case 'delete':\n        if (item.data.type === 'session') {\n          return WorkoutService.deleteWorkoutSession(item.data.id);\n        } else if (item.data.type === 'program') {\n          return WorkoutService.deleteWorkoutProgram(item.data.id);\n        }\n        break;\n    }\n    \n    throw new Error(`Unknown workout sync action: ${item.action}`);\n  }\n\n  /**\n   * Sync progress-related items\n   */\n  private async syncProgressItem(item: any): Promise<any> {\n    switch (item.action) {\n      case 'create':\n        return ProgressService.createProgressRecord(item.data);\n      \n      case 'update':\n        return ProgressService.updateProgressRecord(item.data.id, item.data);\n      \n      case 'delete':\n        return ProgressService.deleteProgressRecord(item.data.id);\n    }\n    \n    throw new Error(`Unknown progress sync action: ${item.action}`);\n  }\n\n  /**\n   * Sync goal-related items\n   */\n  private async syncGoalItem(item: any): Promise<any> {\n    switch (item.action) {\n      case 'create':\n        return ProgressService.createFitnessGoal(item.data);\n      \n      case 'update':\n        return ProgressService.updateGoalProgress(item.data.id, item.data.currentValue);\n      \n      default:\n        throw new Error(`Unknown goal sync action: ${item.action}`);\n    }\n  }\n\n  /**\n   * Force sync all data\n   */\n  async forceSyncAll() {\n    await this.syncPendingData();\n  }\n\n  /**\n   * Clear all pending sync data\n   */\n  clearPendingSync() {\n    appActions.clearPendingSync();\n  }\n\n  /**\n   * Get sync status\n   */\n  getSyncStatus() {\n    const state = useAppStore.getState();\n    return {\n      isOnline: state.offline.isOnline,\n      pendingCount: state.offline.pendingSync.length,\n      lastSyncTime: state.offline.lastSyncTime,\n      syncInProgress: this.syncInProgress,\n    };\n  }\n}\n\n// ============================================================================\n// REACT HOOKS FOR SYNC MANAGEMENT\n// ============================================================================\n\n/**\n * Hook to use sync manager\n */\nexport function useSyncManager() {\n  const syncManager = SyncManager.getInstance();\n  \n  return {\n    addToSyncQueue: (item: Parameters<typeof syncManager.addToSyncQueue>[0]) => \n      syncManager.addToSyncQueue(item),\n    syncPendingData: () => syncManager.syncPendingData(),\n    forceSyncAll: () => syncManager.forceSyncAll(),\n    clearPendingSync: () => syncManager.clearPendingSync(),\n    getSyncStatus: () => syncManager.getSyncStatus(),\n  };\n}\n\n/**\n * Hook to get sync status\n */\nexport function useSyncStatus() {\n  const syncManager = SyncManager.getInstance();\n  const status = syncManager.getSyncStatus();\n  \n  return status;\n}\n\n// ============================================================================\n// OPTIMISTIC UPDATE UTILITIES\n// ============================================================================\n\nexport const optimisticUpdates = {\n  /**\n   * Optimistically update workout session\n   */\n  updateWorkoutSession: (sessionId: string, updates: any) => {\n    const queryKey = ['workouts', 'sessions', sessionId];\n    const previousData = cacheUtils.getQueryData(queryKey);\n\n    // Apply optimistic update\n    cacheUtils.setQueryData(queryKey, {\n      ...(previousData || {}),\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    });\n\n    return previousData;\n  },\n\n  /**\n   * Optimistically add progress record\n   */\n  addProgressRecord: (record: any) => {\n    const queryKey = ['progress', 'records'];\n    const previousData = cacheUtils.getQueryData<any>(queryKey);\n    \n    if (previousData?.data) {\n      const newRecord = {\n        ...record,\n        id: `temp-${Date.now()}`,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      cacheUtils.setQueryData(queryKey, {\n        ...previousData,\n        data: [newRecord, ...previousData.data],\n      });\n    }\n    \n    return previousData;\n  },\n\n  /**\n   * Revert optimistic update\n   */\n  revertUpdate: (queryKey: any[], previousData: any) => {\n    if (previousData !== undefined) {\n      cacheUtils.setQueryData(queryKey, previousData);\n    }\n  },\n};\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport default SyncManager;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;AACA;AACA;AACA;;;;;AAMO,MAAM;IACX,OAAe,SAAsB;IAC7B,iBAAiB,MAAM;IACvB,YAAwB,EAAE,CAAC;IAEnC,aAAsB;QACpB,IAAI,CAAC,qBAAqB;IAC5B;IAEA,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;GAEC,GACD,AAAQ,wBAAwB;QAC9B,wCAAmC;;QAEnC,MAAM;QAKN,MAAM;IASR;IAEA;;GAEC,GACD,eAAe,IAKd,EAAE;QACD,mIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAE1B,oCAAoC;QACpC,IAAI,UAAU,MAAM,EAAE;YACpB,IAAI,CAAC,eAAe;QACtB;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB;QACtB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,UAAU,MAAM,EAAE;YAC5C;QACF;QAEA,MAAM,QAAQ,mIAAA,CAAA,cAAW,CAAC,QAAQ;QAClC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW;QAE9C,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B;QACF;QAEA,IAAI,CAAC,cAAc,GAAG;QAEtB,IAAI;YACF,mIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,QAAQ,EAAE,aAAa,MAAM,CAAC,iBAAiB,CAAC;YAC5D;YAEA,MAAM,cAAc,MAAM,QAAQ,UAAU,CAC1C,aAAa,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,cAAc,CAAC;YAG/C,kBAAkB;YAClB,IAAI,eAAe;YACnB,IAAI,eAAe;YAEnB,YAAY,OAAO,CAAC,CAAC,QAAQ;gBAC3B,MAAM,OAAO,YAAY,CAAC,MAAM;gBAEhC,IAAI,OAAO,MAAM,KAAK,aAAa;oBACjC;oBACA,mIAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE;gBACtC,OAAO;oBACL;oBACA,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM;gBAChE;YACF;YAEA,wBAAwB;YACxB,mIAAA,CAAA,aAAU,CAAC,kBAAkB;YAE7B,2BAA2B;YAC3B,IAAI,iBAAiB,GAAG;gBACtB,mIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;oBACzB,MAAM;oBACN,OAAO;oBACP,SAAS,CAAC,oBAAoB,EAAE,aAAa,MAAM,CAAC;gBACtD;YACF,OAAO;gBACL,mIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;oBACzB,MAAM;oBACN,OAAO;oBACP,SAAS,CAAC,OAAO,EAAE,aAAa,QAAQ,EAAE,aAAa,OAAO,CAAC;gBACjE;YACF;YAEA,oDAAoD;YACpD,6HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;YAC5B,6HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;QAE9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,mIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,IAAI,CAAC,cAAc,GAAG;QACxB;IACF;IAEA;;GAEC,GACD,MAAc,eAAe,IAAS,EAAgB;QACpD,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,KAAK;gBACH,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC/B,KAAK;gBACH,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B;gBACE,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QAC1D;IACF;IAEA;;GAEC,GACD,MAAc,gBAAgB,IAAS,EAAgB;QACrD,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBAChC,OAAO,yIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI;gBACtD,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBACvC,OAAO,yIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI;gBACtD;gBACA;YAEF,KAAK;gBACH,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBAChC,OAAO,yIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI;gBACpE,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBACvC,OAAO,yIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI;gBACpE;gBACA;YAEF,KAAK;gBACH,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBAChC,OAAO,yIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE;gBACzD,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBACvC,OAAO,yIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE;gBACzD;gBACA;QACJ;QAEA,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK,MAAM,EAAE;IAC/D;IAEA;;GAEC,GACD,MAAc,iBAAiB,IAAS,EAAgB;QACtD,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO,yIAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,KAAK,IAAI;YAEvD,KAAK;gBACH,OAAO,yIAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI;YAErE,KAAK;gBACH,OAAO,yIAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE;QAC5D;QAEA,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,MAAM,EAAE;IAChE;IAEA;;GAEC,GACD,MAAc,aAAa,IAAS,EAAgB;QAClD,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO,yIAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,KAAK,IAAI;YAEpD,KAAK;gBACH,OAAO,yIAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,YAAY;YAEhF;gBACE,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,KAAK,MAAM,EAAE;QAC9D;IACF;IAEA;;GAEC,GACD,MAAM,eAAe;QACnB,MAAM,IAAI,CAAC,eAAe;IAC5B;IAEA;;GAEC,GACD,mBAAmB;QACjB,mIAAA,CAAA,aAAU,CAAC,gBAAgB;IAC7B;IAEA;;GAEC,GACD,gBAAgB;QACd,MAAM,QAAQ,mIAAA,CAAA,cAAW,CAAC,QAAQ;QAClC,OAAO;YACL,UAAU,MAAM,OAAO,CAAC,QAAQ;YAChC,cAAc,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM;YAC9C,cAAc,MAAM,OAAO,CAAC,YAAY;YACxC,gBAAgB,IAAI,CAAC,cAAc;QACrC;IACF;AACF;AASO,SAAS;IACd,MAAM,cAAc,YAAY,WAAW;IAE3C,OAAO;QACL,gBAAgB,CAAC,OACf,YAAY,cAAc,CAAC;QAC7B,iBAAiB,IAAM,YAAY,eAAe;QAClD,cAAc,IAAM,YAAY,YAAY;QAC5C,kBAAkB,IAAM,YAAY,gBAAgB;QACpD,eAAe,IAAM,YAAY,aAAa;IAChD;AACF;AAKO,SAAS;IACd,MAAM,cAAc,YAAY,WAAW;IAC3C,MAAM,SAAS,YAAY,aAAa;IAExC,OAAO;AACT;AAMO,MAAM,oBAAoB;IAC/B;;GAEC,GACD,sBAAsB,CAAC,WAAmB;QACxC,MAAM,WAAW;YAAC;YAAY;YAAY;SAAU;QACpD,MAAM,eAAe,6HAAA,CAAA,aAAU,CAAC,YAAY,CAAC;QAE7C,0BAA0B;QAC1B,6HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,UAAU;YAChC,GAAI,gBAAgB,CAAC,CAAC;YACtB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,MAAM,WAAW;YAAC;YAAY;SAAU;QACxC,MAAM,eAAe,6HAAA,CAAA,aAAU,CAAC,YAAY,CAAM;QAElD,IAAI,cAAc,MAAM;YACtB,MAAM,YAAY;gBAChB,GAAG,MAAM;gBACT,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,6HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,UAAU;gBAChC,GAAG,YAAY;gBACf,MAAM;oBAAC;uBAAc,aAAa,IAAI;iBAAC;YACzC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,cAAc,CAAC,UAAiB;QAC9B,IAAI,iBAAiB,WAAW;YAC9B,6HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,UAAU;QACpC;IACF;AACF;uCAMe", "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/theme/mui-theme.ts"], "sourcesContent": ["import { createTheme } from '@mui/material/styles';\n\n// 运动风格的 Material UI 主题\nexport const fitnessTheme = createTheme({\n  palette: {\n    primary: {\n      main: '#FF6B35', // 活力橙色\n      light: '#FF8A65',\n      dark: '#E64A19',\n      contrastText: '#FFFFFF',\n    },\n    secondary: {\n      main: '#4CAF50', // 健康绿色\n      light: '#81C784',\n      dark: '#388E3C',\n      contrastText: '#FFFFFF',\n    },\n    background: {\n      default: '#FAFAFA',\n      paper: '#FFFFFF',\n    },\n    text: {\n      primary: '#212121',\n      secondary: '#757575',\n    },\n    error: {\n      main: '#F44336',\n    },\n    warning: {\n      main: '#FF9800',\n    },\n    info: {\n      main: '#2196F3',\n    },\n    success: {\n      main: '#4CAF50',\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '3rem',\n      fontWeight: 700,\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: '2.5rem',\n      fontWeight: 600,\n      lineHeight: 1.3,\n    },\n    h3: {\n      fontSize: '2rem',\n      fontWeight: 600,\n      lineHeight: 1.4,\n    },\n    h4: {\n      fontSize: '1.5rem',\n      fontWeight: 500,\n      lineHeight: 1.4,\n    },\n    h5: {\n      fontSize: '1.25rem',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n    h6: {\n      fontSize: '1rem',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.6,\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.5,\n    },\n    button: {\n      textTransform: 'none',\n      fontWeight: 600,\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 25,\n          padding: '12px 24px',\n          fontSize: '1rem',\n          fontWeight: 600,\n          textTransform: 'none',\n          boxShadow: 'none',\n          '&:hover': {\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n            transform: 'translateY(-2px)',\n            transition: 'all 0.3s ease',\n          },\n        },\n        contained: {\n          background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',\n          '&:hover': {\n            background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)',\n          },\n        },\n        outlined: {\n          borderWidth: 2,\n          '&:hover': {\n            borderWidth: 2,\n          },\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n          '&:hover': {\n            boxShadow: '0 8px 30px rgba(0,0,0,0.12)',\n            transform: 'translateY(-4px)',\n            transition: 'all 0.3s ease',\n          },\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#FFFFFF',\n          color: '#212121',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          borderRadius: 20,\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiFab: {\n      styleOverrides: {\n        root: {\n          background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',\n          '&:hover': {\n            background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)',\n            transform: 'scale(1.1)',\n          },\n        },\n      },\n    },\n  },\n});\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,cAAW,AAAD,EAAE;IACtC,SAAS;QACP,SAAS;YACP,MAAM;YACN,OAAO;YACP,MAAM;YACN,cAAc;QAChB;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,MAAM;YACN,cAAc;QAChB;QACA,YAAY;YACV,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,WAAW;QACb;QACA,OAAO;YACL,MAAM;QACR;QACA,SAAS;YACP,MAAM;QACR;QACA,MAAM;YACJ,MAAM;QACR;QACA,SAAS;YACP,MAAM;QACR;IACF;IACA,YAAY;QACV,YAAY;QACZ,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,OAAO;YACL,UAAU;YACV,YAAY;QACd;QACA,OAAO;YACL,UAAU;YACV,YAAY;QACd;QACA,QAAQ;YACN,eAAe;YACf,YAAY;QACd;IACF;IACA,OAAO;QACL,cAAc;IAChB;IACA,YAAY;QACV,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,eAAe;oBACf,WAAW;oBACX,WAAW;wBACT,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;gBACF;gBACA,WAAW;oBACT,YAAY;oBACZ,WAAW;wBACT,YAAY;oBACd;gBACF;gBACA,UAAU;oBACR,aAAa;oBACb,WAAW;wBACT,aAAa;oBACf;gBACF;YACF;QACF;QACA,SAAS;YACP,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,WAAW;oBACX,WAAW;wBACT,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;gBACF;YACF;QACF;QACA,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,iBAAiB;oBACjB,OAAO;oBACP,WAAW;gBACb;YACF;QACF;QACA,SAAS;YACP,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,YAAY;gBACd;YACF;QACF;QACA,QAAQ;YACN,gBAAgB;gBACd,MAAM;oBACJ,YAAY;oBACZ,WAAW;wBACT,YAAY;wBACZ,WAAW;oBACb;gBACF;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/providers/app-providers.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { QueryProvider } from '@/lib/providers/query-provider';\nimport { AuthProvider } from '@/lib/providers/auth-provider';\nimport { SyncManager } from '@/lib/sync/sync-manager';\nimport { offlineUtils } from '@/lib/query/config';\nimport { fitnessTheme } from '@/lib/theme/mui-theme';\n\n/**\n * Main App Providers Component\n * Combines all providers and initializes app-wide functionality\n */\nexport function AppProviders({ children }: { children: React.ReactNode }) {\n  useEffect(() => {\n    // Initialize sync manager\n    const syncManager = SyncManager.getInstance();\n    \n    // Setup network listeners\n    const cleanupNetworkListeners = offlineUtils.setupNetworkListeners();\n    \n    // Cleanup on unmount\n    return () => {\n      if (cleanupNetworkListeners) {\n        cleanupNetworkListeners();\n      }\n    };\n  }, []);\n\n  return (\n    <ThemeProvider theme={fitnessTheme}>\n      <CssBaseline />\n      <QueryProvider>\n        <AuthProvider>\n          {children}\n        </AuthProvider>\n      </QueryProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAeO,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,MAAM,cAAc,qIAAA,CAAA,cAAW,CAAC,WAAW;QAE3C,0BAA0B;QAC1B,MAAM,0BAA0B,6HAAA,CAAA,eAAY,CAAC,qBAAqB;QAElE,qBAAqB;QACrB,OAAO;YACL,IAAI,yBAAyB;gBAC3B;YACF;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,+MAAA,CAAA,gBAAa;QAAC,OAAO,mIAAA,CAAA,eAAY;;0BAChC,8OAAC,gNAAA,CAAA,cAAW;;;;;0BACZ,8OAAC,6IAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,4IAAA,CAAA,eAAY;8BACV;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}