{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from \"lucide-react\"\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const toggleMenu = () => setIsOpen(!isOpen)\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">AI-fitness-singles</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/workouts\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Workouts\n            </Link>\n            <Link\n              href=\"/exercises\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Exercises\n            </Link>\n            <Link\n              href=\"/progress\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Progress\n            </Link>\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button variant=\"ghost\" size=\"icon\" onClick={toggleMenu}>\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/workouts\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Dumbbell className=\"h-4 w-4\" />\n                <span>Workouts</span>\n              </Link>\n              <Link\n                href=\"/exercises\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BookOpen className=\"h-4 w-4\" />\n                <span>Exercises</span>\n              </Link>\n              <Link\n                href=\"/progress\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BarChart3 className=\"h-4 w-4\" />\n                <span>Progress</span>\n              </Link>\n              <div className=\"pt-4 border-t\">\n                <div className=\"flex flex-col space-y-2\">\n                  <Button variant=\"outline\" className=\"justify-start\">\n                    Sign In\n                  </Button>\n                  <Button className=\"justify-start bg-blue-600 hover:bg-blue-700\">\n                    Get Started\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAA<PERSON>,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAM9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,SAAS;0CAC1C,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDAGpD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlF;GAnGgB;KAAA", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/progress/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Navigation } from \"@/components/navigation\"\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  TrendingUp,\n  Calendar,\n  Target,\n  Award,\n  Activity,\n  Flame,\n  Clock,\n  BarChart3,\n  Download,\n  Settings,\n  Plus,\n  Eye,\n  TrendingDown,\n  Users,\n  Zap,\n  Heart,\n  X,\n  Edit,\n  Check\n} from \"lucide-react\"\n\n// Mock data for enhanced functionality\nconst progressData = {\n  weeklyActivity: [\n    { week: 'W1', workouts: 3, calories: 450, time: 2.5 },\n    { week: 'W2', workouts: 5, calories: 750, time: 4.2 },\n    { week: 'W3', workouts: 4, calories: 600, time: 3.1 },\n    { week: 'W4', workouts: 6, calories: 900, time: 5.0 },\n    { week: 'W5', workouts: 5, calories: 750, time: 4.5 },\n    { week: 'W6', workouts: 7, calories: 1050, time: 6.2 },\n    { week: 'W7', workouts: 4, calories: 600, time: 3.8 },\n    { week: 'W8', workouts: 6, calories: 900, time: 5.1 }\n  ],\n  goals: [\n    { id: 1, name: 'Weekly Workouts', current: 4, target: 5, unit: 'workouts', color: 'blue' },\n    { id: 2, name: 'Monthly Calories', current: 2340, target: 3000, unit: 'cal', color: 'green' },\n    { id: 3, name: 'Workout Streak', current: 12, target: 30, unit: 'days', color: 'purple' },\n    { id: 4, name: 'Monthly Time', current: 8.5, target: 12, unit: 'hours', color: 'orange' }\n  ],\n  achievements: [\n    { id: 1, title: '100 Workouts', description: 'Completed 100 total workouts', icon: '🏆', date: '2024-12-15', category: 'milestone' },\n    { id: 2, title: '10-Day Streak', description: 'Worked out 10 days in a row', icon: '🔥', date: '2024-12-10', category: 'streak' },\n    { id: 3, title: 'Strength Master', description: 'Completed 25 strength workouts', icon: '💪', date: '2024-12-05', category: 'category' },\n    { id: 4, title: 'Cardio King', description: 'Burned 5000 calories in cardio', icon: '❤️', date: '2024-11-28', category: 'category' },\n    { id: 5, title: 'Early Bird', description: 'Completed 10 morning workouts', icon: '🌅', date: '2024-11-20', category: 'habit' }\n  ],\n  workoutHistory: [\n    { date: '2024-12-20', type: 'Strength Training', duration: 45, calories: 320, exercises: 8 },\n    { date: '2024-12-19', type: 'Cardio Blast', duration: 30, calories: 280, exercises: 6 },\n    { date: '2024-12-18', type: 'Full Body', duration: 60, calories: 420, exercises: 12 },\n    { date: '2024-12-17', type: 'HIIT', duration: 25, calories: 350, exercises: 5 },\n    { date: '2024-12-16', type: 'Yoga Flow', duration: 40, calories: 180, exercises: 10 }\n  ]\n}\n\nexport default function Progress() {\n  const [timeRange, setTimeRange] = useState('8weeks')\n  const [selectedMetric, setSelectedMetric] = useState('workouts')\n  const [showGoalModal, setShowGoalModal] = useState(false)\n  const [showHistoryModal, setShowHistoryModal] = useState(false)\n  const [goals, setGoals] = useState(progressData.goals)\n\n  const timeRanges = [\n    { value: '1week', label: '1 Week' },\n    { value: '4weeks', label: '4 Weeks' },\n    { value: '8weeks', label: '8 Weeks' },\n    { value: '6months', label: '6 Months' },\n    { value: '1year', label: '1 Year' }\n  ]\n\n  const metrics = [\n    { value: 'workouts', label: 'Workouts', icon: Activity },\n    { value: 'calories', label: 'Calories', icon: Flame },\n    { value: 'time', label: 'Time', icon: Clock }\n  ]\n\n  const toggleFavoriteGoal = (goalId: number) => {\n    setGoals(goals.map(goal =>\n      goal.id === goalId\n        ? { ...goal, isFavorite: !goal.isFavorite }\n        : goal\n    ))\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n\n      {/* Header Section */}\n      <section className=\"bg-gradient-to-br from-purple-50 to-indigo-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\">\n              Your Progress\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Track your fitness journey with detailed analytics and insights\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"container mx-auto px-4 py-8\">\n\n        {/* Controls Section */}\n        <div className=\"mb-8 space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center\">\n            <div className=\"flex flex-wrap gap-2\">\n              <span className=\"text-sm font-medium text-gray-700 py-2\">Time Range:</span>\n              {timeRanges.map((range) => (\n                <Button\n                  key={range.value}\n                  variant={timeRange === range.value ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setTimeRange(range.value)}\n                >\n                  {range.label}\n                </Button>\n              ))}\n            </div>\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={() => setShowGoalModal(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Set Goal\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export Data\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Settings className=\"h-4 w-4 mr-2\" />\n                Settings\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"flex flex-wrap gap-2\">\n            <span className=\"text-sm font-medium text-gray-700 py-2\">View:</span>\n            {metrics.map((metric) => {\n              const IconComponent = metric.icon\n              return (\n                <Button\n                  key={metric.value}\n                  variant={selectedMetric === metric.value ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setSelectedMetric(metric.value)}\n                >\n                  <IconComponent className=\"h-4 w-4 mr-2\" />\n                  {metric.label}\n                </Button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Overview Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total Workouts\n              </CardTitle>\n              <div className=\"flex items-center gap-2\">\n                <TrendingUp className=\"h-4 w-4 text-green-500\" />\n                <Activity className=\"h-4 w-4 text-muted-foreground\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">127</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +12 this month (+10.4%)\n              </p>\n              <div className=\"mt-2\">\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  Above Average\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total Time\n              </CardTitle>\n              <div className=\"flex items-center gap-2\">\n                <TrendingUp className=\"h-4 w-4 text-green-500\" />\n                <Clock className=\"h-4 w-4 text-muted-foreground\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">89h</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +8h this month (+9.9%)\n              </p>\n              <div className=\"mt-2\">\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  On Track\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Calories Burned\n              </CardTitle>\n              <div className=\"flex items-center gap-2\">\n                <TrendingUp className=\"h-4 w-4 text-green-500\" />\n                <Flame className=\"h-4 w-4 text-muted-foreground\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">24,567</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +2,340 this month (+10.5%)\n              </p>\n              <div className=\"mt-2\">\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  Excellent\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Current Streak\n              </CardTitle>\n              <div className=\"flex items-center gap-2\">\n                <Flame className=\"h-4 w-4 text-orange-500\" />\n                <Target className=\"h-4 w-4 text-muted-foreground\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">12 days</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Personal best! 🎉\n              </p>\n              <div className=\"mt-2\">\n                <Badge className=\"text-xs bg-orange-100 text-orange-800\">\n                  Hot Streak\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Progress Chart */}\n          <div className=\"lg:col-span-2\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <BarChart3 className=\"h-5 w-5\" />\n                    Weekly Activity\n                  </div>\n                  <Button variant=\"outline\" size=\"sm\" onClick={() => setShowHistoryModal(true)}>\n                    <Eye className=\"h-4 w-4 mr-2\" />\n                    View Details\n                  </Button>\n                </CardTitle>\n                <CardDescription>\n                  Your {selectedMetric} over the past {timeRange === '8weeks' ? '8 weeks' : timeRange}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {/* Enhanced interactive bar chart */}\n                  <div className=\"flex items-end justify-between h-48 border-b border-gray-200 px-2\">\n                    {progressData.weeklyActivity.map((data, index) => {\n                      const value = selectedMetric === 'workouts' ? data.workouts :\n                                   selectedMetric === 'calories' ? data.calories / 150 :\n                                   data.time\n                      const maxValue = selectedMetric === 'workouts' ? 7 :\n                                      selectedMetric === 'calories' ? 7 :\n                                      7\n                      const height = (value / maxValue) * 100\n                      const color = selectedMetric === 'workouts' ? 'bg-blue-500' :\n                                   selectedMetric === 'calories' ? 'bg-green-500' :\n                                   'bg-purple-500'\n\n                      return (\n                        <div key={index} className=\"flex flex-col items-center group cursor-pointer\">\n                          <div className=\"relative\">\n                            <div\n                              className={`${color} w-10 rounded-t transition-all duration-300 group-hover:opacity-80 group-hover:scale-105`}\n                              style={{ height: `${Math.max(height, 8)}%` }}\n                            ></div>\n                            {/* Tooltip on hover */}\n                            <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap\">\n                              {selectedMetric === 'workouts' && `${data.workouts} workouts`}\n                              {selectedMetric === 'calories' && `${data.calories} cal`}\n                              {selectedMetric === 'time' && `${data.time}h`}\n                            </div>\n                          </div>\n                          <span className=\"text-xs text-gray-500 mt-2 font-medium\">\n                            {data.week}\n                          </span>\n                        </div>\n                      )\n                    })}\n                  </div>\n                  <div className=\"flex justify-between text-sm text-gray-600\">\n                    <span>0 {selectedMetric}</span>\n                    <span>\n                      {selectedMetric === 'workouts' && '7 workouts'}\n                      {selectedMetric === 'calories' && '1000+ cal'}\n                      {selectedMetric === 'time' && '7+ hours'}\n                    </span>\n                  </div>\n\n                  {/* Chart summary */}\n                  <div className=\"grid grid-cols-3 gap-4 pt-4 border-t\">\n                    <div className=\"text-center\">\n                      <div className=\"text-lg font-semibold text-blue-600\">\n                        {progressData.weeklyActivity.reduce((sum, data) => sum + data.workouts, 0)}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">Total Workouts</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-lg font-semibold text-green-600\">\n                        {progressData.weeklyActivity.reduce((sum, data) => sum + data.calories, 0)}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">Total Calories</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-lg font-semibold text-purple-600\">\n                        {progressData.weeklyActivity.reduce((sum, data) => sum + data.time, 0).toFixed(1)}h\n                      </div>\n                      <div className=\"text-xs text-gray-500\">Total Time</div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Goals & Achievements */}\n          <div className=\"space-y-6\">\n            {/* Current Goals */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <Target className=\"h-5 w-5\" />\n                    Current Goals\n                  </div>\n                  <Button variant=\"outline\" size=\"sm\" onClick={() => setShowGoalModal(true)}>\n                    <Edit className=\"h-4 w-4\" />\n                  </Button>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                {goals.map((goal) => {\n                  const percentage = Math.min((goal.current / goal.target) * 100, 100)\n                  const colorClass = goal.color === 'blue' ? 'bg-blue-600' :\n                                    goal.color === 'green' ? 'bg-green-600' :\n                                    goal.color === 'purple' ? 'bg-purple-600' :\n                                    'bg-orange-600'\n\n                  return (\n                    <div key={goal.id} className=\"space-y-2\">\n                      <div className=\"flex justify-between items-center text-sm\">\n                        <span className=\"font-medium\">{goal.name}</span>\n                        <div className=\"flex items-center gap-2\">\n                          <span>{goal.current}/{goal.target} {goal.unit}</span>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0\"\n                            onClick={() => toggleFavoriteGoal(goal.id)}\n                          >\n                            <Heart className={`h-3 w-3 ${goal.isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}`} />\n                          </Button>\n                        </div>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div\n                          className={`${colorClass} h-2 rounded-full transition-all duration-500`}\n                          style={{ width: `${percentage}%` }}\n                        ></div>\n                      </div>\n                      <div className=\"flex justify-between text-xs text-gray-500\">\n                        <span>{percentage.toFixed(0)}% complete</span>\n                        {percentage >= 100 ? (\n                          <Badge className=\"text-xs bg-green-100 text-green-800\">\n                            <Check className=\"h-3 w-3 mr-1\" />\n                            Achieved!\n                          </Badge>\n                        ) : (\n                          <span>{(goal.target - goal.current).toFixed(1)} {goal.unit} to go</span>\n                        )}\n                      </div>\n                    </div>\n                  )\n                })}\n              </CardContent>\n            </Card>\n\n            {/* Recent Achievements */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <Award className=\"h-5 w-5\" />\n                    Recent Achievements\n                  </div>\n                  <Badge variant=\"secondary\" className=\"text-xs\">\n                    {progressData.achievements.length} Total\n                  </Badge>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-3 max-h-80 overflow-y-auto\">\n                {progressData.achievements.map((achievement) => {\n                  const bgColor = achievement.category === 'milestone' ? 'bg-yellow-50 border-yellow-200' :\n                                 achievement.category === 'streak' ? 'bg-orange-50 border-orange-200' :\n                                 achievement.category === 'category' ? 'bg-green-50 border-green-200' :\n                                 'bg-blue-50 border-blue-200'\n\n                  return (\n                    <div key={achievement.id} className={`flex items-center gap-3 p-3 rounded-lg border transition-all hover:shadow-md cursor-pointer ${bgColor}`}>\n                      <div className=\"text-2xl\">{achievement.icon}</div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-sm\">{achievement.title}</h4>\n                        <p className=\"text-xs text-gray-600\">{achievement.description}</p>\n                        <p className=\"text-xs text-gray-400 mt-1\">{achievement.date}</p>\n                      </div>\n                      <Badge\n                        variant=\"outline\"\n                        className=\"text-xs capitalize\"\n                      >\n                        {achievement.category}\n                      </Badge>\n                    </div>\n                  )\n                })}\n\n                <Button variant=\"outline\" className=\"w-full text-sm\" size=\"sm\">\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  View All Achievements\n                </Button>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        {/* Monthly Summary & Recent Workouts */}\n        <div className=\"mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Monthly Summary */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Calendar className=\"h-5 w-5\" />\n                This Month's Summary\n              </CardTitle>\n              <CardDescription>\n                December 2024 - Your fitness highlights\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-blue-600\">12</div>\n                  <p className=\"text-sm text-gray-600\">Workouts</p>\n                  <p className=\"text-xs text-green-600\">+20% vs last month</p>\n                </div>\n                <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-green-600\">8.5h</div>\n                  <p className=\"text-sm text-gray-600\">Exercise Time</p>\n                  <p className=\"text-xs text-green-600\">+15% vs last month</p>\n                </div>\n                <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-purple-600\">2,340</div>\n                  <p className=\"text-sm text-gray-600\">Calories</p>\n                  <p className=\"text-xs text-green-600\">+25% vs last month</p>\n                </div>\n                <div className=\"text-center p-4 bg-orange-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-orange-600\">4.2</div>\n                  <p className=\"text-sm text-gray-600\">Avg/Week</p>\n                  <p className=\"text-xs text-green-600\">Above target</p>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-sm\">Monthly Insights</h4>\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center gap-2 text-green-600\">\n                    <TrendingUp className=\"h-4 w-4\" />\n                    <span>Best month for consistency this year</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 text-blue-600\">\n                    <Users className=\"h-4 w-4\" />\n                    <span>Top 15% among similar users</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 text-purple-600\">\n                    <Zap className=\"h-4 w-4\" />\n                    <span>Strength training improved by 18%</span>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Recent Workout History */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <Activity className=\"h-5 w-5\" />\n                  Recent Workouts\n                </div>\n                <Button variant=\"outline\" size=\"sm\" onClick={() => setShowHistoryModal(true)}>\n                  <Eye className=\"h-4 w-4 mr-2\" />\n                  View All\n                </Button>\n              </CardTitle>\n              <CardDescription>\n                Your last 5 workout sessions\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {progressData.workoutHistory.map((workout, index) => (\n                  <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <Activity className=\"h-5 w-5 text-blue-600\" />\n                      </div>\n                      <div>\n                        <h4 className=\"font-medium text-sm\">{workout.type}</h4>\n                        <p className=\"text-xs text-gray-500\">{workout.date}</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium\">{workout.duration}min</div>\n                      <div className=\"text-xs text-gray-500\">{workout.calories} cal</div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Goal Setting Modal */}\n        {showGoalModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-bold\">Set New Goal</h2>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setShowGoalModal(false)}\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">Goal Type</label>\n                    <select className=\"w-full p-2 border border-gray-300 rounded-lg\">\n                      <option>Weekly Workouts</option>\n                      <option>Monthly Calories</option>\n                      <option>Workout Streak</option>\n                      <option>Monthly Time</option>\n                      <option>Custom Goal</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">Target Value</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"Enter target value\"\n                      className=\"w-full p-2 border border-gray-300 rounded-lg\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">Time Frame</label>\n                    <select className=\"w-full p-2 border border-gray-300 rounded-lg\">\n                      <option>Weekly</option>\n                      <option>Monthly</option>\n                      <option>Quarterly</option>\n                      <option>Yearly</option>\n                    </select>\n                  </div>\n\n                  <div className=\"flex gap-2 pt-4\">\n                    <Button\n                      variant=\"outline\"\n                      className=\"flex-1\"\n                      onClick={() => setShowGoalModal(false)}\n                    >\n                      Cancel\n                    </Button>\n                    <Button\n                      className=\"flex-1\"\n                      onClick={() => setShowGoalModal(false)}\n                    >\n                      Create Goal\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Workout History Modal */}\n        {showHistoryModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-bold\">Workout History</h2>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => setShowHistoryModal(false)}\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {progressData.workoutHistory.map((workout, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\">\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                          <Activity className=\"h-6 w-6 text-blue-600\" />\n                        </div>\n                        <div>\n                          <h4 className=\"font-medium\">{workout.type}</h4>\n                          <p className=\"text-sm text-gray-500\">{workout.date}</p>\n                          <p className=\"text-xs text-gray-400\">{workout.exercises} exercises</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"font-medium\">{workout.duration} min</div>\n                        <div className=\"text-sm text-gray-500\">{workout.calories} cal</div>\n                        <Badge variant=\"outline\" className=\"text-xs mt-1\">\n                          Completed\n                        </Badge>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"flex gap-2 pt-4 mt-6 border-t\">\n                  <Button variant=\"outline\" className=\"flex-1\">\n                    <Download className=\"h-4 w-4 mr-2\" />\n                    Export History\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    className=\"flex-1\"\n                    onClick={() => setShowHistoryModal(false)}\n                  >\n                    Close\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AA6BA,uCAAuC;AACvC,MAAM,eAAe;IACnB,gBAAgB;QACd;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;QACpD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;QACpD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;QACpD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;QACpD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;QACpD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAM,MAAM;QAAI;QACrD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;QACpD;YAAE,MAAM;YAAM,UAAU;YAAG,UAAU;YAAK,MAAM;QAAI;KACrD;IACD,OAAO;QACL;YAAE,IAAI;YAAG,MAAM;YAAmB,SAAS;YAAG,QAAQ;YAAG,MAAM;YAAY,OAAO;QAAO;QACzF;YAAE,IAAI;YAAG,MAAM;YAAoB,SAAS;YAAM,QAAQ;YAAM,MAAM;YAAO,OAAO;QAAQ;QAC5F;YAAE,IAAI;YAAG,MAAM;YAAkB,SAAS;YAAI,QAAQ;YAAI,MAAM;YAAQ,OAAO;QAAS;QACxF;YAAE,IAAI;YAAG,MAAM;YAAgB,SAAS;YAAK,QAAQ;YAAI,MAAM;YAAS,OAAO;QAAS;KACzF;IACD,cAAc;QACZ;YAAE,IAAI;YAAG,OAAO;YAAgB,aAAa;YAAgC,MAAM;YAAM,MAAM;YAAc,UAAU;QAAY;QACnI;YAAE,IAAI;YAAG,OAAO;YAAiB,aAAa;YAA+B,MAAM;YAAM,MAAM;YAAc,UAAU;QAAS;QAChI;YAAE,IAAI;YAAG,OAAO;YAAmB,aAAa;YAAkC,MAAM;YAAM,MAAM;YAAc,UAAU;QAAW;QACvI;YAAE,IAAI;YAAG,OAAO;YAAe,aAAa;YAAkC,MAAM;YAAM,MAAM;YAAc,UAAU;QAAW;QACnI;YAAE,IAAI;YAAG,OAAO;YAAc,aAAa;YAAiC,MAAM;YAAM,MAAM;YAAc,UAAU;QAAQ;KAC/H;IACD,gBAAgB;QACd;YAAE,MAAM;YAAc,MAAM;YAAqB,UAAU;YAAI,UAAU;YAAK,WAAW;QAAE;QAC3F;YAAE,MAAM;YAAc,MAAM;YAAgB,UAAU;YAAI,UAAU;YAAK,WAAW;QAAE;QACtF;YAAE,MAAM;YAAc,MAAM;YAAa,UAAU;YAAI,UAAU;YAAK,WAAW;QAAG;QACpF;YAAE,MAAM;YAAc,MAAM;YAAQ,UAAU;YAAI,UAAU;YAAK,WAAW;QAAE;QAC9E;YAAE,MAAM;YAAc,MAAM;YAAa,UAAU;YAAI,UAAU;YAAK,WAAW;QAAG;KACrF;AACH;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,KAAK;IAErD,MAAM,aAAa;QACjB;YAAE,OAAO;YAAS,OAAO;QAAS;QAClC;YAAE,OAAO;YAAU,OAAO;QAAU;QACpC;YAAE,OAAO;YAAU,OAAO;QAAU;QACpC;YAAE,OAAO;YAAW,OAAO;QAAW;QACtC;YAAE,OAAO;YAAS,OAAO;QAAS;KACnC;IAED,MAAM,UAAU;QACd;YAAE,OAAO;YAAY,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;QACvD;YAAE,OAAO;YAAY,OAAO;YAAY,MAAM,uMAAA,CAAA,QAAK;QAAC;QACpD;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM,uMAAA,CAAA,QAAK;QAAC;KAC7C;IAED,MAAM,qBAAqB,CAAC;QAC1B,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,SACR;gBAAE,GAAG,IAAI;gBAAE,YAAY,CAAC,KAAK,UAAU;YAAC,IACxC;IAER;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO7D,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAyC;;;;;;4CACxD,WAAW,GAAG,CAAC,CAAC,sBACf,6LAAC,qIAAA,CAAA,SAAM;oDAEL,SAAS,cAAc,MAAM,KAAK,GAAG,YAAY;oDACjD,MAAK;oDACL,SAAS,IAAM,aAAa,MAAM,KAAK;8DAEtC,MAAM,KAAK;mDALP,MAAM,KAAK;;;;;;;;;;;kDAStB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,SAAS,IAAM,iBAAiB;;kEAClE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAM3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAyC;;;;;;oCACxD,QAAQ,GAAG,CAAC,CAAC;wCACZ,MAAM,gBAAgB,OAAO,IAAI;wCACjC,qBACE,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,mBAAmB,OAAO,KAAK,GAAG,YAAY;4CACvD,MAAK;4CACL,SAAS,IAAM,kBAAkB,OAAO,KAAK;;8DAE7C,6LAAC;oDAAc,WAAU;;;;;;gDACxB,OAAO,KAAK;;2CANR,OAAO,KAAK;;;;;oCASvB;;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAGxB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAG7C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOrD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAGrB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAG7C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOrD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAGrB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAG7C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOrD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAGtB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAG7C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAGnC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,SAAS,IAAM,oBAAoB;;8EACrE,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAIpC,6LAAC,mIAAA,CAAA,kBAAe;;wDAAC;wDACT;wDAAe;wDAAgB,cAAc,WAAW,YAAY;;;;;;;;;;;;;sDAG9E,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ,aAAa,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM;4DACtC,MAAM,QAAQ,mBAAmB,aAAa,KAAK,QAAQ,GAC9C,mBAAmB,aAAa,KAAK,QAAQ,GAAG,MAChD,KAAK,IAAI;4DACtB,MAAM,WAAW,mBAAmB,aAAa,IACjC,mBAAmB,aAAa,IAChC;4DAChB,MAAM,SAAS,AAAC,QAAQ,WAAY;4DACpC,MAAM,QAAQ,mBAAmB,aAAa,gBACjC,mBAAmB,aAAa,iBAChC;4DAEb,qBACE,6LAAC;gEAAgB,WAAU;;kFACzB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,WAAW,GAAG,MAAM,wFAAwF,CAAC;gFAC7G,OAAO;oFAAE,QAAQ,GAAG,KAAK,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;gFAAC;;;;;;0FAG7C,6LAAC;gFAAI,WAAU;;oFACZ,mBAAmB,cAAc,GAAG,KAAK,QAAQ,CAAC,SAAS,CAAC;oFAC5D,mBAAmB,cAAc,GAAG,KAAK,QAAQ,CAAC,IAAI,CAAC;oFACvD,mBAAmB,UAAU,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;;;;;;;;;;;;;kFAGjD,6LAAC;wEAAK,WAAU;kFACb,KAAK,IAAI;;;;;;;+DAdJ;;;;;wDAkBd;;;;;;kEAEF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAG;;;;;;;0EACT,6LAAC;;oEACE,mBAAmB,cAAc;oEACjC,mBAAmB,cAAc;oEACjC,mBAAmB,UAAU;;;;;;;;;;;;;kEAKlC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,aAAa,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;;;;;;kFAE1E,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,aAAa,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;;;;;;kFAE1E,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,aAAa,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE,GAAG,OAAO,CAAC;4EAAG;;;;;;;kFAEpF,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASnD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAGhC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,SAAS,IAAM,iBAAiB;sEAClE,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAItB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,MAAM,GAAG,CAAC,CAAC;oDACV,MAAM,aAAa,KAAK,GAAG,CAAC,AAAC,KAAK,OAAO,GAAG,KAAK,MAAM,GAAI,KAAK;oDAChE,MAAM,aAAa,KAAK,KAAK,KAAK,SAAS,gBACzB,KAAK,KAAK,KAAK,UAAU,iBACzB,KAAK,KAAK,KAAK,WAAW,kBAC1B;oDAElB,qBACE,6LAAC;wDAAkB,WAAU;;0EAC3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAM,KAAK,OAAO;oFAAC;oFAAE,KAAK,MAAM;oFAAC;oFAAE,KAAK,IAAI;;;;;;;0FAC7C,6LAAC,qIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,WAAU;gFACV,SAAS,IAAM,mBAAmB,KAAK,EAAE;0FAEzC,cAAA,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,UAAU,GAAG,8BAA8B,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;0EAIpG,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAW,GAAG,WAAW,6CAA6C,CAAC;oEACvE,OAAO;wEAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGrC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAM,WAAW,OAAO,CAAC;4EAAG;;;;;;;oEAC5B,cAAc,oBACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;;0FACf,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAAiB;;;;;;6FAIpC,6LAAC;;4EAAM,CAAC,KAAK,MAAM,GAAG,KAAK,OAAO,EAAE,OAAO,CAAC;4EAAG;4EAAE,KAAK,IAAI;4EAAC;;;;;;;;;;;;;;uDA7BvD,KAAK,EAAE;;;;;gDAkCrB;;;;;;;;;;;;kDAKJ,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG/B,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAClC,aAAa,YAAY,CAAC,MAAM;gEAAC;;;;;;;;;;;;;;;;;;0DAIxC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;oDACpB,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC;wDAC9B,MAAM,UAAU,YAAY,QAAQ,KAAK,cAAc,mCACxC,YAAY,QAAQ,KAAK,WAAW,mCACpC,YAAY,QAAQ,KAAK,aAAa,iCACtC;wDAEf,qBACE,6LAAC;4DAAyB,WAAW,CAAC,4FAA4F,EAAE,SAAS;;8EAC3I,6LAAC;oEAAI,WAAU;8EAAY,YAAY,IAAI;;;;;;8EAC3C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAuB,YAAY,KAAK;;;;;;sFACtD,6LAAC;4EAAE,WAAU;sFAAyB,YAAY,WAAW;;;;;;sFAC7D,6LAAC;4EAAE,WAAU;sFAA8B,YAAY,IAAI;;;;;;;;;;;;8EAE7D,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAU;8EAET,YAAY,QAAQ;;;;;;;2DAXf,YAAY,EAAE;;;;;oDAe5B;kEAEA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;wDAAiB,MAAK;;0EACxD,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAmC;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAoC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAsB;;;;;;kEACpC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,SAAS,IAAM,oBAAoB;;0EACrE,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIpC,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,aAAa,cAAc,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAuB,QAAQ,IAAI;;;;;;sFACjD,6LAAC;4EAAE,WAAU;sFAAyB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;sEAGtD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAuB,QAAQ,QAAQ;wEAAC;;;;;;;8EACvD,6LAAC;oEAAI,WAAU;;wEAAyB,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;;mDAZnD;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAsBnB,+BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAClD,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;;;;;;;;;;;;;0DAIZ,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAClD,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAClD,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;0EACR,6LAAC;0EAAO;;;;;;;;;;;;;;;;;;0DAIZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS,IAAM,iBAAiB;kEACjC;;;;;;kEAGD,6LAAC,qIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,SAAS,IAAM,iBAAiB;kEACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWZ,kCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,oBAAoB;0DAEnC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,cAAc,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAe,QAAQ,IAAI;;;;;;kFACzC,6LAAC;wEAAE,WAAU;kFAAyB,QAAQ,IAAI;;;;;;kFAClD,6LAAC;wEAAE,WAAU;;4EAAyB,QAAQ,SAAS;4EAAC;;;;;;;;;;;;;;;;;;;kEAG5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEAAe,QAAQ,QAAQ;oEAAC;;;;;;;0EAC/C,6LAAC;gEAAI,WAAU;;oEAAyB,QAAQ,QAAQ;oEAAC;;;;;;;0EACzD,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;;+CAd5C;;;;;;;;;;kDAsBd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,oBAAoB;0DACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA7mBwB;KAAA", "debugId": null}}]}