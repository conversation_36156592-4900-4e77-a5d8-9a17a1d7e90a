/**
 * React Query hooks for progress tracking data
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { ProgressService } from '../api/services/progress';
import type { 
  CreateProgressRecordData, 
  UpdateProgressRecordData 
} from '../api/types';

// Query keys for consistent caching
export const progressKeys = {
  all: ['progress'] as const,
  records: () => [...progressKeys.all, 'records'] as const,
  record: (id: string) => [...progressKeys.records(), id] as const,
  recordsList: (params: any) => [...progressKeys.records(), 'list', params] as const,
  stats: (period: string) => [...progressKeys.all, 'stats', period] as const,
  workoutStats: (period: string) => [...progressKeys.all, 'workout-stats', period] as const,
  exerciseProgress: (exerciseId: string, period: string) => [...progressKeys.all, 'exercise', exerciseId, period] as const,
  bodyMeasurements: (period: string) => [...progressKeys.all, 'body-measurements', period] as const,
  goals: () => [...progressKeys.all, 'goals'] as const,
  achievements: () => [...progressKeys.all, 'achievements'] as const,
  calendar: (year: number, month: number) => [...progressKeys.all, 'calendar', year, month] as const,
  personalRecords: () => [...progressKeys.all, 'personal-records'] as const,
  strengthProgression: (exerciseIds?: string[]) => [...progressKeys.all, 'strength-progression', exerciseIds] as const,
  workoutIntensity: (period: string) => [...progressKeys.all, 'workout-intensity', period] as const,
};

/**
 * Hook to get progress records
 */
export function useProgressRecords(params: Parameters<typeof ProgressService.getProgressRecords>[0] = {}) {
  return useQuery({
    queryKey: progressKeys.recordsList(params),
    queryFn: () => ProgressService.getProgressRecords(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get a specific progress record
 */
export function useProgressRecord(id: string, enabled = true) {
  return useQuery({
    queryKey: progressKeys.record(id),
    queryFn: () => ProgressService.getProgressRecord(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create a progress record
 */
export function useCreateProgressRecord() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateProgressRecordData) => ProgressService.createProgressRecord(data),
    onSuccess: () => {
      // Invalidate progress records and stats
      queryClient.invalidateQueries({ queryKey: progressKeys.records() });
      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });
      queryClient.invalidateQueries({ queryKey: progressKeys.workoutStats('month') });
    },
  });
}

/**
 * Hook to update a progress record
 */
export function useUpdateProgressRecord() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProgressRecordData }) =>
      ProgressService.updateProgressRecord(id, data),
    onSuccess: (updatedRecord) => {
      // Update the specific record cache
      queryClient.setQueryData(progressKeys.record(updatedRecord.id), updatedRecord);
      
      // Invalidate records list and stats
      queryClient.invalidateQueries({ queryKey: progressKeys.records() });
      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });
    },
  });
}

/**
 * Hook to delete a progress record
 */
export function useDeleteProgressRecord() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => ProgressService.deleteProgressRecord(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: progressKeys.record(deletedId) });
      
      // Invalidate records list and stats
      queryClient.invalidateQueries({ queryKey: progressKeys.records() });
      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });
    },
  });
}

/**
 * Hook to get progress statistics
 */
export function useProgressStats(period: 'week' | 'month' | 'year' | 'all' = 'month') {
  return useQuery({
    queryKey: progressKeys.stats(period),
    queryFn: () => ProgressService.getProgressStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get workout statistics
 */
export function useWorkoutStats(period: 'week' | 'month' | 'year' = 'month') {
  return useQuery({
    queryKey: progressKeys.workoutStats(period),
    queryFn: () => ProgressService.getWorkoutStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get exercise progress data
 */
export function useExerciseProgress(exerciseId: string, period: 'week' | 'month' | 'year' = 'month', enabled = true) {
  return useQuery({
    queryKey: progressKeys.exerciseProgress(exerciseId, period),
    queryFn: () => ProgressService.getExerciseProgress(exerciseId, period),
    enabled: enabled && !!exerciseId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get body measurements
 */
export function useBodyMeasurements(period: 'week' | 'month' | 'year' = 'month') {
  return useQuery({
    queryKey: progressKeys.bodyMeasurements(period),
    queryFn: () => ProgressService.getBodyMeasurements(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to add body measurement
 */
export function useAddBodyMeasurement() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Parameters<typeof ProgressService.addBodyMeasurement>[0]) =>
      ProgressService.addBodyMeasurement(data),
    onSuccess: () => {
      // Invalidate body measurements
      queryClient.invalidateQueries({ queryKey: progressKeys.bodyMeasurements('month') });
      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });
    },
  });
}

/**
 * Hook to get fitness goals
 */
export function useFitnessGoals() {
  return useQuery({
    queryKey: progressKeys.goals(),
    queryFn: () => ProgressService.getFitnessGoals(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create fitness goal
 */
export function useCreateFitnessGoal() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Parameters<typeof ProgressService.createFitnessGoal>[0]) =>
      ProgressService.createFitnessGoal(data),
    onSuccess: () => {
      // Invalidate goals
      queryClient.invalidateQueries({ queryKey: progressKeys.goals() });
    },
  });
}

/**
 * Hook to update goal progress
 */
export function useUpdateGoalProgress() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ goalId, currentValue }: { goalId: string; currentValue: number }) =>
      ProgressService.updateGoalProgress(goalId, currentValue),
    onSuccess: () => {
      // Invalidate goals
      queryClient.invalidateQueries({ queryKey: progressKeys.goals() });
    },
  });
}

/**
 * Hook to get achievements
 */
export function useAchievements() {
  return useQuery({
    queryKey: progressKeys.achievements(),
    queryFn: () => ProgressService.getAchievements(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

/**
 * Hook to get workout calendar
 */
export function useWorkoutCalendar(year: number, month: number) {
  return useQuery({
    queryKey: progressKeys.calendar(year, month),
    queryFn: () => ProgressService.getWorkoutCalendar(year, month),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get personal records
 */
export function usePersonalRecords() {
  return useQuery({
    queryKey: progressKeys.personalRecords(),
    queryFn: () => ProgressService.getPersonalRecords(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get strength progression
 */
export function useStrengthProgression(exerciseIds?: string[]) {
  return useQuery({
    queryKey: progressKeys.strengthProgression(exerciseIds),
    queryFn: () => ProgressService.getStrengthProgression(exerciseIds),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get workout intensity analysis
 */
export function useWorkoutIntensity(period: 'week' | 'month' | 'year' = 'month') {
  return useQuery({
    queryKey: progressKeys.workoutIntensity(period),
    queryFn: () => ProgressService.getWorkoutIntensity(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to export progress data
 */
export function useExportProgressData() {
  return useMutation({
    mutationFn: ({ format, period }: { format: 'csv' | 'json'; period: 'month' | 'year' | 'all' }) =>
      ProgressService.exportProgressData(format, period),
    onSuccess: (blob, { format }) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `progress-data.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
}

/**
 * Hook to get progress records with infinite scrolling
 */
export function useInfiniteProgressRecords(params: Omit<Parameters<typeof ProgressService.getProgressRecords>[0], 'offset'> = {}) {
  return useInfiniteQuery({
    queryKey: progressKeys.recordsList(params),
    queryFn: ({ pageParam = 0 }) => 
      ProgressService.getProgressRecords({ ...params, offset: pageParam }),
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      const { pagination } = lastPage;
      return pagination.hasNext ? pagination.page * pagination.limit : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
}
