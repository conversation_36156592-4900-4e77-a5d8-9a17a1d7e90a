import { Metadata } from 'next';
import { siteConfig } from './config';

// Generate canonical URL
export function generateCanonicalUrl(path: string): string {
  const baseUrl = siteConfig.url;
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
}

// Generate Open Graph image URL
export function generateOGImageUrl(title: string, description?: string): string {
  const baseUrl = siteConfig.url;
  const params = new URLSearchParams();
  params.set('title', title);
  if (description) {
    params.set('description', description);
  }
  return `${baseUrl}/api/og?${params.toString()}`;
}

// Truncate text for meta descriptions
export function truncateText(text: string, maxLength: number = 160): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3).trim() + '...';
}

// Generate keywords from content
export function generateKeywords(content: string, additionalKeywords: string[] = []): string[] {
  const baseKeywords = siteConfig.keywords;
  const contentWords = content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3)
    .slice(0, 10);

  return [...baseKeywords, ...additionalKeywords, ...contentWords];
}

// Generate page metadata
export function generatePageMetadata({
  title,
  description,
  path,
  keywords = [],
  image,
  type = 'website'
}: {
  title: string;
  description: string;
  path: string;
  keywords?: string[];
  image?: string;
  type?: 'website' | 'article';
}): Metadata {
  const fullTitle = title.includes(siteConfig.name) ? title : `${title} | ${siteConfig.name}`;
  const canonicalUrl = generateCanonicalUrl(path);
  const ogImage = image || generateOGImageUrl(title, description);

  const allKeywords = generateKeywords(description, keywords);

  return {
    title: fullTitle,
    description: truncateText(description),
    keywords: allKeywords,
    canonical: canonicalUrl,
    openGraph: {
      title: fullTitle,
      description: truncateText(description),
      url: canonicalUrl,
      type,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title
        }
      ],
      siteName: siteConfig.name
    },
    twitter: {
      title: fullTitle,
      description: truncateText(description),
      card: 'summary_large_image',
      images: [ogImage]
    },
    alternates: {
      canonical: canonicalUrl
    }
  };
}

// Generate exercise-specific metadata
export function generateExerciseMetadata(exercise: {
  name: string;
  description: string;
  targetMuscles?: string[];
  equipment?: string;
  difficulty?: string;
  category?: string;
}): Metadata {
  const title = `${exercise.name} - Exercise Guide | ${siteConfig.name}`;
  const description = truncateText(
    `Learn how to perform ${exercise.name} correctly. ${exercise.description} Target muscles: ${exercise.targetMuscles?.join(', ') || 'Multiple'}. Equipment: ${exercise.equipment || 'Bodyweight'}.`
  );
  
  const keywords = generateKeywords(
    `${exercise.name} ${exercise.description}`,
    [
      exercise.category || 'exercise',
      exercise.equipment || 'bodyweight',
      exercise.difficulty || 'fitness',
      ...(exercise.targetMuscles || [])
    ]
  );

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'article',
      images: [
        {
          url: generateOGImageUrl(exercise.name, `${exercise.category} exercise for ${exercise.targetMuscles?.join(', ')}`),
          width: 1200,
          height: 630,
          alt: `${exercise.name} exercise demonstration`
        }
      ]
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image'
    }
  };
}

// Generate workout-specific metadata
export function generateWorkoutMetadata(workout: {
  title: string;
  description: string;
  duration?: number;
  difficulty?: string;
  type?: string;
  targetMuscles?: string[];
}): Metadata {
  const title = `${workout.title} - Workout Program | ${siteConfig.name}`;
  const description = truncateText(
    `${workout.description} Duration: ${workout.duration || 'Flexible'} minutes. Difficulty: ${workout.difficulty || 'All levels'}. Type: ${workout.type || 'General fitness'}.`
  );
  
  const keywords = generateKeywords(
    `${workout.title} ${workout.description}`,
    [
      'workout program',
      'fitness plan',
      workout.type || 'training',
      workout.difficulty || 'exercise',
      ...(workout.targetMuscles || [])
    ]
  );

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'article',
      images: [
        {
          url: generateOGImageUrl(workout.title, `${workout.type} workout program - ${workout.difficulty} level`),
          width: 1200,
          height: 630,
          alt: `${workout.title} workout program`
        }
      ]
    },
    twitter: {
      title,
      description,
      card: 'summary_large_image'
    }
  };
}

// Generate FAQ schema from common questions
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}

// Common fitness-related FAQs
export const commonFitnessQAs = [
  {
    question: "How often should I work out?",
    answer: "For general fitness, aim for at least 150 minutes of moderate-intensity exercise or 75 minutes of vigorous-intensity exercise per week, plus muscle-strengthening activities on 2 or more days per week."
  },
  {
    question: "What's the best time to exercise?",
    answer: "The best time to exercise is when you can be consistent. Some people prefer morning workouts for energy, while others prefer evening sessions. Choose a time that fits your schedule and stick to it."
  },
  {
    question: "Do I need equipment to get fit?",
    answer: "No, you can achieve great fitness results with bodyweight exercises. However, equipment like dumbbells, resistance bands, or kettlebells can add variety and progression to your workouts."
  },
  {
    question: "How long before I see results?",
    answer: "You may notice improvements in energy and mood within a few days. Physical changes typically become noticeable after 2-4 weeks of consistent exercise, with significant changes after 8-12 weeks."
  },
  {
    question: "Should I do cardio or strength training?",
    answer: "Both are important for overall fitness. Cardio improves heart health and endurance, while strength training builds muscle and bone density. A balanced program includes both types of exercise."
  }
];

// Generate rich snippets for search results
export function generateRichSnippets(type: 'exercise' | 'workout' | 'article', data: any) {
  switch (type) {
    case 'exercise':
      return {
        '@context': 'https://schema.org',
        '@type': 'HowTo',
        name: `How to do ${data.name}`,
        description: data.description,
        totalTime: data.duration ? `PT${data.duration}M` : undefined,
        supply: data.equipment ? [data.equipment] : undefined,
        tool: data.equipment ? [data.equipment] : undefined,
        step: data.instructions?.map((instruction: string, index: number) => ({
          '@type': 'HowToStep',
          position: index + 1,
          text: instruction
        }))
      };
    
    case 'workout':
      return {
        '@context': 'https://schema.org',
        '@type': 'ExercisePlan',
        name: data.title,
        description: data.description,
        category: 'Fitness',
        exerciseType: data.type,
        intensity: data.difficulty,
        workload: data.duration ? `${data.duration} minutes` : undefined
      };
    
    default:
      return null;
  }
}
