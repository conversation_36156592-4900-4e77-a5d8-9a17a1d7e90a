module.exports = {

"[project]/src/lib/seo/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "commonFitnessQAs": (()=>commonFitnessQAs),
    "generateCanonicalUrl": (()=>generateCanonicalUrl),
    "generateExerciseMetadata": (()=>generateExerciseMetadata),
    "generateFAQSchema": (()=>generateFAQSchema),
    "generateKeywords": (()=>generateKeywords),
    "generateOGImageUrl": (()=>generateOGImageUrl),
    "generatePageMetadata": (()=>generatePageMetadata),
    "generateRichSnippets": (()=>generateRichSnippets),
    "generateWorkoutMetadata": (()=>generateWorkoutMetadata),
    "truncateText": (()=>truncateText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/seo/config.ts [app-rsc] (ecmascript)");
;
function generateCanonicalUrl(path) {
    const baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].url;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${baseUrl}${cleanPath}`;
}
function generateOGImageUrl(title, description) {
    const baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].url;
    const params = new URLSearchParams();
    params.set('title', title);
    if (description) {
        params.set('description', description);
    }
    return `${baseUrl}/api/og?${params.toString()}`;
}
function truncateText(text, maxLength = 160) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3).trim() + '...';
}
function generateKeywords(content, additionalKeywords = []) {
    const baseKeywords = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].keywords;
    const contentWords = content.toLowerCase().replace(/[^\w\s]/g, ' ').split(/\s+/).filter((word)=>word.length > 3).slice(0, 10);
    return [
        ...baseKeywords,
        ...additionalKeywords,
        ...contentWords
    ];
}
function generatePageMetadata({ title, description, path, keywords = [], image, type = 'website' }) {
    const fullTitle = title.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].name) ? title : `${title} | ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].name}`;
    const canonicalUrl = generateCanonicalUrl(path);
    const ogImage = image || generateOGImageUrl(title, description);
    const allKeywords = generateKeywords(description, keywords);
    return {
        title: fullTitle,
        description: truncateText(description),
        keywords: allKeywords,
        canonical: canonicalUrl,
        openGraph: {
            title: fullTitle,
            description: truncateText(description),
            url: canonicalUrl,
            type,
            images: [
                {
                    url: ogImage,
                    width: 1200,
                    height: 630,
                    alt: title
                }
            ],
            siteName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].name
        },
        twitter: {
            title: fullTitle,
            description: truncateText(description),
            card: 'summary_large_image',
            images: [
                ogImage
            ]
        },
        alternates: {
            canonical: canonicalUrl
        }
    };
}
function generateExerciseMetadata(exercise) {
    const title = `${exercise.name} - Exercise Guide | ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].name}`;
    const description = truncateText(`Learn how to perform ${exercise.name} correctly. ${exercise.description} Target muscles: ${exercise.targetMuscles?.join(', ') || 'Multiple'}. Equipment: ${exercise.equipment || 'Bodyweight'}.`);
    const keywords = generateKeywords(`${exercise.name} ${exercise.description}`, [
        exercise.category || 'exercise',
        exercise.equipment || 'bodyweight',
        exercise.difficulty || 'fitness',
        ...exercise.targetMuscles || []
    ]);
    return {
        title,
        description,
        keywords,
        openGraph: {
            title,
            description,
            type: 'article',
            images: [
                {
                    url: generateOGImageUrl(exercise.name, `${exercise.category} exercise for ${exercise.targetMuscles?.join(', ')}`),
                    width: 1200,
                    height: 630,
                    alt: `${exercise.name} exercise demonstration`
                }
            ]
        },
        twitter: {
            title,
            description,
            card: 'summary_large_image'
        }
    };
}
function generateWorkoutMetadata(workout) {
    const title = `${workout.title} - Workout Program | ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["siteConfig"].name}`;
    const description = truncateText(`${workout.description} Duration: ${workout.duration || 'Flexible'} minutes. Difficulty: ${workout.difficulty || 'All levels'}. Type: ${workout.type || 'General fitness'}.`);
    const keywords = generateKeywords(`${workout.title} ${workout.description}`, [
        'workout program',
        'fitness plan',
        workout.type || 'training',
        workout.difficulty || 'exercise',
        ...workout.targetMuscles || []
    ]);
    return {
        title,
        description,
        keywords,
        openGraph: {
            title,
            description,
            type: 'article',
            images: [
                {
                    url: generateOGImageUrl(workout.title, `${workout.type} workout program - ${workout.difficulty} level`),
                    width: 1200,
                    height: 630,
                    alt: `${workout.title} workout program`
                }
            ]
        },
        twitter: {
            title,
            description,
            card: 'summary_large_image'
        }
    };
}
function generateFAQSchema(faqs) {
    return {
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: faqs.map((faq)=>({
                '@type': 'Question',
                name: faq.question,
                acceptedAnswer: {
                    '@type': 'Answer',
                    text: faq.answer
                }
            }))
    };
}
const commonFitnessQAs = [
    {
        question: "How often should I work out?",
        answer: "For general fitness, aim for at least 150 minutes of moderate-intensity exercise or 75 minutes of vigorous-intensity exercise per week, plus muscle-strengthening activities on 2 or more days per week."
    },
    {
        question: "What's the best time to exercise?",
        answer: "The best time to exercise is when you can be consistent. Some people prefer morning workouts for energy, while others prefer evening sessions. Choose a time that fits your schedule and stick to it."
    },
    {
        question: "Do I need equipment to get fit?",
        answer: "No, you can achieve great fitness results with bodyweight exercises. However, equipment like dumbbells, resistance bands, or kettlebells can add variety and progression to your workouts."
    },
    {
        question: "How long before I see results?",
        answer: "You may notice improvements in energy and mood within a few days. Physical changes typically become noticeable after 2-4 weeks of consistent exercise, with significant changes after 8-12 weeks."
    },
    {
        question: "Should I do cardio or strength training?",
        answer: "Both are important for overall fitness. Cardio improves heart health and endurance, while strength training builds muscle and bone density. A balanced program includes both types of exercise."
    }
];
function generateRichSnippets(type, data) {
    switch(type){
        case 'exercise':
            return {
                '@context': 'https://schema.org',
                '@type': 'HowTo',
                name: `How to do ${data.name}`,
                description: data.description,
                totalTime: data.duration ? `PT${data.duration}M` : undefined,
                supply: data.equipment ? [
                    data.equipment
                ] : undefined,
                tool: data.equipment ? [
                    data.equipment
                ] : undefined,
                step: data.instructions?.map((instruction, index)=>({
                        '@type': 'HowToStep',
                        position: index + 1,
                        text: instruction
                    }))
            };
        case 'workout':
            return {
                '@context': 'https://schema.org',
                '@type': 'ExercisePlan',
                name: data.title,
                description: data.description,
                category: 'Fitness',
                exerciseType: data.type,
                intensity: data.difficulty,
                workload: data.duration ? `${data.duration} minutes` : undefined
            };
        default:
            return null;
    }
}
}}),
"[project]/src/components/ui/breadcrumb.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Breadcrumb": (()=>Breadcrumb),
    "breadcrumbConfigs": (()=>breadcrumbConfigs),
    "generateBreadcrumbs": (()=>generateBreadcrumbs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-rsc] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/house.js [app-rsc] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$structured$2d$data$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/structured-data.tsx [app-rsc] (ecmascript)");
;
;
;
;
function Breadcrumb({ items, className = '' }) {
    // Prepare structured data
    const structuredDataItems = items.map((item)=>({
            name: item.name,
            url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app'}${item.href}`
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$structured$2d$data$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BreadcrumbStructuredData"], {
                items: structuredDataItems
            }, void 0, false, {
                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                className: `flex ${className}`,
                "aria-label": "Breadcrumb",
                role: "navigation",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
                    className: "inline-flex items-center space-x-1 md:space-x-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            className: "inline-flex items-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white",
                                "aria-label": "Home",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"], {
                                        className: "w-4 h-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/breadcrumb.tsx",
                                        lineNumber: 40,
                                        columnNumber: 15
                                    }, this),
                                    "Home"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                                lineNumber: 35,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/breadcrumb.tsx",
                            lineNumber: 34,
                            columnNumber: 11
                        }, this),
                        items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: "inline-flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                        className: "w-4 h-4 text-gray-400 mx-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/breadcrumb.tsx",
                                        lineNumber: 48,
                                        columnNumber: 15
                                    }, this),
                                    item.current || index === items.length - 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400",
                                        "aria-current": "page",
                                        children: item.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/breadcrumb.tsx",
                                        lineNumber: 50,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: item.href,
                                        className: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white",
                                        children: item.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/breadcrumb.tsx",
                                        lineNumber: 57,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, item.href, true, {
                                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                                lineNumber: 47,
                                columnNumber: 13
                            }, this))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/breadcrumb.tsx",
                    lineNumber: 32,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
function generateBreadcrumbs(pathname) {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    segments.forEach((segment, index)=>{
        const href = '/' + segments.slice(0, index + 1).join('/');
        const name = formatSegmentName(segment);
        breadcrumbs.push({
            name,
            href,
            current: index === segments.length - 1
        });
    });
    return breadcrumbs;
}
// Format segment names for display
function formatSegmentName(segment) {
    // Handle dynamic routes
    if (segment.startsWith('[') && segment.endsWith(']')) {
        return 'Details';
    }
    // Convert kebab-case to Title Case
    return segment.split('-').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}
const breadcrumbConfigs = {
    '/exercises': [
        {
            name: 'Exercises',
            href: '/exercises'
        }
    ],
    '/workouts': [
        {
            name: 'Workouts',
            href: '/workouts'
        }
    ],
    '/progress': [
        {
            name: 'Progress',
            href: '/progress'
        }
    ],
    '/dashboard': [
        {
            name: 'Dashboard',
            href: '/dashboard'
        }
    ],
    '/auth/signin': [
        {
            name: 'Sign In',
            href: '/auth/signin'
        }
    ],
    '/auth/signup': [
        {
            name: 'Sign Up',
            href: '/auth/signup'
        }
    ]
};
}}),
"[project]/src/app/workouts/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>WorkoutsLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/seo/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/breadcrumb.tsx [app-rsc] (ecmascript)");
;
;
;
const metadata = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePageMetadata"])({
    title: "Workout Programs - AI-Powered Fitness Plans",
    description: "Discover AI-generated workout programs tailored to your fitness level and goals. From beginner-friendly routines to advanced training plans, find the perfect workout for you.",
    path: "/workouts"
});
function WorkoutsLayout({ children }) {
    const breadcrumbItems = [
        {
            name: 'Workouts',
            href: '/workouts'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "container mx-auto px-4 py-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Breadcrumb"], {
                    items: breadcrumbItems
                }, void 0, false, {
                    fileName: "[project]/src/app/workouts/layout.tsx",
                    lineNumber: 23,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/workouts/layout.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/workouts/layout.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ChevronRight)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-rsc] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m9 18 6-6-6-6",
            key: "mthhwq"
        }
    ]
];
const ChevronRight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])("chevron-right", __iconNode);
;
 //# sourceMappingURL=chevron-right.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-rsc] (ecmascript) <export default as ChevronRight>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChevronRight": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-rsc] (ecmascript)");
}}),

};

//# sourceMappingURL=_d5787c42._.js.map