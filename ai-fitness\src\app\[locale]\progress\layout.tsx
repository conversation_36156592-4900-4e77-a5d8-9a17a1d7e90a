import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo/utils';
import { Breadcrumb } from '@/components/ui/breadcrumb';

export const metadata: Metadata = generatePageMetadata({
  title: "Progress Tracking - Monitor Your Fitness Journey",
  description: "Track your fitness progress with detailed analytics, body measurements, workout history, and performance metrics. Visualize your improvements over time.",
  path: "/progress",
});

export default function ProgressLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Progress', href: '/progress' }
  ];

  return (
    <div>
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      {children}
    </div>
  );
}
