import Link from "next/link";

# Sobre o Workout.cool

## Por que Workout.cool?

Workout.cool nasceu do desejo de oferecer uma plataforma de treino confiável, moderna e ativamente mantida, após o projeto original <WorkoutLol variant="muted" /> ter sido abandonado.

## A história

Workout.cool é o resultado de uma aventura impulsionada pela comunidade.

Fui o **primeiro colaborador de código aberto** do projeto <WorkoutLol variant="muted" />.

Isso significa que vi o projeto *ganhar vida*, *crescer*, depois ser **vendido** e finalmente **abandonado** por seu novo proprietário.

Como muitos usuários, senti uma **profunda frustração** e uma *sensação de abandono* ao ver desaparecer uma ferramenta para a qual havia contribuído tanto, com solicitações de recursos sem resposta e envelhecendo.

---

*<PERSON><PERSON><PERSON> meses*, tentei contatar o novo proprietário—**sem nunca receber uma única resposta** apesar de muitas tentativas (*cerca de 15*).

Diante deste **silêncio** e da **angústia da comunidade**, decidi **tomar o assunto em minhas próprias mãos**:

> Em vez de deixar todo esse trabalho desaparecer, **relancei um projeto ainda mais ambicioso, moderno e aberto para todos.**

Este projeto não é movido pelo lucro, mas pela **paixão** e pelo desejo de servir a comunidade fitness de código aberto.

**Alguém tinha que salvar a comunidade—_decidi ser essa pessoa!_**

## Código aberto e comunidade

Workout.cool é código aberto, garantindo transparência, modularidade e escalabilidade.  
Todos são bem-vindos para contribuir—código, documentação ou ideias!

- [Ver o projeto no GitHub](https://github.com/Snouzy/workout-cool)
- [Comprar um café para apoiar](https://ko-fi.com/workoutcool)

## Junte-se à missão!

Quer contribuir, sugerir um recurso ou simplesmente apoiar o projeto?  
Entre em contato conosco ou abra uma issue no GitHub!

**[<EMAIL>](mailto:<EMAIL>)**