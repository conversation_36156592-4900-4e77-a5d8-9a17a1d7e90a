"use client"

import { useState } from "react"
import { Navigation } from "@/components/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Filter,
  Clock,
  Target,
  Play,
  Heart,
  Zap,
  Star,
  BookOpen,
  Users,
  Dumbbell,
  X,
  Eye,
  Plus
} from "lucide-react"

const exercises = [
  {
    id: 1,
    name: "Push-ups",
    category: "Chest",
    difficulty: "Beginner",
    duration: "30 seconds",
    equipment: "None",
    description: "Classic bodyweight exercise targeting chest, shoulders, and triceps",
    image: "💪",
    muscles: ["Chest", "Shoulders", "Triceps"],
    rating: 4.8,
    views: 12547,
    isFavorite: false,
    instructions: ["Start in plank position", "Lower body to ground", "Push back up", "Repeat"],
    tips: "Keep your core tight and maintain straight line from head to heels",
    calories: 8
  },
  {
    id: 2,
    name: "Squats",
    category: "Legs",
    difficulty: "Beginner",
    duration: "45 seconds",
    equipment: "None",
    description: "Fundamental lower body exercise for strength and mobility",
    image: "🦵",
    muscles: ["Quadriceps", "Glutes", "Hamstrings"],
    rating: 4.9,
    views: 18923,
    isFavorite: true,
    instructions: ["Stand with feet shoulder-width apart", "Lower hips back and down", "Keep chest up", "Return to standing"],
    tips: "Keep your weight on your heels and knees tracking over toes",
    calories: 12
  },
  {
    id: 3,
    name: "Burpees",
    category: "Full Body",
    difficulty: "Advanced",
    duration: "30 seconds",
    equipment: "None",
    description: "High-intensity full-body exercise combining strength and cardio",
    image: "🔥",
    muscles: ["Full Body", "Cardio"],
    rating: 4.6,
    views: 8934,
    isFavorite: false,
    instructions: ["Start standing", "Drop to squat", "Jump back to plank", "Do push-up", "Jump feet forward", "Jump up"],
    tips: "Maintain good form even when tired - quality over speed",
    calories: 15
  },
  {
    id: 4,
    name: "Plank",
    category: "Core",
    difficulty: "Intermediate",
    duration: "60 seconds",
    equipment: "None",
    description: "Isometric core exercise for stability and strength",
    image: "🏋️",
    muscles: ["Core", "Shoulders", "Back"],
    rating: 4.7,
    views: 15632,
    isFavorite: true,
    instructions: ["Start in push-up position", "Hold body straight", "Engage core", "Breathe normally"],
    tips: "Don't let hips sag or pike up - maintain neutral spine",
    calories: 5
  },
  {
    id: 5,
    name: "Mountain Climbers",
    category: "Cardio",
    difficulty: "Intermediate",
    duration: "30 seconds",
    equipment: "None",
    description: "Dynamic cardio exercise that targets core and improves endurance",
    image: "⛰️",
    muscles: ["Core", "Shoulders", "Legs"],
    rating: 4.5,
    views: 11245,
    isFavorite: false,
    instructions: ["Start in plank position", "Bring one knee to chest", "Switch legs quickly", "Keep hips level"],
    tips: "Start slow and build up speed while maintaining form",
    calories: 10
  },
  {
    id: 6,
    name: "Lunges",
    category: "Legs",
    difficulty: "Beginner",
    duration: "45 seconds",
    equipment: "None",
    description: "Unilateral leg exercise for strength and balance",
    image: "🚶",
    muscles: ["Quadriceps", "Glutes", "Calves"],
    rating: 4.8,
    views: 14567,
    isFavorite: false,
    instructions: ["Step forward with one leg", "Lower hips until both knees at 90°", "Push back to start", "Alternate legs"],
    tips: "Keep front knee over ankle and back knee pointing down",
    calories: 9
  },
  {
    id: 7,
    name: "Jumping Jacks",
    category: "Cardio",
    difficulty: "Beginner",
    duration: "30 seconds",
    equipment: "None",
    description: "Classic cardio exercise to get your heart rate up",
    image: "🤸",
    muscles: ["Full Body", "Cardio"],
    rating: 4.4,
    views: 9876,
    isFavorite: false,
    instructions: ["Start with feet together", "Jump feet apart while raising arms", "Jump back to start", "Repeat rhythmically"],
    tips: "Land softly on balls of feet to reduce impact",
    calories: 8
  },
  {
    id: 8,
    name: "Deadlifts",
    category: "Back",
    difficulty: "Advanced",
    duration: "45 seconds",
    equipment: "Dumbbells",
    description: "Compound exercise targeting posterior chain muscles",
    image: "🏋️‍♂️",
    muscles: ["Back", "Glutes", "Hamstrings"],
    rating: 4.9,
    views: 13456,
    isFavorite: true,
    instructions: ["Stand with weights in front", "Hinge at hips", "Lower weights to mid-shin", "Drive hips forward to stand"],
    tips: "Keep back straight and weights close to body throughout movement",
    calories: 14
  },
  {
    id: 9,
    name: "Bicycle Crunches",
    category: "Core",
    difficulty: "Intermediate",
    duration: "45 seconds",
    equipment: "None",
    description: "Dynamic core exercise targeting obliques and abs",
    image: "🚴",
    muscles: ["Core", "Obliques"],
    rating: 4.6,
    views: 10234,
    isFavorite: false,
    instructions: ["Lie on back, hands behind head", "Bring opposite elbow to knee", "Alternate sides", "Keep core engaged"],
    tips: "Focus on rotation from core, not pulling on neck",
    calories: 7
  },
  {
    id: 10,
    name: "Pull-ups",
    category: "Back",
    difficulty: "Advanced",
    duration: "30 seconds",
    equipment: "Pull-up bar",
    description: "Upper body pulling exercise for back and arm strength",
    image: "🏃‍♂️",
    muscles: ["Back", "Biceps", "Shoulders"],
    rating: 4.8,
    views: 16789,
    isFavorite: true,
    instructions: ["Hang from bar with overhand grip", "Pull body up until chin over bar", "Lower with control", "Repeat"],
    tips: "Engage lats and avoid swinging - use assistance if needed",
    calories: 12
  }
]

const categories = [
  { name: "All", count: exercises.length },
  { name: "Chest", count: exercises.filter(e => e.category === "Chest").length },
  { name: "Legs", count: exercises.filter(e => e.category === "Legs").length },
  { name: "Core", count: exercises.filter(e => e.category === "Core").length },
  { name: "Cardio", count: exercises.filter(e => e.category === "Cardio").length },
  { name: "Back", count: exercises.filter(e => e.category === "Back").length },
  { name: "Full Body", count: exercises.filter(e => e.category === "Full Body").length }
]

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case "Beginner": return "bg-green-100 text-green-800"
    case "Intermediate": return "bg-yellow-100 text-yellow-800"
    case "Advanced": return "bg-red-100 text-red-800"
    default: return "bg-gray-100 text-gray-800"
  }
}

const getDifficultyIcon = (difficulty: string) => {
  switch (difficulty) {
    case "Beginner": return <Heart className="h-4 w-4" />
    case "Intermediate": return <Target className="h-4 w-4" />
    case "Advanced": return <Zap className="h-4 w-4" />
    default: return <Target className="h-4 w-4" />
  }
}

export default function Exercises() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedDifficulty, setSelectedDifficulty] = useState("All")
  const [selectedEquipment, setSelectedEquipment] = useState("All")
  const [sortBy, setSortBy] = useState("name")
  const [showExerciseModal, setShowExerciseModal] = useState(false)
  const [selectedExercise, setSelectedExercise] = useState(null)
  const [exerciseList, setExerciseList] = useState(exercises)

  // Get unique equipment types
  const equipmentTypes = ["All", ...Array.from(new Set(exercises.map(e => e.equipment)))]
  const difficulties = ["All", "Beginner", "Intermediate", "Advanced"]

  // Filter and sort exercises
  const filteredExercises = exerciseList
    .filter(exercise => {
      const matchesSearch = exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           exercise.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           exercise.muscles.some(muscle => muscle.toLowerCase().includes(searchTerm.toLowerCase()))
      const matchesCategory = selectedCategory === "All" || exercise.category === selectedCategory
      const matchesDifficulty = selectedDifficulty === "All" || exercise.difficulty === selectedDifficulty
      const matchesEquipment = selectedEquipment === "All" || exercise.equipment === selectedEquipment
      return matchesSearch && matchesCategory && matchesDifficulty && matchesEquipment
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name": return a.name.localeCompare(b.name)
        case "difficulty":
          const difficultyOrder = { "Beginner": 1, "Intermediate": 2, "Advanced": 3 }
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
        case "rating": return b.rating - a.rating
        case "popular": return b.views - a.views
        case "calories": return b.calories - a.calories
        default: return 0
      }
    })

  const toggleFavorite = (id: number) => {
    setExerciseList(exerciseList.map(exercise =>
      exercise.id === id ? { ...exercise, isFavorite: !exercise.isFavorite } : exercise
    ))
  }

  const openExerciseModal = (exercise) => {
    setSelectedExercise(exercise)
    setShowExerciseModal(true)
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      {/* Header Section */}
      <section className="bg-gradient-to-br from-green-50 to-emerald-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              Exercise Database
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover thousands of exercises with detailed instructions and video guides
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar and Sort */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search exercises..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="name">Sort by Name</option>
              <option value="difficulty">Sort by Difficulty</option>
              <option value="rating">Sort by Rating</option>
              <option value="popular">Sort by Popularity</option>
              <option value="calories">Sort by Calories</option>
            </select>
          </div>

          {/* Filter Buttons */}
          <div className="space-y-3">
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700 py-2">Difficulty:</span>
              {difficulties.map(difficulty => (
                <Button
                  key={difficulty}
                  variant={selectedDifficulty === difficulty ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedDifficulty(difficulty)}
                  className="text-xs"
                >
                  {difficulty}
                </Button>
              ))}
            </div>

            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700 py-2">Equipment:</span>
              {equipmentTypes.map(equipment => (
                <Button
                  key={equipment}
                  variant={selectedEquipment === equipment ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedEquipment(equipment)}
                  className="text-xs"
                >
                  {equipment}
                </Button>
              ))}
            </div>
          </div>

          {/* Results Count */}
          <div className="text-sm text-gray-600">
            Showing {filteredExercises.length} of {exerciseList.length} exercises
          </div>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Categories</h2>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.name}
                variant={selectedCategory === category.name ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.name)}
                className="text-sm"
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>

        {/* Exercise Grid */}
        {filteredExercises.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No exercises found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filters</p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("")
                setSelectedCategory("All")
                setSelectedDifficulty("All")
                setSelectedEquipment("All")
              }}
            >
              Clear all filters
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredExercises.map((exercise) => (
              <Card key={exercise.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="text-4xl mb-2">{exercise.image}</div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => toggleFavorite(exercise.id)}
                        className={`p-1 rounded-full transition-colors ${
                          exercise.isFavorite
                            ? 'text-red-500 hover:text-red-600'
                            : 'text-gray-400 hover:text-red-500'
                        }`}
                      >
                        <Heart className={`h-4 w-4 ${exercise.isFavorite ? 'fill-current' : ''}`} />
                      </button>
                      <div className="flex items-center gap-1">
                        {getDifficultyIcon(exercise.difficulty)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                          {exercise.difficulty}
                        </span>
                      </div>
                    </div>
                  </div>
                  <CardTitle className="text-lg">{exercise.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {exercise.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Rating and Views */}
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="font-medium">{exercise.rating}</span>
                      </div>
                      <div className="flex items-center gap-1 text-gray-500">
                        <Eye className="h-4 w-4" />
                        <span>{exercise.views.toLocaleString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{exercise.duration}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Zap className="h-4 w-4" />
                        <span>{exercise.calories} cal</span>
                      </div>
                    </div>

                    <Badge variant="secondary" className="w-fit">
                      {exercise.category}
                    </Badge>

                    <div>
                      <p className="text-xs text-gray-500 mb-1">Target Muscles:</p>
                      <div className="flex flex-wrap gap-1">
                        {exercise.muscles.map((muscle, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                          >
                            {muscle}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Equipment: {exercise.equipment}
                    </div>

                    <Button
                      className="w-full"
                      size="sm"
                      onClick={() => openExerciseModal(exercise)}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      View Exercise
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl mb-2">💪</div>
              <h3 className="font-semibold text-gray-900">500+ Exercises</h3>
              <p className="text-sm text-gray-600">Comprehensive exercise library</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl mb-2">🎯</div>
              <h3 className="font-semibold text-gray-900">All Skill Levels</h3>
              <p className="text-sm text-gray-600">From beginner to advanced</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl mb-2">📱</div>
              <h3 className="font-semibold text-gray-900">Video Guides</h3>
              <p className="text-sm text-gray-600">Step-by-step instructions</p>
            </CardContent>
          </Card>
        </div>

        {/* Exercise Detail Modal */}
        {showExerciseModal && selectedExercise && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                {/* Modal Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="text-5xl">{selectedExercise.image}</div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">{selectedExercise.name}</h2>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="font-medium">{selectedExercise.rating}</span>
                        </div>
                        <span className="text-gray-300">•</span>
                        <div className="flex items-center gap-1 text-gray-500">
                          <Eye className="h-4 w-4" />
                          <span>{selectedExercise.views.toLocaleString()} views</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowExerciseModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                {/* Exercise Info */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <Clock className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                    <div className="text-sm font-medium">{selectedExercise.duration}</div>
                    <div className="text-xs text-gray-500">Duration</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    {getDifficultyIcon(selectedExercise.difficulty)}
                    <div className="text-sm font-medium mt-1">{selectedExercise.difficulty}</div>
                    <div className="text-xs text-gray-500">Difficulty</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <Zap className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                    <div className="text-sm font-medium">{selectedExercise.calories} cal</div>
                    <div className="text-xs text-gray-500">Calories</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <Dumbbell className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                    <div className="text-sm font-medium">{selectedExercise.equipment}</div>
                    <div className="text-xs text-gray-500">Equipment</div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Description</h3>
                  <p className="text-gray-600">{selectedExercise.description}</p>
                </div>

                {/* Instructions */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Instructions</h3>
                  <ol className="space-y-2">
                    {selectedExercise.instructions.map((instruction, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <span className="flex-shrink-0 w-6 h-6 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </span>
                        <span className="text-gray-700">{instruction}</span>
                      </li>
                    ))}
                  </ol>
                </div>

                {/* Tips */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2">Tips</h3>
                  <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
                    <p className="text-blue-800">{selectedExercise.tips}</p>
                  </div>
                </div>

                {/* Target Muscles */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Target Muscles</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedExercise.muscles.map((muscle, index) => (
                      <Badge key={index} variant="outline">
                        {muscle}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button className="flex-1">
                    <Play className="h-4 w-4 mr-2" />
                    Start Exercise
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => toggleFavorite(selectedExercise.id)}
                    className={selectedExercise.isFavorite ? 'text-red-500 border-red-500' : ''}
                  >
                    <Heart className={`h-4 w-4 ${selectedExercise.isFavorite ? 'fill-current' : ''}`} />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
