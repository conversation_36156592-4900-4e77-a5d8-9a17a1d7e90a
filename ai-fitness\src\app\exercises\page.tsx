"use client"

import { Navigation } from "@/components/navigation"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Search,
  Filter,
  Clock,
  Target,
  Play,
  Heart,
  Zap
} from "lucide-react"

const exercises = [
  {
    id: 1,
    name: "Push-ups",
    category: "Chest",
    difficulty: "Beginner",
    duration: "30 seconds",
    equipment: "None",
    description: "Classic bodyweight exercise targeting chest, shoulders, and triceps",
    image: "💪",
    muscles: ["Chest", "Shoulders", "Triceps"]
  },
  {
    id: 2,
    name: "Squats",
    category: "Legs",
    difficulty: "Beginner",
    duration: "45 seconds",
    equipment: "None",
    description: "Fundamental lower body exercise for strength and mobility",
    image: "🦵",
    muscles: ["Quadriceps", "Glutes", "Hamstrings"]
  },
  {
    id: 3,
    name: "Burpees",
    category: "Full Body",
    difficulty: "Advanced",
    duration: "30 seconds",
    equipment: "None",
    description: "High-intensity full-body exercise combining strength and cardio",
    image: "🔥",
    muscles: ["Full Body", "Cardio"]
  },
  {
    id: 4,
    name: "Plank",
    category: "Core",
    difficulty: "Intermediate",
    duration: "60 seconds",
    equipment: "None",
    description: "Isometric core exercise for stability and strength",
    image: "🏋️",
    muscles: ["Core", "Shoulders", "Back"]
  },
  {
    id: 5,
    name: "Mountain Climbers",
    category: "Cardio",
    difficulty: "Intermediate",
    duration: "30 seconds",
    equipment: "None",
    description: "Dynamic cardio exercise that targets core and improves endurance",
    image: "⛰️",
    muscles: ["Core", "Shoulders", "Legs"]
  },
  {
    id: 6,
    name: "Lunges",
    category: "Legs",
    difficulty: "Beginner",
    duration: "45 seconds",
    equipment: "None",
    description: "Unilateral leg exercise for strength and balance",
    image: "🚶",
    muscles: ["Quadriceps", "Glutes", "Calves"]
  },
  {
    id: 7,
    name: "Jumping Jacks",
    category: "Cardio",
    difficulty: "Beginner",
    duration: "30 seconds",
    equipment: "None",
    description: "Classic cardio exercise to get your heart rate up",
    image: "🤸",
    muscles: ["Full Body", "Cardio"]
  },
  {
    id: 8,
    name: "Deadlifts",
    category: "Back",
    difficulty: "Advanced",
    duration: "45 seconds",
    equipment: "Dumbbells",
    description: "Compound exercise targeting posterior chain muscles",
    image: "🏋️‍♂️",
    muscles: ["Back", "Glutes", "Hamstrings"]
  }
]

const categories = [
  { name: "All", count: exercises.length },
  { name: "Chest", count: exercises.filter(e => e.category === "Chest").length },
  { name: "Legs", count: exercises.filter(e => e.category === "Legs").length },
  { name: "Core", count: exercises.filter(e => e.category === "Core").length },
  { name: "Cardio", count: exercises.filter(e => e.category === "Cardio").length },
  { name: "Back", count: exercises.filter(e => e.category === "Back").length },
  { name: "Full Body", count: exercises.filter(e => e.category === "Full Body").length }
]

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case "Beginner": return "bg-green-100 text-green-800"
    case "Intermediate": return "bg-yellow-100 text-yellow-800"
    case "Advanced": return "bg-red-100 text-red-800"
    default: return "bg-gray-100 text-gray-800"
  }
}

const getDifficultyIcon = (difficulty: string) => {
  switch (difficulty) {
    case "Beginner": return <Heart className="h-4 w-4" />
    case "Intermediate": return <Target className="h-4 w-4" />
    case "Advanced": return <Zap className="h-4 w-4" />
    default: return <Target className="h-4 w-4" />
  }
}

export default function Exercises() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      {/* Header Section */}
      <section className="bg-gradient-to-br from-green-50 to-emerald-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              Exercise Database
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover thousands of exercises with detailed instructions and video guides
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search exercises..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Categories</h2>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.name}
                variant={category.name === "All" ? "default" : "outline"}
                size="sm"
                className="text-sm"
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>

        {/* Exercise Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {exercises.map((exercise) => (
            <Card key={exercise.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="text-4xl mb-2">{exercise.image}</div>
                  <div className="flex items-center gap-1">
                    {getDifficultyIcon(exercise.difficulty)}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>
                      {exercise.difficulty}
                    </span>
                  </div>
                </div>
                <CardTitle className="text-lg">{exercise.name}</CardTitle>
                <CardDescription className="text-sm">
                  {exercise.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{exercise.duration}</span>
                    </div>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                      {exercise.category}
                    </span>
                  </div>
                  
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Target Muscles:</p>
                    <div className="flex flex-wrap gap-1">
                      {exercise.muscles.map((muscle, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                        >
                          {muscle}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    Equipment: {exercise.equipment}
                  </div>
                  
                  <Button className="w-full" size="sm">
                    <Play className="h-4 w-4 mr-2" />
                    View Exercise
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl mb-2">💪</div>
              <h3 className="font-semibold text-gray-900">500+ Exercises</h3>
              <p className="text-sm text-gray-600">Comprehensive exercise library</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl mb-2">🎯</div>
              <h3 className="font-semibold text-gray-900">All Skill Levels</h3>
              <p className="text-sm text-gray-600">From beginner to advanced</p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl mb-2">📱</div>
              <h3 className="font-semibold text-gray-900">Video Guides</h3>
              <p className="text-sm text-gray-600">Step-by-step instructions</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
