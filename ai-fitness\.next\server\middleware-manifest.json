{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__e12e3850._.js", "server/edge/chunks/edge-wrapper_dd1a49dc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|api|favicon.ico|robots.txt|sitemap.xml).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|api|favicon.ico|robots.txt|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ijI/fmYj8/CLwWRdf5jC02p22F9VQvsbUiJHG6GyJ58=", "__NEXT_PREVIEW_MODE_ID": "77019f57d68d5c79bbef5502ce025d4b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9efc803fcf388f7bd92b7a7b4be5f7c473436a8a462c778c730006d375c04588", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3d958b95b5c2c8b82b372260081e3cb319608abafb1fc32987ec1253ddf46a2f"}}}, "sortedMiddleware": ["/"], "functions": {}}