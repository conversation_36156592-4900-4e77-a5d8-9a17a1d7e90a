{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-workouts.ts"], "sourcesContent": ["/**\n * React Query hooks for workout data\n */\n\nimport { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';\nimport { WorkoutService } from '../api/services/workouts';\nimport type {\n  CreateWorkoutSessionData\n} from '../api/types';\n\n// Query keys for consistent caching\nexport const workoutKeys = {\n  all: ['workouts'] as const,\n  sessions: () => [...workoutKeys.all, 'sessions'] as const,\n  session: (id: string) => [...workoutKeys.sessions(), id] as const,\n  sessionsList: (params: any) => [...workoutKeys.sessions(), 'list', params] as const,\n  programs: () => [...workoutKeys.all, 'programs'] as const,\n  program: (id: string) => [...workoutKeys.programs(), id] as const,\n  programsList: (params: any) => [...workoutKeys.programs(), 'list', params] as const,\n  userPrograms: () => [...workoutKeys.programs(), 'user'] as const,\n  popular: (limit: number) => [...workoutKeys.programs(), 'popular', limit] as const,\n  recommended: (limit: number) => [...workoutKeys.programs(), 'recommended', limit] as const,\n  stats: (period: string) => [...workoutKeys.all, 'stats', period] as const,\n  history: (params: any) => [...workoutKeys.all, 'history', params] as const,\n};\n\n/**\n * Hook to get workout sessions\n */\nexport function useWorkoutSessions(params: Parameters<typeof WorkoutService.getWorkoutSessions>[0] = {}) {\n  return useQuery({\n    queryKey: workoutKeys.sessionsList(params),\n    queryFn: () => WorkoutService.getWorkoutSessions(params),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Hook to get a specific workout session\n */\nexport function useWorkoutSession(id: string, enabled = true) {\n  return useQuery({\n    queryKey: workoutKeys.session(id),\n    queryFn: () => WorkoutService.getWorkoutSession(id),\n    enabled: enabled && !!id,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to create a workout session\n */\nexport function useCreateWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateWorkoutSessionData) => WorkoutService.createWorkoutSession(data),\n    onSuccess: () => {\n      // Invalidate sessions list to refetch\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to update a workout session\n */\nexport function useUpdateWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: Partial<CreateWorkoutSessionData> }) =>\n      WorkoutService.updateWorkoutSession(id, data),\n    onSuccess: (updatedSession) => {\n      // Update the specific session cache\n      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);\n      \n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to delete a workout session\n */\nexport function useDeleteWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.deleteWorkoutSession(id),\n    onSuccess: (_, deletedId) => {\n      // Remove from cache\n      queryClient.removeQueries({ queryKey: workoutKeys.session(deletedId) });\n      \n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to start a workout session\n */\nexport function useStartWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.startWorkoutSession(id),\n    onSuccess: (updatedSession) => {\n      // Update the specific session cache\n      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);\n      \n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to complete a workout session\n */\nexport function useCompleteWorkoutSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: { duration: number; notes?: string } }) =>\n      WorkoutService.completeWorkoutSession(id, data),\n    onSuccess: (updatedSession) => {\n      // Update the specific session cache\n      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);\n      \n      // Invalidate sessions list and stats\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n      queryClient.invalidateQueries({ queryKey: workoutKeys.stats('month') });\n    },\n  });\n}\n\n/**\n * Hook to get workout programs\n */\nexport function useWorkoutPrograms(params: Parameters<typeof WorkoutService.getWorkoutPrograms>[0] = {}) {\n  return useQuery({\n    queryKey: workoutKeys.programsList(params),\n    queryFn: () => WorkoutService.getWorkoutPrograms(params),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get a specific workout program\n */\nexport function useWorkoutProgram(id: string, enabled = true) {\n  return useQuery({\n    queryKey: workoutKeys.program(id),\n    queryFn: () => WorkoutService.getWorkoutProgram(id),\n    enabled: enabled && !!id,\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get user's joined programs\n */\nexport function useUserPrograms() {\n  return useQuery({\n    queryKey: workoutKeys.userPrograms(),\n    queryFn: () => WorkoutService.getUserPrograms(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to get popular programs\n */\nexport function usePopularPrograms(limit = 10) {\n  return useQuery({\n    queryKey: workoutKeys.popular(limit),\n    queryFn: () => WorkoutService.getPopularPrograms(limit),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  });\n}\n\n/**\n * Hook to get recommended programs\n */\nexport function useRecommendedPrograms(limit = 6) {\n  return useQuery({\n    queryKey: workoutKeys.recommended(limit),\n    queryFn: () => WorkoutService.getRecommendedPrograms(limit),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to create a workout program\n */\nexport function useCreateWorkoutProgram() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: any) => WorkoutService.createWorkoutProgram(data),\n    onSuccess: () => {\n      // Invalidate programs list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.programs() });\n    },\n  });\n}\n\n/**\n * Hook to join a workout program\n */\nexport function useJoinWorkoutProgram() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.joinWorkoutProgram(id),\n    onSuccess: () => {\n      // Invalidate user programs\n      queryClient.invalidateQueries({ queryKey: workoutKeys.userPrograms() });\n    },\n  });\n}\n\n/**\n * Hook to leave a workout program\n */\nexport function useLeaveWorkoutProgram() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => WorkoutService.leaveWorkoutProgram(id),\n    onSuccess: () => {\n      // Invalidate user programs\n      queryClient.invalidateQueries({ queryKey: workoutKeys.userPrograms() });\n    },\n  });\n}\n\n/**\n * Hook to generate AI workout\n */\nexport function useGenerateAIWorkout() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (preferences: Parameters<typeof WorkoutService.generateAIWorkout>[0]) =>\n      WorkoutService.generateAIWorkout(preferences),\n    onSuccess: () => {\n      // Invalidate sessions list\n      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });\n    },\n  });\n}\n\n/**\n * Hook to get workout statistics\n */\nexport function useWorkoutStats(period: 'week' | 'month' | 'year' = 'month') {\n  return useQuery({\n    queryKey: workoutKeys.stats(period),\n    queryFn: () => WorkoutService.getWorkoutStats(period),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to get workout history with infinite scrolling\n */\nexport function useWorkoutHistory(params: Omit<Parameters<typeof WorkoutService.getWorkoutHistory>[0], 'offset'> = {}) {\n  return useInfiniteQuery({\n    queryKey: workoutKeys.history(params),\n    queryFn: ({ pageParam = 0 }) => \n      WorkoutService.getWorkoutHistory({ ...params, offset: pageParam }),\n    initialPageParam: 0,\n    getNextPageParam: (lastPage) => {\n      const { pagination } = lastPage;\n      return pagination.hasNext ? pagination.page * pagination.limit : undefined;\n    },\n    staleTime: 5 * 60 * 1000,\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;AAED;AAAA;AAAA;AAAA;AACA;;;;AAMO,MAAM,cAAc;IACzB,KAAK;QAAC;KAAW;IACjB,UAAU,IAAM;eAAI,YAAY,GAAG;YAAE;SAAW;IAChD,SAAS,CAAC,KAAe;eAAI,YAAY,QAAQ;YAAI;SAAG;IACxD,cAAc,CAAC,SAAgB;eAAI,YAAY,QAAQ;YAAI;YAAQ;SAAO;IAC1E,UAAU,IAAM;eAAI,YAAY,GAAG;YAAE;SAAW;IAChD,SAAS,CAAC,KAAe;eAAI,YAAY,QAAQ;YAAI;SAAG;IACxD,cAAc,CAAC,SAAgB;eAAI,YAAY,QAAQ;YAAI;YAAQ;SAAO;IAC1E,cAAc,IAAM;eAAI,YAAY,QAAQ;YAAI;SAAO;IACvD,SAAS,CAAC,QAAkB;eAAI,YAAY,QAAQ;YAAI;YAAW;SAAM;IACzE,aAAa,CAAC,QAAkB;eAAI,YAAY,QAAQ;YAAI;YAAe;SAAM;IACjF,OAAO,CAAC,SAAmB;eAAI,YAAY,GAAG;YAAE;YAAS;SAAO;IAChE,SAAS,CAAC,SAAgB;eAAI,YAAY,GAAG;YAAE;YAAW;SAAO;AACnE;AAKO,SAAS,mBAAmB,SAAkE,CAAC,CAAC;;IACrG,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,YAAY,CAAC;QACnC,OAAO;2CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QACjD,WAAW,IAAI,KAAK;IACtB;AACF;GANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,EAAU,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;0CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;;QAChD,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,OAAmC,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;;QACpF,SAAS;mDAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2D,GAChF,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,IAAI;;QAC1C,SAAS;mDAAE,CAAC;gBACV,oCAAoC;gBACpC,YAAY,YAAY,CAAC,YAAY,OAAO,CAAC,eAAe,EAAE,GAAG;gBAEjE,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;;QAChE,SAAS;mDAAE,CAAC,GAAG;gBACb,oBAAoB;gBACpB,YAAY,aAAa,CAAC;oBAAE,UAAU,YAAY,OAAO,CAAC;gBAAW;gBAErE,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAbgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAeb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC;;QAC/D,SAAS;kDAAE,CAAC;gBACV,oCAAoC;gBACpC,YAAY,YAAY,CAAC,YAAY,OAAO,CAAC,eAAe,EAAE,GAAG;gBAEjE,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;IAbgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAeb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA8D,GACnF,4IAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC,IAAI;;QAC5C,SAAS;qDAAE,CAAC;gBACV,oCAAoC;gBACpC,YAAY,YAAY,CAAC,YAAY,OAAO,CAAC,eAAe,EAAE,GAAG;gBAEjE,qCAAqC;gBACrC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK,CAAC;gBAAS;YACvE;;IACF;AACF;IAfgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,mBAAmB,SAAkE,CAAC,CAAC;;IACrG,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,YAAY,CAAC;QACnC,OAAO;2CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QACjD,WAAW,KAAK,KAAK;IACvB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,EAAU,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;0CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;;QAChD,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,YAAY;QAClC,OAAO;wCAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,eAAe;;QAC7C,WAAW,IAAI,KAAK;IACtB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,mBAAmB,QAAQ,EAAE;;IAC3C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;2CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QACjD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,uBAAuB,QAAQ,CAAC;;IAC9C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,WAAW,CAAC;QAClC,OAAO;+CAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC;;QACrD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,OAAc,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;;QAC/D,SAAS;mDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;KAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC;;QAC9D,SAAS;iDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,YAAY;gBAAG;YACvE;;IACF;AACF;KAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,KAAe,4IAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC;;QAC/D,SAAS;kDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,YAAY;gBAAG;YACvE;;IACF;AACF;KAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,CAAC,cACX,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;;QACnC,SAAS;gDAAE;gBACT,2BAA2B;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,QAAQ;gBAAG;YACnE;;IACF;AACF;KAXgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,SAAS,gBAAgB,SAAoC,OAAO;;IACzE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,YAAY,KAAK,CAAC;QAC5B,OAAO;wCAAE,IAAM,4IAAA,CAAA,iBAAc,CAAC,eAAe,CAAC;;QAC9C,WAAW,IAAI,KAAK;IACtB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,SAAiF,CAAC,CAAC;;IACnH,OAAO,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;kDAAE,CAAC,EAAE,YAAY,CAAC,EAAE,GACzB,4IAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU;;QAClE,kBAAkB;QAClB,gBAAgB;kDAAE,CAAC;gBACjB,MAAM,EAAE,UAAU,EAAE,GAAG;gBACvB,OAAO,WAAW,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,KAAK,GAAG;YACnE;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;KAZgB;;QACP,sLAAA,CAAA,mBAAgB", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-workouts.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Grid,\n  Button,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  Badge,\n  Tabs,\n  Tab,\n  Paper,\n  Collapse,\n  FormGroup,\n  FormControlLabel,\n  Checkbox,\n  alpha,\n  useTheme,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PlayArrow as PlayIcon,\n  Add as AddIcon,\n  Favorite as FavoriteIcon,\n  FavoriteBorder as FavoriteBorderIcon,\n  AccessTime as ClockIcon,\n  Group as UsersIcon,\n  TrendingUp as TargetIcon,\n  MenuBook as BookIcon,\n  CalendarToday as CalendarIcon,\n  Clear as ClearIcon,\n  FitnessCenter as FitnessCenterIcon,\n  Timer as TimerIcon,\n  EmojiEvents as TrophyIcon,\n} from '@mui/icons-material';\nimport { \n  useWorkoutPrograms,\n  useWorkoutSessions,\n  usePopularPrograms,\n  useRecommendedPrograms,\n  useUserPrograms,\n  useJoinWorkoutProgram,\n  useCreateWorkoutSession\n} from \"@/lib/hooks/use-workouts\";\nimport { useAuth } from \"@/lib/hooks/use-auth\";\nimport Link from \"next/link\";\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`workout-tabpanel-${index}`}\n      aria-labelledby={`workout-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nexport function MuiWorkouts() {\n  const theme = useTheme();\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedFilters, setSelectedFilters] = useState({\n    category: [] as string[],\n    difficulty: [] as string[],\n    duration: [] as string[]\n  });\n\n  const { isAuthenticated } = useAuth();\n\n  // API hooks\n  const { data: programsData, isLoading: isLoadingPrograms, error: programsError } = useWorkoutPrograms({\n    limit: 20,\n    category: selectedFilters.category.length > 0 ? selectedFilters.category[0] : undefined,\n    difficulty: selectedFilters.difficulty.length > 0 ? selectedFilters.difficulty[0] : undefined,\n    duration: selectedFilters.duration.length > 0 ? selectedFilters.duration[0] : undefined\n  });\n\n  const { data: sessionsData, isLoading: isLoadingSessions } = useWorkoutSessions({\n    limit: 20\n  });\n\n  const { data: popularPrograms, isLoading: isLoadingPopular } = usePopularPrograms(6);\n  const { data: recommendedPrograms, isLoading: isLoadingRecommended } = useRecommendedPrograms(6);\n  const { data: userPrograms, isLoading: isLoadingUserPrograms } = useUserPrograms();\n\n  const joinProgramMutation = useJoinWorkoutProgram();\n  const createSessionMutation = useCreateWorkoutSession();\n\n  const handleFilterChange = (type: keyof typeof selectedFilters, value: string) => {\n    setSelectedFilters(prev => {\n      const currentValues = prev[type];\n      const newValues = currentValues.includes(value)\n        ? currentValues.filter(v => v !== value)\n        : [...currentValues, value];\n      \n      return {\n        ...prev,\n        [type]: newValues\n      };\n    });\n  };\n\n  const clearFilters = () => {\n    setSelectedFilters({\n      category: [],\n      difficulty: [],\n      duration: []\n    });\n  };\n\n  const hasActiveFilters = Object.values(selectedFilters).some(arr => arr.length > 0);\n\n  const handleJoinProgram = async (programId: string) => {\n    try {\n      await joinProgramMutation.mutateAsync(programId);\n    } catch (error) {\n      console.error('Failed to join program:', error);\n    }\n  };\n\n  const handleStartWorkout = async (programId: string) => {\n    try {\n      await createSessionMutation.mutateAsync({\n        exercises: [],\n        notes: `Quick workout session - ${new Date().toLocaleDateString()}`\n      });\n    } catch (error) {\n      console.error('Failed to start workout:', error);\n    }\n  };\n\n  const filteredPrograms = programsData?.data?.filter(program =>\n    searchQuery === \"\" ||\n    program.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    program.description?.toLowerCase().includes(searchQuery.toLowerCase())\n  ) || [];\n\n  // Quick stats data\n  const quickStats = [\n    { \n      label: '活跃计划', \n      value: userPrograms?.length || 0, \n      icon: <BookIcon />, \n      color: theme.palette.primary.main \n    },\n    { \n      label: '本周训练', \n      value: sessionsData?.data?.filter(s => s.status === 'COMPLETED').length || 0, \n      icon: <CalendarIcon />, \n      color: theme.palette.secondary.main \n    },\n    { \n      label: '总时长', \n      value: `${sessionsData?.data?.reduce((acc, s) => acc + (s.duration || 0), 0) || 0}分钟`, \n      icon: <TimerIcon />, \n      color: '#9C27B0' \n    },\n  ];\n\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,\n          py: { xs: 6, md: 8 },\n          position: 'relative',\n          overflow: 'hidden',\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>\n            <Typography\n              variant=\"h2\"\n              sx={{\n                fontWeight: 700,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                mb: 2,\n                fontSize: { xs: '2.5rem', md: '3.5rem' }\n              }}\n            >\n              训练计划\n            </Typography>\n            <Typography\n              variant=\"h5\"\n              sx={{\n                color: 'text.secondary',\n                mb: 4,\n                fontWeight: 400,\n                fontSize: { xs: '1.25rem', md: '1.5rem' }\n              }}\n            >\n              发现个性化训练计划，助您达成健身目标\n            </Typography>\n          </Box>\n        </Container>\n\n        {/* Floating Elements */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '20%',\n            right: '10%',\n            width: 80,\n            height: 80,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.secondary.main, 0.3)})`,\n            animation: 'float 6s ease-in-out infinite',\n          }}\n        />\n        <Box\n          sx={{\n            position: 'absolute',\n            bottom: '30%',\n            left: '5%',\n            width: 60,\n            height: 60,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,\n            animation: 'float 4s ease-in-out infinite reverse',\n          }}\n        />\n      </Box>\n\n      {/* Quick Stats */}\n      {isAuthenticated && (\n        <Container maxWidth=\"md\" sx={{ mt: -2, position: 'relative', zIndex: 2, py: 4 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n            <Grid container spacing={2} sx={{ maxWidth: 600 }}>\n              {quickStats.map((stat, index) => (\n                <Grid size={4} key={index}>\n                  <Card\n                    sx={{\n                      textAlign: 'center',\n                      p: 2.5,\n                      minHeight: 160,\n                      background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',\n                      backdropFilter: 'blur(15px)',\n                      border: '1px solid rgba(255,255,255,0.3)',\n                      borderRadius: 3,\n                      transition: 'all 0.3s ease',\n                      '&:hover': {\n                        transform: 'translateY(-4px)',\n                        boxShadow: `0 8px 20px ${alpha(stat.color, 0.15)}`,\n                        border: `1px solid ${alpha(stat.color, 0.2)}`,\n                      }\n                    }}\n                  >\n                    <CardContent sx={{ p: '8px !important' }}>\n                      <Box\n                        sx={{\n                          display: 'inline-flex',\n                          p: 1.5,\n                          borderRadius: '50%',\n                          bgcolor: alpha(stat.color, 0.12),\n                          color: stat.color,\n                          mb: 2\n                        }}\n                      >\n                        {stat.icon}\n                      </Box>\n                      <Typography variant=\"h4\" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>\n                        {stat.value}\n                      </Typography>\n                      <Typography variant=\"body2\" sx={{ color: 'text.secondary', fontWeight: 500 }}>\n                        {stat.label}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </Box>\n        </Container>\n      )}\n\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        {/* Tabs */}\n        <Paper sx={{ mb: 4, borderRadius: 3, overflow: 'hidden' }}>\n          <Tabs\n            value={activeTab}\n            onChange={(_, newValue) => setActiveTab(newValue)}\n            variant=\"fullWidth\"\n            sx={{\n              '& .MuiTab-root': {\n                fontWeight: 600,\n                fontSize: '1rem',\n                textTransform: 'none',\n                py: 2,\n              },\n              '& .Mui-selected': {\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                color: 'white !important',\n              }\n            }}\n          >\n            <Tab label=\"所有计划\" />\n            {isAuthenticated && <Tab label=\"我的计划\" />}\n            {isAuthenticated && <Tab label=\"训练记录\" />}\n          </Tabs>\n        </Paper>\n\n        {/* Search and Filters */}\n        <TabPanel value={activeTab} index={0}>\n          <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>\n            <Box sx={{ display: 'flex', gap: 2, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>\n              <TextField\n                fullWidth\n                placeholder=\"搜索训练计划...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon color=\"action\" />\n                    </InputAdornment>\n                  ),\n                }}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  }\n                }}\n              />\n              <Button\n                variant=\"outlined\"\n                onClick={() => setShowFilters(!showFilters)}\n                startIcon={<FilterIcon />}\n                endIcon={hasActiveFilters && (\n                  <Badge badgeContent={Object.values(selectedFilters).reduce((acc, arr) => acc + arr.length, 0)} color=\"primary\">\n                    <Box />\n                  </Badge>\n                )}\n                sx={{\n                  borderRadius: 3,\n                  minWidth: 120,\n                  fontWeight: 600,\n                  textTransform: 'none',\n                }}\n              >\n                筛选\n              </Button>\n            </Box>\n\n            <Collapse in={showFilters}>\n              <Box sx={{ pt: 3, borderTop: 1, borderColor: 'divider' }}>\n                <Grid container spacing={3}>\n                  <Grid size={{ xs: 12, md: 4 }}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                      类别\n                    </Typography>\n                    <FormGroup>\n                      {['力量训练', '有氧运动', '柔韧性', 'HIIT', '瑜伽'].map((category) => (\n                        <FormControlLabel\n                          key={category}\n                          control={\n                            <Checkbox\n                              checked={selectedFilters.category.includes(category)}\n                              onChange={() => handleFilterChange('category', category)}\n                              sx={{ color: theme.palette.primary.main }}\n                            />\n                          }\n                          label={category}\n                        />\n                      ))}\n                    </FormGroup>\n                  </Grid>\n                  <Grid size={{ xs: 12, md: 4 }}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                      难度\n                    </Typography>\n                    <FormGroup>\n                      {['初级', '中级', '高级'].map((difficulty) => (\n                        <FormControlLabel\n                          key={difficulty}\n                          control={\n                            <Checkbox\n                              checked={selectedFilters.difficulty.includes(difficulty)}\n                              onChange={() => handleFilterChange('difficulty', difficulty)}\n                              sx={{ color: theme.palette.secondary.main }}\n                            />\n                          }\n                          label={difficulty}\n                        />\n                      ))}\n                    </FormGroup>\n                  </Grid>\n                  <Grid size={{ xs: 12, md: 4 }}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                      时长\n                    </Typography>\n                    <FormGroup>\n                      {['15-30分钟', '30-45分钟', '45-60分钟', '60分钟以上'].map((duration) => (\n                        <FormControlLabel\n                          key={duration}\n                          control={\n                            <Checkbox\n                              checked={selectedFilters.duration.includes(duration)}\n                              onChange={() => handleFilterChange('duration', duration)}\n                              sx={{ color: '#9C27B0' }}\n                            />\n                          }\n                          label={duration}\n                        />\n                      ))}\n                    </FormGroup>\n                  </Grid>\n                </Grid>\n                {hasActiveFilters && (\n                  <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>\n                    <Button\n                      variant=\"outlined\"\n                      onClick={clearFilters}\n                      startIcon={<ClearIcon />}\n                      sx={{ borderRadius: 3, textTransform: 'none' }}\n                    >\n                      清除筛选\n                    </Button>\n                  </Box>\n                )}\n              </Box>\n            </Collapse>\n          </Paper>\n\n          {/* Popular Programs */}\n          {!searchQuery && !hasActiveFilters && (\n            <Box sx={{ mb: 6 }}>\n              <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>\n                热门计划\n              </Typography>\n              <Grid container spacing={3}>\n                {isLoadingPopular ? (\n                  Array.from({ length: 6 }).map((_, index) => (\n                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>\n                      <Card sx={{ height: 280, borderRadius: 3 }}>\n                        <CardContent sx={{ p: 3 }}>\n                          <Box sx={{ bgcolor: 'grey.200', height: 200, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                  ))\n                ) : (\n                  popularPrograms?.slice(0, 6).map((program) => (\n                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={program.id}>\n                      <Card\n                        sx={{\n                          height: '100%',\n                          borderRadius: 3,\n                          transition: 'all 0.3s ease',\n                          '&:hover': {\n                            transform: 'translateY(-4px)',\n                            boxShadow: theme.shadows[8],\n                          }\n                        }}\n                      >\n                        <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>\n                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                            <Box sx={{ flex: 1 }}>\n                              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                                {program.title}\n                              </Typography>\n                              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                                {program.description}\n                              </Typography>\n                            </Box>\n                            <IconButton size=\"small\">\n                              <FavoriteBorderIcon />\n                            </IconButton>\n                          </Box>\n\n                          <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>\n                            <Chip\n                              icon={<ClockIcon />}\n                              label={`${program.duration || 'N/A'} 分钟`}\n                              size=\"small\"\n                              variant=\"outlined\"\n                            />\n                            <Chip\n                              icon={<TargetIcon />}\n                              label={program.difficulty || 'N/A'}\n                              size=\"small\"\n                              variant=\"outlined\"\n                            />\n                            <Chip\n                              icon={<UsersIcon />}\n                              label={program.participantCount || 0}\n                              size=\"small\"\n                              variant=\"outlined\"\n                            />\n                          </Box>\n\n                          <Box sx={{ mt: 'auto', display: 'flex', gap: 1 }}>\n                            <Button\n                              variant=\"contained\"\n                              startIcon={<PlayIcon />}\n                              onClick={() => handleStartWorkout(program.id)}\n                              disabled={createSessionMutation.isPending}\n                              sx={{\n                                flex: 1,\n                                borderRadius: 2,\n                                textTransform: 'none',\n                                fontWeight: 600,\n                                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                              }}\n                            >\n                              开始训练\n                            </Button>\n                            <Button\n                              variant=\"outlined\"\n                              onClick={() => handleJoinProgram(program.id)}\n                              disabled={joinProgramMutation.isPending}\n                              sx={{ borderRadius: 2 }}\n                            >\n                              <AddIcon />\n                            </Button>\n                          </Box>\n                        </CardContent>\n                      </Card>\n                    </Grid>\n                  ))\n                )}\n              </Grid>\n            </Box>\n          )}\n\n          {/* All Programs */}\n          <Box>\n            <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>\n              {searchQuery ? `搜索结果: \"${searchQuery}\"` : '所有计划'}\n            </Typography>\n            <Grid container spacing={3}>\n              {isLoadingPrograms ? (\n                Array.from({ length: 9 }).map((_, index) => (\n                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>\n                    <Card sx={{ height: 280, borderRadius: 3 }}>\n                      <CardContent sx={{ p: 3 }}>\n                        <Box sx={{ bgcolor: 'grey.200', height: 200, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))\n              ) : filteredPrograms.length === 0 ? (\n                <Grid size={12}>\n                  <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>\n                    <FitnessCenterIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n                    <Typography variant=\"h6\" sx={{ mb: 1 }}>\n                      {searchQuery ? '未找到相关计划' : '暂无训练计划'}\n                    </Typography>\n                    <Typography color=\"text.secondary\">\n                      {searchQuery ? '尝试调整搜索条件' : '敬请期待更多精彩内容'}\n                    </Typography>\n                  </Paper>\n                </Grid>\n              ) : (\n                filteredPrograms.map((program) => (\n                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={program.id}>\n                    <Card\n                      sx={{\n                        height: '100%',\n                        borderRadius: 3,\n                        transition: 'all 0.3s ease',\n                        '&:hover': {\n                          transform: 'translateY(-4px)',\n                          boxShadow: theme.shadows[8],\n                        }\n                      }}\n                    >\n                      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                          <Box sx={{ flex: 1 }}>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                              {program.title}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                              {program.description}\n                            </Typography>\n                          </Box>\n                          <IconButton size=\"small\">\n                            <FavoriteBorderIcon />\n                          </IconButton>\n                        </Box>\n\n                        <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>\n                          <Chip\n                            icon={<ClockIcon />}\n                            label={`${program.duration || 'N/A'} 分钟`}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            icon={<TargetIcon />}\n                            label={program.difficulty || 'N/A'}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            icon={<UsersIcon />}\n                            label={program.participantCount || 0}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                        </Box>\n\n                        <Box sx={{ mt: 'auto', display: 'flex', gap: 1 }}>\n                          <Link href={`/workouts/${program.id}`} style={{ flex: 1 }}>\n                            <Button\n                              variant=\"contained\"\n                              startIcon={<PlayIcon />}\n                              fullWidth\n                              sx={{\n                                borderRadius: 2,\n                                textTransform: 'none',\n                                fontWeight: 600,\n                                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                              }}\n                            >\n                              查看详情\n                            </Button>\n                          </Link>\n                          <Button\n                            variant=\"outlined\"\n                            onClick={() => handleJoinProgram(program.id)}\n                            disabled={joinProgramMutation.isPending}\n                            sx={{ borderRadius: 2 }}\n                          >\n                            <AddIcon />\n                          </Button>\n                        </Box>\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))\n              )}\n            </Grid>\n          </Box>\n        </TabPanel>\n\n        {/* My Programs Tab */}\n        {isAuthenticated && (\n          <TabPanel value={activeTab} index={1}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>\n              我的计划\n            </Typography>\n            <Grid container spacing={3}>\n              {isLoadingUserPrograms ? (\n                Array.from({ length: 6 }).map((_, index) => (\n                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>\n                    <Card sx={{ height: 280, borderRadius: 3 }}>\n                      <CardContent sx={{ p: 3 }}>\n                        <Box sx={{ bgcolor: 'grey.200', height: 200, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))\n              ) : !userPrograms || userPrograms.length === 0 ? (\n                <Grid size={12}>\n                  <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>\n                    <BookIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n                    <Typography variant=\"h6\" sx={{ mb: 1 }}>\n                      暂无加入的计划\n                    </Typography>\n                    <Typography color=\"text.secondary\" sx={{ mb: 3 }}>\n                      浏览并加入您感兴趣的训练计划\n                    </Typography>\n                    <Button\n                      variant=\"contained\"\n                      onClick={() => setActiveTab(0)}\n                      sx={{\n                        borderRadius: 3,\n                        textTransform: 'none',\n                        fontWeight: 600,\n                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                      }}\n                    >\n                      浏览计划\n                    </Button>\n                  </Paper>\n                </Grid>\n              ) : (\n                userPrograms.map((program) => (\n                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={program.id}>\n                    <Card\n                      sx={{\n                        height: '100%',\n                        borderRadius: 3,\n                        transition: 'all 0.3s ease',\n                        '&:hover': {\n                          transform: 'translateY(-4px)',\n                          boxShadow: theme.shadows[8],\n                        }\n                      }}\n                    >\n                      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                          <Box sx={{ flex: 1 }}>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                              {program.title}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                              {program.description}\n                            </Typography>\n                          </Box>\n                          <Chip label=\"已加入\" color=\"primary\" size=\"small\" />\n                        </Box>\n\n                        <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>\n                          <Chip\n                            icon={<ClockIcon />}\n                            label={`${program.duration || 'N/A'} 分钟`}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            icon={<TargetIcon />}\n                            label={program.difficulty || 'N/A'}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                        </Box>\n\n                        <Box sx={{ mt: 'auto', display: 'flex', gap: 1 }}>\n                          <Button\n                            variant=\"contained\"\n                            startIcon={<PlayIcon />}\n                            onClick={() => handleStartWorkout(program.id)}\n                            disabled={createSessionMutation.isPending}\n                            sx={{\n                              flex: 1,\n                              borderRadius: 2,\n                              textTransform: 'none',\n                              fontWeight: 600,\n                              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                            }}\n                          >\n                            开始训练\n                          </Button>\n                          <Link href={`/workouts/${program.id}`}>\n                            <Button\n                              variant=\"outlined\"\n                              sx={{ borderRadius: 2 }}\n                            >\n                              <BookIcon />\n                            </Button>\n                          </Link>\n                        </Box>\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))\n              )}\n            </Grid>\n          </TabPanel>\n        )}\n\n        {/* My Sessions Tab */}\n        {isAuthenticated && (\n          <TabPanel value={activeTab} index={2}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>\n              训练记录\n            </Typography>\n            {isLoadingSessions ? (\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                {Array.from({ length: 5 }).map((_, index) => (\n                  <Card key={index} sx={{ borderRadius: 3 }}>\n                    <CardContent sx={{ p: 3 }}>\n                      <Box sx={{ bgcolor: 'grey.200', height: 80, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />\n                    </CardContent>\n                  </Card>\n                ))}\n              </Box>\n            ) : !sessionsData?.data || sessionsData.data.length === 0 ? (\n              <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>\n                <CalendarIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n                <Typography variant=\"h6\" sx={{ mb: 1 }}>\n                  暂无训练记录\n                </Typography>\n                <Typography color=\"text.secondary\" sx={{ mb: 3 }}>\n                  开始您的第一次训练吧\n                </Typography>\n                <Button\n                  variant=\"contained\"\n                  onClick={() => setActiveTab(0)}\n                  sx={{\n                    borderRadius: 3,\n                    textTransform: 'none',\n                    fontWeight: 600,\n                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                  }}\n                >\n                  开始训练\n                </Button>\n              </Paper>\n            ) : (\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                {sessionsData.data.map((session) => (\n                  <Card key={session.id} sx={{ borderRadius: 3, transition: 'all 0.3s ease', '&:hover': { boxShadow: theme.shadows[4] } }}>\n                    <CardContent sx={{ p: 3 }}>\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                            {session.notes || '训练记录'}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                            训练课程\n                          </Typography>\n                          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                            <Chip\n                              icon={<CalendarIcon />}\n                              label={new Date().toLocaleDateString()}\n                              size=\"small\"\n                              variant=\"outlined\"\n                            />\n                            {session.duration && (\n                              <Chip\n                                icon={<ClockIcon />}\n                                label={`${session.duration} 分钟`}\n                                size=\"small\"\n                                variant=\"outlined\"\n                              />\n                            )}\n                          </Box>\n                        </Box>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Chip\n                            label={\n                              session.status === 'COMPLETED' ? '已完成' :\n                              session.status === 'IN_PROGRESS' ? '进行中' : '待开始'\n                            }\n                            color={\n                              session.status === 'COMPLETED' ? 'success' :\n                              session.status === 'IN_PROGRESS' ? 'warning' : 'default'\n                            }\n                            variant=\"filled\"\n                          />\n                          <Link href={`/workouts/sessions/${session.id}`}>\n                            <Button variant=\"outlined\" size=\"small\" sx={{ borderRadius: 2 }}>\n                              查看详情\n                            </Button>\n                          </Link>\n                        </Box>\n                      </Box>\n                    </CardContent>\n                  </Card>\n                ))}\n              </Box>\n            )}\n          </TabPanel>\n        )}\n      </Container>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AASA;AACA;;;AArDA;;;;;;;;;;;;;;;;;;;AA6DA,SAAS,SAAS,KAAoB;IACpC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,GAAG;IAE7C,qBACE,6LAAC;QACC,MAAK;QACL,QAAQ,UAAU;QAClB,IAAI,CAAC,iBAAiB,EAAE,OAAO;QAC/B,mBAAiB,CAAC,YAAY,EAAE,OAAO;QACtC,GAAG,KAAK;kBAER,UAAU,uBAAS,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,IAAI;YAAE;sBAAI;;;;;;;;;;;AAG/C;KAdS;AAgBF,SAAS;;IACd,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,UAAU,EAAE;QACZ,YAAY,EAAE;QACd,UAAU,EAAE;IACd;IAEA,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAElC,YAAY;IACZ,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE;QACpG,OAAO;QACP,UAAU,gBAAgB,QAAQ,CAAC,MAAM,GAAG,IAAI,gBAAgB,QAAQ,CAAC,EAAE,GAAG;QAC9E,YAAY,gBAAgB,UAAU,CAAC,MAAM,GAAG,IAAI,gBAAgB,UAAU,CAAC,EAAE,GAAG;QACpF,UAAU,gBAAgB,QAAQ,CAAC,MAAM,GAAG,IAAI,gBAAgB,QAAQ,CAAC,EAAE,GAAG;IAChF;IAEA,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE;QAC9E,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,eAAe,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE;IAClF,MAAM,EAAE,MAAM,mBAAmB,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,yBAAsB,AAAD,EAAE;IAC9F,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,qBAAqB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;IAE/E,MAAM,sBAAsB,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD;IAChD,MAAM,wBAAwB,CAAA,GAAA,yIAAA,CAAA,0BAAuB,AAAD;IAEpD,MAAM,qBAAqB,CAAC,MAAoC;QAC9D,mBAAmB,CAAA;YACjB,MAAM,gBAAgB,IAAI,CAAC,KAAK;YAChC,MAAM,YAAY,cAAc,QAAQ,CAAC,SACrC,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM,SAChC;mBAAI;gBAAe;aAAM;YAE7B,OAAO;gBACL,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,eAAe;QACnB,mBAAmB;YACjB,UAAU,EAAE;YACZ,YAAY,EAAE;YACd,UAAU,EAAE;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;IAEjF,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,oBAAoB,WAAW,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;gBACtC,WAAW,EAAE;gBACb,OAAO,CAAC,wBAAwB,EAAE,IAAI,OAAO,kBAAkB,IAAI;YACrE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,mBAAmB,cAAc,MAAM,OAAO,CAAA,UAClD,gBAAgB,MAChB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,QAChE,EAAE;IAEP,mBAAmB;IACnB,MAAM,aAAa;QACjB;YACE,OAAO;YACP,OAAO,cAAc,UAAU;YAC/B,oBAAM,6LAAC,gKAAA,CAAA,UAAQ;;;;;YACf,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QACnC;QACA;YACE,OAAO;YACP,OAAO,cAAc,MAAM,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;YAC3E,oBAAM,6LAAC,qKAAA,CAAA,UAAY;;;;;YACnB,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI;QACrC;QACA;YACE,OAAO;YACP,OAAO,GAAG,cAAc,MAAM,OAAO,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC;YACrF,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;YAChB,OAAO;QACT;KACD;IAED,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,WAAW;YAAS,SAAS;QAAqB;;0BAE3D,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;oBACrI,IAAI;wBAAE,IAAI;wBAAG,IAAI;oBAAE;oBACnB,UAAU;oBACV,UAAU;gBACZ;;kCAEA,6LAAC,6MAAA,CAAA,YAAS;wBAAC,UAAS;kCAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,WAAW;gCAAU,UAAU;gCAAY,QAAQ;4BAAE;;8CAC9D,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,YAAY;wCACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACpG,gBAAgB;wCAChB,sBAAsB;wCACtB,qBAAqB;wCACrB,IAAI;wCACJ,UAAU;4CAAE,IAAI;4CAAU,IAAI;wCAAS;oCACzC;8CACD;;;;;;8CAGD,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,OAAO;wCACP,IAAI;wCACJ,YAAY;wCACZ,UAAU;4CAAE,IAAI;4CAAW,IAAI;wCAAS;oCAC1C;8CACD;;;;;;;;;;;;;;;;;kCAOL,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;kCAEF,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;;;;;;;YAKH,iCACC,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI,CAAC;oBAAG,UAAU;oBAAY,QAAQ;oBAAG,IAAI;gBAAE;0BAC5E,cAAA,6LAAC,2LAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,gBAAgB;oBAAS;8BACnD,cAAA,6LAAC,8LAAA,CAAA,OAAI;wBAAC,SAAS;wBAAC,SAAS;wBAAG,IAAI;4BAAE,UAAU;wBAAI;kCAC7C,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,8LAAA,CAAA,OAAI;gCAAC,MAAM;0CACV,cAAA,6LAAC,8LAAA,CAAA,OAAI;oCACH,IAAI;wCACF,WAAW;wCACX,GAAG;wCACH,WAAW;wCACX,YAAY;wCACZ,gBAAgB;wCAChB,QAAQ;wCACR,cAAc;wCACd,YAAY;wCACZ,WAAW;4CACT,WAAW;4CACX,WAAW,CAAC,WAAW,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,OAAO;4CAClD,QAAQ,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,MAAM;wCAC/C;oCACF;8CAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;wCAAC,IAAI;4CAAE,GAAG;wCAAiB;;0DACrC,6LAAC,2LAAA,CAAA,MAAG;gDACF,IAAI;oDACF,SAAS;oDACT,GAAG;oDACH,cAAc;oDACd,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE;oDAC3B,OAAO,KAAK,KAAK;oDACjB,IAAI;gDACN;0DAEC,KAAK,IAAI;;;;;;0DAEZ,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,YAAY;oDAAK,OAAO,KAAK,KAAK;oDAAE,IAAI;gDAAE;0DACtE,KAAK,KAAK;;;;;;0DAEb,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAQ,IAAI;oDAAE,OAAO;oDAAkB,YAAY;gDAAI;0DACxE,KAAK,KAAK;;;;;;;;;;;;;;;;;+BAnCC;;;;;;;;;;;;;;;;;;;;0BA8C9B,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;;kCAEnC,6LAAC,iMAAA,CAAA,QAAK;wBAAC,IAAI;4BAAE,IAAI;4BAAG,cAAc;4BAAG,UAAU;wBAAS;kCACtD,cAAA,6LAAC,8LAAA,CAAA,OAAI;4BACH,OAAO;4BACP,UAAU,CAAC,GAAG,WAAa,aAAa;4BACxC,SAAQ;4BACR,IAAI;gCACF,kBAAkB;oCAChB,YAAY;oCACZ,UAAU;oCACV,eAAe;oCACf,IAAI;gCACN;gCACA,mBAAmB;oCACjB,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oCACpG,OAAO;gCACT;4BACF;;8CAEA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;gCACV,iCAAmB,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;gCAC9B,iCAAmB,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAS,OAAO;wBAAW,OAAO;;0CACjC,6LAAC,iMAAA,CAAA,QAAK;gCAAC,IAAI;oCAAE,GAAG;oCAAG,IAAI;oCAAG,cAAc;gCAAE;;kDACxC,6LAAC,2LAAA,CAAA,MAAG;wCAAC,IAAI;4CAAE,SAAS;4CAAQ,KAAK;4CAAG,IAAI;4CAAG,eAAe;gDAAE,IAAI;gDAAU,IAAI;4CAAM;wCAAE;;0DACpF,6LAAC,6MAAA,CAAA,YAAS;gDACR,SAAS;gDACT,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,YAAY;oDACV,8BACE,6LAAC,4NAAA,CAAA,iBAAc;wDAAC,UAAS;kEACvB,cAAA,6LAAC,8JAAA,CAAA,UAAU;4DAAC,OAAM;;;;;;;;;;;gDAGxB;gDACA,IAAI;oDACF,4BAA4B;wDAC1B,cAAc;oDAChB;gDACF;;;;;;0DAEF,6LAAC,oMAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe,CAAC;gDAC/B,yBAAW,6LAAC,kKAAA,CAAA,UAAU;;;;;gDACtB,SAAS,kCACP,6LAAC,iMAAA,CAAA,QAAK;oDAAC,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,MAAM,EAAE;oDAAI,OAAM;8DACnG,cAAA,6LAAC,2LAAA,CAAA,MAAG;;;;;;;;;;gDAGR,IAAI;oDACF,cAAc;oDACd,UAAU;oDACV,YAAY;oDACZ,eAAe;gDACjB;0DACD;;;;;;;;;;;;kDAKH,6LAAC,0MAAA,CAAA,WAAQ;wCAAC,IAAI;kDACZ,cAAA,6LAAC,2LAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,IAAI;gDAAG,WAAW;gDAAG,aAAa;4CAAU;;8DACrD,6LAAC,8LAAA,CAAA,OAAI;oDAAC,SAAS;oDAAC,SAAS;;sEACvB,6LAAC,8LAAA,CAAA,OAAI;4DAAC,MAAM;gEAAE,IAAI;gEAAI,IAAI;4DAAE;;8EAC1B,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAY,IAAI;wEAAE,YAAY;wEAAK,IAAI;oEAAE;8EAAG;;;;;;8EAGhE,6LAAC,6MAAA,CAAA,YAAS;8EACP;wEAAC;wEAAQ;wEAAQ;wEAAO;wEAAQ;qEAAK,CAAC,GAAG,CAAC,CAAC,yBAC1C,6LAAC,kOAAA,CAAA,mBAAgB;4EAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;gFACP,SAAS,gBAAgB,QAAQ,CAAC,QAAQ,CAAC;gFAC3C,UAAU,IAAM,mBAAmB,YAAY;gFAC/C,IAAI;oFAAE,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;gFAAC;;;;;;4EAG5C,OAAO;2EARF;;;;;;;;;;;;;;;;sEAab,6LAAC,8LAAA,CAAA,OAAI;4DAAC,MAAM;gEAAE,IAAI;gEAAI,IAAI;4DAAE;;8EAC1B,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAY,IAAI;wEAAE,YAAY;wEAAK,IAAI;oEAAE;8EAAG;;;;;;8EAGhE,6LAAC,6MAAA,CAAA,YAAS;8EACP;wEAAC;wEAAM;wEAAM;qEAAK,CAAC,GAAG,CAAC,CAAC,2BACvB,6LAAC,kOAAA,CAAA,mBAAgB;4EAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;gFACP,SAAS,gBAAgB,UAAU,CAAC,QAAQ,CAAC;gFAC7C,UAAU,IAAM,mBAAmB,cAAc;gFACjD,IAAI;oFAAE,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI;gFAAC;;;;;;4EAG9C,OAAO;2EARF;;;;;;;;;;;;;;;;sEAab,6LAAC,8LAAA,CAAA,OAAI;4DAAC,MAAM;gEAAE,IAAI;gEAAI,IAAI;4DAAE;;8EAC1B,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAY,IAAI;wEAAE,YAAY;wEAAK,IAAI;oEAAE;8EAAG;;;;;;8EAGhE,6LAAC,6MAAA,CAAA,YAAS;8EACP;wEAAC;wEAAW;wEAAW;wEAAW;qEAAS,CAAC,GAAG,CAAC,CAAC,yBAChD,6LAAC,kOAAA,CAAA,mBAAgB;4EAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;gFACP,SAAS,gBAAgB,QAAQ,CAAC,QAAQ,CAAC;gFAC3C,UAAU,IAAM,mBAAmB,YAAY;gFAC/C,IAAI;oFAAE,OAAO;gFAAU;;;;;;4EAG3B,OAAO;2EARF;;;;;;;;;;;;;;;;;;;;;;gDAcd,kCACC,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,IAAI;wDAAG,IAAI;wDAAG,WAAW;wDAAG,aAAa;oDAAU;8DAC5D,cAAA,6LAAC,oMAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS;wDACT,yBAAW,6LAAC,6JAAA,CAAA,UAAS;;;;;wDACrB,IAAI;4DAAE,cAAc;4DAAG,eAAe;wDAAO;kEAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUV,CAAC,eAAe,CAAC,kCAChB,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,IAAI;gCAAE;;kDACf,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,IAAI;4CAAE,YAAY;4CAAK,IAAI;4CAAG,OAAO;wCAAe;kDAAG;;;;;;kDAGhF,6LAAC,8LAAA,CAAA,OAAI;wCAAC,SAAS;wCAAC,SAAS;kDACtB,mBACC,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,8LAAA,CAAA,OAAI;gDAAC,MAAM;oDAAE,IAAI;oDAAI,IAAI;oDAAG,IAAI;gDAAE;0DACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;oDAAC,IAAI;wDAAE,QAAQ;wDAAK,cAAc;oDAAE;8DACvC,cAAA,6LAAC,mNAAA,CAAA,cAAW;wDAAC,IAAI;4DAAE,GAAG;wDAAE;kEACtB,cAAA,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAY,QAAQ;gEAAK,cAAc;gEAAG,WAAW;4DAAkC;;;;;;;;;;;;;;;;+CAHtE;;;;wDAS7C,iBAAiB,MAAM,GAAG,GAAG,IAAI,CAAC,wBAChC,6LAAC,8LAAA,CAAA,OAAI;gDAAC,MAAM;oDAAE,IAAI;oDAAI,IAAI;oDAAG,IAAI;gDAAE;0DACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;oDACH,IAAI;wDACF,QAAQ;wDACR,cAAc;wDACd,YAAY;wDACZ,WAAW;4DACT,WAAW;4DACX,WAAW,MAAM,OAAO,CAAC,EAAE;wDAC7B;oDACF;8DAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;wDAAC,IAAI;4DAAE,GAAG;4DAAG,QAAQ;4DAAQ,SAAS;4DAAQ,eAAe;wDAAS;;0EAChF,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,SAAS;oEAAQ,gBAAgB;oEAAiB,YAAY;oEAAc,IAAI;gEAAE;;kFAC3F,6LAAC,2LAAA,CAAA,MAAG;wEAAC,IAAI;4EAAE,MAAM;wEAAE;;0FACjB,6LAAC,gNAAA,CAAA,aAAU;gFAAC,SAAQ;gFAAK,IAAI;oFAAE,YAAY;oFAAK,IAAI;gFAAE;0FACnD,QAAQ,KAAK;;;;;;0FAEhB,6LAAC,gNAAA,CAAA,aAAU;gFAAC,SAAQ;gFAAQ,OAAM;gFAAiB,IAAI;oFAAE,IAAI;gFAAE;0FAC5D,QAAQ,WAAW;;;;;;;;;;;;kFAGxB,6LAAC,gNAAA,CAAA,aAAU;wEAAC,MAAK;kFACf,cAAA,6LAAC,sKAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;0EAIvB,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,SAAS;oEAAQ,KAAK;oEAAG,IAAI;oEAAG,UAAU;gEAAO;;kFAC1D,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAS;;;;;wEAChB,OAAO,GAAG,QAAQ,QAAQ,IAAI,MAAM,GAAG,CAAC;wEACxC,MAAK;wEACL,SAAQ;;;;;;kFAEV,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;wEACjB,OAAO,QAAQ,UAAU,IAAI;wEAC7B,MAAK;wEACL,SAAQ;;;;;;kFAEV,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;wEAChB,OAAO,QAAQ,gBAAgB,IAAI;wEACnC,MAAK;wEACL,SAAQ;;;;;;;;;;;;0EAIZ,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,IAAI;oEAAQ,SAAS;oEAAQ,KAAK;gEAAE;;kFAC7C,6LAAC,oMAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,yBAAW,6LAAC,iKAAA,CAAA,UAAQ;;;;;wEACpB,SAAS,IAAM,mBAAmB,QAAQ,EAAE;wEAC5C,UAAU,sBAAsB,SAAS;wEACzC,IAAI;4EACF,MAAM;4EACN,cAAc;4EACd,eAAe;4EACf,YAAY;4EACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wEACtG;kFACD;;;;;;kFAGD,6LAAC,oMAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,SAAS,IAAM,kBAAkB,QAAQ,EAAE;wEAC3C,UAAU,oBAAoB,SAAS;wEACvC,IAAI;4EAAE,cAAc;wEAAE;kFAEtB,cAAA,6LAAC,2JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAtEyB,QAAQ,EAAE;;;;;;;;;;;;;;;;0CAmF/D,6LAAC,2LAAA,CAAA,MAAG;;kDACF,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,IAAI;4CAAE,YAAY;4CAAK,IAAI;4CAAG,OAAO;wCAAe;kDAC1E,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,GAAG;;;;;;kDAE5C,6LAAC,8LAAA,CAAA,OAAI;wCAAC,SAAS;wCAAC,SAAS;kDACtB,oBACC,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,8LAAA,CAAA,OAAI;gDAAC,MAAM;oDAAE,IAAI;oDAAI,IAAI;oDAAG,IAAI;gDAAE;0DACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;oDAAC,IAAI;wDAAE,QAAQ;wDAAK,cAAc;oDAAE;8DACvC,cAAA,6LAAC,mNAAA,CAAA,cAAW;wDAAC,IAAI;4DAAE,GAAG;wDAAE;kEACtB,cAAA,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAY,QAAQ;gEAAK,cAAc;gEAAG,WAAW;4DAAkC;;;;;;;;;;;;;;;;+CAHtE;;;;wDAQ3C,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC,8LAAA,CAAA,OAAI;4CAAC,MAAM;sDACV,cAAA,6LAAC,iMAAA,CAAA,QAAK;gDAAC,IAAI;oDAAE,GAAG;oDAAG,WAAW;oDAAU,cAAc;gDAAE;;kEACtD,6LAAC,qKAAA,CAAA,UAAiB;wDAAC,IAAI;4DAAE,UAAU;4DAAI,OAAO;4DAAkB,IAAI;wDAAE;;;;;;kEACtE,6LAAC,gNAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAK,IAAI;4DAAE,IAAI;wDAAE;kEAClC,cAAc,YAAY;;;;;;kEAE7B,6LAAC,gNAAA,CAAA,aAAU;wDAAC,OAAM;kEACf,cAAc,aAAa;;;;;;;;;;;;;;;;mDAKlC,iBAAiB,GAAG,CAAC,CAAC,wBACpB,6LAAC,8LAAA,CAAA,OAAI;gDAAC,MAAM;oDAAE,IAAI;oDAAI,IAAI;oDAAG,IAAI;gDAAE;0DACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;oDACH,IAAI;wDACF,QAAQ;wDACR,cAAc;wDACd,YAAY;wDACZ,WAAW;4DACT,WAAW;4DACX,WAAW,MAAM,OAAO,CAAC,EAAE;wDAC7B;oDACF;8DAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;wDAAC,IAAI;4DAAE,GAAG;4DAAG,QAAQ;4DAAQ,SAAS;4DAAQ,eAAe;wDAAS;;0EAChF,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,SAAS;oEAAQ,gBAAgB;oEAAiB,YAAY;oEAAc,IAAI;gEAAE;;kFAC3F,6LAAC,2LAAA,CAAA,MAAG;wEAAC,IAAI;4EAAE,MAAM;wEAAE;;0FACjB,6LAAC,gNAAA,CAAA,aAAU;gFAAC,SAAQ;gFAAK,IAAI;oFAAE,YAAY;oFAAK,IAAI;gFAAE;0FACnD,QAAQ,KAAK;;;;;;0FAEhB,6LAAC,gNAAA,CAAA,aAAU;gFAAC,SAAQ;gFAAQ,OAAM;gFAAiB,IAAI;oFAAE,IAAI;gFAAE;0FAC5D,QAAQ,WAAW;;;;;;;;;;;;kFAGxB,6LAAC,gNAAA,CAAA,aAAU;wEAAC,MAAK;kFACf,cAAA,6LAAC,sKAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;0EAIvB,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,SAAS;oEAAQ,KAAK;oEAAG,IAAI;oEAAG,UAAU;gEAAO;;kFAC1D,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAS;;;;;wEAChB,OAAO,GAAG,QAAQ,QAAQ,IAAI,MAAM,GAAG,CAAC;wEACxC,MAAK;wEACL,SAAQ;;;;;;kFAEV,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;wEACjB,OAAO,QAAQ,UAAU,IAAI;wEAC7B,MAAK;wEACL,SAAQ;;;;;;kFAEV,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;wEAChB,OAAO,QAAQ,gBAAgB,IAAI;wEACnC,MAAK;wEACL,SAAQ;;;;;;;;;;;;0EAIZ,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,IAAI;oEAAQ,SAAS;oEAAQ,KAAK;gEAAE;;kFAC7C,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wEAAE,OAAO;4EAAE,MAAM;wEAAE;kFACtD,cAAA,6LAAC,oMAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,yBAAW,6LAAC,iKAAA,CAAA,UAAQ;;;;;4EACpB,SAAS;4EACT,IAAI;gFACF,cAAc;gFACd,eAAe;gFACf,YAAY;gFACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;4EACtG;sFACD;;;;;;;;;;;kFAIH,6LAAC,oMAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,SAAS,IAAM,kBAAkB,QAAQ,EAAE;wEAC3C,UAAU,oBAAoB,SAAS;wEACvC,IAAI;4EAAE,cAAc;wEAAE;kFAEtB,cAAA,6LAAC,2JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAtEyB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAmF9D,iCACC,6LAAC;wBAAS,OAAO;wBAAW,OAAO;;0CACjC,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,IAAI;oCAAE,YAAY;oCAAK,IAAI;oCAAG,OAAO;gCAAe;0CAAG;;;;;;0CAGhF,6LAAC,8LAAA,CAAA,OAAI;gCAAC,SAAS;gCAAC,SAAS;0CACtB,wBACC,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,8LAAA,CAAA,OAAI;wCAAC,MAAM;4CAAE,IAAI;4CAAI,IAAI;4CAAG,IAAI;wCAAE;kDACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4CAAC,IAAI;gDAAE,QAAQ;gDAAK,cAAc;4CAAE;sDACvC,cAAA,6LAAC,mNAAA,CAAA,cAAW;gDAAC,IAAI;oDAAE,GAAG;gDAAE;0DACtB,cAAA,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,SAAS;wDAAY,QAAQ;wDAAK,cAAc;wDAAG,WAAW;oDAAkC;;;;;;;;;;;;;;;;uCAHtE;;;;gDAQ3C,CAAC,gBAAgB,aAAa,MAAM,KAAK,kBAC3C,6LAAC,8LAAA,CAAA,OAAI;oCAAC,MAAM;8CACV,cAAA,6LAAC,iMAAA,CAAA,QAAK;wCAAC,IAAI;4CAAE,GAAG;4CAAG,WAAW;4CAAU,cAAc;wCAAE;;0DACtD,6LAAC,gKAAA,CAAA,UAAQ;gDAAC,IAAI;oDAAE,UAAU;oDAAI,OAAO;oDAAkB,IAAI;gDAAE;;;;;;0DAC7D,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,IAAI;gDAAE;0DAAG;;;;;;0DAGxC,6LAAC,gNAAA,CAAA,aAAU;gDAAC,OAAM;gDAAiB,IAAI;oDAAE,IAAI;gDAAE;0DAAG;;;;;;0DAGlD,6LAAC,oMAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa;gDAC5B,IAAI;oDACF,cAAc;oDACd,eAAe;oDACf,YAAY;oDACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gDACtG;0DACD;;;;;;;;;;;;;;;;2CAML,aAAa,GAAG,CAAC,CAAC,wBAChB,6LAAC,8LAAA,CAAA,OAAI;wCAAC,MAAM;4CAAE,IAAI;4CAAI,IAAI;4CAAG,IAAI;wCAAE;kDACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4CACH,IAAI;gDACF,QAAQ;gDACR,cAAc;gDACd,YAAY;gDACZ,WAAW;oDACT,WAAW;oDACX,WAAW,MAAM,OAAO,CAAC,EAAE;gDAC7B;4CACF;sDAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;gDAAC,IAAI;oDAAE,GAAG;oDAAG,QAAQ;oDAAQ,SAAS;oDAAQ,eAAe;gDAAS;;kEAChF,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,gBAAgB;4DAAiB,YAAY;4DAAc,IAAI;wDAAE;;0EAC3F,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,MAAM;gEAAE;;kFACjB,6LAAC,gNAAA,CAAA,aAAU;wEAAC,SAAQ;wEAAK,IAAI;4EAAE,YAAY;4EAAK,IAAI;wEAAE;kFACnD,QAAQ,KAAK;;;;;;kFAEhB,6LAAC,gNAAA,CAAA,aAAU;wEAAC,SAAQ;wEAAQ,OAAM;wEAAiB,IAAI;4EAAE,IAAI;wEAAE;kFAC5D,QAAQ,WAAW;;;;;;;;;;;;0EAGxB,6LAAC,8LAAA,CAAA,OAAI;gEAAC,OAAM;gEAAM,OAAM;gEAAU,MAAK;;;;;;;;;;;;kEAGzC,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,KAAK;4DAAG,IAAI;4DAAG,UAAU;wDAAO;;0EAC1D,6LAAC,8LAAA,CAAA,OAAI;gEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAS;;;;;gEAChB,OAAO,GAAG,QAAQ,QAAQ,IAAI,MAAM,GAAG,CAAC;gEACxC,MAAK;gEACL,SAAQ;;;;;;0EAEV,6LAAC,8LAAA,CAAA,OAAI;gEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;gEACjB,OAAO,QAAQ,UAAU,IAAI;gEAC7B,MAAK;gEACL,SAAQ;;;;;;;;;;;;kEAIZ,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,IAAI;4DAAQ,SAAS;4DAAQ,KAAK;wDAAE;;0EAC7C,6LAAC,oMAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,yBAAW,6LAAC,iKAAA,CAAA,UAAQ;;;;;gEACpB,SAAS,IAAM,mBAAmB,QAAQ,EAAE;gEAC5C,UAAU,sBAAsB,SAAS;gEACzC,IAAI;oEACF,MAAM;oEACN,cAAc;oEACd,eAAe;oEACf,YAAY;oEACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gEACtG;0EACD;;;;;;0EAGD,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;0EACnC,cAAA,6LAAC,oMAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,IAAI;wEAAE,cAAc;oEAAE;8EAEtB,cAAA,6LAAC,gKAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA7DsB,QAAQ,EAAE;;;;;;;;;;;;;;;;oBA2E9D,iCACC,6LAAC;wBAAS,OAAO;wBAAW,OAAO;;0CACjC,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,IAAI;oCAAE,YAAY;oCAAK,IAAI;oCAAG,OAAO;gCAAe;0CAAG;;;;;;4BAG/E,kCACC,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAE;0CACzD,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,8LAAA,CAAA,OAAI;wCAAa,IAAI;4CAAE,cAAc;wCAAE;kDACtC,cAAA,6LAAC,mNAAA,CAAA,cAAW;4CAAC,IAAI;gDAAE,GAAG;4CAAE;sDACtB,cAAA,6LAAC,2LAAA,CAAA,MAAG;gDAAC,IAAI;oDAAE,SAAS;oDAAY,QAAQ;oDAAI,cAAc;oDAAG,WAAW;gDAAkC;;;;;;;;;;;uCAFnG;;;;;;;;;uCAOb,CAAC,cAAc,QAAQ,aAAa,IAAI,CAAC,MAAM,KAAK,kBACtD,6LAAC,iMAAA,CAAA,QAAK;gCAAC,IAAI;oCAAE,GAAG;oCAAG,WAAW;oCAAU,cAAc;gCAAE;;kDACtD,6LAAC,qKAAA,CAAA,UAAY;wCAAC,IAAI;4CAAE,UAAU;4CAAI,OAAO;4CAAkB,IAAI;wCAAE;;;;;;kDACjE,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,IAAI;4CAAE,IAAI;wCAAE;kDAAG;;;;;;kDAGxC,6LAAC,gNAAA,CAAA,aAAU;wCAAC,OAAM;wCAAiB,IAAI;4CAAE,IAAI;wCAAE;kDAAG;;;;;;kDAGlD,6LAAC,oMAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,IAAI;4CACF,cAAc;4CACd,eAAe;4CACf,YAAY;4CACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACtG;kDACD;;;;;;;;;;;qDAKH,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAE;0CACzD,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,wBACtB,6LAAC,8LAAA,CAAA,OAAI;wCAAkB,IAAI;4CAAE,cAAc;4CAAG,YAAY;4CAAiB,WAAW;gDAAE,WAAW,MAAM,OAAO,CAAC,EAAE;4CAAC;wCAAE;kDACpH,cAAA,6LAAC,mNAAA,CAAA,cAAW;4CAAC,IAAI;gDAAE,GAAG;4CAAE;sDACtB,cAAA,6LAAC,2LAAA,CAAA,MAAG;gDAAC,IAAI;oDAAE,SAAS;oDAAQ,gBAAgB;oDAAiB,YAAY;gDAAS;;kEAChF,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,MAAM;wDAAE;;0EACjB,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAK,IAAI;oEAAE,YAAY;oEAAK,IAAI;gEAAE;0EACnD,QAAQ,KAAK,IAAI;;;;;;0EAEpB,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,OAAM;gEAAiB,IAAI;oEAAE,IAAI;gEAAE;0EAAG;;;;;;0EAGlE,6LAAC,2LAAA,CAAA,MAAG;gEAAC,IAAI;oEAAE,SAAS;oEAAQ,KAAK;oEAAG,UAAU;gEAAO;;kFACnD,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,qKAAA,CAAA,UAAY;;;;;wEACnB,OAAO,IAAI,OAAO,kBAAkB;wEACpC,MAAK;wEACL,SAAQ;;;;;;oEAET,QAAQ,QAAQ,kBACf,6LAAC,8LAAA,CAAA,OAAI;wEACH,oBAAM,6LAAC,kKAAA,CAAA,UAAS;;;;;wEAChB,OAAO,GAAG,QAAQ,QAAQ,CAAC,GAAG,CAAC;wEAC/B,MAAK;wEACL,SAAQ;;;;;;;;;;;;;;;;;;kEAKhB,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;wDAAE;;0EACvD,6LAAC,8LAAA,CAAA,OAAI;gEACH,OACE,QAAQ,MAAM,KAAK,cAAc,QACjC,QAAQ,MAAM,KAAK,gBAAgB,QAAQ;gEAE7C,OACE,QAAQ,MAAM,KAAK,cAAc,YACjC,QAAQ,MAAM,KAAK,gBAAgB,YAAY;gEAEjD,SAAQ;;;;;;0EAEV,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE;0EAC5C,cAAA,6LAAC,oMAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAW,MAAK;oEAAQ,IAAI;wEAAE,cAAc;oEAAE;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAxChE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDvC;GA9xBgB;;QACA,wMAAA,CAAA,WAAQ;QAUM,qIAAA,CAAA,UAAO;QAGgD,yIAAA,CAAA,qBAAkB;QAOxC,yIAAA,CAAA,qBAAkB;QAIhB,yIAAA,CAAA,qBAAkB;QACV,yIAAA,CAAA,yBAAsB;QAC5B,yIAAA,CAAA,kBAAe;QAEpD,yIAAA,CAAA,wBAAqB;QACnB,yIAAA,CAAA,0BAAuB;;;MA9BvC", "debugId": null}}]}