{"common": {"loading": "Loading...", "error": "Error", "retry": "Retry", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "clear": "Clear", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close"}, "navigation": {"home": "Home", "workouts": "Workout Plans", "exercises": "Exercise Database", "progress": "Progress Tracking", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "homepage": {"title": "AI-fitness-singles", "subtitle": "Smart Fitness Platform for Singles", "description": "AI-powered fitness platform designed for singles. Create personalized workout plans, access comprehensive exercise database, track progress with detailed analytics, and achieve your fitness goals.", "getStarted": "Get Started", "learnMore": "Learn More", "features": {"title": "Core Features", "workouts": {"title": "Smart Workout Plans", "description": "AI-generated personalized training programs"}, "exercises": {"title": "Exercise Library", "description": "Comprehensive database with detailed instructions"}, "progress": {"title": "Progress Analytics", "description": "Track your fitness journey with detailed insights"}}, "stats": {"title": "Quick Stats", "totalWorkouts": "Total Workouts", "completedSessions": "Completed Sessions", "totalExercises": "Total Exercises", "progressScore": "Progress Score"}}, "workouts": {"title": "Workout Plans", "subtitle": "Discover and create personalized training programs", "createNew": "Create New Workout", "searchPlaceholder": "Search workout plans...", "filters": {"all": "All Levels", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "card": {"level": "Level", "duration": "Duration", "exercises": "Exercises", "startWorkout": "Start Workout", "viewDetails": "View Details"}, "empty": {"title": "No workout plans found", "description": "Try adjusting your search or filters"}}, "exercises": {"title": "Exercise Database", "subtitle": "Comprehensive library of exercises with detailed instructions", "searchPlaceholder": "Search exercises...", "filters": {"all": "All Categories", "strength": "Strength", "cardio": "Cardio", "flexibility": "Flexibility", "balance": "Balance"}, "card": {"muscleGroup": "Muscle Group", "equipment": "Equipment", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewExercise": "View Exercise", "addToWorkout": "Add to Workout"}, "attributes": {"muscleGroup": "Muscle Group", "equipment": "Equipment", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "empty": {"title": "No exercises found", "description": "Try adjusting your search or filters"}}, "progress": {"title": "Progress Tracking", "subtitle": "Monitor your fitness journey with detailed analytics", "overview": {"title": "Progress Overview", "thisWeek": "This Week", "thisMonth": "This Month", "allTime": "All Time"}, "charts": {"workoutFrequency": "Workout Frequency", "progressTrend": "Progress Trend", "exerciseDistribution": "Exercise Distribution"}, "stats": {"totalWorkouts": "Total Workouts", "totalTime": "Total Time", "averageSession": "Average Session", "caloriesBurned": "Calories Burned"}, "empty": {"title": "No progress data yet", "description": "Start working out to see your progress here"}}, "apiTest": {"title": "API Integration Test", "subtitle": "Testing workout-cool API integration", "programs": {"title": "Programs API Test", "test": "Test Programs API", "success": "Programs API working correctly", "error": "Programs API error"}, "exercises": {"title": "Exercises API Test", "test": "Test Exercises API", "success": "Exercises API working correctly", "error": "Exercises API error"}, "progress": {"title": "Progress API Test", "test": "Test Progress API", "success": "Progress API working correctly", "error": "Progress API error"}}, "language": {"switch": "Switch Language", "english": "English", "chinese": "中文"}}