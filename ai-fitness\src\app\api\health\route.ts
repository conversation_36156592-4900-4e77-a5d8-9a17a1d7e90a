import { NextRequest, NextResponse } from 'next/server';

/**
 * Health Check API Endpoint
 * Used for monitoring and load balancer health checks
 */

export async function GET(request: NextRequest) {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      checks: {
        api: await checkApiHealth(),
        database: await checkDatabaseHealth(),
        external: await checkExternalServices(),
      }
    };

    // Determine overall health status
    const allChecksHealthy = Object.values(healthData.checks).every(check => check.status === 'healthy');
    
    if (!allChecksHealthy) {
      healthData.status = 'degraded';
    }

    const statusCode = healthData.status === 'healthy' ? 200 : 503;

    return NextResponse.json(healthData, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}

/**
 * Check API health
 */
async function checkApiHealth(): Promise<{ status: string; responseTime?: number; error?: string }> {
  try {
    const startTime = Date.now();

    // Simple internal check - just verify we can respond
    const responseTime = Date.now() - startTime;

    return {
      status: 'healthy',
      responseTime
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check database health (if applicable)
 */
async function checkDatabaseHealth(): Promise<{ status: string; responseTime?: number; error?: string }> {
  try {
    // If using a database, add connection check here
    // For now, we'll just return healthy since we're using external APIs
    return {
      status: 'healthy',
      responseTime: 0
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check external services health
 */
async function checkExternalServices(): Promise<{ status: string; services?: any; error?: string }> {
  try {
    const services = {
      workoutApi: await checkWorkoutApiHealth(),
    };
    
    const allServicesHealthy = Object.values(services).every(service => service.status === 'healthy');
    
    return {
      status: allServicesHealthy ? 'healthy' : 'degraded',
      services
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check workout API health
 */
async function checkWorkoutApiHealth(): Promise<{ status: string; responseTime?: number; error?: string }> {
  try {
    const startTime = Date.now();
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    if (!apiBaseUrl) {
      return {
        status: 'degraded',
        error: 'API base URL not configured'
      };
    }

    // For now, just return healthy since we don't have the actual API running
    // In production, this would make a real API call
    const responseTime = Date.now() - startTime;

    return {
      status: 'healthy',
      responseTime
    };
  } catch (error) {
    return {
      status: 'degraded',
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
}
