#!/bin/bash

# AI-fitness-singles Deployment Script
# This script handles deployment to various environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-fitness-singles"
DOCKER_IMAGE="ai-fitness-singles"
DOCKER_TAG="latest"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_warning "Docker is not installed (required for Docker deployment)"
    fi
    
    log_success "Dependencies check completed"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    npm ci
    log_success "Dependencies installed"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    # Type checking
    if npm run type-check; then
        log_success "Type checking passed"
    else
        log_error "Type checking failed"
        exit 1
    fi
    
    # Linting
    if npm run lint; then
        log_success "Linting passed"
    else
        log_error "Linting failed"
        exit 1
    fi
    
    # Tests (if available)
    if npm run test --if-present; then
        log_success "Tests passed"
    else
        log_warning "No tests found or tests failed"
    fi
}

# Build application
build_app() {
    log_info "Building application..."
    
    if npm run build; then
        log_success "Build completed successfully"
    else
        log_error "Build failed"
        exit 1
    fi
}

# Deploy to Vercel
deploy_vercel() {
    log_info "Deploying to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        log_error "Vercel CLI is not installed. Install with: npm i -g vercel"
        exit 1
    fi
    
    # Deploy to production
    if vercel --prod --yes; then
        log_success "Deployed to Vercel successfully"
    else
        log_error "Vercel deployment failed"
        exit 1
    fi
}

# Build Docker image
build_docker() {
    log_info "Building Docker image..."
    
    if docker build -t $DOCKER_IMAGE:$DOCKER_TAG .; then
        log_success "Docker image built successfully"
    else
        log_error "Docker build failed"
        exit 1
    fi
}

# Deploy with Docker Compose
deploy_docker() {
    log_info "Deploying with Docker Compose..."
    
    if docker-compose up -d; then
        log_success "Docker deployment completed"
    else
        log_error "Docker deployment failed"
        exit 1
    fi
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local url=${1:-"http://localhost:3000"}
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$url/api/health" > /dev/null 2>&1; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying in 5 seconds..."
        sleep 5
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Cleanup
cleanup() {
    log_info "Cleaning up..."
    
    # Remove old Docker images
    if command -v docker &> /dev/null; then
        docker system prune -f
    fi
    
    log_success "Cleanup completed"
}

# Main deployment function
deploy() {
    local environment=${1:-"development"}
    local deployment_type=${2:-"vercel"}
    
    log_info "Starting deployment to $environment using $deployment_type"
    
    # Pre-deployment checks
    check_dependencies
    install_dependencies
    run_tests
    build_app
    
    # Deploy based on type
    case $deployment_type in
        "vercel")
            deploy_vercel
            ;;
        "docker")
            build_docker
            deploy_docker
            ;;
        "docker-build")
            build_docker
            ;;
        *)
            log_error "Unknown deployment type: $deployment_type"
            log_info "Available types: vercel, docker, docker-build"
            exit 1
            ;;
    esac
    
    # Post-deployment
    if [ "$deployment_type" = "docker" ]; then
        health_check "http://localhost:3000"
    fi
    
    cleanup
    
    log_success "Deployment completed successfully!"
}

# Show usage
show_usage() {
    echo "Usage: $0 [ENVIRONMENT] [DEPLOYMENT_TYPE]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  development  - Development environment (default)"
    echo "  staging      - Staging environment"
    echo "  production   - Production environment"
    echo ""
    echo "DEPLOYMENT_TYPE:"
    echo "  vercel       - Deploy to Vercel (default)"
    echo "  docker       - Deploy using Docker Compose"
    echo "  docker-build - Build Docker image only"
    echo ""
    echo "Examples:"
    echo "  $0                           # Deploy to development using Vercel"
    echo "  $0 production vercel         # Deploy to production using Vercel"
    echo "  $0 staging docker            # Deploy to staging using Docker"
    echo "  $0 production docker-build   # Build Docker image for production"
}

# Main script
main() {
    case ${1:-""} in
        "-h"|"--help"|"help")
            show_usage
            exit 0
            ;;
        *)
            deploy "$1" "$2"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
