/**
 * Authentication API Service
 * Handles authentication-related API calls to workout-cool backend
 */

import { apiClient } from '../client';
import { API_CONFIG } from '../config';
import { WorkoutCoolAdapter, type WorkoutCoolUser } from '../adapters/workout-cool.adapter';
import type { User, Session, AuthCredentials, SignUpData } from '../types';

export class AuthService {
  /**
   * Sign in with email and password - adapted for Better Auth
   */
  static async signIn(credentials: AuthCredentials): Promise<Session> {
    const response = await apiClient.post<{
      user: WorkoutCoolUser;
      session: { id: string; userId: string; expiresAt: string };
    }>(API_CONFIG.ENDPOINTS.AUTH.SIGNIN, {
      email: credentials.email,
      password: credentials.password,
    }, {
      requireAuth: false,
    });

    return {
      user: WorkoutCoolAdapter.adaptUser(response.user),
      sessionId: response.session.id,
      expiresAt: new Date(response.session.expiresAt),
    };
  }

  /**
   * Sign up with user data - adapted for Better Auth
   */
  static async signUp(userData: SignUpData): Promise<Session> {
    const response = await apiClient.post<{
      user: WorkoutCoolUser;
      session: { id: string; userId: string; expiresAt: string };
    }>(API_CONFIG.ENDPOINTS.AUTH.SIGNUP, {
      email: userData.email,
      password: userData.password,
      firstName: userData.firstName,
      lastName: userData.lastName,
      name: `${userData.firstName} ${userData.lastName}`,
    }, {
      requireAuth: false,
    });

    return {
      user: WorkoutCoolAdapter.adaptUser(response.user),
      sessionId: response.session.id,
      expiresAt: new Date(response.session.expiresAt),
    };
  }

  /**
   * Sign out current user - adapted for Better Auth
   */
  static async signOut(): Promise<void> {
    return apiClient.post<void>(API_CONFIG.ENDPOINTS.AUTH.SIGNOUT);
  }

  /**
   * Get current session
   */
  static async getSession(): Promise<Session | null> {
    try {
      return await apiClient.get<Session>(API_CONFIG.ENDPOINTS.AUTH.SESSION);
    } catch (error: any) {
      // Return null if not authenticated
      if (error.status === 401) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(
      API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD,
      { email },
      { requireAuth: false }
    );
  }

  /**
   * Reset password with token
   */
  static async resetPassword(
    token: string,
    newPassword: string
  ): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(
      API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD,
      { token, password: newPassword },
      { requireAuth: false }
    );
  }

  /**
   * Verify email with token
   */
  static async verifyEmail(token: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(
      '/api/auth/verify-email',
      { token },
      { requireAuth: false }
    );
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<Session> {
    return apiClient.post<Session>('/api/auth/refresh');
  }

  /**
   * Update user profile
   */
  static async updateProfile(updates: Partial<User>): Promise<User> {
    return apiClient.patch<User>('/api/auth/profile', updates);
  }

  /**
   * Change password
   */
  static async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>('/api/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  /**
   * Delete account
   */
  static async deleteAccount(password: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>('/api/auth/account', {
      body: JSON.stringify({ password }),
    });
  }

  /**
   * Get user sessions
   */
  static async getUserSessions(): Promise<Array<{
    id: string;
    userAgent: string;
    ip: string;
    createdAt: string;
    lastUsed: string;
    isCurrent: boolean;
  }>> {
    return apiClient.get<Array<{
      id: string;
      userAgent: string;
      ip: string;
      createdAt: string;
      lastUsed: string;
      isCurrent: boolean;
    }>>('/api/auth/sessions');
  }

  /**
   * Revoke a specific session
   */
  static async revokeSession(sessionId: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`/api/auth/sessions/${sessionId}`);
  }

  /**
   * Revoke all other sessions
   */
  static async revokeAllOtherSessions(): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>('/api/auth/sessions/revoke-all');
  }
}
