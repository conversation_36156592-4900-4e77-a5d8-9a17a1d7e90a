(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{143:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))},3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(5155),a=t(2115),l=t(5220),i=t(6695),n=t(7516),o=t(7023),x=t(9579),d=t(5690),c=t(2659),m=t(465),h=t(2713);function u(){let[e,s]=(0,a.useState)(!0),[t,u]=(0,a.useState)(null);return((0,a.useEffect)(()=>{let e=setTimeout(()=>{s(!1)},1e3);return()=>clearTimeout(e)},[]),e)?(0,r.jsx)(o.AV,{title:"Loading AI-fitness-singles",description:"Preparing your fitness platform..."}):t?(0,r.jsx)(x.M,{title:"Failed to load homepage",message:t,onRetry:()=>{u(null),s(!0),setTimeout(()=>{s(!1)},1e3)}}):(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(n.V,{}),(0,r.jsx)("section",{className:"relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-20 sm:py-32",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl sm:text-6xl font-bold text-gray-900 mb-6",children:["AI-fitness-singles",(0,r.jsx)("span",{className:"block text-blue-600",children:"Smart Fitness Platform"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Create custom workout plans, access comprehensive exercise database, and track your fitness progress with detailed analytics."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(l.$,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:[(0,r.jsx)(d.A,{className:"mr-2 h-5 w-5"}),"Start Training"]}),(0,r.jsxs)(l.$,{variant:"outline",size:"lg",className:"border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3",children:[(0,r.jsx)(c.A,{className:"mr-2 h-5 w-5"}),"Browse Exercises"]})]})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Everything You Need to Train"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Comprehensive fitness platform with workout builder, exercise library, and progress tracking."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsx)(i.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(i.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)(i.ZB,{className:"text-xl font-semibold text-gray-900",children:"Workout Builder"}),(0,r.jsx)(i.BT,{className:"text-gray-600",children:"Create custom workout plans with our intuitive drag-and-drop builder."})]})}),(0,r.jsx)(i.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(i.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)(i.ZB,{className:"text-xl font-semibold text-gray-900",children:"Exercise Database"}),(0,r.jsx)(i.BT,{className:"text-gray-600",children:"Access thousands of exercises with detailed instructions and video guides."})]})}),(0,r.jsx)(i.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(i.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsx)(i.ZB,{className:"text-xl font-semibold text-gray-900",children:"Progress Tracking"}),(0,r.jsx)(i.BT,{className:"text-gray-600",children:"Monitor your improvements with detailed analytics and visual reports."})]})}),(0,r.jsx)(i.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(i.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-orange-600"})}),(0,r.jsx)(i.ZB,{className:"text-xl font-semibold text-gray-900",children:"Workout Sessions"}),(0,r.jsx)(i.BT,{className:"text-gray-600",children:"Execute your workouts with guided sessions and real-time tracking."})]})})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Start Training Today"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Build your first workout plan in minutes and begin your fitness journey."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(l.$,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:"Create Workout Plan"}),(0,r.jsx)(l.$,{variant:"outline",size:"lg",className:"border-gray-300 text-gray-700 hover:bg-gray-100 px-8 py-3",children:"Explore Exercises"})]})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[76,96,358],()=>s(143)),_N_E=e.O()}]);