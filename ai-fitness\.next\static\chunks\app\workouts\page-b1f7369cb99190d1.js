(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[347],{4281:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>P});var i=a(5155),l=a(2115),t=a(7516),c=a(6695),r=a(5220),n=a(6126),d=a(2523),x=a(7023),m=a(9579),o=a(4092),h=a(2659),j=a(9074),g=a(4186),u=a(7924),N=a(6932),p=a(4416),f=a(1976),v=a(6785),y=a(7580),w=a(5690),b=a(4616),A=a(9150),k=a(5251),C=a(6874),S=a.n(C);function P(){var s,e,a;let[C,P]=(0,l.useState)("programs"),[z,W]=(0,l.useState)(""),[$,L]=(0,l.useState)(!1),[M,Z]=(0,l.useState)({category:[],difficulty:[],duration:[]}),{isAuthenticated:D}=(0,k.As)(),{data:E,isLoading:T,error:R}=(0,A.Mm)({limit:20,category:M.category.length>0?M.category[0]:void 0,difficulty:M.difficulty.length>0?M.difficulty[0]:void 0,duration:M.duration.length>0?M.duration[0]:void 0}),{data:B,isLoading:F}=(0,A.My)({limit:20}),{data:O,isLoading:_}=(0,A.mS)(6),{data:I,isLoading:V}=(0,A.Ox)(6),{data:K,isLoading:Y}=(0,A.sK)(),G=(0,A.t2)(),H=(0,A.bR)(),J=(s,e)=>{Z(a=>{let i=a[s],l=i.includes(e)?i.filter(s=>s!==e):[...i,e];return{...a,[s]:l}})},Q=Object.values(M).some(s=>s.length>0),q=async s=>{try{await G.mutateAsync(s)}catch(s){console.error("Failed to join program:",s)}},U=async s=>{try{await H.mutateAsync({exercises:[],notes:"Quick workout session - ".concat(new Date().toLocaleDateString())})}catch(s){console.error("Failed to start workout:",s)}},X=(null==E||null==(s=E.data)?void 0:s.filter(s=>{var e;return""===z||s.title.toLowerCase().includes(z.toLowerCase())||(null==(e=s.description)?void 0:e.toLowerCase().includes(z.toLowerCase()))}))||[];return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(t.V,{}),(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Workout Programs"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover personalized workout programs designed to help you reach your fitness goals"})]}),D&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,i.jsx)(c.Zp,{children:(0,i.jsx)(c.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Active Programs"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==K?void 0:K.length)||0})]}),(0,i.jsx)(h.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,i.jsx)(c.Zp,{children:(0,i.jsx)(c.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==B||null==(e=B.data)?void 0:e.filter(s=>"COMPLETED"===s.status).length)||0})]}),(0,i.jsx)(j.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,i.jsx)(c.Zp,{children:(0,i.jsx)(c.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Total Minutes"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==B||null==(a=B.data)?void 0:a.reduce((s,e)=>s+(e.duration||0),0))||0})]}),(0,i.jsx)(g.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,i.jsxs)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm",children:[(0,i.jsx)("button",{onClick:()=>P("programs"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("programs"===C?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:"All Programs"}),D&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("button",{onClick:()=>P("my-programs"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("my-programs"===C?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:"My Programs"}),(0,i.jsx)("button",{onClick:()=>P("sessions"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("sessions"===C?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:"My Sessions"})]})]}),"programs"===C&&(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-8",children:[(0,i.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,i.jsx)(d.p,{type:"text",placeholder:"Search workout programs...",value:z,onChange:s=>W(s.target.value),className:"pl-10"})]}),(0,i.jsxs)(r.$,{variant:"outline",onClick:()=>L(!$),className:"flex items-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4"}),"Filters",Q&&(0,i.jsx)(n.E,{variant:"secondary",className:"ml-1",children:Object.values(M).reduce((s,e)=>s+e.length,0)})]})]}),$&&(0,i.jsxs)("div",{className:"border-t pt-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Category"}),(0,i.jsx)("div",{className:"space-y-2",children:["Strength","Cardio","Flexibility","HIIT","Yoga"].map(s=>(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:M.category.includes(s),onChange:()=>J("category",s),className:"rounded border-gray-300"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:s})]},s))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Difficulty"}),(0,i.jsx)("div",{className:"space-y-2",children:["Beginner","Intermediate","Advanced"].map(s=>(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:M.difficulty.includes(s),onChange:()=>J("difficulty",s),className:"rounded border-gray-300"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:s})]},s))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Duration"}),(0,i.jsx)("div",{className:"space-y-2",children:["15-30 min","30-45 min","45-60 min","60+ min"].map(s=>(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:M.duration.includes(s),onChange:()=>J("duration",s),className:"rounded border-gray-300"}),(0,i.jsx)("span",{className:"text-sm text-gray-700",children:s})]},s))})]})]}),Q&&(0,i.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,i.jsxs)(r.$,{variant:"outline",onClick:()=>{Z({category:[],difficulty:[],duration:[]})},className:"flex items-center gap-2",children:[(0,i.jsx)(p.A,{className:"h-4 w-4"}),"Clear Filters"]})})]})]}),"programs"===C&&(0,i.jsxs)(i.Fragment,{children:[!z&&!Q&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Popular Programs"}),_?(0,i.jsx)(x.z0,{}):(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:null==O?void 0:O.slice(0,6).map(s=>(0,i.jsxs)(c.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,i.jsx)(c.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{className:"text-lg",children:s.title}),(0,i.jsx)(c.BT,{children:s.description})]}),(0,i.jsx)(r.$,{variant:"ghost",size:"icon",children:(0,i.jsx)(f.A,{className:"h-4 w-4"})})]})}),(0,i.jsx)(c.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:[s.duration||"N/A"," min"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.difficulty||"N/A"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(y.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.participantCount||0})]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(r.$,{size:"sm",className:"flex-1",onClick:()=>U(s.id),disabled:H.isPending,children:[(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]}),(0,i.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>q(s.id),disabled:G.isPending,children:(0,i.jsx)(b.A,{className:"h-4 w-4"})})]})]})})]},s.id))})]}),D&&(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Recommended for You"}),V?(0,i.jsx)(x.z0,{}):(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:null==I?void 0:I.map(s=>(0,i.jsxs)(c.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,i.jsx)(c.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{className:"text-lg",children:s.title}),(0,i.jsx)(c.BT,{children:s.description})]}),(0,i.jsx)(r.$,{variant:"ghost",size:"icon",children:(0,i.jsx)(f.A,{className:"h-4 w-4"})})]})}),(0,i.jsx)(c.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:[s.duration||"N/A"," min"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.difficulty||"N/A"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(y.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.participantCount||0})]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(r.$,{size:"sm",className:"flex-1",onClick:()=>U(s.id),disabled:H.isPending,children:[(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]}),(0,i.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>q(s.id),disabled:G.isPending,children:(0,i.jsx)(b.A,{className:"h-4 w-4"})})]})]})})]},s.id))})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:z?'Search Results for "'.concat(z,'"'):"All Programs"}),T?(0,i.jsx)(x.z0,{}):R?(0,i.jsx)(m.Kw,{title:"Failed to load programs",message:"Please try again later",onRetry:()=>window.location.reload()}):0===X.length?z?(0,i.jsx)(o.wT,{searchTerm:z,onClearSearch:()=>W("")}):(0,i.jsx)(o.sM,{}):(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:X.map(s=>(0,i.jsxs)(c.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,i.jsx)(c.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{className:"text-lg",children:s.title}),(0,i.jsx)(c.BT,{children:s.description})]}),(0,i.jsx)(r.$,{variant:"ghost",size:"icon",children:(0,i.jsx)(f.A,{className:"h-4 w-4"})})]})}),(0,i.jsx)(c.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:[s.duration||"N/A"," min"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.difficulty||"N/A"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(y.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.participantCount||0})]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(S(),{href:"/workouts/".concat(s.id),className:"flex-1",children:(0,i.jsxs)(r.$,{size:"sm",className:"w-full",children:[(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"View Details"]})}),(0,i.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>q(s.id),disabled:G.isPending,children:(0,i.jsx)(b.A,{className:"h-4 w-4"})})]})]})})]},s.id))})]})]}),"my-programs"===C&&D&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"My Programs"}),Y?(0,i.jsx)(x.z0,{}):K&&0!==K.length?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:K.map(s=>(0,i.jsxs)(c.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,i.jsx)(c.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{className:"text-lg",children:s.title}),(0,i.jsx)(c.BT,{children:s.description})]}),(0,i.jsx)(n.E,{variant:"secondary",children:"Joined"})]})}),(0,i.jsx)(c.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:[s.duration||"N/A"," min"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:s.difficulty||"N/A"})]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(r.$,{size:"sm",className:"flex-1",onClick:()=>U(s.id),disabled:H.isPending,children:[(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]}),(0,i.jsx)(S(),{href:"/workouts/".concat(s.id),children:(0,i.jsx)(r.$,{variant:"outline",size:"sm",children:(0,i.jsx)(h.A,{className:"h-4 w-4"})})})]})]})})]},s.id))}):(0,i.jsx)(o.sM,{})]}),"sessions"===C&&D&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"My Workout Sessions"}),F?(0,i.jsx)(x.z0,{}):(null==B?void 0:B.data)&&0!==B.data.length?(0,i.jsx)("div",{className:"space-y-4",children:B.data.map(s=>(0,i.jsx)(c.Zp,{children:(0,i.jsx)(c.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-lg",children:s.notes||"Workout Session"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Workout Session"}),(0,i.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-sm text-gray-500",children:[(0,i.jsxs)("span",{className:"flex items-center gap-1",children:[(0,i.jsx)(j.A,{className:"h-4 w-4"}),new Date().toLocaleDateString()]}),s.duration&&(0,i.jsxs)("span",{className:"flex items-center gap-1",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),s.duration," min"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(n.E,{variant:"COMPLETED"===s.status?"default":"IN_PROGRESS"===s.status?"secondary":"outline",children:s.status}),(0,i.jsx)(S(),{href:"/workouts/sessions/".concat(s.id),children:(0,i.jsx)(r.$,{variant:"outline",size:"sm",children:"View Details"})})]})]})})},s.id))}):(0,i.jsx)(o.sM,{})]})]})]})}},6865:(s,e,a)=>{Promise.resolve().then(a.bind(a,4281))}},s=>{var e=e=>s(s.s=e);s.O(0,[76,96,358],()=>e(6865)),_N_E=s.O()}]);