# AI-fitness-singles Deployment Guide

This document provides comprehensive instructions for deploying the AI-fitness-singles application to various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Deployment Options](#deployment-options)
4. [Vercel Deployment](#vercel-deployment)
5. [Docker Deployment](#docker-deployment)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Monitoring and Health Checks](#monitoring-and-health-checks)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools

- Node.js 18+ and npm
- Git
- Docker (for containerized deployment)
- Vercel CLI (for Vercel deployment)

### Installation

```bash
# Install Node.js dependencies
npm install

# Install Vercel CLI globally
npm install -g vercel

# Install Docker (platform-specific)
# Visit https://docs.docker.com/get-docker/
```

## Environment Configuration

### Environment Variables

Create the following environment files:

#### `.env.local` (Development)
```env
NEXT_PUBLIC_APP_NAME=AI-fitness-singles
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
AUTH_SECRET=your-development-secret
```

#### `.env.production` (Production)
```env
NEXT_PUBLIC_APP_NAME=AI-fitness-singles
NEXT_PUBLIC_APP_URL=https://ai-fitness-singles.vercel.app
NEXT_PUBLIC_API_BASE_URL=https://workout-cool-api.herokuapp.com
AUTH_SECRET=your-production-secret
DATABASE_URL=postgresql://username:password@hostname:port/database
```

### Required Secrets

For production deployment, ensure these secrets are configured:

- `AUTH_SECRET`: Strong random string for authentication
- `DATABASE_URL`: Database connection string (if using database sessions)
- `VERCEL_TOKEN`: Vercel deployment token
- `VERCEL_ORG_ID`: Vercel organization ID
- `VERCEL_PROJECT_ID`: Vercel project ID

## Deployment Options

### Quick Deployment Commands

```bash
# Deploy to Vercel (recommended)
npm run deploy:vercel

# Deploy using Docker
npm run deploy:docker

# Build Docker image only
npm run docker:build
```

### Manual Deployment Script

```bash
# Make script executable
chmod +x scripts/deploy.sh

# Deploy to different environments
./scripts/deploy.sh production vercel
./scripts/deploy.sh staging docker
./scripts/deploy.sh development docker-build
```

## Vercel Deployment

### Automatic Deployment

1. **Connect Repository**
   ```bash
   vercel --prod
   ```

2. **Configure Environment Variables**
   - Go to Vercel Dashboard → Project Settings → Environment Variables
   - Add all required environment variables for production

3. **Custom Domain** (Optional)
   - Add custom domain in Vercel Dashboard
   - Configure DNS records as instructed

### Manual Deployment

```bash
# Login to Vercel
vercel login

# Deploy to production
vercel --prod

# Deploy to preview
vercel
```

### Vercel Configuration

The `vercel.json` file includes:
- Security headers
- Redirects and rewrites
- Function configuration
- Cron jobs setup

## Docker Deployment

### Single Container

```bash
# Build image
docker build -t ai-fitness-singles .

# Run container
docker run -p 3000:3000 \
  -e NODE_ENV=production \
  -e AUTH_SECRET=your-secret \
  ai-fitness-singles
```

### Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Docker Setup

```bash
# Build for production
docker build -t ai-fitness-singles:latest .

# Tag for registry
docker tag ai-fitness-singles:latest your-registry/ai-fitness-singles:latest

# Push to registry
docker push your-registry/ai-fitness-singles:latest
```

## CI/CD Pipeline

### GitHub Actions

The `.github/workflows/deploy.yml` file provides:

1. **Automated Testing**
   - Type checking
   - Linting
   - Unit tests
   - Security scanning

2. **Multi-Environment Deployment**
   - Vercel for main branch
   - Docker builds for all branches
   - Production deployment for production branch

3. **Quality Checks**
   - Lighthouse performance audits
   - Security vulnerability scanning
   - Bundle size analysis

### Required GitHub Secrets

```
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id
PRODUCTION_HOST=your-server-ip
PRODUCTION_USER=your-username
PRODUCTION_SSH_KEY=your-private-key
SLACK_WEBHOOK=your-slack-webhook
SNYK_TOKEN=your-snyk-token
```

### Manual CI/CD Trigger

```bash
# Trigger deployment
git push origin main

# Trigger production deployment
git push origin production
```

## Monitoring and Health Checks

### Health Check Endpoint

The application includes a comprehensive health check at `/api/health`:

```bash
# Check application health
curl https://ai-fitness-singles.vercel.app/api/health

# Local health check
npm run health-check
```

### Health Check Response

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "production",
  "uptime": 3600,
  "memory": {
    "used": 45,
    "total": 128,
    "external": 12
  },
  "checks": {
    "api": { "status": "healthy", "responseTime": 150 },
    "database": { "status": "healthy", "responseTime": 50 },
    "external": { "status": "healthy", "services": {...} }
  }
}
```

### Monitoring Setup

1. **Uptime Monitoring**
   - Configure monitoring service to check `/api/health`
   - Set up alerts for downtime

2. **Performance Monitoring**
   - Lighthouse CI for performance metrics
   - Real User Monitoring (RUM) tools

3. **Error Tracking**
   - Sentry for error monitoring
   - Application logs analysis

## Troubleshooting

### Common Issues

#### Build Failures

```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build
```

#### Environment Variable Issues

```bash
# Check environment variables
printenv | grep NEXT_PUBLIC

# Verify .env files
cat .env.local
cat .env.production
```

#### Docker Issues

```bash
# Check Docker logs
docker logs container-name

# Debug container
docker exec -it container-name /bin/sh

# Clean Docker system
docker system prune -a
```

#### Vercel Deployment Issues

```bash
# Check deployment logs
vercel logs

# Redeploy
vercel --prod --force
```

### Performance Issues

1. **Bundle Size Analysis**
   ```bash
   npm run analyze
   ```

2. **Lighthouse Audit**
   ```bash
   npx lighthouse https://your-domain.com --output html
   ```

3. **Memory Usage**
   - Monitor `/api/health` endpoint
   - Check Docker container stats

### Security Issues

1. **Security Headers**
   - Verify headers in browser dev tools
   - Use security header analyzers

2. **Dependency Vulnerabilities**
   ```bash
   npm audit
   npm audit fix
   ```

3. **Environment Security**
   - Rotate secrets regularly
   - Use secure secret management

## Support

For deployment issues:

1. Check the health endpoint: `/api/health`
2. Review application logs
3. Verify environment configuration
4. Check external service dependencies
5. Contact the development team

## Additional Resources

- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Vercel Documentation](https://vercel.com/docs)
- [Docker Documentation](https://docs.docker.com/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
