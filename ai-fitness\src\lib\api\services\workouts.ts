/**
 * Workout API Service
 * Handles all workout-related API calls
 */

import { apiClient } from '../client';
import { API_CONFIG } from '../config';
import type {
  WorkoutSession,
  Program,
  CreateWorkoutSessionData,
  PaginatedResponse
} from '../types';

export class WorkoutService {
  /**
   * Get user's workout sessions with optional filtering
   */
  static async getWorkoutSessions(params: {
    limit?: number;
    offset?: number;
    status?: string;
    programId?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<PaginatedResponse<WorkoutSession>> {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());
    if (params.status) searchParams.append('status', params.status);
    if (params.programId) searchParams.append('programId', params.programId);
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);

    const queryString = searchParams.toString();
    const url = queryString 
      ? `${API_CONFIG.ENDPOINTS.WORKOUTS.LIST}?${queryString}`
      : API_CONFIG.ENDPOINTS.WORKOUTS.LIST;

    return apiClient.get<PaginatedResponse<WorkoutSession>>(url);
  }

  /**
   * Get workout session by ID
   */
  static async getWorkoutSession(id: string): Promise<WorkoutSession> {
    return apiClient.get<WorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id));
  }

  /**
   * Create a new workout session
   */
  static async createWorkoutSession(data: CreateWorkoutSessionData): Promise<WorkoutSession> {
    return apiClient.post<WorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.CREATE, data);
  }

  /**
   * Update workout session
   */
  static async updateWorkoutSession(
    id: string,
    data: Partial<CreateWorkoutSessionData>
  ): Promise<WorkoutSession> {
    return apiClient.patch<WorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.UPDATE(id), data);
  }

  /**
   * Delete workout session
   */
  static async deleteWorkoutSession(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.WORKOUTS.DELETE(id));
  }

  /**
   * Start a workout session
   */
  static async startWorkoutSession(id: string): Promise<WorkoutSession> {
    return apiClient.post<WorkoutSession>(`${API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id)}/start`);
  }

  /**
   * Complete a workout session
   */
  static async completeWorkoutSession(
    id: string, 
    data: { duration: number; notes?: string }
  ): Promise<WorkoutSession> {
    return apiClient.post<WorkoutSession>(
      `${API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id)}/complete`, 
      data
    );
  }

  /**
   * Get workout programs
   */
  static async getWorkoutPrograms(params: {
    limit?: number;
    offset?: number;
    category?: string;
    difficulty?: string;
    duration?: string;
  } = {}): Promise<PaginatedResponse<Program>> {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());
    if (params.category) searchParams.append('category', params.category);
    if (params.difficulty) searchParams.append('difficulty', params.difficulty);
    if (params.duration) searchParams.append('duration', params.duration);

    const queryString = searchParams.toString();
    const url = queryString 
      ? `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${queryString}`
      : API_CONFIG.ENDPOINTS.PROGRAMS.LIST;

    return apiClient.get<PaginatedResponse<Program>>(url);
  }

  /**
   * Get workout program by ID
   */
  static async getWorkoutProgram(id: string): Promise<Program> {
    return apiClient.get<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id));
  }

  /**
   * Create a new workout program
   */
  static async createWorkoutProgram(data: any): Promise<Program> {
    return apiClient.post<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.CREATE, data);
  }

  /**
   * Update workout program
   */
  static async updateWorkoutProgram(
    id: string,
    data: any
  ): Promise<Program> {
    return apiClient.patch<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.UPDATE(id), data);
  }

  /**
   * Delete workout program
   */
  static async deleteWorkoutProgram(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRAMS.DELETE(id));
  }

  /**
   * Join a workout program
   */
  static async joinWorkoutProgram(id: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id)}/join`);
  }

  /**
   * Leave a workout program
   */
  static async leaveWorkoutProgram(id: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id)}/leave`);
  }

  /**
   * Get user's joined programs
   */
  static async getUserPrograms(): Promise<Program[]> {
    const response = await apiClient.get<{ programs: Program[] }>('/api/user/programs');
    return response.programs || [];
  }

  /**
   * Get popular workout programs
   */
  static async getPopularPrograms(limit = 10): Promise<Program[]> {
    const searchParams = new URLSearchParams({
      sort: 'popular',
      limit: limit.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
    const response = await apiClient.get<PaginatedResponse<Program>>(url);
    return response.data || [];
  }

  /**
   * Get recommended workout programs for user
   */
  static async getRecommendedPrograms(limit = 6): Promise<Program[]> {
    const searchParams = new URLSearchParams({
      recommended: 'true',
      limit: limit.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
    const response = await apiClient.get<PaginatedResponse<Program>>(url);
    return response.data || [];
  }

  /**
   * Generate AI workout plan
   */
  static async generateAIWorkout(preferences: {
    goals: string[];
    equipment: string[];
    duration: number;
    difficulty: string;
    muscleGroups: string[];
  }): Promise<WorkoutSession> {
    return apiClient.post<WorkoutSession>('/api/workouts/generate', preferences);
  }

  /**
   * Get workout statistics
   */
  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    totalWorkouts: number;
    totalDuration: number;
    averageDuration: number;
    caloriesBurned: number;
    streakDays: number;
    completionRate: number;
    favoriteExercises: Array<{ name: string; count: number }>;
    weeklyProgress: Array<{ date: string; workouts: number; duration: number }>;
  }> {
    return apiClient.get(`/api/workouts/stats?period=${period}`);
  }

  /**
   * Get workout history
   */
  static async getWorkoutHistory(params: {
    limit?: number;
    offset?: number;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<PaginatedResponse<WorkoutSession>> {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);

    const queryString = searchParams.toString();
    const url = queryString 
      ? `/api/workouts/history?${queryString}`
      : '/api/workouts/history';

    return apiClient.get<PaginatedResponse<WorkoutSession>>(url);
  }
}
