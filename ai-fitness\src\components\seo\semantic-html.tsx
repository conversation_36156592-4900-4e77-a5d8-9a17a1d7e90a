import React from 'react';
import { cn } from '@/lib/utils';

// Semantic article component
interface ArticleProps {
  children: React.ReactNode;
  className?: string;
  itemScope?: boolean;
  itemType?: string;
}

export function Article({ children, className, itemScope, itemType }: ArticleProps) {
  return (
    <article 
      className={cn(className)}
      itemScope={itemScope}
      itemType={itemType}
    >
      {children}
    </article>
  );
}

// Semantic section component
interface SectionProps {
  children: React.ReactNode;
  className?: string;
  ariaLabel?: string;
  ariaLabelledBy?: string;
  role?: string;
}

export function Section({ children, className, ariaLabel, ariaLabelledBy, role }: SectionProps) {
  return (
    <section 
      className={cn(className)}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      role={role}
    >
      {children}
    </section>
  );
}

// Semantic header component
interface HeaderProps {
  children: React.ReactNode;
  className?: string;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  id?: string;
}

export function SemanticHeader({ children, className, level = 1, id }: HeaderProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  return (
    <Tag 
      className={cn(className)}
      id={id}
    >
      {children}
    </Tag>
  );
}

// Semantic navigation component
interface NavProps {
  children: React.ReactNode;
  className?: string;
  ariaLabel: string;
  role?: string;
}

export function Nav({ children, className, ariaLabel, role = 'navigation' }: NavProps) {
  return (
    <nav 
      className={cn(className)}
      aria-label={ariaLabel}
      role={role}
    >
      {children}
    </nav>
  );
}

// Semantic main content component
interface MainProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

export function Main({ children, className, id = 'main-content' }: MainProps) {
  return (
    <main 
      className={cn(className)}
      id={id}
      role="main"
    >
      {children}
    </main>
  );
}

// Semantic aside component
interface AsideProps {
  children: React.ReactNode;
  className?: string;
  ariaLabel?: string;
}

export function Aside({ children, className, ariaLabel }: AsideProps) {
  return (
    <aside 
      className={cn(className)}
      aria-label={ariaLabel}
    >
      {children}
    </aside>
  );
}

// Semantic footer component
interface FooterProps {
  children: React.ReactNode;
  className?: string;
  role?: string;
}

export function Footer({ children, className, role = 'contentinfo' }: FooterProps) {
  return (
    <footer 
      className={cn(className)}
      role={role}
    >
      {children}
    </footer>
  );
}

// Semantic figure component for images with captions
interface FigureProps {
  children: React.ReactNode;
  className?: string;
  caption?: string;
}

export function Figure({ children, className, caption }: FigureProps) {
  return (
    <figure className={cn(className)}>
      {children}
      {caption && (
        <figcaption className="text-sm text-gray-600 mt-2">
          {caption}
        </figcaption>
      )}
    </figure>
  );
}

// Semantic time component
interface TimeProps {
  dateTime: string;
  children: React.ReactNode;
  className?: string;
}

export function Time({ dateTime, children, className }: TimeProps) {
  return (
    <time 
      dateTime={dateTime}
      className={cn(className)}
    >
      {children}
    </time>
  );
}

// Semantic address component
interface AddressProps {
  children: React.ReactNode;
  className?: string;
}

export function Address({ children, className }: AddressProps) {
  return (
    <address className={cn(className)}>
      {children}
    </address>
  );
}

// Semantic definition list for key-value pairs
interface DefinitionListProps {
  items: Array<{ term: string; definition: string | React.ReactNode }>;
  className?: string;
}

export function DefinitionList({ items, className }: DefinitionListProps) {
  return (
    <dl className={cn(className)}>
      {items.map((item, index) => (
        <div key={index} className="mb-2">
          <dt className="font-semibold">{item.term}</dt>
          <dd className="ml-4">{item.definition}</dd>
        </div>
      ))}
    </dl>
  );
}

// Semantic blockquote component
interface BlockquoteProps {
  children: React.ReactNode;
  cite?: string;
  author?: string;
  className?: string;
}

export function Blockquote({ children, cite, author, className }: BlockquoteProps) {
  return (
    <blockquote 
      className={cn("border-l-4 border-blue-500 pl-4 italic", className)}
      cite={cite}
    >
      {children}
      {author && (
        <footer className="text-sm text-gray-600 mt-2">
          — <cite>{author}</cite>
        </footer>
      )}
    </blockquote>
  );
}

// Semantic mark component for highlighting
interface MarkProps {
  children: React.ReactNode;
  className?: string;
}

export function Mark({ children, className }: MarkProps) {
  return (
    <mark className={cn("bg-yellow-200 px-1", className)}>
      {children}
    </mark>
  );
}

// Semantic abbreviation component
interface AbbrProps {
  children: React.ReactNode;
  title: string;
  className?: string;
}

export function Abbr({ children, title, className }: AbbrProps) {
  return (
    <abbr 
      title={title}
      className={cn("border-b border-dotted border-gray-400 cursor-help", className)}
    >
      {children}
    </abbr>
  );
}
