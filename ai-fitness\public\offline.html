<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - AI-fitness-singles</title>
    <meta name="description" content="You are currently offline. AI-fitness-singles will work again when your connection is restored.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            width: 100%;
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .tips {
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            text-align: left;
        }
        
        .tips h3 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .tips ul {
            list-style: none;
        }
        
        .tips li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        
        .tips li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        
        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .online {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        @media (max-width: 480px) {
            h1 {
                font-size: 2rem;
            }
            
            .offline-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="offline-icon">
            📱
        </div>
        
        <h1>You're Offline</h1>
        
        <p>
            It looks like you've lost your internet connection. Don't worry - 
            AI-fitness-singles will work again as soon as your connection is restored.
        </p>
        
        <div>
            <button class="retry-button" onclick="window.location.reload()">
                Try Again
            </button>
            <a href="/" class="retry-button">
                Go Home
            </a>
        </div>
        
        <div class="connection-status" id="connectionStatus">
            <span id="statusText">Checking connection...</span>
        </div>
        
        <div class="tips">
            <h3>While you wait, here are some tips:</h3>
            <ul>
                <li>Check your WiFi or mobile data connection</li>
                <li>Try moving to an area with better signal</li>
                <li>Restart your router if using WiFi</li>
                <li>Some cached content may still be available</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status online';
                statusText.textContent = '✅ Connection restored! You can refresh the page.';
            } else {
                statusElement.className = 'connection-status offline';
                statusText.textContent = '❌ Still offline. Waiting for connection...';
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', function() {
            updateConnectionStatus();
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        });
        
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial check
        updateConnectionStatus();
        
        // Periodic check every 5 seconds
        setInterval(updateConnectionStatus, 5000);
        
        // Auto-retry every 30 seconds
        setInterval(() => {
            if (navigator.onLine) {
                window.location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
