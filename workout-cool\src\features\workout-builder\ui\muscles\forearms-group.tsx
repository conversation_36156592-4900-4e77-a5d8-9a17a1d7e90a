import React from "react";
import { ExerciseAttributeValueEnum } from "@prisma/client";

export const ForearmsGroup = ({
  onToggleMuscle,
  getMuscleClasses,
}: {
  onToggleMuscle: (muscle: ExerciseAttributeValueEnum) => void;
  getMuscleClasses: (muscle: ExerciseAttributeValueEnum) => string;
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(ExerciseAttributeValueEnum.FOREARMS)}>
      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 355.58,156.70
           C 355.59,156.41 355.36,155.93 355.14,155.87
             354.89,155.80 354.44,156.04 354.24,156.27
             353.09,157.56 351.97,158.87 350.89,160.21
             349.34,162.13 347.83,164.08 346.29,166.01
             343.77,169.18 342.67,172.95 341.43,176.65
             338.78,184.50 336.21,192.37 333.61,200.23
             333.56,200.37 333.47,200.49 333.41,200.62
             333.41,200.62 333.22,200.56 333.22,200.56
             333.93,198.29 334.63,196.01 335.37,193.75
             337.89,185.98 340.45,178.23 342.93,170.45
             343.64,168.21 345.02,166.40 346.37,164.53
             347.10,163.53 347.70,162.43 348.24,161.32
             348.74,160.29 348.53,159.30 347.48,158.68
             346.54,158.12 345.55,157.52 344.49,157.26
             343.88,157.12 343.42,157.14 343.10,157.31
             343.10,157.31 343.10,157.31 343.10,157.31
             343.10,157.31 343.08,157.33 343.08,157.33
             342.97,157.39 342.88,157.48 342.81,157.58
             342.81,157.58 340.41,159.83 340.41,159.83
             339.15,160.69 337.92,161.59 336.53,162.57
             336.53,162.57 333.38,166.06 333.38,166.06
             332.94,166.53 332.53,167.03 332.12,167.52
             327.93,172.51 326.70,178.25 326.95,184.54
             327.20,190.89 326.96,197.24 325.80,203.51
             325.66,203.51 325.52,203.50 325.38,203.50
             325.49,199.61 325.66,195.72 325.69,191.83
             325.71,189.79 325.50,187.74 325.37,185.71
             325.35,185.36 325.33,184.98 325.16,184.69
             324.95,184.33 324.59,184.05 324.29,183.73
             324.05,184.06 323.71,184.35 323.60,184.71
             322.71,187.71 322.00,190.76 320.98,193.70
             318.82,199.88 316.52,206.00 314.26,212.14
             313.85,213.26 314.07,213.77 315.21,213.55
             319.80,212.64 323.64,214.58 327.53,216.36
             328.39,216.75 328.98,216.80 329.48,215.84
             333.40,208.28 337.44,200.78 341.28,193.19
             344.89,186.07 347.98,178.75 350.48,171.20
             350.94,169.78 351.85,168.51 352.52,167.15
             353.47,165.20 354.59,163.29 355.25,161.25
             355.71,159.84 355.54,158.22 355.58,156.70"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path94"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 328.92,170.86
           C 331.73,166.23 335.11,161.99 338.87,158.01
             339.52,157.32 339.80,156.59 339.61,155.68
             339.18,153.57 338.81,151.44 338.37,149.33
             338.28,148.91 338.00,148.53 337.81,148.13
             337.65,148.13 337.49,148.12 337.33,148.12
             332.60,155.30 328.07,162.55 327.92,171.34
             328.04,171.41 328.15,171.47 328.27,171.54
             328.49,171.31 328.76,171.12 328.92,170.86"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path92"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 507.15,213.55
           C 508.29,213.77 508.51,213.26 508.10,212.14
             505.85,206.00 503.54,199.88 501.38,193.70
             500.36,190.76 499.65,187.71 498.76,184.71
             498.65,184.35 498.31,184.06 498.08,183.73
             497.78,184.05 497.41,184.33 497.20,184.69
             497.03,184.98 497.01,185.36 496.99,185.71
             496.86,187.74 496.65,189.79 496.67,191.83
             496.70,195.72 496.87,199.61 496.98,203.50
             496.84,203.50 496.70,203.51 496.56,203.51
             495.41,197.24 495.16,190.89 495.41,184.54
             495.66,178.25 494.43,172.51 490.25,167.52
             489.97,167.19 489.70,166.86 489.42,166.54
             489.42,166.54 489.42,166.54 489.42,166.54
             489.28,166.37 489.13,166.22 488.98,166.06
             488.98,166.06 485.83,162.57 485.83,162.57
             484.44,161.59 483.21,160.69 481.95,159.83
             481.95,159.83 479.55,157.58 479.55,157.58
             479.48,157.48 479.39,157.39 479.28,157.33
             479.28,157.33 479.26,157.31 479.26,157.31
             479.26,157.31 479.26,157.31 479.26,157.31
             478.94,157.14 478.48,157.12 477.87,157.26
             476.81,157.52 475.82,158.12 474.88,158.68
             473.83,159.30 473.62,160.29 474.12,161.32
             474.66,162.43 475.26,163.53 475.99,164.53
             477.34,166.40 478.72,168.21 479.43,170.45
             481.91,178.23 484.47,185.98 486.99,193.75
             487.73,196.01 488.43,198.29 489.14,200.56
             489.08,200.58 489.02,200.60 488.96,200.62
             488.89,200.49 488.80,200.37 488.75,200.23
             486.15,192.37 483.58,184.50 480.94,176.65
             479.69,172.95 478.59,169.18 476.07,166.01
             474.53,164.08 473.02,162.13 471.47,160.21
             470.39,158.87 469.27,157.56 468.12,156.27
             467.92,156.04 467.47,155.80 467.22,155.87
             467.00,155.93 466.77,156.41 466.78,156.70
             466.83,158.22 466.65,159.84 467.11,161.25
             467.77,163.29 468.89,165.20 469.84,167.15
             470.51,168.51 471.42,169.78 471.89,171.20
             474.38,178.75 477.47,186.07 481.08,193.19
             484.92,200.78 488.96,208.28 492.89,215.84
             493.38,216.80 493.97,216.75 494.83,216.36
             498.72,214.58 502.56,212.64 507.15,213.55"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path86"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 483.49,158.01
           C 487.25,161.99 490.63,166.23 493.44,170.86
             493.60,171.12 493.87,171.31 494.09,171.54
             494.21,171.47 494.32,171.41 494.44,171.34
             494.30,162.55 489.77,155.30 485.03,148.12
             484.87,148.12 484.72,148.13 484.56,148.13
             484.36,148.53 484.08,148.91 483.99,149.33
             483.55,151.44 483.18,153.57 482.75,155.68
             482.57,156.59 482.84,157.32 483.49,158.01"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path80"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 43.75,147.00
           C 43.75,147.00 37.50,154.25 37.50,154.25
             37.50,154.25 34.25,163.00 34.25,163.00
             34.25,163.00 32.00,173.00 32.00,173.00
             32.00,173.00 30.75,183.25 30.75,183.25
             30.75,183.25 27.75,195.75 27.75,195.75
             27.75,195.75 22.50,207.50 22.50,207.50
             22.50,207.50 20.00,213.50 20.00,213.50
             20.00,213.50 22.00,213.75 22.00,213.75
             22.00,213.75 26.75,212.00 26.75,211.75
             26.75,211.50 33.25,203.50 33.25,203.50
             33.25,203.50 39.00,196.25 39.00,196.25
             39.00,196.25 36.75,213.75 36.75,213.75
             36.75,213.75 37.50,222.25 37.50,222.25
             37.50,222.25 37.75,224.25 37.75,224.25
             37.75,224.25 41.00,221.25 41.00,221.25
             41.00,221.25 47.75,210.25 47.75,210.25
             47.75,210.25 54.25,195.75 54.25,195.75
             54.25,195.75 62.00,173.75 62.00,173.75M 63.75,166.75
           C 63.75,166.75 66.25,161.25 66.25,161.25
             66.25,161.25 66.25,153.50 66.25,153.50
             66.25,153.50 63.00,150.75 63.00,150.75
             63.00,150.75 59.00,155.50 59.00,155.50
             59.00,155.50 50.75,166.00 50.75,166.00
             50.75,166.00 50.00,157.50 50.00,157.50
             50.00,157.50 48.25,150.25 48.25,150.25
             48.25,150.25 46.75,148.75 46.75,148.75
             46.75,148.75 43.50,146.75 43.50,146.75"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 168.50,176.75
           C 168.50,176.75 171.50,183.25 171.50,183.25
             171.50,183.25 175.50,195.75 175.50,195.75
             175.50,195.75 184.50,212.50 184.50,212.50
             184.50,212.50 191.00,227.00 191.00,227.00
             191.00,227.00 192.25,220.50 192.25,220.50
             192.25,220.50 192.25,213.25 192.25,213.25
             192.25,213.25 191.25,200.00 191.25,200.00
             191.25,200.00 188.75,193.25 188.75,193.25
             188.75,193.25 200.00,207.00 200.00,207.00
             200.00,207.00 205.00,212.00 205.00,212.00
             205.00,212.00 210.00,213.50 210.00,213.50
             210.00,213.50 206.50,207.25 206.50,207.25
             206.50,207.25 201.75,194.50 201.75,194.50
             201.75,194.50 198.75,183.25 198.75,183.25
             198.75,183.25 197.25,168.25 197.25,168.25
             197.25,168.25 192.75,158.50 192.75,158.50
             192.75,158.50 189.00,151.50 189.00,151.50
             189.00,151.50 186.50,148.50 186.50,148.50
             186.50,148.50 183.25,148.50 183.25,148.50
             183.25,148.50 181.50,152.50 181.50,152.50
             181.50,152.50 180.50,155.75 180.50,155.75
             180.50,155.75 180.75,159.25 180.75,159.25
             180.75,159.25 179.25,167.75 179.25,167.75
             179.25,167.75 173.25,158.50 173.25,158.50
             173.25,158.50 167.00,152.25 167.00,152.25
             167.00,152.25 164.00,152.00 164.00,152.00
             164.00,152.00 164.00,155.00 164.00,155.00
             164.00,155.00 165.25,164.00 165.25,164.00
             165.25,164.00 167.25,175.75 167.25,175.75"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 334.25,147.25
           C 334.25,147.25 326.75,161.25 326.75,161.25
             326.75,161.25 324.25,172.50 324.25,172.50
             324.25,172.50 324.00,180.00 324.00,180.00
             324.00,180.00 318.75,189.25 318.75,189.25
             318.75,189.25 315.25,200.50 315.25,200.75
             315.25,201.00 310.50,214.25 310.50,214.25
             310.50,214.25 317.25,214.25 317.25,214.25
             317.25,214.25 325.75,217.00 325.75,217.00
             325.75,217.00 329.75,218.75 329.75,218.75
             329.75,218.75 335.25,213.75 335.25,213.75
             332.22,215.34 348.00,186.50 348.00,186.50
             348.00,186.50 356.75,167.00 356.75,167.00
             356.75,167.00 358.25,155.50 358.25,155.50
             358.25,155.50 355.50,154.50 355.50,154.50
             355.50,154.50 350.00,156.50 350.00,156.50
             350.00,156.50 342.75,155.25 342.75,155.25
             342.75,155.25 340.25,153.25 340.25,153.25
             340.25,153.25 338.00,146.25 338.00,146.25
             338.00,146.25 333.75,147.50 333.75,147.50"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 464.50,162.00
           C 464.50,162.00 485.75,208.75 485.75,208.75
             485.75,208.75 491.00,215.75 491.00,215.75
             491.00,215.75 492.00,218.25 492.00,218.25
             492.00,218.25 498.50,215.75 498.50,215.75
             498.50,215.75 510.25,214.25 510.25,214.25
             510.25,214.25 510.25,211.25 510.25,211.25
             510.25,211.25 506.00,196.50 506.00,196.50
             506.00,196.50 500.00,182.25 500.00,182.25
             500.00,182.25 498.50,178.25 498.50,178.25
             498.50,178.25 497.25,162.75 497.25,162.75
             497.25,162.75 492.75,153.50 492.75,153.50
             492.75,153.50 487.50,146.25 487.50,146.25
             487.50,146.25 485.50,145.00 485.50,145.00
             485.50,145.00 483.25,150.00 483.25,150.00
             483.25,150.00 481.25,154.00 481.25,154.00
             481.25,154.00 472.50,156.75 472.50,156.75
             472.50,156.75 464.75,155.00 464.75,155.00
             464.75,155.00 463.75,157.50 463.75,157.50
             463.75,157.50 463.50,159.50 464.50,162.25"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 184.01,182.16
           C 182.46,177.62 180.92,173.06 179.01,168.66
             177.34,164.81 174.77,161.45 172.04,158.24
             170.81,156.80 169.62,155.31 168.35,153.90
             168.03,153.55 167.33,153.14 167.04,153.27
             166.62,153.46 166.28,154.08 166.15,154.58
             165.35,157.56 165.82,160.46 167.22,163.12
             169.42,167.29 169.45,171.29 170.98,175.72
             173.32,182.49 176.47,190.34 179.17,196.97
             182.25,204.56 186.54,211.29 189.65,218.87
             189.89,219.45 190.15,220.02 190.40,220.59
             190.54,220.56 190.68,220.53 190.83,220.50
             190.89,219.68 191.04,218.87 191.01,218.05
             190.61,205.71 187.98,193.80 184.01,182.16"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path154"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 182.33,152.10
           C 180.80,155.22 180.88,158.50 181.14,161.83
             182.08,174.13 186.40,185.26 192.66,195.74
             195.54,200.56 198.76,205.15 202.46,209.38
             203.03,210.03 203.78,210.53 204.45,211.10
             204.57,210.98 204.69,210.87 204.80,210.76
             204.43,210.11 204.02,209.49 203.69,208.81
             199.12,199.31 196.21,189.39 195.39,178.81
             194.62,168.95 191.67,159.76 185.65,151.73
             184.29,149.90 183.34,150.04 182.33,152.10"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path156"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 62.31,163.12
           C 63.71,160.46 64.19,157.56 63.39,154.58
             63.25,154.08 62.91,153.46 62.49,153.27
             62.20,153.14 61.50,153.55 61.18,153.90
             59.91,155.31 58.73,156.80 57.49,158.24
             54.77,161.45 52.19,164.81 50.52,168.66
             48.61,173.06 47.07,177.62 45.52,182.16
             41.56,193.80 38.92,205.71 38.52,218.05
             38.50,218.87 38.64,219.68 38.71,220.50
             38.85,220.53 38.99,220.56 39.14,220.59
             39.39,220.02 39.65,219.45 39.88,218.87
             43.00,211.29 47.28,204.56 50.36,196.97
             53.06,190.34 56.21,182.49 58.55,175.72
             60.09,171.29 60.12,167.29 62.31,163.12"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path144"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.FOREARMS)}
        d="M 27.08,209.38
           C 30.78,205.15 33.99,200.56 36.87,195.74
             43.13,185.26 47.45,174.13 48.40,161.83
             48.65,158.50 48.73,155.22 47.21,152.10
             46.20,150.04 45.25,149.90 43.88,151.73
             37.87,159.76 34.92,168.95 34.15,178.81
             33.32,189.39 30.42,199.31 25.84,208.81
             25.52,209.49 25.10,210.11 24.73,210.76
             24.85,210.87 24.96,210.98 25.08,211.10
             25.75,210.53 26.50,210.03 27.08,209.38"
        data-elem={ExerciseAttributeValueEnum.FOREARMS}
        id="path146"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
};
