(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18940:(e,s,r)=>{Promise.resolve().then(r.bind(r,58061))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},58061:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>h});var t=r(60687),a=r(44493),i=r(29523),n=r(58559),l=r(41923),d=r(28947),c=r(86561),o=r(40228),x=r(48730),m=r(97840),u=r(25541);function h(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome back, Alex! \uD83D\uDC4B"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Ready to crush your fitness goals today?"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Workouts This Week"}),(0,t.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"4"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2 from last week"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Calories Burned"}),(0,t.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"1,247"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"+180 from yesterday"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Current Streak"}),(0,t.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"12 days"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Personal best!"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Total Workouts"}),(0,t.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"127"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Since joining"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),"Today's Workout"]}),(0,t.jsx)(a.BT,{children:"Upper Body Strength Training"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Push-up Variations"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"3 sets \xd7 12 reps"})]}),(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Dumbbell Rows"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"3 sets \xd7 10 reps"})]}),(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Shoulder Press"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"3 sets \xd7 8 reps"})]}),(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})]}),(0,t.jsxs)(i.$,{className:"w-full",size:"lg",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]})]})})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5"}),"Weekly Progress"]})}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Workout Goal"}),(0,t.jsx)("span",{children:"4/5"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"80%"}})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Calorie Goal"}),(0,t.jsx)("span",{children:"1247/1500"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"83%"}})})]})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsx)(a.ZB,{children:"Quick Actions"})}),(0,t.jsxs)(a.Wu,{className:"space-y-3",children:[(0,t.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Log Quick Workout"]}),(0,t.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Update Goals"]}),(0,t.jsxs)(i.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"View Progress"]})]})]})]})]})]})})}},59767:(e,s,r)=>{Promise.resolve().then(r.bind(r,80559))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\dashboard\\page.tsx","default")},89698:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\dashboard\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[96,76],()=>r(89698));module.exports=t})();