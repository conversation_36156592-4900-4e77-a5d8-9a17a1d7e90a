import Link from "next/link";

# 关于 Workout.cool

## 为什么选择 Workout.cool？

Workout.cool 诞生于在原项目 <WorkoutLol variant="muted" /> 被放弃后，为用户提供一个可靠、现代化且持续维护的健身平台的愿望。

## 我们的故事

Workout.cool 是社区驱动的成果。

我是 <WorkoutLol variant="muted" /> 项目的**第一位开源贡献者**。

这意味着我见证了这个项目的*诞生*、*成长*，然后被**出售**，最终被新所有者**抛弃**。

和许多用户一样，我感到**深深的沮丧**和*被抛弃的感觉*，看着一个我贡献了这么多的工具消失，功能请求得不到回应并逐渐过时。

---

*几个月来*，我试图联系新所有者——尽管尝试了很多次（*大约15次*），但**从未收到过任何回复**。

面对这种**沉默**和**社区的困境**，我决定**自己动手**：

> 与其让所有这些工作消失，**我重新启动了一个更加雄心勃勃、现代化、对所有人开放的项目。**

这个项目不是由利润驱动，而是由**激情**和为开源健身社区服务的愿望驱动。

**必须有人来拯救这个社区——_我决定成为那个人！_**

## 开源与社区

Workout.cool 是开源的，确保透明度、模块化和可扩展性。  
欢迎所有人贡献——代码、文档或想法！

- [在 GitHub 上查看项目](https://github.com/Snouzy/workout-cool)
- [买杯咖啡支持我们](https://ko-fi.com/workoutcool)

## 加入我们的使命！

想要贡献、建议功能或仅仅支持项目？  
联系我们或在 GitHub 上创建 issue！

**[<EMAIL>](mailto:<EMAIL>)**