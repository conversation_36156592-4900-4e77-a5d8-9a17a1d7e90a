{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e4842bbd.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_e4842bbd-module__r7o-_q__className\",\n  \"variable\": \"inter_e4842bbd-module__r7o-_q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e4842bbd.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/providers/app-providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppProviders = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProviders() from the server but AppProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/app-providers.tsx <module evaluation>\",\n    \"AppProviders\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,4EACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/providers/app-providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppProviders = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProviders() from the server but AppProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/app-providers.tsx\",\n    \"AppProviders\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wDACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/seo/config.ts"], "sourcesContent": ["import { Metadata } from 'next';\n\n// Base SEO configuration\nexport const siteConfig = {\n  name: 'AI-fitness-singles',\n  title: 'AI-fitness-singles - Smart Fitness Platform for Singles',\n  description: 'AI-powered fitness platform designed for singles. Create personalized workout plans, access comprehensive exercise database, track progress with detailed analytics, and achieve your fitness goals.',\n  url: process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app',\n  ogImage: '/images/og-image.jpg',\n  creator: 'AI-fitness-singles Team',\n  keywords: [\n    'fitness',\n    'workout',\n    'exercise',\n    'AI fitness',\n    'personal trainer',\n    'fitness tracking',\n    'workout plans',\n    'fitness for singles',\n    'health',\n    'wellness',\n    'strength training',\n    'cardio',\n    'fitness analytics',\n    'progress tracking'\n  ],\n  authors: [\n    {\n      name: 'AI-fitness-singles',\n      url: process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app',\n    }\n  ],\n  social: {\n    twitter: '@aifitnesssingle',\n    facebook: 'aifitnessingles',\n    instagram: 'aifitnessingles',\n    youtube: '@aifitnessingles'\n  }\n};\n\n// Default metadata for the application\nexport const defaultMetadata: Metadata = {\n  title: {\n    default: siteConfig.title,\n    template: `%s | ${siteConfig.name}`\n  },\n  description: siteConfig.description,\n  keywords: siteConfig.keywords,\n  authors: siteConfig.authors,\n  creator: siteConfig.creator,\n  metadataBase: new URL(siteConfig.url),\n  alternates: {\n    canonical: '/',\n  },\n  openGraph: {\n    type: 'website',\n    locale: 'en_US',\n    url: siteConfig.url,\n    title: siteConfig.title,\n    description: siteConfig.description,\n    siteName: siteConfig.name,\n    images: [\n      {\n        url: siteConfig.ogImage,\n        width: 1200,\n        height: 630,\n        alt: siteConfig.title,\n      }\n    ],\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: siteConfig.title,\n    description: siteConfig.description,\n    images: [siteConfig.ogImage],\n    creator: siteConfig.social.twitter,\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  verification: {\n    google: process.env.GOOGLE_SITE_VERIFICATION,\n    yandex: process.env.YANDEX_VERIFICATION,\n    yahoo: process.env.YAHOO_VERIFICATION,\n  },\n  category: 'fitness',\n};\n\n// Generate metadata for specific pages\nexport function generatePageMetadata({\n  title,\n  description,\n  path = '',\n  image,\n  noIndex = false,\n}: {\n  title: string;\n  description: string;\n  path?: string;\n  image?: string;\n  noIndex?: boolean;\n}): Metadata {\n  const url = `${siteConfig.url}${path}`;\n  const ogImage = image || siteConfig.ogImage;\n\n  return {\n    title,\n    description,\n    alternates: {\n      canonical: url,\n    },\n    openGraph: {\n      title,\n      description,\n      url,\n      images: [\n        {\n          url: ogImage,\n          width: 1200,\n          height: 630,\n          alt: title,\n        }\n      ],\n    },\n    twitter: {\n      title,\n      description,\n      images: [ogImage],\n    },\n    robots: noIndex ? {\n      index: false,\n      follow: false,\n    } : undefined,\n  };\n}\n\n// Structured data schemas\nexport const organizationSchema = {\n  '@context': 'https://schema.org',\n  '@type': 'Organization',\n  name: siteConfig.name,\n  url: siteConfig.url,\n  logo: `${siteConfig.url}/images/logo.png`,\n  description: siteConfig.description,\n  sameAs: [\n    `https://twitter.com/${siteConfig.social.twitter.replace('@', '')}`,\n    `https://facebook.com/${siteConfig.social.facebook}`,\n    `https://instagram.com/${siteConfig.social.instagram}`,\n    `https://youtube.com/${siteConfig.social.youtube.replace('@', '')}`\n  ],\n  contactPoint: {\n    '@type': 'ContactPoint',\n    contactType: 'customer service',\n    availableLanguage: 'English'\n  }\n};\n\nexport const websiteSchema = {\n  '@context': 'https://schema.org',\n  '@type': 'WebSite',\n  name: siteConfig.name,\n  url: siteConfig.url,\n  description: siteConfig.description,\n  potentialAction: {\n    '@type': 'SearchAction',\n    target: `${siteConfig.url}/search?q={search_term_string}`,\n    'query-input': 'required name=search_term_string'\n  }\n};\n\nexport const webApplicationSchema = {\n  '@context': 'https://schema.org',\n  '@type': 'WebApplication',\n  name: siteConfig.name,\n  url: siteConfig.url,\n  description: siteConfig.description,\n  applicationCategory: 'HealthApplication',\n  operatingSystem: 'Web Browser',\n  offers: {\n    '@type': 'Offer',\n    price: '0',\n    priceCurrency: 'USD'\n  },\n  featureList: [\n    'AI-powered workout planning',\n    'Exercise database',\n    'Progress tracking',\n    'Fitness analytics',\n    'Personalized recommendations'\n  ]\n};\n"], "names": [], "mappings": ";;;;;;;;AAGO,MAAM,aAAa;IACxB,MAAM;IACN,OAAO;IACP,aAAa;IACb,KAAK,QAAQ,GAAG,CAAC,mBAAmB,IAAI;IACxC,SAAS;IACT,SAAS;IACT,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;YACE,MAAM;YACN,KAAK,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAC1C;KACD;IACD,QAAQ;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,SAAS;IACX;AACF;AAGO,MAAM,kBAA4B;IACvC,OAAO;QACL,SAAS,WAAW,KAAK;QACzB,UAAU,CAAC,KAAK,EAAE,WAAW,IAAI,EAAE;IACrC;IACA,aAAa,WAAW,WAAW;IACnC,UAAU,WAAW,QAAQ;IAC7B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,cAAc,IAAI,IAAI,WAAW,GAAG;IACpC,YAAY;QACV,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,WAAW,GAAG;QACnB,OAAO,WAAW,KAAK;QACvB,aAAa,WAAW,WAAW;QACnC,UAAU,WAAW,IAAI;QACzB,QAAQ;YACN;gBACE,KAAK,WAAW,OAAO;gBACvB,OAAO;gBACP,QAAQ;gBACR,KAAK,WAAW,KAAK;YACvB;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO,WAAW,KAAK;QACvB,aAAa,WAAW,WAAW;QACnC,QAAQ;YAAC,WAAW,OAAO;SAAC;QAC5B,SAAS,WAAW,MAAM,CAAC,OAAO;IACpC;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ,QAAQ,GAAG,CAAC,wBAAwB;QAC5C,QAAQ,QAAQ,GAAG,CAAC,mBAAmB;QACvC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;IACvC;IACA,UAAU;AACZ;AAGO,SAAS,qBAAqB,EACnC,KAAK,EACL,WAAW,EACX,OAAO,EAAE,EACT,KAAK,EACL,UAAU,KAAK,EAOhB;IACC,MAAM,MAAM,GAAG,WAAW,GAAG,GAAG,MAAM;IACtC,MAAM,UAAU,SAAS,WAAW,OAAO;IAE3C,OAAO;QACL;QACA;QACA,YAAY;YACV,WAAW;QACb;QACA,WAAW;YACT;YACA;YACA;YACA,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,QAAQ;gBAAC;aAAQ;QACnB;QACA,QAAQ,UAAU;YAChB,OAAO;YACP,QAAQ;QACV,IAAI;IACN;AACF;AAGO,MAAM,qBAAqB;IAChC,YAAY;IACZ,SAAS;IACT,MAAM,WAAW,IAAI;IACrB,KAAK,WAAW,GAAG;IACnB,MAAM,GAAG,WAAW,GAAG,CAAC,gBAAgB,CAAC;IACzC,aAAa,WAAW,WAAW;IACnC,QAAQ;QACN,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK;QACnE,CAAC,qBAAqB,EAAE,WAAW,MAAM,CAAC,QAAQ,EAAE;QACpD,CAAC,sBAAsB,EAAE,WAAW,MAAM,CAAC,SAAS,EAAE;QACtD,CAAC,oBAAoB,EAAE,WAAW,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK;KACpE;IACD,cAAc;QACZ,SAAS;QACT,aAAa;QACb,mBAAmB;IACrB;AACF;AAEO,MAAM,gBAAgB;IAC3B,YAAY;IACZ,SAAS;IACT,MAAM,WAAW,IAAI;IACrB,KAAK,WAAW,GAAG;IACnB,aAAa,WAAW,WAAW;IACnC,iBAAiB;QACf,SAAS;QACT,QAAQ,GAAG,WAAW,GAAG,CAAC,8BAA8B,CAAC;QACzD,eAAe;IACjB;AACF;AAEO,MAAM,uBAAuB;IAClC,YAAY;IACZ,SAAS;IACT,MAAM,WAAW,IAAI;IACrB,KAAK,WAAW,GAAG;IACnB,aAAa,WAAW,WAAW;IACnC,qBAAqB;IACrB,iBAAiB;IACjB,QAAQ;QACN,SAAS;QACT,OAAO;QACP,eAAe;IACjB;IACA,aAAa;QACX;QACA;QACA;QACA;QACA;KACD;AACH", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/seo/structured-data.tsx"], "sourcesContent": ["import Script from 'next/script';\n\ninterface StructuredDataProps {\n  data: Record<string, any>;\n}\n\nexport function StructuredData({ data }: StructuredDataProps) {\n  return (\n    <Script\n      id=\"structured-data\"\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(data)\n      }}\n    />\n  );\n}\n\n// Exercise structured data\nexport function ExerciseStructuredData({ exercise }: { exercise: any }) {\n  const schema = {\n    '@context': 'https://schema.org',\n    '@type': 'ExerciseAction',\n    name: exercise.name,\n    description: exercise.description,\n    target: {\n      '@type': 'BodyPart',\n      name: exercise.targetMuscles?.join(', ') || 'Full Body'\n    },\n    instrument: exercise.equipment || 'Bodyweight',\n    exerciseType: exercise.category || 'Strength Training',\n    difficulty: exercise.difficulty || 'Intermediate',\n    instructions: exercise.instructions || exercise.description\n  };\n\n  return <StructuredData data={schema} />;\n}\n\n// Workout program structured data\nexport function WorkoutProgramStructuredData({ program }: { program: any }) {\n  const schema = {\n    '@context': 'https://schema.org',\n    '@type': 'ExercisePlan',\n    name: program.title || program.name,\n    description: program.description,\n    category: 'Fitness',\n    exerciseType: program.type || 'Strength Training',\n    intensity: program.difficulty || 'Intermediate',\n    workload: program.duration ? `${program.duration} minutes` : undefined,\n    repetitions: program.weeks?.length ? `${program.weeks.length} weeks` : undefined,\n    restPeriods: program.restDays ? `${program.restDays} rest days` : undefined\n  };\n\n  return <StructuredData data={schema} />;\n}\n\n// Article structured data for blog posts or guides\nexport function ArticleStructuredData({ \n  title, \n  description, \n  author, \n  datePublished, \n  dateModified,\n  image,\n  url \n}: {\n  title: string;\n  description: string;\n  author?: string;\n  datePublished?: string;\n  dateModified?: string;\n  image?: string;\n  url?: string;\n}) {\n  const schema = {\n    '@context': 'https://schema.org',\n    '@type': 'Article',\n    headline: title,\n    description,\n    author: {\n      '@type': 'Person',\n      name: author || 'AI-fitness-singles Team'\n    },\n    publisher: {\n      '@type': 'Organization',\n      name: 'AI-fitness-singles',\n      logo: {\n        '@type': 'ImageObject',\n        url: '/images/logo.png'\n      }\n    },\n    datePublished,\n    dateModified: dateModified || datePublished,\n    image: image ? {\n      '@type': 'ImageObject',\n      url: image\n    } : undefined,\n    url,\n    mainEntityOfPage: {\n      '@type': 'WebPage',\n      '@id': url\n    }\n  };\n\n  return <StructuredData data={schema} />;\n}\n\n// FAQ structured data\nexport function FAQStructuredData({ faqs }: { faqs: Array<{ question: string; answer: string }> }) {\n  const schema = {\n    '@context': 'https://schema.org',\n    '@type': 'FAQPage',\n    mainEntity: faqs.map(faq => ({\n      '@type': 'Question',\n      name: faq.question,\n      acceptedAnswer: {\n        '@type': 'Answer',\n        text: faq.answer\n      }\n    }))\n  };\n\n  return <StructuredData data={schema} />;\n}\n\n// Breadcrumb structured data\nexport function BreadcrumbStructuredData({ items }: { \n  items: Array<{ name: string; url: string }> \n}) {\n  const schema = {\n    '@context': 'https://schema.org',\n    '@type': 'BreadcrumbList',\n    itemListElement: items.map((item, index) => ({\n      '@type': 'ListItem',\n      position: index + 1,\n      name: item.name,\n      item: item.url\n    }))\n  };\n\n  return <StructuredData data={schema} />;\n}\n\n// Rating/Review structured data\nexport function ReviewStructuredData({ \n  itemName,\n  rating,\n  reviewCount,\n  bestRating = 5,\n  worstRating = 1 \n}: {\n  itemName: string;\n  rating: number;\n  reviewCount: number;\n  bestRating?: number;\n  worstRating?: number;\n}) {\n  const schema = {\n    '@context': 'https://schema.org',\n    '@type': 'Product',\n    name: itemName,\n    aggregateRating: {\n      '@type': 'AggregateRating',\n      ratingValue: rating,\n      reviewCount,\n      bestRating,\n      worstRating\n    }\n  };\n\n  return <StructuredData data={schema} />;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAMO,SAAS,eAAe,EAAE,IAAI,EAAuB;IAC1D,qBACE,8OAAC,8HAAA,CAAA,UAAM;QACL,IAAG;QACH,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN;AAGO,SAAS,uBAAuB,EAAE,QAAQ,EAAqB;IACpE,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,MAAM,SAAS,IAAI;QACnB,aAAa,SAAS,WAAW;QACjC,QAAQ;YACN,SAAS;YACT,MAAM,SAAS,aAAa,EAAE,KAAK,SAAS;QAC9C;QACA,YAAY,SAAS,SAAS,IAAI;QAClC,cAAc,SAAS,QAAQ,IAAI;QACnC,YAAY,SAAS,UAAU,IAAI;QACnC,cAAc,SAAS,YAAY,IAAI,SAAS,WAAW;IAC7D;IAEA,qBAAO,8OAAC;QAAe,MAAM;;;;;;AAC/B;AAGO,SAAS,6BAA6B,EAAE,OAAO,EAAoB;IACxE,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,MAAM,QAAQ,KAAK,IAAI,QAAQ,IAAI;QACnC,aAAa,QAAQ,WAAW;QAChC,UAAU;QACV,cAAc,QAAQ,IAAI,IAAI;QAC9B,WAAW,QAAQ,UAAU,IAAI;QACjC,UAAU,QAAQ,QAAQ,GAAG,GAAG,QAAQ,QAAQ,CAAC,QAAQ,CAAC,GAAG;QAC7D,aAAa,QAAQ,KAAK,EAAE,SAAS,GAAG,QAAQ,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG;QACvE,aAAa,QAAQ,QAAQ,GAAG,GAAG,QAAQ,QAAQ,CAAC,UAAU,CAAC,GAAG;IACpE;IAEA,qBAAO,8OAAC;QAAe,MAAM;;;;;;AAC/B;AAGO,SAAS,sBAAsB,EACpC,KAAK,EACL,WAAW,EACX,MAAM,EACN,aAAa,EACb,YAAY,EACZ,KAAK,EACL,GAAG,EASJ;IACC,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,UAAU;QACV;QACA,QAAQ;YACN,SAAS;YACT,MAAM,UAAU;QAClB;QACA,WAAW;YACT,SAAS;YACT,MAAM;YACN,MAAM;gBACJ,SAAS;gBACT,KAAK;YACP;QACF;QACA;QACA,cAAc,gBAAgB;QAC9B,OAAO,QAAQ;YACb,SAAS;YACT,KAAK;QACP,IAAI;QACJ;QACA,kBAAkB;YAChB,SAAS;YACT,OAAO;QACT;IACF;IAEA,qBAAO,8OAAC;QAAe,MAAM;;;;;;AAC/B;AAGO,SAAS,kBAAkB,EAAE,IAAI,EAAyD;IAC/F,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,SAAS;gBACT,MAAM,IAAI,QAAQ;gBAClB,gBAAgB;oBACd,SAAS;oBACT,MAAM,IAAI,MAAM;gBAClB;YACF,CAAC;IACH;IAEA,qBAAO,8OAAC;QAAe,MAAM;;;;;;AAC/B;AAGO,SAAS,yBAAyB,EAAE,KAAK,EAE/C;IACC,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC3C,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,GAAG;YAChB,CAAC;IACH;IAEA,qBAAO,8OAAC;QAAe,MAAM;;;;;;AAC/B;AAGO,SAAS,qBAAqB,EACnC,QAAQ,EACR,MAAM,EACN,WAAW,EACX,aAAa,CAAC,EACd,cAAc,CAAC,EAOhB;IACC,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,MAAM;QACN,iBAAiB;YACf,SAAS;YACT,aAAa;YACb;YACA;YACA;QACF;IACF;IAEA,qBAAO,8OAAC;QAAe,MAAM;;;;;;AAC/B", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata, Viewport } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { AppProviders } from \"@/components/providers/app-providers\";\nimport { defaultMetadata, organizationSchema, websiteSchema, webApplicationSchema } from \"@/lib/seo/config\";\nimport { StructuredData } from \"@/components/seo/structured-data\";\n// import { WebVitals } from \"@/components/performance/web-vitals\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  display: \"swap\",\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = defaultMetadata;\n\nexport const viewport: Viewport = {\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 5,\n  userScalable: true,\n  themeColor: [\n    { media: '(prefers-color-scheme: light)', color: '#ffffff' },\n    { media: '(prefers-color-scheme: dark)', color: '#000000' }\n  ],\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className={inter.variable}>\n      <head>\n        <StructuredData data={organizationSchema} />\n        <StructuredData data={websiteSchema} />\n        <StructuredData data={webApplicationSchema} />\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />\n        <link rel=\"dns-prefetch\" href=\"//fonts.gstatic.com\" />\n\n        {/* PWA Manifest */}\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n\n        {/* Apple Touch Icons */}\n        <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/icons/apple-touch-icon.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/icons/favicon-32x32.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/icons/favicon-16x16.png\" />\n\n        {/* Apple PWA Meta Tags */}\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"AI-fitness-singles\" />\n\n        {/* Microsoft Tiles */}\n        <meta name=\"msapplication-TileColor\" content=\"#2563eb\" />\n        <meta name=\"msapplication-config\" content=\"/browserconfig.xml\" />\n      </head>\n      <body\n        className={`${inter.className} antialiased`}\n        suppressHydrationWarning={true}\n      >\n        <AppProviders>\n          {children}\n        </AppProviders>\n        {/* <WebVitals /> */}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;;;;;;;AASO,MAAM,WAAqB,2HAAA,CAAA,kBAAe;AAE1C,MAAM,WAAqB;IAChC,OAAO;IACP,cAAc;IACd,cAAc;IACd,cAAc;IACd,YAAY;QACV;YAAE,OAAO;YAAiC,OAAO;QAAU;QAC3D;YAAE,OAAO;YAAgC,OAAO;QAAU;KAC3D;AACH;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,QAAQ;;0BACvC,8OAAC;;kCACC,8OAAC,+IAAA,CAAA,iBAAc;wBAAC,MAAM,2HAAA,CAAA,qBAAkB;;;;;;kCACxC,8OAAC,+IAAA,CAAA,iBAAc;wBAAC,MAAM,2HAAA,CAAA,gBAAa;;;;;;kCACnC,8OAAC,+IAAA,CAAA,iBAAc;wBAAC,MAAM,2HAAA,CAAA,uBAAoB;;;;;;kCAC1C,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAG9B,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAG1B,8OAAC;wBAAK,KAAI;wBAAmB,OAAM;wBAAU,MAAK;;;;;;kCAClD,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;wBAAQ,MAAK;;;;;;kCACrD,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;wBAAQ,MAAK;;;;;;kCAGrD,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAGhD,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;kCAC7C,8OAAC;wBAAK,MAAK;wBAAuB,SAAQ;;;;;;;;;;;;0BAE5C,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,YAAY,CAAC;gBAC3C,0BAA0B;0BAE1B,cAAA,8OAAC,mJAAA,CAAA,eAAY;8BACV;;;;;;;;;;;;;;;;;AAMX", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/dist/client/script.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/dist/client/script.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/script.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/script.js"], "sourcesContent": ["module.exports = require('./dist/client/script')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}