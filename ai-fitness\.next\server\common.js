exports.id=76,exports.ids=[76],exports.modules={4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(49384),a=s(82348);function i(...e){return(0,a.QP)((0,r.$)(e))}},8195:(e,t,s)=>{"use strict";s.d(t,{AppProviders:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AppProviders() from the server but AppProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\providers\\app-providers.tsx","AppProviders")},26608:(e,t,s)=>{"use strict";s.d(t,{AppProviders:()=>d});var r=s(60687),a=s(43210),i=s(39091),n=s(62381);function o({children:e}){let[t]=(0,a.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>t?.status!==401&&t?.status!==403&&t?.status!==422&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchInterval:!1},mutations:{retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<2,retryDelay:e=>Math.min(1e3*2**e,1e4)}}}));return(0,r.jsxs)(n.Ht,{client:t,children:[e,!1]})}var c=s(29908);let l=(0,a.createContext)(void 0);function u({children:e}){let{user:t,isAuthenticated:s,isLoading:i,error:n}=(0,c.As)(),[o,u]=(0,a.useState)(0);return(0,r.jsx)(l.Provider,{value:{user:t,isAuthenticated:s,isLoading:i,error:n,refreshAuth:()=>{u(e=>e+1)}},children:e})}function d({children:e}){return(0,r.jsx)(o,{children:(0,r.jsx)(u,{children:e})})}s(61604),s(54102)},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(60687),a=s(43210),i=s(81391),n=s(24224),o=s(4780);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...n},l)=>{let u=a?i.DX:"button";return(0,r.jsx)(u,{className:(0,o.cn)(c({variant:t,size:s,className:e})),ref:l,...n})});l.displayName="Button"},29908:(e,t,s)=>{"use strict";s.d(t,{As:()=>u,go:()=>d,Rt:()=>p,yC:()=>m});var r=s(32775),a=s(62381),i=s(27914),n=s(51787),o=s(38922);class c{static async signIn(e){return n.u.post(o.i.ENDPOINTS.AUTH.SIGNIN,e,{requireAuth:!1})}static async signUp(e){return n.u.post(o.i.ENDPOINTS.AUTH.SIGNUP,e,{requireAuth:!1})}static async signOut(){return n.u.post(o.i.ENDPOINTS.AUTH.SIGNOUT)}static async getSession(){try{return await n.u.get(o.i.ENDPOINTS.AUTH.SESSION)}catch(e){if(401===e.status)return null;throw e}}static async requestPasswordReset(e){return n.u.post(o.i.ENDPOINTS.AUTH.RESET_PASSWORD,{email:e},{requireAuth:!1})}static async resetPassword(e,t){return n.u.post(o.i.ENDPOINTS.AUTH.RESET_PASSWORD,{token:e,password:t},{requireAuth:!1})}static async verifyEmail(e){return n.u.post("/api/auth/verify-email",{token:e},{requireAuth:!1})}static async refreshToken(){return n.u.post("/api/auth/refresh")}static async updateProfile(e){return n.u.patch("/api/auth/profile",e)}static async changePassword(e,t){return n.u.post("/api/auth/change-password",{currentPassword:e,newPassword:t})}static async deleteAccount(e){return n.u.delete("/api/auth/account",{body:JSON.stringify({password:e})})}static async getUserSessions(){return n.u.get("/api/auth/sessions")}static async revokeSession(e){return n.u.delete(`/api/auth/sessions/${e}`)}static async revokeAllOtherSessions(){return n.u.post("/api/auth/sessions/revoke-all")}}let l={all:["auth"],session:()=>[...l.all,"session"],sessions:()=>[...l.all,"sessions"]};function u(){let{data:e,isLoading:t,error:s}=(0,r.I)({queryKey:l.session(),queryFn:()=>c.getSession(),staleTime:3e5,retry:!1});return{user:e?.user||null,isAuthenticated:!!e?.user,isLoading:t,error:s,session:e}}function d(){let e=(0,a.jE)();return(0,i.n)({mutationFn:e=>c.signIn(e),onSuccess:t=>{e.setQueryData(l.session(),t),e.invalidateQueries()},onError:e=>{console.error("Sign in failed:",e)}})}function m(){let e=(0,a.jE)();return(0,i.n)({mutationFn:e=>c.signUp(e),onSuccess:t=>{e.setQueryData(l.session(),t),e.invalidateQueries()},onError:e=>{console.error("Sign up failed:",e)}})}function p(){let e=(0,a.jE)();return(0,i.n)({mutationFn:()=>c.signOut(),onSuccess:()=>{e.setQueryData(l.session(),null),e.clear()},onError:t=>{console.error("Sign out failed:",t),e.setQueryData(l.session(),null),e.clear()}})}},32562:(e,t,s)=>{Promise.resolve().then(s.bind(s,8195))},35032:(e,t,s)=>{"use strict";s.d(t,{cG:()=>x,d2:()=>h,hQ:()=>f,qt:()=>y,sM:()=>g,wT:()=>S});var r=s(60687),a=s(99270),i=s(96474),n=s(21067),o=s(58559),c=s(25541),l=s(28947),u=s(40228),d=s(29523),m=s(4780);function p({icon:e,title:t,description:s,action:n,className:o}){return(0,r.jsxs)("div",{className:(0,m.cn)("flex flex-col items-center justify-center p-12 text-center",o),children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6",children:e||(0,r.jsx)(a.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 max-w-md",children:s}),n&&(0,r.jsxs)(d.$,{onClick:n.onClick,className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),n.label]})]})}function g({onCreateWorkout:e}){return(0,r.jsx)(p,{icon:(0,r.jsx)(n.A,{className:"h-8 w-8 text-gray-400"}),title:"No workouts yet",description:"Start your fitness journey by creating your first workout plan. Choose from our templates or build your own custom routine.",action:e?{label:"Create First Workout",onClick:e}:void 0})}function h({onAddExercise:e}){return(0,r.jsx)(p,{icon:(0,r.jsx)(o.A,{className:"h-8 w-8 text-gray-400"}),title:"No exercises found",description:"We couldn't find any exercises matching your criteria. Try adjusting your filters or browse our complete exercise database.",action:e?{label:"Browse All Exercises",onClick:e}:void 0})}function f({onStartWorkout:e}){return(0,r.jsx)(p,{icon:(0,r.jsx)(c.A,{className:"h-8 w-8 text-gray-400"}),title:"No progress data yet",description:"Complete your first workout to start tracking your fitness progress. We'll show you detailed analytics and insights as you build your routine.",action:e?{label:"Start First Workout",onClick:e}:void 0})}function y({onCreateGoal:e}){return(0,r.jsx)(p,{icon:(0,r.jsx)(l.A,{className:"h-8 w-8 text-gray-400"}),title:"No goals set",description:"Set your first fitness goal to stay motivated and track your progress. Choose from weekly workouts, monthly targets, or create custom goals.",action:e?{label:"Set First Goal",onClick:e}:void 0})}function x({onStartWorkout:e}){return(0,r.jsx)(p,{icon:(0,r.jsx)(u.A,{className:"h-8 w-8 text-gray-400"}),title:"No workout history",description:"Your workout history will appear here once you complete your first session. Start working out to build your fitness timeline.",action:e?{label:"Start Working Out",onClick:e}:void 0})}function S({searchTerm:e,onClearSearch:t}){return(0,r.jsx)(p,{icon:(0,r.jsx)(a.A,{className:"h-8 w-8 text-gray-400"}),title:`No results for "${e}"`,description:"We couldn't find anything matching your search. Try different keywords or browse our categories to discover new content.",action:t?{label:"Clear Search",onClick:t}:void 0})}},38922:(e,t,s)=>{"use strict";s.d(t,{i:()=>r});let r={BASE_URL:"http://localhost:3000",ENDPOINTS:{AUTH:{SIGNIN:"/api/auth/signin",SIGNUP:"/api/auth/signup",SIGNOUT:"/api/auth/signout",SESSION:"/api/auth/session",RESET_PASSWORD:"/api/auth/reset-password"},EXERCISES:{LIST:"/api/exercises",SEARCH:"/api/exercises/search",DETAILS:e=>`/api/exercises/${e}`,ATTRIBUTES:"/api/exercises/attributes"},WORKOUTS:{LIST:"/api/workout-sessions",CREATE:"/api/workout-sessions",DETAILS:e=>`/api/workout-sessions/${e}`,UPDATE:e=>`/api/workout-sessions/${e}`,DELETE:e=>`/api/workout-sessions/${e}`,COMPLETE:e=>`/api/workout-sessions/${e}/complete`,SYNC:"/api/workout-sessions/sync"},PROGRAMS:{LIST:"/api/programs",CREATE:"/api/programs",DETAILS:e=>`/api/programs/${e}`,UPDATE:e=>`/api/programs/${e}`,DELETE:e=>`/api/programs/${e}`,ENROLL:e=>`/api/programs/${e}/enroll`,SESSIONS:e=>`/api/programs/${e}/sessions`,START_SESSION:"/api/programs/sessions/start",COMPLETE_SESSION:"/api/programs/sessions/complete"},PROGRESS:{LIST:"/api/progress",DETAILS:e=>`/api/progress/${e}`,CREATE:"/api/progress",UPDATE:e=>`/api/progress/${e}`,DELETE:e=>`/api/progress/${e}`,OVERVIEW:"/api/progress/overview",STATS:"/api/progress/stats",HISTORY:"/api/progress/history",GOALS:"/api/progress/goals",EXPORT:"/api/progress/export"},PREMIUM:{PLANS:"/api/premium/plans",SUBSCRIPTION:"/api/premium/subscription",CHECKOUT:"/api/premium/checkout",BILLING_PORTAL:"/api/premium/billing-portal"},USERS:{PROFILE:"/api/users/profile",UPDATE_PROFILE:"/api/users/profile",PREFERENCES:"/api/users/preferences"}},TIMEOUT:{DEFAULT:1e4,UPLOAD:3e4,DOWNLOAD:6e4},RETRY:{ATTEMPTS:3,DELAY:1e3,BACKOFF_FACTOR:2}}},40487:(e,t,s)=>{"use strict";s.d(t,{A:()=>u,d9:()=>d,IS:()=>l,Fb:()=>c});var r=s(32775),a=s(51787),i=s(38922);class n{static async getExercises(e={}){let t=new URLSearchParams;e.search&&t.append("search",e.search),e.equipment?.length&&e.equipment.forEach(e=>t.append("equipment",e)),e.muscles?.length&&e.muscles.forEach(e=>t.append("muscles",e)),e.difficulty?.length&&e.difficulty.forEach(e=>t.append("difficulty",e)),e.category?.length&&e.category.forEach(e=>t.append("category",e)),e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString());let s=t.toString(),r=s?`${i.i.ENDPOINTS.EXERCISES.LIST}?${s}`:i.i.ENDPOINTS.EXERCISES.LIST;return a.u.get(r)}static async searchExercises(e,t=20){let s=new URLSearchParams({q:e,limit:t.toString()}),r=`${i.i.ENDPOINTS.EXERCISES.SEARCH}?${s.toString()}`;return(await a.u.get(r)).exercises||[]}static async getExerciseById(e){let t=i.i.ENDPOINTS.EXERCISES.DETAILS(e);return a.u.get(t)}static async getExerciseAttributes(){return a.u.get(i.i.ENDPOINTS.EXERCISES.ATTRIBUTES)}static async getExercisesByFilters(e,t,s=3){let r=new URLSearchParams;e.forEach(e=>r.append("equipment",e)),t.forEach(e=>r.append("muscles",e)),r.append("limit",s.toString());let n=`${i.i.ENDPOINTS.EXERCISES.LIST}?${r.toString()}`;return(await a.u.get(n)).data||[]}static async getRandomExercises(e=6){let t=new URLSearchParams({random:"true",limit:e.toString()}),s=`${i.i.ENDPOINTS.EXERCISES.LIST}?${t.toString()}`;return(await a.u.get(s)).data||[]}static async getPopularExercises(e=10){let t=new URLSearchParams({sort:"popular",limit:e.toString()}),s=`${i.i.ENDPOINTS.EXERCISES.LIST}?${t.toString()}`;return(await a.u.get(s)).data||[]}static async getExercisesByMuscleGroup(e,t=20){let s=new URLSearchParams({muscles:e,limit:t.toString()}),r=`${i.i.ENDPOINTS.EXERCISES.LIST}?${s.toString()}`;return(await a.u.get(r)).data||[]}static async getExercisesByEquipment(e,t=20){let s=new URLSearchParams({equipment:e,limit:t.toString()}),r=`${i.i.ENDPOINTS.EXERCISES.LIST}?${s.toString()}`;return(await a.u.get(r)).data||[]}}let o={all:["exercises"],lists:()=>[...o.all,"list"],list:e=>[...o.lists(),e],details:()=>[...o.all,"detail"],detail:e=>[...o.details(),e],search:e=>[...o.all,"search",e],attributes:()=>[...o.all,"attributes"],random:e=>[...o.all,"random",e],popular:e=>[...o.all,"popular",e],byMuscle:(e,t)=>[...o.all,"muscle",e,t],byEquipment:(e,t)=>[...o.all,"equipment",e,t],byFilters:(e,t,s)=>[...o.all,"filters",{equipment:e,muscles:t,limit:s}]};function c(e={},t=!0){return(0,r.I)({queryKey:o.list(e),queryFn:()=>n.getExercises(e),enabled:t,staleTime:3e5})}function l(e,t=20,s=!0){return(0,r.I)({queryKey:o.search(e),queryFn:()=>n.searchExercises(e,t),enabled:s&&e.length>0,staleTime:12e4})}function u(e,t=!0){return(0,r.I)({queryKey:o.detail(e),queryFn:()=>n.getExerciseById(e),enabled:t&&!!e,staleTime:6e5})}function d(){return(0,r.I)({queryKey:o.attributes(),queryFn:()=>n.getExerciseAttributes(),staleTime:18e5})}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>u,ZB:()=>c,Zp:()=>n,aR:()=>o});var r=s(60687),a=s(43210),i=s(4780);let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let u=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},48144:(e,t,s)=>{"use strict";s.d(t,{n:()=>i});var r=s(51787),a=s(38922);class i{static async getProgressRecords(e={}){let t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.type&&t.append("type",e.type),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate),e.exerciseId&&t.append("exerciseId",e.exerciseId),e.workoutId&&t.append("workoutId",e.workoutId);let s=t.toString(),i=s?`${a.i.ENDPOINTS.PROGRESS.LIST}?${s}`:a.i.ENDPOINTS.PROGRESS.LIST;return r.u.get(i)}static async getProgressRecord(e){return r.u.get(a.i.ENDPOINTS.PROGRESS.DETAILS(e))}static async createProgressRecord(e){return r.u.post(a.i.ENDPOINTS.PROGRESS.CREATE,e)}static async updateProgressRecord(e,t){return r.u.patch(a.i.ENDPOINTS.PROGRESS.UPDATE(e),t)}static async deleteProgressRecord(e){return r.u.delete(a.i.ENDPOINTS.PROGRESS.DELETE(e))}static async getProgressStats(e="month"){return r.u.get(`${a.i.ENDPOINTS.PROGRESS.STATS}?period=${e}`)}static async getWorkoutStats(e="month"){return r.u.get(`/api/progress/workout-stats?period=${e}`)}static async getExerciseProgress(e,t="month"){return r.u.get(`/api/progress/exercise/${e}?period=${t}`)}static async getBodyMeasurements(e="month"){return r.u.get(`/api/progress/body-measurements?period=${e}`)}static async addBodyMeasurement(e){return r.u.post("/api/progress/body-measurements",e)}static async getFitnessGoals(){return r.u.get("/api/progress/goals")}static async createFitnessGoal(e){return r.u.post("/api/progress/goals",e)}static async updateGoalProgress(e,t){return r.u.patch(`/api/progress/goals/${e}`,{currentValue:t})}static async getAchievements(){return r.u.get("/api/progress/achievements")}static async getWorkoutCalendar(e,t){return r.u.get(`/api/progress/calendar?year=${e}&month=${t}`)}static async getPersonalRecords(){return r.u.get("/api/progress/personal-records")}static async getStrengthProgression(e){let t=e?`?exercises=${e.join(",")}`:"";return r.u.get(`/api/progress/strength-progression${t}`)}static async exportProgressData(e="csv",t="all"){let s=await fetch(`${a.i.BASE_URL}/api/progress/export?format=${e}&period=${t}`,{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("auth-token")}`}});if(!s.ok)throw Error("Failed to export data");return s.blob()}static async getWorkoutIntensity(e="month"){return r.u.get(`/api/progress/workout-intensity?period=${e}`)}}},51787:(e,t,s)=>{"use strict";s.d(t,{u:()=>n});var r=s(38922),a=s(51923);class i{constructor(){this.baseUrl=r.i.BASE_URL,this.defaultHeaders={"Content-Type":"application/json",Accept:"application/json"}}getAuthToken(){return null}buildHeaders(e={}){let t={...this.defaultHeaders};if(!1!==e.requireAuth){let e=this.getAuthToken();e&&(t.Authorization=`Bearer ${e}`)}return e.headers&&Object.assign(t,e.headers),t}async handleResponse(e){let t,s=e.headers.get("content-type"),r=s?.includes("application/json");try{t=r?await e.json():await e.text()}catch(t){throw new a.hD("Failed to parse response",e.status)}if(!e.ok)switch(e.status){case 401:throw new a.v3(t.message||"Authentication required");case 422:throw new a.yI(t.message||"Validation failed",t.errors);case 404:throw new a.hD(t.message||"Resource not found",404);case 500:throw new a.hD(t.message||"Internal server error",500);default:throw new a.hD(t.message||"Request failed",e.status)}return t}async makeRequest(e,t={}){let{timeout:s=r.i.TIMEOUT.DEFAULT,retries:i=r.i.RETRY.ATTEMPTS,...n}=t,o=new AbortController,c=setTimeout(()=>o.abort(),s),l={...n,headers:this.buildHeaders(t),signal:o.signal},u=null;for(let t=0;t<=i;t++)try{let t=await fetch(`${this.baseUrl}${e}`,l);return clearTimeout(c),await this.handleResponse(t)}catch(s){if(u=s,s instanceof a.v3||s instanceof a.yI)throw s;if(t===i)break;let e=r.i.RETRY.DELAY*Math.pow(r.i.RETRY.BACKOFF_FACTOR,t);await new Promise(t=>setTimeout(t,e))}if(clearTimeout(c),u){if("AbortError"===u.name)throw new a.Dr("Request timeout");if("TypeError"===u.name)throw new a.Dr("Network error - please check your connection");throw u}throw Error("Request failed after all retries")}async get(e,t={}){return this.makeRequest(e,{...t,method:"GET"})}async post(e,t,s={}){return this.makeRequest(e,{...s,method:"POST",body:t?JSON.stringify(t):void 0})}async put(e,t,s={}){return this.makeRequest(e,{...s,method:"PUT",body:t?JSON.stringify(t):void 0})}async patch(e,t,s={}){return this.makeRequest(e,{...s,method:"PATCH",body:t?JSON.stringify(t):void 0})}async delete(e,t={}){return this.makeRequest(e,{...t,method:"DELETE"})}}let n=new i},51923:(e,t,s)=>{"use strict";s.d(t,{Dr:()=>a,hD:()=>r,u1:()=>o,v3:()=>i,yI:()=>n});class r extends Error{constructor(e,t=500,s,a){super(e),this.name="ApiError",this.status=t,this.code=s,this.details=a,Error.captureStackTrace&&Error.captureStackTrace(this,r)}get isClientError(){return this.status>=400&&this.status<500}get isServerError(){return this.status>=500}toJSON(){return{name:this.name,message:this.message,status:this.status,code:this.code,details:this.details,stack:this.stack}}}class a extends r{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class i extends r{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class n extends r{constructor(e="Validation failed",t={}){super(e,422,"VALIDATION_ERROR",t),this.name="ValidationError",this.fieldErrors=t}getFieldError(e){let t=this.fieldErrors[e];return t&&t.length>0?t[0]:null}hasFieldError(e){return!!this.fieldErrors[e]?.length}getAllFieldErrors(){return Object.values(this.fieldErrors).flat()}}function o(e){return e instanceof r||e instanceof Error?e.message:"string"==typeof e?e:"An unexpected error occurred"}},52027:(e,t,s)=>{"use strict";s.d(t,{AV:()=>o,B0:()=>n,z0:()=>c});var r=s(60687),a=s(4780);function i({size:e="md",className:t}){return(0,r.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e],t)})}function n({className:e}){return(0,r.jsx)("div",{className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow animate-pulse",e),children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})})}function o({title:e="Loading...",description:t="Please wait while we load your content"}){return(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/3 mx-auto mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto animate-pulse"})]})})}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,r.jsx)(i,{size:"lg"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:e}),(0,r.jsx)("p",{className:"text-gray-600 text-center max-w-md",children:t})]})})]})}function c({items:e=6,className:t}){return(0,r.jsx)("div",{className:(0,a.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",t),children:Array.from({length:e}).map((e,t)=>(0,r.jsx)(n,{},t))})}},52316:(e,t,s)=>{"use strict";s.d(t,{CU:()=>u,E$:()=>p,J9:()=>h,eC:()=>g,n8:()=>m,t0:()=>d,tR:()=>y,wZ:()=>f});var r=s(26787),a=s(59350),i=s(40158);let n={theme:"system",language:"en",units:"metric",notifications:{workoutReminders:!0,progressUpdates:!0,achievements:!0,marketing:!1},privacy:{shareProgress:!1,showInLeaderboards:!0,allowDataCollection:!0}},o={defaultDuration:45,preferredDifficulty:"INTERMEDIATE",favoriteCategories:[],excludedEquipment:[],restTimeBetweenSets:60,autoStartNextExercise:!1,playWorkoutMusic:!0,voiceInstructions:!1},c={sidebarCollapsed:!1,activeWorkoutSession:null,currentPage:"/",breadcrumbs:[],notifications:[],modals:{workoutComplete:!1,goalAchieved:!1,subscriptionPrompt:!1}},l={isOnline:!0,pendingSync:[],lastSyncTime:null},u=(0,r.v)()((0,a.Zr)((0,i.D)((e,t)=>({user:null,isAuthenticated:!1,settings:n,workoutPreferences:o,ui:c,offline:l,setUser:t=>e(e=>{e.user=t}),setAuthenticated:t=>e(e=>{e.isAuthenticated=t,t||(e.user=null)}),updateSettings:t=>e(e=>{Object.assign(e.settings,t)}),updateWorkoutPreferences:t=>e(e=>{Object.assign(e.workoutPreferences,t)}),updateUIState:t=>e(e=>{Object.assign(e.ui,t)}),addNotification:t=>e(e=>{let s={...t,id:`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,timestamp:Date.now(),read:!1};e.ui.notifications.unshift(s),e.ui.notifications.length>50&&(e.ui.notifications=e.ui.notifications.slice(0,50))}),markNotificationRead:t=>e(e=>{let s=e.ui.notifications.find(e=>e.id===t);s&&(s.read=!0)}),clearNotifications:()=>e(e=>{e.ui.notifications=[]}),setOnlineStatus:t=>e(e=>{e.offline.isOnline=t}),addPendingSync:t=>e(e=>{let s={...t,id:`sync-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,timestamp:Date.now()};e.offline.pendingSync.push(s)}),removePendingSync:t=>e(e=>{e.offline.pendingSync=e.offline.pendingSync.filter(e=>e.id!==t)}),clearPendingSync:()=>e(e=>{e.offline.pendingSync=[]}),updateLastSyncTime:()=>e(e=>{e.offline.lastSyncTime=Date.now()}),reset:()=>e(()=>({user:null,isAuthenticated:!1,settings:n,workoutPreferences:o,ui:c,offline:l}))})),{name:"ai-fitness-app-store",storage:(0,a.KU)(()=>localStorage),partialize:e=>({settings:e.settings,workoutPreferences:e.workoutPreferences,ui:{sidebarCollapsed:e.ui.sidebarCollapsed},offline:{pendingSync:e.offline.pendingSync,lastSyncTime:e.offline.lastSyncTime}})})),d=()=>u(e=>e.settings),m=()=>u(e=>e.workoutPreferences),p=()=>u(e=>e.ui.notifications),g=()=>u(e=>e.offline),h=()=>u(e=>e.ui.notifications.filter(e=>!e.read).length),f=()=>u(e=>e.offline.pendingSync.length>0),y={setUser:e=>u.getState().setUser(e),setAuthenticated:e=>u.getState().setAuthenticated(e),updateSettings:e=>u.getState().updateSettings(e),updateWorkoutPreferences:e=>u.getState().updateWorkoutPreferences(e),addNotification:e=>u.getState().addNotification(e),setOnlineStatus:e=>u.getState().setOnlineStatus(e),addPendingSync:e=>u.getState().addPendingSync(e),removePendingSync:e=>u.getState().removePendingSync(e),updateLastSyncTime:()=>u.getState().updateLastSyncTime(),reset:()=>u.getState().reset()}},54102:(e,t,s)=>{"use strict";s.d(t,{GN:()=>o,Pb:()=>l});var r=s(39091),a=s(83982),i=s(52316);let n=new r.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<1,onError:e=>{console.error("Mutation error:",e),i.tR.addNotification({type:"error",title:"Operation Failed",message:e?.message||"An unexpected error occurred"})}}}});(0,a.s)({storage:void 0,key:"ai-fitness-query-cache",serialize:JSON.stringify,deserialize:JSON.parse});let o={invalidateEntity:e=>{n.invalidateQueries({queryKey:[e]})},removeEntity:(e,t)=>{t?n.removeQueries({queryKey:[e,t]}):n.removeQueries({queryKey:[e]})},prefetch:async(e,t)=>{await n.prefetchQuery({queryKey:e,queryFn:t,staleTime:6e5})},setQueryData:(e,t)=>{n.setQueryData(e,t)},getQueryData:e=>n.getQueryData(e),clearAll:()=>{n.clear()},resetQueries:e=>{e?n.resetQueries({queryKey:e}):n.resetQueries()},cancelQueries:e=>{e?n.cancelQueries({queryKey:e}):n.cancelQueries()}},c={auth:{all:["auth"],session:()=>[...c.auth.all,"session"],user:()=>[...c.auth.all,"user"]},exercises:{all:["exercises"],lists:()=>[...c.exercises.all,"list"],list:e=>[...c.exercises.lists(),e],details:()=>[...c.exercises.all,"detail"],detail:e=>[...c.exercises.details(),e],search:e=>[...c.exercises.all,"search",e],attributes:()=>[...c.exercises.all,"attributes"]},workouts:{all:["workouts"],sessions:()=>[...c.workouts.all,"sessions"],session:e=>[...c.workouts.sessions(),e],programs:()=>[...c.workouts.all,"programs"],program:e=>[...c.workouts.programs(),e],history:e=>[...c.workouts.all,"history",e],stats:e=>[...c.workouts.all,"stats",e]},progress:{all:["progress"],records:()=>[...c.progress.all,"records"],record:e=>[...c.progress.records(),e],stats:e=>[...c.progress.all,"stats",e],goals:()=>[...c.progress.all,"goals"],achievements:()=>[...c.progress.all,"achievements"],calendar:(e,t)=>[...c.progress.all,"calendar",e,t]},user:{all:["user"],profile:()=>[...c.user.all,"profile"],preferences:()=>[...c.user.all,"preferences"],subscription:()=>[...c.user.all,"subscription"]}},l={isOnline:()=>"undefined"==typeof navigator||navigator.onLine,setupNetworkListeners:()=>{},queueOfflineMutation:(e,t,s)=>{i.tR.addPendingSync({type:e,action:t,data:s})}};n.getQueryCache().subscribe(e=>{e.type}),n.getMutationCache().subscribe(e=>{"updated"===e.type&&"success"===e.mutation.state.status&&i.tR.addNotification({type:"success",title:"Success",message:"Operation completed successfully"})})},61135:()=>{},61604:(e,t,s)=>{"use strict";s.d(t,{Dj:()=>o,aF:()=>c,dW:()=>l});var r=s(52316),a=s(75799),i=s(48144),n=s(54102);class o{constructor(){this.syncInProgress=!1,this.syncQueue=[],this.setupNetworkListeners()}static getInstance(){return o.instance||(o.instance=new o),o.instance}setupNetworkListeners(){}addToSyncQueue(e){r.tR.addPendingSync(e),navigator.onLine&&this.syncPendingData()}async syncPendingData(){if(this.syncInProgress||!navigator.onLine)return;let e=r.CU.getState().offline.pendingSync;if(0!==e.length){this.syncInProgress=!0;try{r.tR.addNotification({type:"info",title:"Syncing Data",message:`Syncing ${e.length} pending items...`});let t=await Promise.allSettled(e.map(e=>this.syncSingleItem(e))),s=0,a=0;t.forEach((t,i)=>{let n=e[i];"fulfilled"===t.status?(s++,r.tR.removePendingSync(n.id)):(a++,console.error(`Failed to sync item ${n.id}:`,t.reason))}),r.tR.updateLastSyncTime(),0===a?r.tR.addNotification({type:"success",title:"Sync Complete",message:`Successfully synced ${s} items`}):r.tR.addNotification({type:"warning",title:"Sync Partially Complete",message:`Synced ${s} items, ${a} failed`}),n.GN.invalidateEntity("workouts"),n.GN.invalidateEntity("progress")}catch(e){console.error("Sync failed:",e),r.tR.addNotification({type:"error",title:"Sync Failed",message:"Failed to sync offline data. Will retry later."})}finally{this.syncInProgress=!1}}}async syncSingleItem(e){switch(e.type){case"workout":return this.syncWorkoutItem(e);case"progress":return this.syncProgressItem(e);case"goal":return this.syncGoalItem(e);default:throw Error(`Unknown sync item type: ${e.type}`)}}async syncWorkoutItem(e){switch(e.action){case"create":if("session"===e.data.type)return a.f.createWorkoutSession(e.data);if("program"===e.data.type)return a.f.createWorkoutProgram(e.data);break;case"update":if("session"===e.data.type)return a.f.updateWorkoutSession(e.data.id,e.data);if("program"===e.data.type)return a.f.updateWorkoutProgram(e.data.id,e.data);break;case"delete":if("session"===e.data.type)return a.f.deleteWorkoutSession(e.data.id);if("program"===e.data.type)return a.f.deleteWorkoutProgram(e.data.id)}throw Error(`Unknown workout sync action: ${e.action}`)}async syncProgressItem(e){switch(e.action){case"create":return i.n.createProgressRecord(e.data);case"update":return i.n.updateProgressRecord(e.data.id,e.data);case"delete":return i.n.deleteProgressRecord(e.data.id)}throw Error(`Unknown progress sync action: ${e.action}`)}async syncGoalItem(e){switch(e.action){case"create":return i.n.createFitnessGoal(e.data);case"update":return i.n.updateGoalProgress(e.data.id,e.data.currentValue);default:throw Error(`Unknown goal sync action: ${e.action}`)}}async forceSyncAll(){await this.syncPendingData()}clearPendingSync(){r.tR.clearPendingSync()}getSyncStatus(){let e=r.CU.getState();return{isOnline:e.offline.isOnline,pendingCount:e.offline.pendingSync.length,lastSyncTime:e.offline.lastSyncTime,syncInProgress:this.syncInProgress}}}function c(){let e=o.getInstance();return{addToSyncQueue:t=>e.addToSyncQueue(t),syncPendingData:()=>e.syncPendingData(),forceSyncAll:()=>e.forceSyncAll(),clearPendingSync:()=>e.clearPendingSync(),getSyncStatus:()=>e.getSyncStatus()}}function l(){return o.getInstance().getSyncStatus()}},70293:(e,t,s)=>{"use strict";s.d(t,{Kw:()=>l,M:()=>u});var r=s(60687),a=s(43649),i=s(78122),n=s(32192),o=s(29523),c=s(4780);function l({title:e="Something went wrong",message:t="An unexpected error occurred. Please try again.",onRetry:s,className:n}){return(0,r.jsxs)("div",{className:(0,c.cn)("flex flex-col items-center justify-center p-8 text-center",n),children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(a.A,{className:"h-8 w-8 text-red-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e}),(0,r.jsx)("p",{className:"text-gray-600 mb-6 max-w-md",children:t}),s&&(0,r.jsxs)(o.$,{onClick:s,className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),"Try Again"]})]})}function u({title:e="Oops! Something went wrong",message:t="We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.",onRetry:s,showHomeButton:c=!0}){return(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-red-50 to-orange-100 py-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-4",children:"Error"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Something unexpected happened"})]})})}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px]",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-6",children:(0,r.jsx)(a.A,{className:"h-12 w-12 text-red-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:e}),(0,r.jsx)("p",{className:"text-gray-600 text-center max-w-2xl mb-8",children:t}),(0,r.jsxs)("div",{className:"flex gap-4",children:[s&&(0,r.jsxs)(o.$,{onClick:s,className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),"Try Again"]}),c&&(0,r.jsxs)(o.$,{variant:"outline",onClick:()=>window.location.href="/",className:"flex items-center gap-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),"Go Home"]})]})]})})]})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75799:(e,t,s)=>{"use strict";s.d(t,{f:()=>i});var r=s(51787),a=s(38922);class i{static async getWorkoutSessions(e={}){let t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.status&&t.append("status",e.status),e.programId&&t.append("programId",e.programId),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate);let s=t.toString(),i=s?`${a.i.ENDPOINTS.WORKOUTS.LIST}?${s}`:a.i.ENDPOINTS.WORKOUTS.LIST;return r.u.get(i)}static async getWorkoutSession(e){return r.u.get(a.i.ENDPOINTS.WORKOUTS.DETAILS(e))}static async createWorkoutSession(e){return r.u.post(a.i.ENDPOINTS.WORKOUTS.CREATE,e)}static async updateWorkoutSession(e,t){return r.u.patch(a.i.ENDPOINTS.WORKOUTS.UPDATE(e),t)}static async deleteWorkoutSession(e){return r.u.delete(a.i.ENDPOINTS.WORKOUTS.DELETE(e))}static async startWorkoutSession(e){return r.u.post(`${a.i.ENDPOINTS.WORKOUTS.DETAILS(e)}/start`)}static async completeWorkoutSession(e,t){return r.u.post(`${a.i.ENDPOINTS.WORKOUTS.DETAILS(e)}/complete`,t)}static async getWorkoutPrograms(e={}){let t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.category&&t.append("category",e.category),e.difficulty&&t.append("difficulty",e.difficulty),e.duration&&t.append("duration",e.duration);let s=t.toString(),i=s?`${a.i.ENDPOINTS.PROGRAMS.LIST}?${s}`:a.i.ENDPOINTS.PROGRAMS.LIST;return r.u.get(i)}static async getWorkoutProgram(e){return r.u.get(a.i.ENDPOINTS.PROGRAMS.DETAILS(e))}static async createWorkoutProgram(e){return r.u.post(a.i.ENDPOINTS.PROGRAMS.CREATE,e)}static async updateWorkoutProgram(e,t){return r.u.patch(a.i.ENDPOINTS.PROGRAMS.UPDATE(e),t)}static async deleteWorkoutProgram(e){return r.u.delete(a.i.ENDPOINTS.PROGRAMS.DELETE(e))}static async joinWorkoutProgram(e){return r.u.post(`${a.i.ENDPOINTS.PROGRAMS.DETAILS(e)}/join`)}static async leaveWorkoutProgram(e){return r.u.post(`${a.i.ENDPOINTS.PROGRAMS.DETAILS(e)}/leave`)}static async getUserPrograms(){return(await r.u.get("/api/user/programs")).programs||[]}static async getPopularPrograms(e=10){let t=new URLSearchParams({sort:"popular",limit:e.toString()}),s=`${a.i.ENDPOINTS.PROGRAMS.LIST}?${t.toString()}`;return(await r.u.get(s)).data||[]}static async getRecommendedPrograms(e=6){let t=new URLSearchParams({recommended:"true",limit:e.toString()}),s=`${a.i.ENDPOINTS.PROGRAMS.LIST}?${t.toString()}`;return(await r.u.get(s)).data||[]}static async generateAIWorkout(e){return r.u.post("/api/workouts/generate",e)}static async getWorkoutStats(e="month"){return r.u.get(`/api/workouts/stats?period=${e}`)}static async getWorkoutHistory(e={}){let t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate);let s=t.toString(),a=s?`/api/workouts/history?${s}`:"/api/workouts/history";return r.u.get(a)}}},78335:()=>{},82639:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},86246:(e,t,s)=>{"use strict";s.d(t,{V:()=>E});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),o=s(29523),c=s(96834),l=s(21067),u=s(87891),d=s(70663),m=s(97051),p=s(40083),g=s(11860),h=s(12941),f=s(82080),y=s(53411),x=s(58869),S=s(29908),N=s(52316);function E(){let[e,t]=(0,a.useState)(!1),{user:s,isAuthenticated:i,isLoading:E}=(0,S.As)(),w=(0,S.Rt)(),v=(0,N.eC)(),P=(0,N.J9)(),b=(0,N.wZ)(),T=async()=>{try{await w.mutateAsync()}catch(e){console.error("Sign out failed:",e)}};return(0,r.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)(l.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI-fitness-singles"})]}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,r.jsx)(n(),{href:"/workouts",className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Workouts"}),(0,r.jsx)(n(),{href:"/exercises",className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Exercises"}),(0,r.jsx)(n(),{href:"/progress",className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Progress"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[v.isOnline?(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)(d.A,{className:"h-4 w-4 text-red-600"}),b&&(0,r.jsx)(c.E,{variant:"outline",className:"text-xs",children:v.pendingSync.length})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-600"}),P>0&&(0,r.jsx)(c.E,{variant:"destructive",className:"absolute -top-2 -right-2 h-4 w-4 p-0 text-xs flex items-center justify-center",children:P>9?"9+":P})]})]}),E?(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"}):i&&s?(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:["Welcome, ",s.name||s.email]}),(0,r.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:T,disabled:w.isPending,className:"flex items-center space-x-1",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Sign Out"})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,r.jsx)(n(),{href:"/auth/signin",children:"Sign In"})}),(0,r.jsx)(o.$,{size:"sm",asChild:!0,children:(0,r.jsx)(n(),{href:"/auth/signup",children:"Sign Up"})})]})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"p-2",children:e?(0,r.jsx)(g.A,{className:"h-6 w-6"}):(0,r.jsx)(h.A,{className:"h-6 w-6"})})})]}),e&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[v.isOnline?(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)(d.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:v.isOnline?"Online":"Offline"}),b&&(0,r.jsxs)(c.E,{variant:"outline",className:"text-xs",children:[v.pendingSync.length," pending"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-600"}),P>0&&(0,r.jsx)(c.E,{variant:"destructive",className:"text-xs",children:P})]})]}),(0,r.jsx)(n(),{href:"/workouts",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>t(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Workouts"})]})}),(0,r.jsx)(n(),{href:"/exercises",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>t(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Exercises"})]})}),(0,r.jsx)(n(),{href:"/progress",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>t(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Progress"})]})}),(0,r.jsx)("div",{className:"pt-4 border-t",children:E?(0,r.jsx)("div",{className:"px-3 py-2",children:(0,r.jsx)("div",{className:"w-full h-8 bg-gray-200 rounded animate-pulse"})}):i&&s?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"px-3 py-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:s.name||s.email})]})}),(0,r.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:T,disabled:w.isPending,className:"w-full justify-start px-3",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]}):(0,r.jsxs)("div",{className:"space-y-2 px-3",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"sm",className:"w-full",asChild:!0,children:(0,r.jsx)(n(),{href:"/auth/signin",onClick:()=>t(!1),children:"Sign In"})}),(0,r.jsx)(o.$,{size:"sm",className:"w-full",asChild:!0,children:(0,r.jsx)(n(),{href:"/auth/signup",onClick:()=>t(!1),children:"Sign Up"})})]})})]})})]})})}},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687),a=s(43210),i=s(4780);let n=a.forwardRef(({className:e,type:t,...s},a)=>(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));n.displayName="Input"},90887:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>o});var r=s(37413),a=s(85041),i=s.n(a);s(61135);var n=s(8195);let o={title:"AI-fitness-singles - Smart Fitness Platform",description:"AI-powered fitness platform for singles. Create custom workout plans, access comprehensive exercise database, and track your fitness progress with detailed analytics."};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().className} antialiased`,suppressHydrationWarning:!0,children:(0,r.jsx)(n.AppProviders,{children:e})})})}},95698:(e,t,s)=>{"use strict";s.d(t,{Mm:()=>u,My:()=>c,Ox:()=>g,Rc:()=>d,bR:()=>l,ij:()=>f,mS:()=>p,sK:()=>m,t2:()=>h});var r=s(32775),a=s(62381),i=s(27914),n=s(75799);let o={all:["workouts"],sessions:()=>[...o.all,"sessions"],session:e=>[...o.sessions(),e],sessionsList:e=>[...o.sessions(),"list",e],programs:()=>[...o.all,"programs"],program:e=>[...o.programs(),e],programsList:e=>[...o.programs(),"list",e],userPrograms:()=>[...o.programs(),"user"],popular:e=>[...o.programs(),"popular",e],recommended:e=>[...o.programs(),"recommended",e],stats:e=>[...o.all,"stats",e],history:e=>[...o.all,"history",e]};function c(e={}){return(0,r.I)({queryKey:o.sessionsList(e),queryFn:()=>n.f.getWorkoutSessions(e),staleTime:12e4})}function l(){let e=(0,a.jE)();return(0,i.n)({mutationFn:e=>n.f.createWorkoutSession(e),onSuccess:()=>{e.invalidateQueries({queryKey:o.sessions()})}})}function u(e={}){return(0,r.I)({queryKey:o.programsList(e),queryFn:()=>n.f.getWorkoutPrograms(e),staleTime:6e5})}function d(e,t=!0){return(0,r.I)({queryKey:o.program(e),queryFn:()=>n.f.getWorkoutProgram(e),enabled:t&&!!e,staleTime:6e5})}function m(){return(0,r.I)({queryKey:o.userPrograms(),queryFn:()=>n.f.getUserPrograms(),staleTime:3e5})}function p(e=10){return(0,r.I)({queryKey:o.popular(e),queryFn:()=>n.f.getPopularPrograms(e),staleTime:9e5})}function g(e=6){return(0,r.I)({queryKey:o.recommended(e),queryFn:()=>n.f.getRecommendedPrograms(e),staleTime:6e5})}function h(){let e=(0,a.jE)();return(0,i.n)({mutationFn:e=>n.f.joinWorkoutProgram(e),onSuccess:()=>{e.invalidateQueries({queryKey:o.userPrograms()})}})}function f(){let e=(0,a.jE)();return(0,i.n)({mutationFn:e=>n.f.leaveWorkoutProgram(e),onSuccess:()=>{e.invalidateQueries({queryKey:o.userPrograms()})}})}},95770:(e,t,s)=>{Promise.resolve().then(s.bind(s,26608))},96487:()=>{},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(24224),i=s(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...s})}}};