/**
 * Sync Manager for Offline Data Synchronization
 * Handles syncing offline data when connection is restored
 */

import { useAppStore, appActions } from '../store/app-store';
import { WorkoutService } from '../api/services/workouts';
import { ProgressService } from '../api/services/progress';
import { queryClient, cacheUtils } from '../query/config';

// ============================================================================
// SYNC MANAGER CLASS
// ============================================================================

export class SyncManager {
  private static instance: SyncManager;
  private syncInProgress = false;
  private syncQueue: Array<any> = [];

  private constructor() {
    this.setupNetworkListeners();
  }

  static getInstance(): SyncManager {
    if (!SyncManager.instance) {
      SyncManager.instance = new SyncManager();
    }
    return SyncManager.instance;
  }

  /**
   * Setup network event listeners
   */
  private setupNetworkListeners() {
    if (typeof window === 'undefined') return;

    const handleOnline = () => {
      appActions.setOnlineStatus(true);
      this.syncPendingData();
    };

    const handleOffline = () => {
      appActions.setOnlineStatus(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial status
    appActions.setOnlineStatus(navigator.onLine);
  }

  /**
   * Add data to sync queue for offline processing
   */
  addToSyncQueue(item: {
    type: 'workout' | 'progress' | 'goal';
    action: 'create' | 'update' | 'delete';
    data: any;
    localId?: string;
  }) {
    appActions.addPendingSync(item);
    
    // Try to sync immediately if online
    if (navigator.onLine) {
      this.syncPendingData();
    }
  }

  /**
   * Sync all pending data
   */
  async syncPendingData() {
    if (this.syncInProgress || !navigator.onLine) {
      return;
    }

    const state = useAppStore.getState();
    const pendingItems = state.offline.pendingSync;

    if (pendingItems.length === 0) {
      return;
    }

    this.syncInProgress = true;

    try {
      appActions.addNotification({
        type: 'info',
        title: 'Syncing Data',
        message: `Syncing ${pendingItems.length} pending items...`,
      });

      const syncResults = await Promise.allSettled(
        pendingItems.map(item => this.syncSingleItem(item))
      );

      // Process results
      let successCount = 0;
      let failureCount = 0;

      syncResults.forEach((result, index) => {
        const item = pendingItems[index];
        
        if (result.status === 'fulfilled') {
          successCount++;
          appActions.removePendingSync(item.id);
        } else {
          failureCount++;
          console.error(`Failed to sync item ${item.id}:`, result.reason);
        }
      });

      // Update last sync time
      appActions.updateLastSyncTime();

      // Show result notification
      if (failureCount === 0) {
        appActions.addNotification({
          type: 'success',
          title: 'Sync Complete',
          message: `Successfully synced ${successCount} items`,
        });
      } else {
        appActions.addNotification({
          type: 'warning',
          title: 'Sync Partially Complete',
          message: `Synced ${successCount} items, ${failureCount} failed`,
        });
      }

      // Invalidate relevant queries to refetch fresh data
      cacheUtils.invalidateEntity('workouts');
      cacheUtils.invalidateEntity('progress');

    } catch (error) {
      console.error('Sync failed:', error);
      appActions.addNotification({
        type: 'error',
        title: 'Sync Failed',
        message: 'Failed to sync offline data. Will retry later.',
      });
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync a single item
   */
  private async syncSingleItem(item: any): Promise<any> {
    switch (item.type) {
      case 'workout':
        return this.syncWorkoutItem(item);
      case 'progress':
        return this.syncProgressItem(item);
      case 'goal':
        return this.syncGoalItem(item);
      default:
        throw new Error(`Unknown sync item type: ${item.type}`);
    }
  }

  /**
   * Sync workout-related items
   */
  private async syncWorkoutItem(item: any): Promise<any> {
    switch (item.action) {
      case 'create':
        if (item.data.type === 'session') {
          return WorkoutService.createWorkoutSession(item.data);
        } else if (item.data.type === 'program') {
          return WorkoutService.createWorkoutProgram(item.data);
        }
        break;
      
      case 'update':
        if (item.data.type === 'session') {
          return WorkoutService.updateWorkoutSession(item.data.id, item.data);
        } else if (item.data.type === 'program') {
          return WorkoutService.updateWorkoutProgram(item.data.id, item.data);
        }
        break;
      
      case 'delete':
        if (item.data.type === 'session') {
          return WorkoutService.deleteWorkoutSession(item.data.id);
        } else if (item.data.type === 'program') {
          return WorkoutService.deleteWorkoutProgram(item.data.id);
        }
        break;
    }
    
    throw new Error(`Unknown workout sync action: ${item.action}`);
  }

  /**
   * Sync progress-related items
   */
  private async syncProgressItem(item: any): Promise<any> {
    switch (item.action) {
      case 'create':
        return ProgressService.createProgressRecord(item.data);
      
      case 'update':
        return ProgressService.updateProgressRecord(item.data.id, item.data);
      
      case 'delete':
        return ProgressService.deleteProgressRecord(item.data.id);
    }
    
    throw new Error(`Unknown progress sync action: ${item.action}`);
  }

  /**
   * Sync goal-related items
   */
  private async syncGoalItem(item: any): Promise<any> {
    switch (item.action) {
      case 'create':
        return ProgressService.createFitnessGoal(item.data);
      
      case 'update':
        return ProgressService.updateGoalProgress(item.data.id, item.data.currentValue);
      
      default:
        throw new Error(`Unknown goal sync action: ${item.action}`);
    }
  }

  /**
   * Force sync all data
   */
  async forceSyncAll() {
    await this.syncPendingData();
  }

  /**
   * Clear all pending sync data
   */
  clearPendingSync() {
    appActions.clearPendingSync();
  }

  /**
   * Get sync status
   */
  getSyncStatus() {
    const state = useAppStore.getState();
    return {
      isOnline: state.offline.isOnline,
      pendingCount: state.offline.pendingSync.length,
      lastSyncTime: state.offline.lastSyncTime,
      syncInProgress: this.syncInProgress,
    };
  }
}

// ============================================================================
// REACT HOOKS FOR SYNC MANAGEMENT
// ============================================================================

/**
 * Hook to use sync manager
 */
export function useSyncManager() {
  const syncManager = SyncManager.getInstance();
  
  return {
    addToSyncQueue: (item: Parameters<typeof syncManager.addToSyncQueue>[0]) => 
      syncManager.addToSyncQueue(item),
    syncPendingData: () => syncManager.syncPendingData(),
    forceSyncAll: () => syncManager.forceSyncAll(),
    clearPendingSync: () => syncManager.clearPendingSync(),
    getSyncStatus: () => syncManager.getSyncStatus(),
  };
}

/**
 * Hook to get sync status
 */
export function useSyncStatus() {
  const syncManager = SyncManager.getInstance();
  const status = syncManager.getSyncStatus();
  
  return status;
}

// ============================================================================
// OPTIMISTIC UPDATE UTILITIES
// ============================================================================

export const optimisticUpdates = {
  /**
   * Optimistically update workout session
   */
  updateWorkoutSession: (sessionId: string, updates: any) => {
    const queryKey = ['workouts', 'sessions', sessionId];
    const previousData = cacheUtils.getQueryData(queryKey);
    
    // Apply optimistic update
    cacheUtils.setQueryData(queryKey, {
      ...previousData,
      ...updates,
      updatedAt: new Date().toISOString(),
    });
    
    return previousData;
  },

  /**
   * Optimistically add progress record
   */
  addProgressRecord: (record: any) => {
    const queryKey = ['progress', 'records'];
    const previousData = cacheUtils.getQueryData<any>(queryKey);
    
    if (previousData?.data) {
      const newRecord = {
        ...record,
        id: `temp-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      cacheUtils.setQueryData(queryKey, {
        ...previousData,
        data: [newRecord, ...previousData.data],
      });
    }
    
    return previousData;
  },

  /**
   * Revert optimistic update
   */
  revertUpdate: (queryKey: any[], previousData: any) => {
    if (previousData !== undefined) {
      cacheUtils.setQueryData(queryKey, previousData);
    }
  },
};

// ============================================================================
// EXPORTS
// ============================================================================

export default SyncManager;
