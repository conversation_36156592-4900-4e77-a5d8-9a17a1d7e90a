(()=>{var e={};e.id=316,e.ids=[316],e.modules={662:(e,s,t)=>{Promise.resolve().then(t.bind(t,39200))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38926:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>q});var a=t(60687),r=t(43210),i=t(86246),n=t(44493),l=t(29523),c=t(96834),d=t(52027),o=t(35032),x=t(31158),m=t(53411),h=t(28947),u=t(86561),g=t(40228),j=t(25541),p=t(58559),y=t(48730),v=t(41923),N=t(45583),f=t(13861),b=t(96474),w=t(32775),k=t(27914),A=t(48144);let P={all:["progress"],records:()=>[...P.all,"records"],record:e=>[...P.records(),e],recordsList:e=>[...P.records(),"list",e],stats:e=>[...P.all,"stats",e],workoutStats:e=>[...P.all,"workout-stats",e],exerciseProgress:(e,s)=>[...P.all,"exercise",e,s],bodyMeasurements:e=>[...P.all,"body-measurements",e],goals:()=>[...P.all,"goals"],achievements:()=>[...P.all,"achievements"],calendar:(e,s)=>[...P.all,"calendar",e,s],personalRecords:()=>[...P.all,"personal-records"],strengthProgression:e=>[...P.all,"strength-progression",e],workoutIntensity:e=>[...P.all,"workout-intensity",e]};var D=t(29908);function q(){var e,s;let[t,q]=(0,r.useState)("overview"),[C,W]=(0,r.useState)("month"),{isAuthenticated:T}=(0,D.As)(),{data:R,isLoading:S}=function(e="month"){return(0,w.I)({queryKey:P.stats(e),queryFn:()=>A.n.getProgressStats(e),staleTime:3e5})}(C),{data:B,isLoading:L}=function(e="month"){return(0,w.I)({queryKey:P.workoutStats(e),queryFn:()=>A.n.getWorkoutStats(e),staleTime:3e5})}(C),{data:_,isLoading:Z}=function(e="month"){return(0,w.I)({queryKey:P.bodyMeasurements(e),queryFn:()=>A.n.getBodyMeasurements(e),staleTime:6e5})}(C),{data:F,isLoading:I}=(0,w.I)({queryKey:P.goals(),queryFn:()=>A.n.getFitnessGoals(),staleTime:3e5}),{data:M,isLoading:E}=(0,w.I)({queryKey:P.achievements(),queryFn:()=>A.n.getAchievements(),staleTime:9e5}),{data:$,isLoading:G}=(0,w.I)({queryKey:P.personalRecords(),queryFn:()=>A.n.getPersonalRecords(),staleTime:6e5}),{data:U,isLoading:K}=function(e="month"){return(0,w.I)({queryKey:P.workoutIntensity(e),queryFn:()=>A.n.getWorkoutIntensity(e),staleTime:6e5})}(C),V=new Date,{data:O,isLoading:Y}=(e=V.getFullYear(),s=V.getMonth()+1,(0,w.I)({queryKey:P.calendar(e,s),queryFn:()=>A.n.getWorkoutCalendar(e,s),staleTime:6e5})),z=(0,k.n)({mutationFn:({format:e,period:s})=>A.n.exportProgressData(e,s),onSuccess:(e,{format:s})=>{let t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`progress-data.${s}`,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t)}}),H=e=>{z.mutate({format:e,period:"all"})};return T?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i.V,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Progress Dashboard"}),(0,a.jsx)("p",{className:"text-xl text-gray-600",children:"Track your fitness journey and celebrate your achievements"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex bg-white rounded-lg p-1 shadow-sm",children:["week","month","year"].map(e=>(0,a.jsx)("button",{onClick:()=>W(e),className:`px-3 py-1 rounded-md text-sm font-medium transition-colors ${C===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>H("csv"),disabled:z.isPending,className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),z.isPending?"Exporting...":"Export"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm",children:[{id:"overview",label:"Overview",icon:m.A},{id:"goals",label:"Goals",icon:h.A},{id:"achievements",label:"Achievements",icon:u.A},{id:"history",label:"History",icon:g.A}].map(e=>(0,a.jsxs)("button",{onClick:()=>q(e.id),className:`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${t===e.id?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(e.icon,{className:"h-4 w-4"}),e.label]},e.id))}),"overview"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Workouts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?"...":B?.totalWorkouts||0}),(0,a.jsxs)("p",{className:"text-xs text-green-600 flex items-center gap-1",children:[(0,a.jsx)(j.A,{className:"h-3 w-3"}),"+12% from last ",C]})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Duration"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?"...":`${Math.round((B?.totalDuration||0)/60)}h`}),(0,a.jsxs)("p",{className:"text-xs text-green-600 flex items-center gap-1",children:[(0,a.jsx)(j.A,{className:"h-3 w-3"}),"+8% from last ",C]})]}),(0,a.jsx)(y.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Calories Burned"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?"...":B?.caloriesBurned||0}),(0,a.jsxs)("p",{className:"text-xs text-green-600 flex items-center gap-1",children:[(0,a.jsx)(j.A,{className:"h-3 w-3"}),"+15% from last ",C]})]}),(0,a.jsx)(v.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Current Streak"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?"...":B?.streakDays||0}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"days"})]}),(0,a.jsx)(N.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Weekly Progress"}),(0,a.jsx)(n.BT,{children:"Your workout activity over time"})]}),(0,a.jsx)(n.Wu,{children:L?(0,a.jsx)(d.B0,{}):B?.weeklyProgress?(0,a.jsx)("div",{className:"space-y-4",children:B.weeklyProgress.slice(-8).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e.date}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm",children:e.workouts})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsxs)("span",{className:"text-sm",children:[Math.round(e.duration/60),"h"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)("span",{className:"text-sm",children:e.calories})]})]})]},s))}):(0,a.jsx)(o.hQ,{})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Workout Calendar"}),(0,a.jsx)(n.BT,{children:"This month's activity"})]}),(0,a.jsx)(n.Wu,{children:Y?(0,a.jsx)(d.B0,{}):O?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-7 gap-2 text-center",children:[["S","M","T","W","T","F","S"].map(e=>(0,a.jsx)("div",{className:"text-xs font-medium text-gray-500 p-2",children:e},e)),O.calendar.slice(0,35).map((e,s)=>(0,a.jsx)("div",{className:`p-2 text-xs rounded ${e.hasWorkout?"bg-blue-100 text-blue-800 font-medium":"text-gray-400"}`,children:new Date(e.date).getDate()},s))]}),(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-600",children:O.monthStats.totalWorkouts}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Workouts"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[Math.round(O.monthStats.totalDuration/60),"h"]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Duration"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-bold text-orange-600",children:O.monthStats.activeDays}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Active Days"})]})]})})]}):(0,a.jsx)(o.hQ,{})})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{children:"Recent Personal Records"}),(0,a.jsx)(n.BT,{children:"Your latest achievements"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"View All"]})]})}),(0,a.jsx)(n.Wu,{children:G?(0,a.jsx)(d.B0,{}):$?.recentPRs&&$.recentPRs.length>0?(0,a.jsx)("div",{className:"space-y-4",children:$.recentPRs.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.exerciseName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.recordType,": ",e.value," ",e.unit]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-green-600",children:["+",e.improvement," ",e.unit]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:new Date(e.achievedDate).toLocaleDateString()})]})]},s))}):(0,a.jsx)(o.cG,{})})]})]}),"goals"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Fitness Goals"}),(0,a.jsxs)(l.$,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Add Goal"]})]}),I?(0,a.jsx)(d.B0,{}):F?.goals&&F.goals.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:F.goals.map(e=>(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-lg",children:e.title}),(0,a.jsx)(n.BT,{children:e.description})]}),(0,a.jsx)(c.E,{variant:"completed"===e.status?"default":"secondary",children:e.status})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(e.progress,100)}%`}})}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[e.currentValue," / ",e.targetValue," ",e.unit]}),(0,a.jsxs)("span",{children:["Due: ",new Date(e.deadline).toLocaleDateString()]})]})]})})]},e.id))}):(0,a.jsx)(o.qt,{})]}),"achievements"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Achievements & Badges"}),E?(0,a.jsx)(d.B0,{}):M?(0,a.jsxs)("div",{className:"space-y-8",children:[M.milestones&&M.milestones.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Milestones"}),(0,a.jsx)("div",{className:"space-y-3",children:M.milestones.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Achieved on ",new Date(e.achievedDate).toLocaleDateString()]})]})]},e.id))})]}),M.badges&&M.badges.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Badges"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:M.badges.map(e=>(0,a.jsx)(n.Zp,{className:e.earnedDate?"border-green-200":"opacity-60",children:(0,a.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:e.icon}),(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),e.earnedDate?(0,a.jsxs)(c.E,{variant:"default",children:["Earned ",new Date(e.earnedDate).toLocaleDateString()]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${(e.progress||0)/e.requirement*100}%`}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.progress||0," / ",e.requirement]})]})]})},e.id))})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No achievements yet"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Start working out to earn your first badges!"})]})]}),"history"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Workout History"}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>H("csv"),children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Export History"]})]}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Workout history"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your detailed workout history will appear here"})]})})})]})]})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i.V,{}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Sign in to view your progress"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Track your fitness journey with detailed analytics and insights"}),(0,a.jsx)(l.$,{asChild:!0,children:(0,a.jsx)("a",{href:"/auth/signin",children:"Sign In"})})]})})]})}},39200:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\progress\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\progress\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},82472:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["progress",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,39200)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\progress\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\progress\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/progress/page",pathname:"/progress",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},95934:(e,s,t)=>{Promise.resolve().then(t.bind(t,38926))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[96,76],()=>t(82472));module.exports=a})();