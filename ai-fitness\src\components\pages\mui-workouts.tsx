'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Badge,
  Tabs,
  Tab,
  Paper,
  Collapse,
  FormGroup,
  FormControlLabel,
  Checkbox,
  alpha,
  useTheme,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  PlayArrow as PlayIcon,
  Add as AddIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  AccessTime as ClockIcon,
  Group as UsersIcon,
  TrendingUp as TargetIcon,
  MenuBook as BookIcon,
  CalendarToday as CalendarIcon,
  Clear as ClearIcon,
  FitnessCenter as FitnessCenterIcon,
  Timer as TimerIcon,
  EmojiEvents as TrophyIcon,
} from '@mui/icons-material';
import { 
  useWorkoutPrograms,
  useWorkoutSessions,
  usePopularPrograms,
  useRecommendedPrograms,
  useUserPrograms,
  useJoinWorkoutProgram,
  useCreateWorkoutSession
} from "@/lib/hooks/use-workouts";
import { useAuth } from "@/lib/hooks/use-auth";
import Link from "next/link";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`workout-tabpanel-${index}`}
      aria-labelledby={`workout-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

export function MuiWorkouts() {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    category: [] as string[],
    difficulty: [] as string[],
    duration: [] as string[]
  });

  const { isAuthenticated } = useAuth();

  // API hooks
  const { data: programsData, isLoading: isLoadingPrograms, error: programsError } = useWorkoutPrograms({
    limit: 20,
    category: selectedFilters.category.length > 0 ? selectedFilters.category[0] : undefined,
    difficulty: selectedFilters.difficulty.length > 0 ? selectedFilters.difficulty[0] : undefined,
    duration: selectedFilters.duration.length > 0 ? selectedFilters.duration[0] : undefined
  });

  const { data: sessionsData, isLoading: isLoadingSessions } = useWorkoutSessions({
    limit: 20
  });

  const { data: popularPrograms, isLoading: isLoadingPopular } = usePopularPrograms(6);
  const { data: recommendedPrograms, isLoading: isLoadingRecommended } = useRecommendedPrograms(6);
  const { data: userPrograms, isLoading: isLoadingUserPrograms } = useUserPrograms();

  const joinProgramMutation = useJoinWorkoutProgram();
  const createSessionMutation = useCreateWorkoutSession();

  const handleFilterChange = (type: keyof typeof selectedFilters, value: string) => {
    setSelectedFilters(prev => {
      const currentValues = prev[type];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      return {
        ...prev,
        [type]: newValues
      };
    });
  };

  const clearFilters = () => {
    setSelectedFilters({
      category: [],
      difficulty: [],
      duration: []
    });
  };

  const hasActiveFilters = Object.values(selectedFilters).some(arr => arr.length > 0);

  const handleJoinProgram = async (programId: string) => {
    try {
      await joinProgramMutation.mutateAsync(programId);
    } catch (error) {
      console.error('Failed to join program:', error);
    }
  };

  const handleStartWorkout = async (programId: string) => {
    try {
      await createSessionMutation.mutateAsync({
        exercises: [],
        notes: `Quick workout session - ${new Date().toLocaleDateString()}`
      });
    } catch (error) {
      console.error('Failed to start workout:', error);
    }
  };

  const filteredPrograms = programsData?.data?.filter(program =>
    searchQuery === "" ||
    program.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    program.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  // Quick stats data
  const quickStats = [
    { 
      label: '活跃计划', 
      value: userPrograms?.length || 0, 
      icon: <BookIcon />, 
      color: theme.palette.primary.main 
    },
    { 
      label: '本周训练', 
      value: sessionsData?.data?.filter(s => s.status === 'COMPLETED').length || 0, 
      icon: <CalendarIcon />, 
      color: theme.palette.secondary.main 
    },
    { 
      label: '总时长', 
      value: `${sessionsData?.data?.reduce((acc, s) => acc + (s.duration || 0), 0) || 0}分钟`, 
      icon: <TimerIcon />, 
      color: '#9C27B0' 
    },
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          py: { xs: 6, md: 8 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
                fontSize: { xs: '2.5rem', md: '3.5rem' }
              }}
            >
              训练计划
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: 'text.secondary',
                mb: 4,
                fontWeight: 400,
                fontSize: { xs: '1.25rem', md: '1.5rem' }
              }}
            >
              发现个性化训练计划，助您达成健身目标
            </Typography>
          </Box>
        </Container>

        {/* Floating Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.secondary.main, 0.3)})`,
            animation: 'float 6s ease-in-out infinite',
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '30%',
            left: '5%',
            width: 60,
            height: 60,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,
            animation: 'float 4s ease-in-out infinite reverse',
          }}
        />
      </Box>

      {/* Quick Stats */}
      {isAuthenticated && (
        <Container maxWidth="md" sx={{ mt: -2, position: 'relative', zIndex: 2, py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <Grid container spacing={2} sx={{ maxWidth: 600 }}>
              {quickStats.map((stat, index) => (
                <Grid size={4} key={index}>
                  <Card
                    sx={{
                      textAlign: 'center',
                      p: 2.5,
                      minHeight: 160,
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',
                      backdropFilter: 'blur(15px)',
                      border: '1px solid rgba(255,255,255,0.3)',
                      borderRadius: 3,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 8px 20px ${alpha(stat.color, 0.15)}`,
                        border: `1px solid ${alpha(stat.color, 0.2)}`,
                      }
                    }}
                  >
                    <CardContent sx={{ p: '8px !important' }}>
                      <Box
                        sx={{
                          display: 'inline-flex',
                          p: 1.5,
                          borderRadius: '50%',
                          bgcolor: alpha(stat.color, 0.12),
                          color: stat.color,
                          mb: 2
                        }}
                      >
                        {stat.icon}
                      </Box>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 500 }}>
                        {stat.label}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Container>
      )}

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Tabs */}
        <Paper sx={{ mb: 4, borderRadius: 3, overflow: 'hidden' }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            variant="fullWidth"
            sx={{
              '& .MuiTab-root': {
                fontWeight: 600,
                fontSize: '1rem',
                textTransform: 'none',
                py: 2,
              },
              '& .Mui-selected': {
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                color: 'white !important',
              }
            }}
          >
            <Tab label="所有计划" />
            {isAuthenticated && <Tab label="我的计划" />}
            {isAuthenticated && <Tab label="训练记录" />}
          </Tabs>
        </Paper>

        {/* Search and Filters */}
        <TabPanel value={activeTab} index={0}>
          <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>
              <TextField
                fullWidth
                placeholder="搜索训练计划..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
              <Button
                variant="outlined"
                onClick={() => setShowFilters(!showFilters)}
                startIcon={<FilterIcon />}
                endIcon={hasActiveFilters && (
                  <Badge badgeContent={Object.values(selectedFilters).reduce((acc, arr) => acc + arr.length, 0)} color="primary">
                    <Box />
                  </Badge>
                )}
                sx={{
                  borderRadius: 3,
                  minWidth: 120,
                  fontWeight: 600,
                  textTransform: 'none',
                }}
              >
                筛选
              </Button>
            </Box>

            <Collapse in={showFilters}>
              <Box sx={{ pt: 3, borderTop: 1, borderColor: 'divider' }}>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                      类别
                    </Typography>
                    <FormGroup>
                      {['力量训练', '有氧运动', '柔韧性', 'HIIT', '瑜伽'].map((category) => (
                        <FormControlLabel
                          key={category}
                          control={
                            <Checkbox
                              checked={selectedFilters.category.includes(category)}
                              onChange={() => handleFilterChange('category', category)}
                              sx={{ color: theme.palette.primary.main }}
                            />
                          }
                          label={category}
                        />
                      ))}
                    </FormGroup>
                  </Grid>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                      难度
                    </Typography>
                    <FormGroup>
                      {['初级', '中级', '高级'].map((difficulty) => (
                        <FormControlLabel
                          key={difficulty}
                          control={
                            <Checkbox
                              checked={selectedFilters.difficulty.includes(difficulty)}
                              onChange={() => handleFilterChange('difficulty', difficulty)}
                              sx={{ color: theme.palette.secondary.main }}
                            />
                          }
                          label={difficulty}
                        />
                      ))}
                    </FormGroup>
                  </Grid>
                  <Grid size={{ xs: 12, md: 4 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                      时长
                    </Typography>
                    <FormGroup>
                      {['15-30分钟', '30-45分钟', '45-60分钟', '60分钟以上'].map((duration) => (
                        <FormControlLabel
                          key={duration}
                          control={
                            <Checkbox
                              checked={selectedFilters.duration.includes(duration)}
                              onChange={() => handleFilterChange('duration', duration)}
                              sx={{ color: '#9C27B0' }}
                            />
                          }
                          label={duration}
                        />
                      ))}
                    </FormGroup>
                  </Grid>
                </Grid>
                {hasActiveFilters && (
                  <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
                    <Button
                      variant="outlined"
                      onClick={clearFilters}
                      startIcon={<ClearIcon />}
                      sx={{ borderRadius: 3, textTransform: 'none' }}
                    >
                      清除筛选
                    </Button>
                  </Box>
                )}
              </Box>
            </Collapse>
          </Paper>

          {/* Popular Programs */}
          {!searchQuery && !hasActiveFilters && (
            <Box sx={{ mb: 6 }}>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>
                热门计划
              </Typography>
              <Grid container spacing={3}>
                {isLoadingPopular ? (
                  Array.from({ length: 6 }).map((_, index) => (
                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                      <Card sx={{ height: 280, borderRadius: 3 }}>
                        <CardContent sx={{ p: 3 }}>
                          <Box sx={{ bgcolor: 'grey.200', height: 200, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />
                        </CardContent>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  popularPrograms?.slice(0, 6).map((program) => (
                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={program.id}>
                      <Card
                        sx={{
                          height: '100%',
                          borderRadius: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: theme.shadows[8],
                          }
                        }}
                      >
                        <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                {program.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                {program.description}
                              </Typography>
                            </Box>
                            <IconButton size="small">
                              <FavoriteBorderIcon />
                            </IconButton>
                          </Box>

                          <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>
                            <Chip
                              icon={<ClockIcon />}
                              label={`${program.duration || 'N/A'} 分钟`}
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              icon={<TargetIcon />}
                              label={program.difficulty || 'N/A'}
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              icon={<UsersIcon />}
                              label={program.participantCount || 0}
                              size="small"
                              variant="outlined"
                            />
                          </Box>

                          <Box sx={{ mt: 'auto', display: 'flex', gap: 1 }}>
                            <Button
                              variant="contained"
                              startIcon={<PlayIcon />}
                              onClick={() => handleStartWorkout(program.id)}
                              disabled={createSessionMutation.isPending}
                              sx={{
                                flex: 1,
                                borderRadius: 2,
                                textTransform: 'none',
                                fontWeight: 600,
                                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                              }}
                            >
                              开始训练
                            </Button>
                            <Button
                              variant="outlined"
                              onClick={() => handleJoinProgram(program.id)}
                              disabled={joinProgramMutation.isPending}
                              sx={{ borderRadius: 2 }}
                            >
                              <AddIcon />
                            </Button>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))
                )}
              </Grid>
            </Box>
          )}

          {/* All Programs */}
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>
              {searchQuery ? `搜索结果: "${searchQuery}"` : '所有计划'}
            </Typography>
            <Grid container spacing={3}>
              {isLoadingPrograms ? (
                Array.from({ length: 9 }).map((_, index) => (
                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                    <Card sx={{ height: 280, borderRadius: 3 }}>
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ bgcolor: 'grey.200', height: 200, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : filteredPrograms.length === 0 ? (
                <Grid size={12}>
                  <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>
                    <FitnessCenterIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" sx={{ mb: 1 }}>
                      {searchQuery ? '未找到相关计划' : '暂无训练计划'}
                    </Typography>
                    <Typography color="text.secondary">
                      {searchQuery ? '尝试调整搜索条件' : '敬请期待更多精彩内容'}
                    </Typography>
                  </Paper>
                </Grid>
              ) : (
                filteredPrograms.map((program) => (
                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={program.id}>
                    <Card
                      sx={{
                        height: '100%',
                        borderRadius: 3,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: theme.shadows[8],
                        }
                      }}
                    >
                      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                              {program.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {program.description}
                            </Typography>
                          </Box>
                          <IconButton size="small">
                            <FavoriteBorderIcon />
                          </IconButton>
                        </Box>

                        <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>
                          <Chip
                            icon={<ClockIcon />}
                            label={`${program.duration || 'N/A'} 分钟`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<TargetIcon />}
                            label={program.difficulty || 'N/A'}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<UsersIcon />}
                            label={program.participantCount || 0}
                            size="small"
                            variant="outlined"
                          />
                        </Box>

                        <Box sx={{ mt: 'auto', display: 'flex', gap: 1 }}>
                          <Link href={`/workouts/${program.id}`} style={{ flex: 1 }}>
                            <Button
                              variant="contained"
                              startIcon={<PlayIcon />}
                              fullWidth
                              sx={{
                                borderRadius: 2,
                                textTransform: 'none',
                                fontWeight: 600,
                                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                              }}
                            >
                              查看详情
                            </Button>
                          </Link>
                          <Button
                            variant="outlined"
                            onClick={() => handleJoinProgram(program.id)}
                            disabled={joinProgramMutation.isPending}
                            sx={{ borderRadius: 2 }}
                          >
                            <AddIcon />
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              )}
            </Grid>
          </Box>
        </TabPanel>

        {/* My Programs Tab */}
        {isAuthenticated && (
          <TabPanel value={activeTab} index={1}>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>
              我的计划
            </Typography>
            <Grid container spacing={3}>
              {isLoadingUserPrograms ? (
                Array.from({ length: 6 }).map((_, index) => (
                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                    <Card sx={{ height: 280, borderRadius: 3 }}>
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ bgcolor: 'grey.200', height: 200, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : !userPrograms || userPrograms.length === 0 ? (
                <Grid size={12}>
                  <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>
                    <BookIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" sx={{ mb: 1 }}>
                      暂无加入的计划
                    </Typography>
                    <Typography color="text.secondary" sx={{ mb: 3 }}>
                      浏览并加入您感兴趣的训练计划
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => setActiveTab(0)}
                      sx={{
                        borderRadius: 3,
                        textTransform: 'none',
                        fontWeight: 600,
                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      }}
                    >
                      浏览计划
                    </Button>
                  </Paper>
                </Grid>
              ) : (
                userPrograms.map((program) => (
                  <Grid size={{ xs: 12, md: 6, lg: 4 }} key={program.id}>
                    <Card
                      sx={{
                        height: '100%',
                        borderRadius: 3,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: theme.shadows[8],
                        }
                      }}
                    >
                      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                              {program.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {program.description}
                            </Typography>
                          </Box>
                          <Chip label="已加入" color="primary" size="small" />
                        </Box>

                        <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>
                          <Chip
                            icon={<ClockIcon />}
                            label={`${program.duration || 'N/A'} 分钟`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<TargetIcon />}
                            label={program.difficulty || 'N/A'}
                            size="small"
                            variant="outlined"
                          />
                        </Box>

                        <Box sx={{ mt: 'auto', display: 'flex', gap: 1 }}>
                          <Button
                            variant="contained"
                            startIcon={<PlayIcon />}
                            onClick={() => handleStartWorkout(program.id)}
                            disabled={createSessionMutation.isPending}
                            sx={{
                              flex: 1,
                              borderRadius: 2,
                              textTransform: 'none',
                              fontWeight: 600,
                              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                            }}
                          >
                            开始训练
                          </Button>
                          <Link href={`/workouts/${program.id}`}>
                            <Button
                              variant="outlined"
                              sx={{ borderRadius: 2 }}
                            >
                              <BookIcon />
                            </Button>
                          </Link>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              )}
            </Grid>
          </TabPanel>
        )}

        {/* My Sessions Tab */}
        {isAuthenticated && (
          <TabPanel value={activeTab} index={2}>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>
              训练记录
            </Typography>
            {isLoadingSessions ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {Array.from({ length: 5 }).map((_, index) => (
                  <Card key={index} sx={{ borderRadius: 3 }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ bgcolor: 'grey.200', height: 80, borderRadius: 2, animation: 'pulse 1.5s ease-in-out infinite' }} />
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : !sessionsData?.data || sessionsData.data.length === 0 ? (
              <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>
                <CalendarIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" sx={{ mb: 1 }}>
                  暂无训练记录
                </Typography>
                <Typography color="text.secondary" sx={{ mb: 3 }}>
                  开始您的第一次训练吧
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => setActiveTab(0)}
                  sx={{
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  }}
                >
                  开始训练
                </Button>
              </Paper>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {sessionsData.data.map((session) => (
                  <Card key={session.id} sx={{ borderRadius: 3, transition: 'all 0.3s ease', '&:hover': { boxShadow: theme.shadows[4] } }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                            {session.notes || '训练记录'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            训练课程
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                            <Chip
                              icon={<CalendarIcon />}
                              label={new Date().toLocaleDateString()}
                              size="small"
                              variant="outlined"
                            />
                            {session.duration && (
                              <Chip
                                icon={<ClockIcon />}
                                label={`${session.duration} 分钟`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Chip
                            label={
                              session.status === 'COMPLETED' ? '已完成' :
                              session.status === 'IN_PROGRESS' ? '进行中' : '待开始'
                            }
                            color={
                              session.status === 'COMPLETED' ? 'success' :
                              session.status === 'IN_PROGRESS' ? 'warning' : 'default'
                            }
                            variant="filled"
                          />
                          <Link href={`/workouts/sessions/${session.id}`}>
                            <Button variant="outlined" size="small" sx={{ borderRadius: 2 }}>
                              查看详情
                            </Button>
                          </Link>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}
          </TabPanel>
        )}
      </Container>
    </Box>
  );
}
