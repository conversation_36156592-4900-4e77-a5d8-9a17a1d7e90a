{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from \"lucide-react\"\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const toggleMenu = () => setIsOpen(!isOpen)\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">Workout.cool</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/workouts\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Workouts\n            </Link>\n            <Link\n              href=\"/exercises\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Exercises\n            </Link>\n            <Link\n              href=\"/progress\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Progress\n            </Link>\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button variant=\"ghost\" size=\"icon\" onClick={toggleMenu}>\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/workouts\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Dumbbell className=\"h-4 w-4\" />\n                <span>Workouts</span>\n              </Link>\n              <Link\n                href=\"/exercises\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BookOpen className=\"h-4 w-4\" />\n                <span>Exercises</span>\n              </Link>\n              <Link\n                href=\"/progress\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BarChart3 className=\"h-4 w-4\" />\n                <span>Progress</span>\n              </Link>\n              <div className=\"pt-4 border-t\">\n                <div className=\"flex flex-col space-y-2\">\n                  <Button variant=\"outline\" className=\"justify-start\">\n                    Sign In\n                  </Button>\n                  <Button className=\"justify-start bg-blue-600 hover:bg-blue-700\">\n                    Get Started\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,SAAS;0CAC1C,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDAGpD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlF", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/exercises/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Navigation } from \"@/components/navigation\"\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport {\n  Search,\n  Filter,\n  Clock,\n  Target,\n  Play,\n  Heart,\n  Zap\n} from \"lucide-react\"\n\nconst exercises = [\n  {\n    id: 1,\n    name: \"Push-ups\",\n    category: \"Chest\",\n    difficulty: \"Beginner\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"Classic bodyweight exercise targeting chest, shoulders, and triceps\",\n    image: \"💪\",\n    muscles: [\"Chest\", \"Shoulders\", \"Triceps\"]\n  },\n  {\n    id: 2,\n    name: \"Squats\",\n    category: \"Legs\",\n    difficulty: \"Beginner\",\n    duration: \"45 seconds\",\n    equipment: \"None\",\n    description: \"Fundamental lower body exercise for strength and mobility\",\n    image: \"🦵\",\n    muscles: [\"Quadriceps\", \"Glutes\", \"Hamstrings\"]\n  },\n  {\n    id: 3,\n    name: \"Burpees\",\n    category: \"Full Body\",\n    difficulty: \"Advanced\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"High-intensity full-body exercise combining strength and cardio\",\n    image: \"🔥\",\n    muscles: [\"Full Body\", \"Cardio\"]\n  },\n  {\n    id: 4,\n    name: \"Plank\",\n    category: \"Core\",\n    difficulty: \"Intermediate\",\n    duration: \"60 seconds\",\n    equipment: \"None\",\n    description: \"Isometric core exercise for stability and strength\",\n    image: \"🏋️\",\n    muscles: [\"Core\", \"Shoulders\", \"Back\"]\n  },\n  {\n    id: 5,\n    name: \"Mountain Climbers\",\n    category: \"Cardio\",\n    difficulty: \"Intermediate\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"Dynamic cardio exercise that targets core and improves endurance\",\n    image: \"⛰️\",\n    muscles: [\"Core\", \"Shoulders\", \"Legs\"]\n  },\n  {\n    id: 6,\n    name: \"Lunges\",\n    category: \"Legs\",\n    difficulty: \"Beginner\",\n    duration: \"45 seconds\",\n    equipment: \"None\",\n    description: \"Unilateral leg exercise for strength and balance\",\n    image: \"🚶\",\n    muscles: [\"Quadriceps\", \"Glutes\", \"Calves\"]\n  },\n  {\n    id: 7,\n    name: \"Jumping Jacks\",\n    category: \"Cardio\",\n    difficulty: \"Beginner\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"Classic cardio exercise to get your heart rate up\",\n    image: \"🤸\",\n    muscles: [\"Full Body\", \"Cardio\"]\n  },\n  {\n    id: 8,\n    name: \"Deadlifts\",\n    category: \"Back\",\n    difficulty: \"Advanced\",\n    duration: \"45 seconds\",\n    equipment: \"Dumbbells\",\n    description: \"Compound exercise targeting posterior chain muscles\",\n    image: \"🏋️‍♂️\",\n    muscles: [\"Back\", \"Glutes\", \"Hamstrings\"]\n  }\n]\n\nconst categories = [\n  { name: \"All\", count: exercises.length },\n  { name: \"Chest\", count: exercises.filter(e => e.category === \"Chest\").length },\n  { name: \"Legs\", count: exercises.filter(e => e.category === \"Legs\").length },\n  { name: \"Core\", count: exercises.filter(e => e.category === \"Core\").length },\n  { name: \"Cardio\", count: exercises.filter(e => e.category === \"Cardio\").length },\n  { name: \"Back\", count: exercises.filter(e => e.category === \"Back\").length },\n  { name: \"Full Body\", count: exercises.filter(e => e.category === \"Full Body\").length }\n]\n\nconst getDifficultyColor = (difficulty: string) => {\n  switch (difficulty) {\n    case \"Beginner\": return \"bg-green-100 text-green-800\"\n    case \"Intermediate\": return \"bg-yellow-100 text-yellow-800\"\n    case \"Advanced\": return \"bg-red-100 text-red-800\"\n    default: return \"bg-gray-100 text-gray-800\"\n  }\n}\n\nconst getDifficultyIcon = (difficulty: string) => {\n  switch (difficulty) {\n    case \"Beginner\": return <Heart className=\"h-4 w-4\" />\n    case \"Intermediate\": return <Target className=\"h-4 w-4\" />\n    case \"Advanced\": return <Zap className=\"h-4 w-4\" />\n    default: return <Target className=\"h-4 w-4\" />\n  }\n}\n\nexport default function Exercises() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n\n      {/* Header Section */}\n      <section className=\"bg-gradient-to-br from-green-50 to-emerald-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\">\n              Exercise Database\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover thousands of exercises with detailed instructions and video guides\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"container mx-auto px-4 py-8\">\n\n        {/* Search and Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-8\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search exercises...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <Button variant=\"outline\">\n            <Filter className=\"h-4 w-4 mr-2\" />\n            Filters\n          </Button>\n        </div>\n\n        {/* Categories */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Categories</h2>\n          <div className=\"flex flex-wrap gap-2\">\n            {categories.map((category) => (\n              <Button\n                key={category.name}\n                variant={category.name === \"All\" ? \"default\" : \"outline\"}\n                size=\"sm\"\n                className=\"text-sm\"\n              >\n                {category.name} ({category.count})\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Exercise Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {exercises.map((exercise) => (\n            <Card key={exercise.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"text-4xl mb-2\">{exercise.image}</div>\n                  <div className=\"flex items-center gap-1\">\n                    {getDifficultyIcon(exercise.difficulty)}\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                      {exercise.difficulty}\n                    </span>\n                  </div>\n                </div>\n                <CardTitle className=\"text-lg\">{exercise.name}</CardTitle>\n                <CardDescription className=\"text-sm\">\n                  {exercise.description}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                    <div className=\"flex items-center gap-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      <span>{exercise.duration}</span>\n                    </div>\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium\">\n                      {exercise.category}\n                    </span>\n                  </div>\n                  \n                  <div>\n                    <p className=\"text-xs text-gray-500 mb-1\">Target Muscles:</p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {exercise.muscles.map((muscle, index) => (\n                        <span\n                          key={index}\n                          className=\"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs\"\n                        >\n                          {muscle}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-xs text-gray-500\">\n                    Equipment: {exercise.equipment}\n                  </div>\n                  \n                  <Button className=\"w-full\" size=\"sm\">\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    View Exercise\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card className=\"text-center\">\n            <CardContent className=\"p-6\">\n              <div className=\"text-3xl mb-2\">💪</div>\n              <h3 className=\"font-semibold text-gray-900\">500+ Exercises</h3>\n              <p className=\"text-sm text-gray-600\">Comprehensive exercise library</p>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"text-center\">\n            <CardContent className=\"p-6\">\n              <div className=\"text-3xl mb-2\">🎯</div>\n              <h3 className=\"font-semibold text-gray-900\">All Skill Levels</h3>\n              <p className=\"text-sm text-gray-600\">From beginner to advanced</p>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"text-center\">\n            <CardContent className=\"p-6\">\n              <div className=\"text-3xl mb-2\">📱</div>\n              <h3 className=\"font-semibold text-gray-900\">Video Guides</h3>\n              <p className=\"text-sm text-gray-600\">Step-by-step instructions</p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAeA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAS;YAAa;SAAU;IAC5C;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAc;YAAU;SAAa;IACjD;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAa;SAAS;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAa;SAAO;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAa;SAAO;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAc;YAAU;SAAS;IAC7C;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAa;SAAS;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAU;SAAa;IAC3C;CACD;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,OAAO,UAAU,MAAM;IAAC;IACvC;QAAE,MAAM;QAAS,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,MAAM;IAAC;IAC7E;QAAE,MAAM;QAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IAC3E;QAAE,MAAM;QAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IAC3E;QAAE,MAAM;QAAU,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;IAAC;IAC/E;QAAE,MAAM;QAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IAC3E;QAAE,MAAM;QAAa,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,MAAM;IAAC;CACtF;AAED,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YAAY,OAAO;QACxB,KAAK;YAAgB,OAAO;QAC5B,KAAK;YAAY,OAAO;QACxB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YAAY,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzC,KAAK;YAAgB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC9C,KAAK;YAAY,qBAAO,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvC;YAAS,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;IACpC;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO7D,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAGd,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,SAAS,IAAI,KAAK,QAAQ,YAAY;wCAC/C,MAAK;wCACL,WAAU;;4CAET,SAAS,IAAI;4CAAC;4CAAG,SAAS,KAAK;4CAAC;;uCAL5B,SAAS,IAAI;;;;;;;;;;;;;;;;kCAY1B,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;gCAAmB,WAAU;;kDAChC,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,SAAS,KAAK;;;;;;kEAC9C,8OAAC;wDAAI,WAAU;;4DACZ,kBAAkB,SAAS,UAAU;0EACtC,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,SAAS,UAAU,GAAG;0EACrG,SAAS,UAAU;;;;;;;;;;;;;;;;;;0DAI1B,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,SAAS,IAAI;;;;;;0DAC7C,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,SAAS,WAAW;;;;;;;;;;;;kDAGzB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM,SAAS,QAAQ;;;;;;;;;;;;sEAE1B,8OAAC;4DAAK,WAAU;sEACb,SAAS,QAAQ;;;;;;;;;;;;8DAItB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;;;;;;;;;;;;8DASb,8OAAC;oDAAI,WAAU;;wDAAwB;wDACzB,SAAS,SAAS;;;;;;;8DAGhC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;oDAAS,MAAK;;sEAC9B,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;+BA/C9B,SAAS,EAAE;;;;;;;;;;kCAyD1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}