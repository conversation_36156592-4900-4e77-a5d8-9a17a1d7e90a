"use client"

import { useState, useEffect } from "react"
import { Metadata } from 'next'
import { Navigation } from "@/components/Navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { LoadingPage, LoadingGrid } from "@/components/ui/loading"
import { ErrorMessage } from "@/components/ui/error"
import { EmptyExercises, EmptySearchResults } from "@/components/ui/empty-state"
import {
  Search,
  Filter,
  Clock,
  Target,
  Play,
  Heart,
  Zap,
  Star,
  BookOpen,
  Users,
  Dumbbell,
  X,
  Eye,
  Plus
} from "lucide-react"
import { 
  useExercises, 
  useExerciseSearch, 
  useExerciseAttributes,
  useInfiniteExercises 
} from "@/lib/hooks/use-exercises"
import type { ExerciseSearchParams } from "@/lib/api/types"

export default function ExercisesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [selectedFilters, setSelectedFilters] = useState<ExerciseSearchParams>({
    equipment: [],
    muscles: [],
    difficulty: [],
    category: []
  })

  // Use search when there's a query, otherwise use filtered exercises
  const { data: searchResults, isLoading: isSearching } = useExerciseSearch(
    searchQuery, 
    20, 
    searchQuery.length > 0
  )

  const { 
    data: exercisesData, 
    isLoading: isLoadingExercises,
    error: exercisesError 
  } = useExercises(
    searchQuery.length === 0 ? selectedFilters : {},
    searchQuery.length === 0
  )

  const { 
    data: attributesData, 
    isLoading: isLoadingAttributes 
  } = useExerciseAttributes()

  const exercises = searchQuery.length > 0 ? searchResults : exercisesData?.data
  const isLoading = searchQuery.length > 0 ? isSearching : isLoadingExercises

  const handleFilterChange = (type: keyof ExerciseSearchParams, value: string) => {
    setSelectedFilters(prev => {
      const currentValues = prev[type] as string[] || []
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value]
      
      return {
        ...prev,
        [type]: newValues
      }
    })
  }

  const clearFilters = () => {
    setSelectedFilters({
      equipment: [],
      muscles: [],
      difficulty: [],
      category: []
    })
  }

  const hasActiveFilters = Object.values(selectedFilters).some(
    arr => Array.isArray(arr) && arr.length > 0
  )

  if (isLoadingAttributes) {
    return <LoadingPage />
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Exercise Library
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover thousands of exercises with detailed instructions, muscle targeting, and difficulty levels
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search exercises..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Filter Toggle */}
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-1">
                  {Object.values(selectedFilters).reduce((acc, arr) => acc + (arr?.length || 0), 0)}
                </Badge>
              )}
            </Button>
          </div>

          {/* Filter Panel */}
          {showFilters && attributesData && (
            <div className="border-t pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Equipment Filter */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Equipment</h3>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {attributesData.equipment?.map((item) => (
                      <label key={item.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedFilters.equipment?.includes(item.id) || false}
                          onChange={() => handleFilterChange('equipment', item.id)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm text-gray-700">{item.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Muscles Filter */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Muscles</h3>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {attributesData.muscles?.map((item) => (
                      <label key={item.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedFilters.muscles?.includes(item.id) || false}
                          onChange={() => handleFilterChange('muscles', item.id)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm text-gray-700">{item.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Categories Filter */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Categories</h3>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {attributesData.categories?.map((item) => (
                      <label key={item.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedFilters.category?.includes(item.id) || false}
                          onChange={() => handleFilterChange('category', item.id)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm text-gray-700">{item.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Difficulty Filter */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Difficulty</h3>
                  <div className="space-y-2">
                    {attributesData.difficulties?.map((item) => (
                      <label key={item.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedFilters.difficulty?.includes(item.id) || false}
                          onChange={() => handleFilterChange('difficulty', item.id)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm text-gray-700">{item.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {hasActiveFilters && (
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" onClick={clearFilters} className="flex items-center gap-2">
                    <X className="h-4 w-4" />
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Results */}
        {isLoading ? (
          <LoadingGrid />
        ) : exercisesError ? (
          <ErrorMessage 
            title="Failed to load exercises"
            message="Please try again later"
            onRetry={() => window.location.reload()}
          />
        ) : !exercises || exercises.length === 0 ? (
          searchQuery ? (
            <EmptySearchResults
              searchTerm={searchQuery}
              onClearSearch={() => setSearchQuery("")}
            />
          ) : (
            <EmptyExercises />
          )
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {exercises.map((exercise) => (
              <Card key={exercise.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{exercise.name}</CardTitle>
                      <CardDescription>{exercise.nameEn}</CardDescription>
                    </div>
                    <Button variant="ghost" size="icon">
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Exercise Attributes */}
                    <div className="flex flex-wrap gap-2">
                      {exercise.attributes?.slice(0, 3).map((attr, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {attr.attributeName?.name || attr.attributeValue?.value}
                        </Badge>
                      ))}
                      {exercise.attributes && exercise.attributes.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{exercise.attributes.length - 3} more
                        </Badge>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">
                        <Play className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Load More Button (if using pagination) */}
        {exercises && exercises.length > 0 && exercisesData?.pagination?.hasNext && (
          <div className="text-center mt-8">
            <Button variant="outline" size="lg">
              Load More Exercises
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
