module.exports = {

"[project]/.next-internal/server/app/api/programs/public/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/programs/public/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Public Programs API Route
 * Calls workout-cool server action directly for public programs
 */ __turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Mock data for testing - replace with actual workout-cool integration
const mockPrograms = [
    {
        id: '1',
        slug: 'beginner-strength',
        slugEn: 'beginner-strength',
        title: 'Beginner Strength Training',
        titleEn: 'Beginner Strength Training',
        description: 'A comprehensive strength training program for beginners',
        descriptionEn: 'A comprehensive strength training program for beginners',
        category: 'Strength',
        image: '/images/programs/strength.jpg',
        level: 'BEGINNER',
        type: 'Strength Training',
        durationWeeks: 8,
        sessionsPerWeek: 3,
        sessionDurationMin: 45,
        equipment: [
            'Dumbbells',
            'Barbell',
            'Bench'
        ],
        isPremium: false,
        participantCount: 150,
        totalWeeks: 8,
        totalSessions: 24,
        totalExercises: 12,
        totalEnrollments: 150
    },
    {
        id: '2',
        slug: 'cardio-hiit',
        slugEn: 'cardio-hiit',
        title: 'HIIT Cardio Blast',
        titleEn: 'HIIT Cardio Blast',
        description: 'High-intensity interval training for maximum fat burn',
        descriptionEn: 'High-intensity interval training for maximum fat burn',
        category: 'Cardio',
        image: '/images/programs/hiit.jpg',
        level: 'INTERMEDIATE',
        type: 'HIIT',
        durationWeeks: 6,
        sessionsPerWeek: 4,
        sessionDurationMin: 30,
        equipment: [
            'None'
        ],
        isPremium: true,
        participantCount: 89,
        totalWeeks: 6,
        totalSessions: 24,
        totalExercises: 20,
        totalEnrollments: 89
    }
];
async function GET(request) {
    try {
        // For now, return mock data
        // TODO: Integrate with workout-cool server actions when available
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockPrograms);
    } catch (error) {
        console.error('Error fetching public programs:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch public programs'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__102100e6._.js.map