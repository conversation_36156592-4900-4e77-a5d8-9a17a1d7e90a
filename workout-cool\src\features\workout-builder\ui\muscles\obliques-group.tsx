import React from "react";
import { ExerciseAttributeValueEnum } from "@prisma/client";

export const ObliquesGroup = ({
  onToggleMuscle,
  getMuscleClasses,
}: {
  onToggleMuscle: (muscle: ExerciseAttributeValueEnum) => void;
  getMuscleClasses: (muscle: ExerciseAttributeValueEnum) => string;
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(ExerciseAttributeValueEnum.OBLIQUES)}>
      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.OBLIQUES)}
        d="M 134.28,178.67
           C 135.80,178.08 137.44,177.63 138.74,176.72
             142.62,174.02 145.10,170.17 146.78,165.84
             147.58,163.80 147.29,161.70 146.02,159.83
             143.83,156.62 141.13,153.89 138.15,151.43
             137.82,151.16 137.39,151.02 136.74,150.68
             136.87,151.36 136.92,151.76 137.02,152.15
             138.19,156.68 138.78,161.29 138.44,165.97
             138.11,170.47 136.77,174.56 133.37,177.76
             133.19,177.94 133.06,178.17 132.66,178.69
             133.43,178.69 133.91,178.81 134.28,178.67"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        id="path128"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.OBLIQUES)}
        d="M 96.36,177.87
           C 93.31,175.14 91.86,171.59 91.33,167.64
             90.63,162.39 91.23,157.23 92.57,152.14
             92.68,151.73 92.71,151.30 92.78,150.88
             92.69,150.83 92.60,150.78 92.51,150.72
             92.06,151.03 91.54,151.26 91.17,151.65
             88.72,154.19 86.19,156.67 83.92,159.37
             82.10,161.53 81.92,164.15 83.14,166.68
             84.09,168.66 85.19,170.60 86.47,172.39
             88.38,175.05 90.74,177.28 93.94,178.36
             94.81,178.66 95.73,179.06 96.79,178.44
             96.58,178.17 96.50,177.99 96.36,177.87"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        id="path130"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.OBLIQUES)}
        d="M 133.30,127.21
           C 134.46,133.05 135.57,138.89 136.92,144.67
             137.30,146.28 138.27,147.83 139.28,149.17
             140.60,150.93 142.19,152.50 143.76,154.05
             146.20,156.46 148.04,159.20 148.93,162.54
             149.03,162.88 149.17,163.22 149.39,163.82
             150.71,162.19 151.16,160.53 151.28,158.82
             151.28,158.82 152.05,150.83 152.05,150.83
             152.19,149.95 152.36,149.07 152.47,148.18
             153.20,142.15 153.30,136.10 152.92,130.04
             152.72,126.77 152.45,123.50 152.14,120.24
             152.01,118.81 151.55,118.53 150.26,119.02
             150.26,119.02 135.99,124.40 135.99,124.40
             135.99,124.40 135.99,124.41 135.99,124.41
             135.78,124.42 135.56,124.44 135.32,124.48
             133.82,124.70 133.01,125.72 133.30,127.21"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        id="path192"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.OBLIQUES)}
        d="M 79.63,119.11
           C 79.59,119.09 79.56,119.08 79.52,119.06
             79.52,119.06 79.39,119.02 79.39,119.02
             79.39,119.02 79.39,119.02 79.39,119.02
             77.87,118.42 77.56,119.01 77.32,121.30
             76.60,128.01 76.33,134.74 76.69,141.49
             76.83,144.18 77.31,147.83 77.60,150.78
             77.60,150.78 77.60,150.83 77.60,150.83
             77.60,150.83 78.30,158.17 78.30,158.17
             78.30,158.23 78.30,158.29 78.30,158.36
             78.33,160.24 78.75,162.03 80.08,163.65
             80.28,163.40 80.39,163.33 80.42,163.23
             80.55,162.88 80.67,162.51 80.78,162.15
             81.49,159.74 82.57,157.54 84.32,155.70
             85.82,154.13 87.32,152.56 88.81,150.98
             91.12,148.51 92.82,145.72 93.31,142.29
             93.42,141.50 93.60,140.72 93.76,139.94
             94.61,135.79 95.50,131.66 96.28,127.50
             96.64,125.60 95.80,124.63 93.88,124.42
             93.80,124.42 93.73,124.42 93.65,124.41
             93.65,124.41 93.66,124.40 93.66,124.40
             93.66,124.40 79.63,119.11 79.63,119.11"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        id="path194"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 131.50,125.25
           C 131.50,125.25 135.75,145.25 135.75,145.25
             135.75,145.25 136.75,154.00 136.75,154.00
             136.75,154.00 137.00,164.75 137.00,164.75
             137.00,164.75 136.25,171.75 136.25,171.75
             136.25,171.75 132.75,176.75 132.75,176.75
             132.75,176.75 134.75,180.25 134.75,180.25
             134.75,180.25 133.50,192.00 133.50,192.00
             133.50,192.00 130.75,202.50 130.75,202.50
             130.75,202.50 124.75,217.25 124.75,217.25
             124.75,217.25 123.25,220.50 123.25,220.50
             123.25,220.50 132.00,217.25 132.00,217.25
             132.00,217.25 144.75,204.00 144.75,204.00
             144.75,204.00 151.25,192.75 151.25,192.75
             151.25,192.75 152.50,184.25 152.50,184.25
             152.50,184.25 152.00,171.75 152.00,171.75
             152.00,171.75 151.25,167.00 151.25,167.00
             151.25,167.00 154.75,158.50 154.75,158.50
             154.75,158.50 155.25,133.75 155.25,133.75
             155.25,133.75 154.50,116.50 154.50,116.25
             154.50,116.00 152.00,117.00 152.00,117.00
             152.00,117.00 145.75,120.25 145.75,120.25
             145.75,120.25 138.25,123.50 138.25,123.50
             138.25,123.50 131.50,125.00 131.50,125.00"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        stroke="black"
        strokeWidth="0"
      />

      {/* front  */}
      <path
        className="fill-transparent"
        d="M 75.00,115.75
           C 75.00,115.75 74.00,138.50 74.00,138.50
             74.00,138.50 75.50,151.00 75.50,151.00
             75.50,151.00 75.75,163.25 75.75,163.25
             75.75,163.25 78.75,166.50 78.75,166.50
             78.75,166.50 76.00,179.50 76.00,179.50
             76.00,179.50 76.00,187.00 76.00,187.00
             76.00,187.00 77.75,192.00 77.75,192.00
             77.75,192.00 81.50,197.50 81.50,197.50
             81.50,197.50 87.25,205.50 87.25,205.50
             87.25,205.50 94.00,213.50 94.00,213.50
             94.00,213.50 102.25,219.25 102.25,219.25
             102.25,219.25 106.00,220.75 106.00,220.75
             106.00,220.75 101.25,207.00 101.25,207.00
             101.25,207.00 96.75,194.50 96.75,194.50
             96.75,194.50 95.00,180.00 94.75,180.00
             94.50,180.00 96.25,177.25 96.25,177.25
             96.25,177.25 93.50,170.50 93.50,170.50
             93.50,170.50 93.25,155.00 93.25,155.00
             93.25,155.00 94.00,142.75 94.00,142.75
             94.00,142.75 96.75,127.75 96.75,127.75
             96.75,127.75 98.50,125.00 98.50,125.00
             98.50,125.00 80.50,119.00 80.50,119.00
             80.50,119.00 75.25,115.50 75.25,115.50"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.OBLIQUES)}
        d="M 125.27,217.07
           C 125.09,217.39 125.10,217.82 125.02,218.20
             125.41,218.20 125.87,218.33 126.20,218.18
             127.38,217.66 128.62,217.18 129.65,216.44
             137.46,210.87 143.48,203.73 147.82,195.19
             149.29,192.30 150.60,189.30 150.80,186.01
             151.14,180.36 150.16,174.85 148.66,169.42
             148.53,168.97 148.27,168.57 147.93,167.83
             147.49,168.64 147.24,169.11 146.99,169.57
             144.93,173.36 142.29,176.69 138.49,178.77
             136.30,179.97 135.45,181.45 135.46,183.84
             135.49,188.83 134.47,193.68 133.09,198.45
             131.22,204.97 128.54,211.14 125.27,217.07"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        id="path112"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.OBLIQUES)}
        d="M 97.07,214.29
           C 99.04,215.92 101.02,217.56 103.56,218.25
             103.88,218.33 104.26,218.19 104.62,218.15
             104.55,217.83 104.56,217.48 104.41,217.20
             103.37,215.13 102.21,213.10 101.24,211.00
             97.13,202.07 94.13,192.84 94.06,182.87
             94.05,181.21 93.41,180.03 91.92,179.28
             87.87,177.21 85.11,173.86 82.81,170.05
             82.45,169.45 82.15,168.80 81.81,168.18
             81.68,168.18 81.55,168.18 81.41,168.17
             81.20,168.73 80.92,169.26 80.80,169.83
             80.19,172.85 79.48,175.86 79.05,178.91
             78.44,183.21 78.48,187.48 80.18,191.62
             83.88,200.63 89.61,208.11 97.07,214.29"
        data-elem={ExerciseAttributeValueEnum.OBLIQUES}
        id="path114"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
};
