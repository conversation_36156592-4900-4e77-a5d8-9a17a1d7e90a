{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from \"lucide-react\"\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const toggleMenu = () => setIsOpen(!isOpen)\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">Workout.cool</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/workouts\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Workouts\n            </Link>\n            <Link\n              href=\"/exercises\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Exercises\n            </Link>\n            <Link\n              href=\"/progress\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Progress\n            </Link>\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button variant=\"ghost\" size=\"icon\" onClick={toggleMenu}>\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/workouts\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Dumbbell className=\"h-4 w-4\" />\n                <span>Workouts</span>\n              </Link>\n              <Link\n                href=\"/exercises\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BookOpen className=\"h-4 w-4\" />\n                <span>Exercises</span>\n              </Link>\n              <Link\n                href=\"/progress\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BarChart3 className=\"h-4 w-4\" />\n                <span>Progress</span>\n              </Link>\n              <div className=\"pt-4 border-t\">\n                <div className=\"flex flex-col space-y-2\">\n                  <Button variant=\"outline\" className=\"justify-start\">\n                    Sign In\n                  </Button>\n                  <Button className=\"justify-start bg-blue-600 hover:bg-blue-700\">\n                    Get Started\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAA<PERSON>,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAM9D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,SAAS;0CAC1C,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDAGpD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlF;GAnGgB;KAAA", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/workouts/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Navigation } from \"@/components/navigation\"\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport {\n  Clock,\n  Users,\n  Zap,\n  Target,\n  Play,\n  Plus,\n  Filter,\n  Search\n} from \"lucide-react\"\n\nconst workoutPlans = [\n  {\n    id: 1,\n    title: \"Morning Energy Boost\",\n    description: \"Quick 15-minute workout to start your day\",\n    duration: 15,\n    difficulty: \"Beginner\",\n    category: \"Cardio\",\n    exercises: 8,\n    image: \"🌅\"\n  },\n  {\n    id: 2,\n    title: \"Upper Body Strength\",\n    description: \"Build strength in your arms, chest, and shoulders\",\n    duration: 45,\n    difficulty: \"Intermediate\",\n    category: \"Strength\",\n    exercises: 12,\n    image: \"💪\"\n  },\n  {\n    id: 3,\n    title: \"Core Crusher\",\n    description: \"Intense core workout for a stronger midsection\",\n    duration: 30,\n    difficulty: \"Advanced\",\n    category: \"Core\",\n    exercises: 10,\n    image: \"🔥\"\n  },\n  {\n    id: 4,\n    title: \"Full Body HIIT\",\n    description: \"High-intensity interval training for maximum burn\",\n    duration: 25,\n    difficulty: \"Intermediate\",\n    category: \"HIIT\",\n    exercises: 6,\n    image: \"⚡\"\n  },\n  {\n    id: 5,\n    title: \"Yoga Flow\",\n    description: \"Gentle stretching and mindfulness practice\",\n    duration: 40,\n    difficulty: \"Beginner\",\n    category: \"Flexibility\",\n    exercises: 15,\n    image: \"🧘\"\n  },\n  {\n    id: 6,\n    title: \"Lower Body Power\",\n    description: \"Strengthen your legs and glutes\",\n    duration: 35,\n    difficulty: \"Intermediate\",\n    category: \"Strength\",\n    exercises: 9,\n    image: \"🦵\"\n  }\n]\n\nconst getDifficultyColor = (difficulty: string) => {\n  switch (difficulty) {\n    case \"Beginner\": return \"bg-green-100 text-green-800\"\n    case \"Intermediate\": return \"bg-yellow-100 text-yellow-800\"\n    case \"Advanced\": return \"bg-red-100 text-red-800\"\n    default: return \"bg-gray-100 text-gray-800\"\n  }\n}\n\nexport default function Workouts() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n\n      {/* Header Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\">\n              Your Workouts\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Create, customize, and track your workout plans\n            </p>\n          </div>\n\n          <div className=\"flex justify-center\">\n            <Button size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\">\n              <Plus className=\"mr-2 h-5 w-5\" />\n              Create New Workout\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Search and Filters */}\n\n        {/* Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-8\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search workouts...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <Button variant=\"outline\">\n            <Filter className=\"h-4 w-4 mr-2\" />\n            Filters\n          </Button>\n        </div>\n\n        {/* Workout Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {workoutPlans.map((workout) => (\n            <Card key={workout.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"text-4xl mb-2\">{workout.image}</div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(workout.difficulty)}`}>\n                    {workout.difficulty}\n                  </span>\n                </div>\n                <CardTitle className=\"text-lg\">{workout.title}</CardTitle>\n                <CardDescription>{workout.description}</CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                    <div className=\"flex items-center gap-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      <span>{workout.duration} min</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Target className=\"h-4 w-4\" />\n                      <span>{workout.exercises} exercises</span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium\">\n                      {workout.category}\n                    </span>\n                  </div>\n                  \n                  <Button className=\"w-full\">\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    Start Workout\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Popular Categories */}\n        <div className=\"mt-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n            Popular Categories\n          </h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            {[\n              { name: \"Strength\", icon: \"💪\", count: 24 },\n              { name: \"Cardio\", icon: \"❤️\", count: 18 },\n              { name: \"HIIT\", icon: \"⚡\", count: 12 },\n              { name: \"Yoga\", icon: \"🧘\", count: 15 }\n            ].map((category) => (\n              <Card key={category.name} className=\"text-center hover:shadow-md transition-shadow cursor-pointer\">\n                <CardContent className=\"p-6\">\n                  <div className=\"text-3xl mb-2\">{category.icon}</div>\n                  <h3 className=\"font-semibold text-gray-900\">{category.name}</h3>\n                  <p className=\"text-sm text-gray-600\">{category.count} workouts</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;IACT;CACD;AAED,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YAAY,OAAO;QACxB,KAAK;YAAgB,OAAO;QAC5B,KAAK;YAAY,OAAO;QACxB;YAAS,OAAO;IAClB;AACF;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;;kDAC1B,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAIb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAGd,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,QAAQ,KAAK;;;;;;kEAC7C,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,QAAQ,UAAU,GAAG;kEACpG,QAAQ,UAAU;;;;;;;;;;;;0DAGvB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,QAAQ,KAAK;;;;;;0DAC7C,6LAAC,mIAAA,CAAA,kBAAe;0DAAE,QAAQ,WAAW;;;;;;;;;;;;kDAEvC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;;wEAAM,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;;wEAAM,QAAQ,SAAS;wEAAC;;;;;;;;;;;;;;;;;;;8DAI7B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;8DAIrB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;+BA/B9B,QAAQ,EAAE;;;;;;;;;;kCAyCzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAY,MAAM;wCAAM,OAAO;oCAAG;oCAC1C;wCAAE,MAAM;wCAAU,MAAM;wCAAM,OAAO;oCAAG;oCACxC;wCAAE,MAAM;wCAAQ,MAAM;wCAAK,OAAO;oCAAG;oCACrC;wCAAE,MAAM;wCAAQ,MAAM;wCAAM,OAAO;oCAAG;iCACvC,CAAC,GAAG,CAAC,CAAC,yBACL,6LAAC,mIAAA,CAAA,OAAI;wCAAqB,WAAU;kDAClC,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;8DAAiB,SAAS,IAAI;;;;;;8DAC7C,6LAAC;oDAAG,WAAU;8DAA+B,SAAS,IAAI;;;;;;8DAC1D,6LAAC;oDAAE,WAAU;;wDAAyB,SAAS,KAAK;wDAAC;;;;;;;;;;;;;uCAJ9C,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAatC;KAjHwB", "debugId": null}}]}