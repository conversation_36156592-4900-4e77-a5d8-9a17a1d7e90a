module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/providers/query-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QueryProvider": (()=>QueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
/**
 * React Query Provider for AI-fitness application
 * Provides global state management and caching for API requests
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function QueryProvider({ children }) {
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
            defaultOptions: {
                queries: {
                    // Stale time: how long data is considered fresh
                    staleTime: 5 * 60 * 1000,
                    // Cache time: how long data stays in cache after component unmounts
                    gcTime: 10 * 60 * 1000,
                    // Retry configuration
                    retry: (failureCount, error)=>{
                        // Don't retry on authentication errors
                        if (error?.status === 401 || error?.status === 403) {
                            return false;
                        }
                        // Don't retry on validation errors
                        if (error?.status === 422) {
                            return false;
                        }
                        // Retry up to 3 times for other errors
                        return failureCount < 3;
                    },
                    // Retry delay with exponential backoff
                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
                    // Refetch on window focus (useful for real-time data)
                    refetchOnWindowFocus: false,
                    // Refetch on reconnect
                    refetchOnReconnect: true,
                    // Background refetch interval (disabled by default)
                    refetchInterval: false
                },
                mutations: {
                    // Retry mutations on network errors
                    retry: (failureCount, error)=>{
                        // Don't retry on client errors (4xx)
                        if (error?.status >= 400 && error?.status < 500) {
                            return false;
                        }
                        // Retry up to 2 times for server errors
                        return failureCount < 2;
                    },
                    // Retry delay for mutations
                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 10000)
                }
            }
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {
                initialIsOpen: false,
                buttonPosition: "bottom-right"
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/query-provider.tsx",
                lineNumber: 80,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/providers/query-provider.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/api/config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Configuration for AI-fitness application
 * Connects to workout-cool backend API
 */ __turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG)
});
const API_CONFIG = {
    // Base URL for the workout-cool backend API
    BASE_URL: process.env.NEXT_PUBLIC_WORKOUT_COOL_API_URL || 'http://localhost:3000',
    // API endpoints - Updated to match workout-cool API structure
    ENDPOINTS: {
        // Authentication - Better Auth endpoints
        AUTH: {
            SIGNIN: '/api/auth/sign-in',
            SIGNUP: '/api/auth/sign-up',
            SIGNOUT: '/api/auth/sign-out',
            SESSION: '/api/auth/session',
            RESET_PASSWORD: '/api/auth/reset-password',
            VERIFY_EMAIL: '/api/auth/verify-email'
        },
        // Public Programs - workout-cool public API
        PROGRAMS: {
            PUBLIC_LIST: '/api/programs/public',
            DETAILS: (slug)=>`/api/programs/${slug}`,
            ENROLL: (id)=>`/api/programs/${id}/enroll`,
            PROGRESS: (id)=>`/api/programs/${id}/progress`,
            SESSIONS: (programId)=>`/api/programs/${programId}/sessions`,
            SESSION_DETAIL: (programId, sessionId)=>`/api/programs/${programId}/sessions/${sessionId}`
        },
        // Exercises - workout-cool exercise API
        EXERCISES: {
            PUBLIC_LIST: '/api/exercises/public',
            SEARCH: '/api/exercises/search',
            DETAILS: (id)=>`/api/exercises/${id}`,
            ATTRIBUTES: '/api/exercises/attributes',
            BY_MUSCLE_GROUP: (muscleGroup)=>`/api/exercises/muscle-groups/${muscleGroup}`
        },
        // Workout Sessions - workout-cool session management
        WORKOUTS: {
            LIST: '/api/workout-sessions',
            CREATE: '/api/workout-sessions',
            DETAILS: (id)=>`/api/workout-sessions/${id}`,
            UPDATE: (id)=>`/api/workout-sessions/${id}`,
            DELETE: (id)=>`/api/workout-sessions/${id}`,
            COMPLETE: (id)=>`/api/workout-sessions/${id}/complete`,
            SYNC: '/api/workout-sessions/sync',
            USER_SESSIONS: (userId)=>`/api/users/${userId}/workout-sessions`
        },
        // User Progress - workout-cool progress tracking
        PROGRESS: {
            USER_STATS: (userId)=>`/api/users/${userId}/stats`,
            PROGRAM_PROGRESS: (userId, programId)=>`/api/users/${userId}/programs/${programId}/progress`,
            WORKOUT_HISTORY: (userId)=>`/api/users/${userId}/workout-history`,
            BODY_MEASUREMENTS: (userId)=>`/api/users/${userId}/body-measurements`,
            GOALS: (userId)=>`/api/users/${userId}/goals`
        },
        // Premium - workout-cool subscription system
        PREMIUM: {
            PLANS: '/api/premium/plans',
            STATUS: '/api/premium/status',
            SUBSCRIPTION: '/api/premium/subscription',
            CHECKOUT: '/api/premium/checkout',
            BILLING_PORTAL: '/api/premium/billing-portal'
        },
        // User Management - workout-cool user API
        USERS: {
            PROFILE: '/api/users/profile',
            UPDATE_PROFILE: '/api/users/profile',
            PREFERENCES: '/api/users/preferences',
            ENROLLMENTS: (userId)=>`/api/users/${userId}/program-enrollments`
        },
        // Health Check
        HEALTH: '/api/health'
    },
    // Request timeouts
    TIMEOUT: {
        DEFAULT: 10000,
        UPLOAD: 30000,
        DOWNLOAD: 60000
    },
    // Retry configuration
    RETRY: {
        ATTEMPTS: 3,
        DELAY: 1000,
        BACKOFF_FACTOR: 2
    }
};
}}),
"[project]/src/lib/api/errors.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Error Classes for AI-fitness application
 * Provides structured error handling for different types of API errors
 */ __turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "AuthenticationError": (()=>AuthenticationError),
    "AuthorizationError": (()=>AuthorizationError),
    "ConflictError": (()=>ConflictError),
    "NetworkError": (()=>NetworkError),
    "NotFoundError": (()=>NotFoundError),
    "RateLimitError": (()=>RateLimitError),
    "ValidationError": (()=>ValidationError),
    "getErrorCode": (()=>getErrorCode),
    "getErrorMessage": (()=>getErrorMessage),
    "isApiError": (()=>isApiError),
    "isAuthenticationError": (()=>isAuthenticationError),
    "isNetworkError": (()=>isNetworkError),
    "isValidationError": (()=>isValidationError),
    "shouldRetry": (()=>shouldRetry)
});
class ApiError extends Error {
    status;
    code;
    details;
    constructor(message, status = 500, code, details){
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.code = code;
        this.details = details;
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ApiError);
        }
    }
    /**
   * Check if error is a client error (4xx)
   */ get isClientError() {
        return this.status >= 400 && this.status < 500;
    }
    /**
   * Check if error is a server error (5xx)
   */ get isServerError() {
        return this.status >= 500;
    }
    /**
   * Convert error to JSON for logging
   */ toJSON() {
        return {
            name: this.name,
            message: this.message,
            status: this.status,
            code: this.code,
            details: this.details,
            stack: this.stack
        };
    }
}
class NetworkError extends ApiError {
    constructor(message = 'Network error occurred'){
        super(message, 0, 'NETWORK_ERROR');
        this.name = 'NetworkError';
    }
}
class AuthenticationError extends ApiError {
    constructor(message = 'Authentication required'){
        super(message, 401, 'AUTHENTICATION_ERROR');
        this.name = 'AuthenticationError';
    }
}
class AuthorizationError extends ApiError {
    constructor(message = 'Access denied'){
        super(message, 403, 'AUTHORIZATION_ERROR');
        this.name = 'AuthorizationError';
    }
}
class ValidationError extends ApiError {
    fieldErrors;
    constructor(message = 'Validation failed', fieldErrors = {}){
        super(message, 422, 'VALIDATION_ERROR', fieldErrors);
        this.name = 'ValidationError';
        this.fieldErrors = fieldErrors;
    }
    /**
   * Get error message for a specific field
   */ getFieldError(field) {
        const errors = this.fieldErrors[field];
        return errors && errors.length > 0 ? errors[0] : null;
    }
    /**
   * Check if a specific field has errors
   */ hasFieldError(field) {
        return Boolean(this.fieldErrors[field]?.length);
    }
    /**
   * Get all field error messages as a flat array
   */ getAllFieldErrors() {
        return Object.values(this.fieldErrors).flat();
    }
}
class NotFoundError extends ApiError {
    constructor(message = 'Resource not found'){
        super(message, 404, 'NOT_FOUND_ERROR');
        this.name = 'NotFoundError';
    }
}
class ConflictError extends ApiError {
    constructor(message = 'Resource conflict'){
        super(message, 409, 'CONFLICT_ERROR');
        this.name = 'ConflictError';
    }
}
class RateLimitError extends ApiError {
    retryAfter;
    constructor(message = 'Rate limit exceeded', retryAfter){
        super(message, 429, 'RATE_LIMIT_ERROR', {
            retryAfter
        });
        this.name = 'RateLimitError';
        this.retryAfter = retryAfter;
    }
}
function isApiError(error) {
    return error instanceof ApiError;
}
function isNetworkError(error) {
    return error instanceof NetworkError;
}
function isAuthenticationError(error) {
    return error instanceof AuthenticationError;
}
function isValidationError(error) {
    return error instanceof ValidationError;
}
function getErrorMessage(error) {
    if (isApiError(error)) {
        return error.message;
    }
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'An unexpected error occurred';
}
function getErrorCode(error) {
    if (isApiError(error)) {
        return error.code || null;
    }
    return null;
}
function shouldRetry(error) {
    if (isNetworkError(error)) {
        return true;
    }
    if (isApiError(error)) {
        // Retry on server errors but not client errors
        return error.isServerError;
    }
    return false;
}
}}),
"[project]/src/lib/api/client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Client for AI-fitness application
 * Handles HTTP requests, authentication, and error handling
 */ __turbopack_context__.s({
    "apiClient": (()=>apiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/errors.ts [app-ssr] (ecmascript)");
;
;
class ApiClient {
    baseUrl;
    defaultHeaders;
    constructor(){
        this.baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }
    /**
   * Get authentication token from session storage or cookies
   */ getAuthToken() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return null;
    }
    /**
   * Build request headers with authentication if available
   */ buildHeaders(options = {}) {
        const headers = {
            ...this.defaultHeaders
        };
        // Add authentication header if required and available
        if (options.requireAuth !== false) {
            const token = this.getAuthToken();
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }
        // Merge with custom headers
        if (options.headers) {
            Object.assign(headers, options.headers);
        }
        return headers;
    }
    /**
   * Handle API response and errors
   */ async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        const isJson = contentType?.includes('application/json');
        let data;
        try {
            data = isJson ? await response.json() : await response.text();
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]('Failed to parse response', response.status);
        }
        if (!response.ok) {
            switch(response.status){
                case 401:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthenticationError"](data.message || 'Authentication required');
                case 422:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationError"](data.message || 'Validation failed', data.errors);
                case 404:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Resource not found', 404);
                case 500:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Internal server error', 500);
                default:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Request failed', response.status);
            }
        }
        return data;
    }
    /**
   * Make HTTP request with retry logic
   */ async makeRequest(url, options = {}) {
        const { timeout = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT.DEFAULT, retries = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.ATTEMPTS, ...requestOptions } = options;
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), timeout);
        const requestConfig = {
            ...requestOptions,
            headers: this.buildHeaders(options),
            signal: controller.signal
        };
        let lastError = null;
        for(let attempt = 0; attempt <= retries; attempt++){
            try {
                const response = await fetch(`${this.baseUrl}${url}`, requestConfig);
                clearTimeout(timeoutId);
                return await this.handleResponse(response);
            } catch (error) {
                lastError = error;
                // Don't retry on authentication or validation errors
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthenticationError"] || error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationError"]) {
                    throw error;
                }
                // Don't retry on the last attempt
                if (attempt === retries) {
                    break;
                }
                // Wait before retrying
                const delay = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.DELAY * Math.pow(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.BACKOFF_FACTOR, attempt);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            }
        }
        clearTimeout(timeoutId);
        // Handle network errors
        if (lastError) {
            if (lastError.name === 'AbortError') {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NetworkError"]('Request timeout');
            }
            if (lastError.name === 'TypeError') {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NetworkError"]('Network error - please check your connection');
            }
            throw lastError;
        }
        throw new Error('Request failed after all retries');
    }
    /**
   * GET request
   */ async get(url, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'GET'
        });
    }
    /**
   * POST request
   */ async post(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * PUT request
   */ async put(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * PATCH request
   */ async patch(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * DELETE request
   */ async delete(url, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'DELETE'
        });
    }
}
const apiClient = new ApiClient();
}}),
"[project]/src/lib/api/adapters/workout-cool.adapter.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Adapter for workout-cool API data structures
 * Converts workout-cool data models to our frontend types
 */ __turbopack_context__.s({
    "WorkoutCoolAdapter": (()=>WorkoutCoolAdapter)
});
class WorkoutCoolAdapter {
    /**
   * Convert workout-cool program to frontend Program type
   */ static adaptProgram(wcProgram) {
        return {
            id: wcProgram.id,
            title: wcProgram.titleEn || wcProgram.title,
            description: wcProgram.descriptionEn || wcProgram.description,
            slug: wcProgram.slugEn || wcProgram.slug,
            category: wcProgram.category,
            image: wcProgram.image,
            difficulty: wcProgram.level.toLowerCase(),
            duration: wcProgram.sessionDurationMin,
            durationWeeks: wcProgram.durationWeeks,
            sessionsPerWeek: wcProgram.sessionsPerWeek,
            equipment: wcProgram.equipment,
            isPremium: wcProgram.isPremium,
            isActive: wcProgram.isActive,
            participantCount: wcProgram.participantCount,
            createdAt: new Date(wcProgram.createdAt),
            updatedAt: new Date(wcProgram.updatedAt),
            coaches: wcProgram.coaches?.map((coach)=>({
                    id: coach.id,
                    name: coach.name,
                    image: coach.image,
                    order: coach.order
                })) || [],
            weeks: wcProgram.weeks?.map((week)=>({
                    id: week.id,
                    weekNumber: week.weekNumber,
                    sessions: week.sessions.map((session)=>this.adaptProgramSession(session))
                })) || []
        };
    }
    /**
   * Convert workout-cool program session to frontend ProgramSession type
   */ static adaptProgramSession(wcSession) {
        return {
            id: wcSession.id,
            sessionNumber: wcSession.sessionNumber,
            title: wcSession.titleEn || wcSession.title,
            description: wcSession.descriptionEn || wcSession.description,
            estimatedDuration: wcSession.estimatedMinutes,
            equipment: wcSession.equipment,
            isPremium: wcSession.isPremium,
            exercises: wcSession.exercises.map((ex)=>({
                    id: ex.id,
                    order: ex.order,
                    exerciseId: ex.exercise.id,
                    exercise: this.adaptExercise(ex.exercise),
                    sets: ex.suggestedSets.map((set)=>({
                            setIndex: set.setIndex,
                            reps: set.reps,
                            weight: set.weight,
                            duration: set.duration,
                            restTime: set.restTime
                        }))
                }))
        };
    }
    /**
   * Convert workout-cool exercise to frontend Exercise type
   */ static adaptExercise(wcExercise) {
        // Extract muscle groups and equipment from attributes
        const muscleGroups = [];
        const equipment = [];
        let category = 'strength';
        let difficulty = 'beginner';
        wcExercise.attributes.forEach((attr)=>{
            const attrName = attr.attributeName.nameEn || attr.attributeName.name;
            const attrValue = attr.attributeValue.valueEn || attr.attributeValue.value;
            if (attrName.toLowerCase().includes('muscle') || attrName.toLowerCase().includes('target')) {
                muscleGroups.push(attrValue);
            } else if (attrName.toLowerCase().includes('equipment')) {
                equipment.push(attrValue);
            } else if (attrName.toLowerCase().includes('category') || attrName.toLowerCase().includes('type')) {
                if (attrValue.toLowerCase().includes('cardio')) category = 'cardio';
                else if (attrValue.toLowerCase().includes('flexibility') || attrValue.toLowerCase().includes('stretch')) category = 'flexibility';
                else if (attrValue.toLowerCase().includes('balance')) category = 'balance';
            } else if (attrName.toLowerCase().includes('difficulty') || attrName.toLowerCase().includes('level')) {
                if (attrValue.toLowerCase().includes('intermediate')) difficulty = 'intermediate';
                else if (attrValue.toLowerCase().includes('advanced')) difficulty = 'advanced';
            }
        });
        return {
            id: wcExercise.id,
            name: wcExercise.nameEn || wcExercise.name,
            description: wcExercise.descriptionEn || wcExercise.description,
            category,
            muscleGroups,
            equipment,
            difficulty,
            instructions: (wcExercise.instructionsEn || wcExercise.instructions)?.split('\n') || [],
            tips: (wcExercise.tipsEn || wcExercise.tips)?.split('\n') || [],
            imageUrl: wcExercise.imageUrl,
            videoUrl: wcExercise.videoUrl
        };
    }
    /**
   * Convert workout-cool user to frontend User type
   */ static adaptUser(wcUser) {
        return {
            id: wcUser.id,
            email: wcUser.email,
            name: wcUser.name,
            firstName: wcUser.firstName,
            lastName: wcUser.lastName,
            avatar: wcUser.image,
            role: wcUser.role,
            isPremium: wcUser.isPremium || false,
            preferences: {
                language: wcUser.locale || 'en',
                timezone: 'UTC',
                units: 'metric',
                notifications: {
                    email: true,
                    push: true,
                    workout: true,
                    progress: true
                }
            },
            createdAt: new Date(wcUser.createdAt),
            updatedAt: new Date(wcUser.updatedAt)
        };
    }
    /**
   * Convert workout-cool workout session to frontend WorkoutSession type
   */ static adaptWorkoutSession(wcSession) {
        return {
            id: wcSession.id,
            userId: wcSession.userId,
            programId: wcSession.programId,
            sessionId: wcSession.sessionId,
            status: wcSession.status.toLowerCase(),
            startedAt: wcSession.startedAt ? new Date(wcSession.startedAt) : undefined,
            completedAt: wcSession.completedAt ? new Date(wcSession.completedAt) : undefined,
            duration: wcSession.duration,
            notes: wcSession.notes,
            exercises: wcSession.exercises.map((ex)=>({
                    id: ex.id,
                    exerciseId: ex.exerciseId,
                    order: ex.order,
                    exercise: this.adaptExercise(ex.exercise),
                    sets: ex.sets.map((set)=>({
                            id: set.id,
                            setIndex: set.setIndex,
                            reps: set.reps,
                            weight: set.weight,
                            duration: set.duration,
                            restTime: set.restTime,
                            completed: set.completed
                        }))
                }))
        };
    }
    /**
   * Convert frontend data to workout-cool format for API requests
   */ static toWorkoutCoolFormat = {
        /**
     * Convert frontend workout session to workout-cool format
     */ workoutSession: (session)=>({
                userId: session.userId,
                programId: session.programId,
                sessionId: session.sessionId,
                status: session.status?.toUpperCase(),
                startedAt: session.startedAt?.toISOString(),
                completedAt: session.completedAt?.toISOString(),
                duration: session.duration,
                notes: session.notes,
                exercises: session.exercises?.map((ex)=>({
                        exerciseId: ex.exerciseId,
                        order: ex.order,
                        sets: ex.sets.map((set)=>({
                                setIndex: set.setIndex,
                                reps: set.reps,
                                weight: set.weight,
                                duration: set.duration,
                                restTime: set.restTime,
                                completed: set.completed
                            }))
                    }))
            })
    };
}
}}),
"[project]/src/lib/api/services/auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Authentication API Service
 * Handles authentication-related API calls to workout-cool backend
 */ __turbopack_context__.s({
    "AuthService": (()=>AuthService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/adapters/workout-cool.adapter.ts [app-ssr] (ecmascript)");
;
;
;
class AuthService {
    /**
   * Sign in with email and password - adapted for Better Auth
   */ static async signIn(credentials) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNIN, {
            email: credentials.email,
            password: credentials.password
        }, {
            requireAuth: false
        });
        return {
            user: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptUser(response.user),
            sessionId: response.session.id,
            expiresAt: new Date(response.session.expiresAt)
        };
    }
    /**
   * Sign up with user data - adapted for Better Auth
   */ static async signUp(userData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNUP, {
            email: userData.email,
            password: userData.password,
            firstName: userData.firstName,
            lastName: userData.lastName,
            name: `${userData.firstName} ${userData.lastName}`
        }, {
            requireAuth: false
        });
        return {
            user: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptUser(response.user),
            sessionId: response.session.id,
            expiresAt: new Date(response.session.expiresAt)
        };
    }
    /**
   * Sign out current user - adapted for Better Auth
   */ static async signOut() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNOUT);
    }
    /**
   * Get current session
   */ static async getSession() {
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SESSION);
        } catch (error) {
            // Return null if not authenticated
            if (error.status === 401) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Request password reset
   */ static async requestPasswordReset(email) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.RESET_PASSWORD, {
            email
        }, {
            requireAuth: false
        });
    }
    /**
   * Reset password with token
   */ static async resetPassword(token, newPassword) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.RESET_PASSWORD, {
            token,
            password: newPassword
        }, {
            requireAuth: false
        });
    }
    /**
   * Verify email with token
   */ static async verifyEmail(token) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/verify-email', {
            token
        }, {
            requireAuth: false
        });
    }
    /**
   * Refresh authentication token
   */ static async refreshToken() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/refresh');
    }
    /**
   * Update user profile
   */ static async updateProfile(updates) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch('/api/auth/profile', updates);
    }
    /**
   * Change password
   */ static async changePassword(currentPassword, newPassword) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/change-password', {
            currentPassword,
            newPassword
        });
    }
    /**
   * Delete account
   */ static async deleteAccount(password) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete('/api/auth/account', {
            body: JSON.stringify({
                password
            })
        });
    }
    /**
   * Get user sessions
   */ static async getUserSessions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/auth/sessions');
    }
    /**
   * Revoke a specific session
   */ static async revokeSession(sessionId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/api/auth/sessions/${sessionId}`);
    }
    /**
   * Revoke all other sessions
   */ static async revokeAllOtherSessions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/sessions/revoke-all');
    }
}
}}),
"[project]/src/lib/hooks/use-auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Authentication React Query hooks
 */ __turbopack_context__.s({
    "authKeys": (()=>authKeys),
    "useAuth": (()=>useAuth),
    "useChangePassword": (()=>useChangePassword),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsPremium": (()=>useIsPremium),
    "useRequestPasswordReset": (()=>useRequestPasswordReset),
    "useResetPassword": (()=>useResetPassword),
    "useRevokeSession": (()=>useRevokeSession),
    "useSession": (()=>useSession),
    "useSignIn": (()=>useSignIn),
    "useSignOut": (()=>useSignOut),
    "useSignUp": (()=>useSignUp),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUserSessions": (()=>useUserSessions),
    "useVerifyEmail": (()=>useVerifyEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/auth.ts [app-ssr] (ecmascript)");
;
;
const authKeys = {
    all: [
        'auth'
    ],
    session: ()=>[
            ...authKeys.all,
            'session'
        ],
    sessions: ()=>[
            ...authKeys.all,
            'sessions'
        ]
};
function useSession() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: authKeys.session(),
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].getSession(),
        staleTime: 5 * 60 * 1000,
        retry: false
    });
}
function useAuth() {
    const { data: session, isLoading, error } = useSession();
    return {
        user: session?.user || null,
        isAuthenticated: !!session?.user,
        isLoading,
        error,
        session
    };
}
function useIsAdmin() {
    const { user, isAuthenticated } = useAuth();
    return isAuthenticated && user?.role === 'admin';
}
function useIsPremium() {
    const { user, isAuthenticated } = useAuth();
    return isAuthenticated && user?.isPremium === true;
}
function useSignIn() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (credentials)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].signIn(credentials),
        onSuccess: (session)=>{
            // Update session cache
            queryClient.setQueryData(authKeys.session(), session);
            // Invalidate all queries to refetch with new auth state
            queryClient.invalidateQueries();
        },
        onError: (error)=>{
            console.error('Sign in failed:', error);
        }
    });
}
function useSignUp() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (userData)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].signUp(userData),
        onSuccess: (session)=>{
            // Update session cache
            queryClient.setQueryData(authKeys.session(), session);
            // Invalidate all queries to refetch with new auth state
            queryClient.invalidateQueries();
        },
        onError: (error)=>{
            console.error('Sign up failed:', error);
        }
    });
}
function useSignOut() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].signOut(),
        onSuccess: ()=>{
            // Clear session cache
            queryClient.setQueryData(authKeys.session(), null);
            // Clear all cached data
            queryClient.clear();
        },
        onError: (error)=>{
            console.error('Sign out failed:', error);
            // Even if sign out fails on server, clear local cache
            queryClient.setQueryData(authKeys.session(), null);
            queryClient.clear();
        }
    });
}
function useRequestPasswordReset() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (email)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].requestPasswordReset(email),
        onError: (error)=>{
            console.error('Password reset request failed:', error);
        }
    });
}
function useResetPassword() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ token, newPassword })=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].resetPassword(token, newPassword),
        onError: (error)=>{
            console.error('Password reset failed:', error);
        }
    });
}
function useVerifyEmail() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (token)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].verifyEmail(token),
        onError: (error)=>{
            console.error('Email verification failed:', error);
        }
    });
}
function useUpdateProfile() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (updates)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].updateProfile(updates),
        onSuccess: (updatedUser)=>{
            // Update session cache with new user data
            const currentSession = queryClient.getQueryData(authKeys.session());
            if (currentSession) {
                queryClient.setQueryData(authKeys.session(), {
                    ...currentSession,
                    user: updatedUser
                });
            }
        },
        onError: (error)=>{
            console.error('Profile update failed:', error);
        }
    });
}
function useChangePassword() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ currentPassword, newPassword })=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].changePassword(currentPassword, newPassword),
        onError: (error)=>{
            console.error('Password change failed:', error);
        }
    });
}
function useUserSessions() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: authKeys.sessions(),
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].getUserSessions(),
        staleTime: 2 * 60 * 1000
    });
}
function useRevokeSession() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (sessionId)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].revokeSession(sessionId),
        onSuccess: ()=>{
            // Refetch sessions list
            queryClient.invalidateQueries({
                queryKey: authKeys.sessions()
            });
        },
        onError: (error)=>{
            console.error('Session revocation failed:', error);
        }
    });
}
}}),
"[project]/src/lib/providers/auth-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuthContext": (()=>useAuthContext),
    "withAdminAuth": (()=>withAdminAuth),
    "withAuth": (()=>withAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
/**
 * Authentication Provider for AI-fitness application
 * Provides authentication context and session management
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/use-auth.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const { user, isAuthenticated, isLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const [refreshKey, setRefreshKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const refreshAuth = ()=>{
        setRefreshKey((prev)=>prev + 1);
    };
    // Update API client with auth token when user changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, [
        user
    ]);
    const contextValue = {
        user,
        isAuthenticated,
        isLoading,
        error,
        refreshAuth
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/providers/auth-provider.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
function useAuthContext() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}
function withAuth(Component) {
    return function AuthenticatedComponent(props) {
        const { isAuthenticated, isLoading } = useAuthContext();
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 82,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 81,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-md p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-center text-gray-900 mb-4",
                            children: "Authentication Required"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 91,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-center mb-6",
                            children: "Please sign in to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 94,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.href = '/auth/signin',
                                    className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
                                    children: "Sign In"
                                }, void 0, false, {
                                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                                    lineNumber: 98,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.href = '/auth/signup',
                                    className: "w-full bg-gray-200 text-gray-900 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors",
                                    children: "Sign Up"
                                }, void 0, false, {
                                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                                    lineNumber: 104,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 90,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 89,
                columnNumber: 9
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/lib/providers/auth-provider.tsx",
            lineNumber: 116,
            columnNumber: 12
        }, this);
    };
}
function withAdminAuth(Component) {
    return function AdminAuthenticatedComponent(props) {
        const { user, isAuthenticated, isLoading } = useAuthContext();
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 132,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 131,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated || user?.role !== 'admin') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-md p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-center text-gray-900 mb-4",
                            children: "Admin Access Required"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-center mb-6",
                            children: "You need administrator privileges to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>window.location.href = '/',
                            className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
                            children: "Go Home"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 140,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/lib/providers/auth-provider.tsx",
            lineNumber: 158,
            columnNumber: 12
        }, this);
    };
}
}}),
"[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Global Application State Management with Zustand
 * Handles app-wide state that needs to persist across components
 */ __turbopack_context__.s({
    "appActions": (()=>appActions),
    "useAppStore": (()=>useAppStore),
    "useHasPendingSync": (()=>useHasPendingSync),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsOffline": (()=>useIsOffline),
    "useNotifications": (()=>useNotifications),
    "useOfflineState": (()=>useOfflineState),
    "useSettings": (()=>useSettings),
    "useUIState": (()=>useUIState),
    "useUnreadNotificationCount": (()=>useUnreadNotificationCount),
    "useUser": (()=>useUser),
    "useWorkoutPreferences": (()=>useWorkoutPreferences)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2f$immer$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware/immer.mjs [app-ssr] (ecmascript)");
;
;
;
// ============================================================================
// DEFAULT VALUES
// ============================================================================
const defaultSettings = {
    theme: 'system',
    language: 'en',
    units: 'metric',
    notifications: {
        workoutReminders: true,
        progressUpdates: true,
        achievements: true,
        marketing: false
    },
    privacy: {
        shareProgress: false,
        showInLeaderboards: true,
        allowDataCollection: true
    }
};
const defaultWorkoutPreferences = {
    defaultDuration: 45,
    preferredDifficulty: 'INTERMEDIATE',
    favoriteCategories: [],
    excludedEquipment: [],
    restTimeBetweenSets: 60,
    autoStartNextExercise: false,
    playWorkoutMusic: true,
    voiceInstructions: false
};
const defaultUIState = {
    sidebarCollapsed: false,
    activeWorkoutSession: null,
    currentPage: '/',
    breadcrumbs: [],
    notifications: [],
    modals: {
        workoutComplete: false,
        goalAchieved: false,
        subscriptionPrompt: false
    }
};
const defaultOfflineState = {
    isOnline: true,
    pendingSync: [],
    lastSyncTime: null
};
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2f$immer$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["immer"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        settings: defaultSettings,
        workoutPreferences: defaultWorkoutPreferences,
        ui: defaultUIState,
        offline: defaultOfflineState,
        // Actions
        setUser: (user)=>set((state)=>{
                state.user = user;
            }),
        setAuthenticated: (authenticated)=>set((state)=>{
                state.isAuthenticated = authenticated;
                if (!authenticated) {
                    state.user = null;
                }
            }),
        updateSettings: (newSettings)=>set((state)=>{
                Object.assign(state.settings, newSettings);
            }),
        updateWorkoutPreferences: (newPreferences)=>set((state)=>{
                Object.assign(state.workoutPreferences, newPreferences);
            }),
        updateUIState: (newUIState)=>set((state)=>{
                Object.assign(state.ui, newUIState);
            }),
        addNotification: (notification)=>set((state)=>{
                // Ensure notifications array exists
                if (!state.ui.notifications) {
                    state.ui.notifications = [];
                }
                const newNotification = {
                    ...notification,
                    id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now(),
                    read: false
                };
                state.ui.notifications.unshift(newNotification);
                // Keep only last 50 notifications
                if (state.ui.notifications.length > 50) {
                    state.ui.notifications = state.ui.notifications.slice(0, 50);
                }
            }),
        markNotificationRead: (id)=>set((state)=>{
                // Ensure notifications array exists
                if (!state.ui.notifications) {
                    state.ui.notifications = [];
                    return;
                }
                const notification = state.ui.notifications.find((n)=>n.id === id);
                if (notification) {
                    notification.read = true;
                }
            }),
        clearNotifications: ()=>set((state)=>{
                state.ui.notifications = [];
            }),
        setOnlineStatus: (isOnline)=>set((state)=>{
                state.offline.isOnline = isOnline;
            }),
        addPendingSync: (item)=>set((state)=>{
                const newItem = {
                    ...item,
                    id: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now()
                };
                state.offline.pendingSync.push(newItem);
            }),
        removePendingSync: (id)=>set((state)=>{
                state.offline.pendingSync = state.offline.pendingSync.filter((item)=>item.id !== id);
            }),
        clearPendingSync: ()=>set((state)=>{
                state.offline.pendingSync = [];
            }),
        updateLastSyncTime: ()=>set((state)=>{
                state.offline.lastSyncTime = Date.now();
            }),
        reset: ()=>set(()=>({
                    user: null,
                    isAuthenticated: false,
                    settings: defaultSettings,
                    workoutPreferences: defaultWorkoutPreferences,
                    ui: defaultUIState,
                    offline: defaultOfflineState
                }))
    })), {
    name: 'ai-fitness-app-store',
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
    partialize: (state)=>({
            // Only persist certain parts of the state
            settings: state.settings,
            workoutPreferences: state.workoutPreferences,
            ui: {
                sidebarCollapsed: state.ui.sidebarCollapsed,
                // Don't persist notifications and modals
                notifications: []
            },
            offline: {
                pendingSync: state.offline.pendingSync,
                lastSyncTime: state.offline.lastSyncTime
            }
        })
}));
const useUser = ()=>useAppStore((state)=>state.user);
const useIsAuthenticated = ()=>useAppStore((state)=>state.isAuthenticated);
const useSettings = ()=>useAppStore((state)=>state.settings);
const useWorkoutPreferences = ()=>useAppStore((state)=>state.workoutPreferences);
const useUIState = ()=>useAppStore((state)=>state.ui);
const useNotifications = ()=>useAppStore((state)=>state.ui.notifications || []);
const useOfflineState = ()=>useAppStore((state)=>state.offline);
const useUnreadNotificationCount = ()=>useAppStore((state)=>(state.ui.notifications || []).filter((n)=>!n.read).length);
const useHasPendingSync = ()=>useAppStore((state)=>state.offline.pendingSync.length > 0);
const useIsOffline = ()=>useAppStore((state)=>!state.offline.isOnline);
const appActions = {
    setUser: (user)=>useAppStore.getState().setUser(user),
    setAuthenticated: (authenticated)=>useAppStore.getState().setAuthenticated(authenticated),
    updateSettings: (settings)=>useAppStore.getState().updateSettings(settings),
    updateWorkoutPreferences: (preferences)=>useAppStore.getState().updateWorkoutPreferences(preferences),
    addNotification: (notification)=>useAppStore.getState().addNotification(notification),
    setOnlineStatus: (isOnline)=>useAppStore.getState().setOnlineStatus(isOnline),
    addPendingSync: (item)=>useAppStore.getState().addPendingSync(item),
    removePendingSync: (id)=>useAppStore.getState().removePendingSync(id),
    clearPendingSync: ()=>useAppStore.getState().clearPendingSync(),
    updateLastSyncTime: ()=>useAppStore.getState().updateLastSyncTime(),
    reset: ()=>useAppStore.getState().reset()
};
}}),
"[project]/src/lib/api/services/workouts.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Workout API Service
 * Handles all workout-related API calls
 */ __turbopack_context__.s({
    "WorkoutService": (()=>WorkoutService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/adapters/workout-cool.adapter.ts [app-ssr] (ecmascript)");
;
;
;
class WorkoutService {
    /**
   * Get user's workout sessions with optional filtering - adapted for workout-cool API
   */ static async getWorkoutSessions(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.status) searchParams.append('status', params.status.toUpperCase());
        if (params.programId) searchParams.append('programId', params.programId);
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.LIST;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        // Adapt workout-cool sessions to our format
        const adaptedSessions = response.sessions?.map((session)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(session)) || [];
        return {
            data: adaptedSessions,
            pagination: {
                page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,
                limit: params.limit || 10,
                total: adaptedSessions.length,
                totalPages: Math.ceil(adaptedSessions.length / (params.limit || 10))
            }
        };
    }
    /**
   * Get workout session by ID - adapted for workout-cool API
   */ static async getWorkoutSession(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id));
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Create a new workout session - adapted for workout-cool API
   */ static async createWorkoutSession(data) {
        const workoutCoolData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].toWorkoutCoolFormat.workoutSession(data);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.CREATE, workoutCoolData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Update workout session - adapted for workout-cool API
   */ static async updateWorkoutSession(id, data) {
        const workoutCoolData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].toWorkoutCoolFormat.workoutSession(data);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.UPDATE(id), workoutCoolData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Delete workout session - adapted for workout-cool API
   */ static async deleteWorkoutSession(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DELETE(id));
    }
    /**
   * Start a workout session - adapted for workout-cool API
   */ static async startWorkoutSession(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id)}/start`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Complete a workout session - adapted for workout-cool API
   */ static async completeWorkoutSession(id, data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.COMPLETE(id), data);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Get workout programs - adapted for workout-cool public API
   */ static async getWorkoutPrograms(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.category) searchParams.append('category', params.category);
        if (params.difficulty) searchParams.append('level', params.difficulty.toUpperCase());
        if (params.duration) searchParams.append('duration', params.duration);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.PUBLIC_LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.PUBLIC_LIST;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        // Adapt workout-cool programs to our format
        const adaptedPrograms = response.map((program)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptProgram(program));
        return {
            data: adaptedPrograms,
            pagination: {
                page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,
                limit: params.limit || 10,
                total: adaptedPrograms.length,
                totalPages: Math.ceil(adaptedPrograms.length / (params.limit || 10))
            }
        };
    }
    /**
   * Get workout program by ID
   */ static async getWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id));
    }
    /**
   * Create a new workout program
   */ static async createWorkoutProgram(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.CREATE, data);
    }
    /**
   * Update workout program
   */ static async updateWorkoutProgram(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.UPDATE(id), data);
    }
    /**
   * Delete workout program
   */ static async deleteWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DELETE(id));
    }
    /**
   * Join a workout program
   */ static async joinWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id)}/join`);
    }
    /**
   * Leave a workout program
   */ static async leaveWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id)}/leave`);
    }
    /**
   * Get user's joined programs
   */ static async getUserPrograms() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/user/programs');
        return response.programs || [];
    }
    /**
   * Get popular workout programs
   */ static async getPopularPrograms(limit = 10) {
        const searchParams = new URLSearchParams({
            sort: 'popular',
            limit: limit.toString()
        });
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data || [];
    }
    /**
   * Get recommended workout programs for user
   */ static async getRecommendedPrograms(limit = 6) {
        const searchParams = new URLSearchParams({
            recommended: 'true',
            limit: limit.toString()
        });
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data || [];
    }
    /**
   * Generate AI workout plan
   */ static async generateAIWorkout(preferences) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/workouts/generate', preferences);
    }
    /**
   * Get workout statistics
   */ static async getWorkoutStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/workouts/stats?period=${period}`);
    }
    /**
   * Get workout history
   */ static async getWorkoutHistory(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        const queryString = searchParams.toString();
        const url = queryString ? `/api/workouts/history?${queryString}` : '/api/workouts/history';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
}
}}),
"[project]/src/lib/api/workout-cool-client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Client for fetching data from workout-cool backend
 * This module handles communication with workout-cool via HTTP requests
 */ // Types matching workout-cool's data structures
__turbopack_context__.s({
    "workoutCoolClient": (()=>workoutCoolClient)
});
class WorkoutCoolClient {
    baseUrl;
    isAvailable = false;
    constructor(){
        this.baseUrl = process.env.WORKOUT_COOL_API_URL || 'http://localhost:3000';
        this.checkAvailability();
    }
    async checkAvailability() {
        try {
            const response = await fetch(`${this.baseUrl}/api/health`, {
                method: 'GET',
                signal: AbortSignal.timeout(3000)
            });
            if (response.ok) {
                this.isAvailable = true;
                console.log('workout-cool backend is available');
            }
        } catch (error) {
            console.log('workout-cool backend not available:', error);
            this.isAvailable = false;
        }
    }
    /**
   * Get public programs from workout-cool backend
   */ async getPublicPrograms() {
        if (!this.isAvailable) {
            throw new Error('workout-cool backend not available');
        }
        try {
            // For now, return enhanced mock data that represents real workout-cool structure
            // In a real implementation, this would call workout-cool's API endpoints
            const enhancedPrograms = [
                {
                    id: 'wc-prog-001',
                    slug: 'full-body-strength-beginner',
                    slugEn: 'full-body-strength-beginner',
                    title: 'Programme Force Corps Complet Débutant',
                    titleEn: 'Full Body Strength Training for Beginners',
                    description: 'Un programme complet de musculation pour débutants ciblant tous les groupes musculaires',
                    descriptionEn: 'A comprehensive strength training program for beginners targeting all muscle groups',
                    category: 'Strength Training',
                    image: '/images/programs/full-body-strength.jpg',
                    level: 'BEGINNER',
                    type: 'Strength',
                    durationWeeks: 8,
                    sessionsPerWeek: 3,
                    sessionDurationMin: 45,
                    equipment: [
                        'Dumbbells',
                        'Barbell',
                        'Bench',
                        'Pull-up Bar'
                    ],
                    isPremium: false,
                    participantCount: 245,
                    totalWeeks: 8,
                    totalSessions: 24,
                    totalExercises: 15,
                    totalEnrollments: 245
                },
                {
                    id: 'wc-prog-002',
                    slug: 'hiit-cardio-intermediate',
                    slugEn: 'hiit-cardio-intermediate',
                    title: 'HIIT Cardio Intensif',
                    titleEn: 'Intensive HIIT Cardio',
                    description: 'Entraînement cardio haute intensité pour brûler les graisses rapidement',
                    descriptionEn: 'High-intensity cardio training for rapid fat burning',
                    category: 'Cardio',
                    image: '/images/programs/hiit-cardio.jpg',
                    level: 'INTERMEDIATE',
                    type: 'HIIT',
                    durationWeeks: 6,
                    sessionsPerWeek: 4,
                    sessionDurationMin: 30,
                    equipment: [
                        'None'
                    ],
                    isPremium: true,
                    participantCount: 156,
                    totalWeeks: 6,
                    totalSessions: 24,
                    totalExercises: 20,
                    totalEnrollments: 156
                },
                {
                    id: 'wc-prog-003',
                    slug: 'yoga-flexibility-all-levels',
                    slugEn: 'yoga-flexibility-all-levels',
                    title: 'Yoga Flexibilité Tous Niveaux',
                    titleEn: 'Yoga Flexibility for All Levels',
                    description: 'Programme de yoga pour améliorer la flexibilité et la mobilité',
                    descriptionEn: 'Yoga program to improve flexibility and mobility',
                    category: 'Flexibility',
                    image: '/images/programs/yoga-flexibility.jpg',
                    level: 'ALL_LEVELS',
                    type: 'Yoga',
                    durationWeeks: 4,
                    sessionsPerWeek: 5,
                    sessionDurationMin: 25,
                    equipment: [
                        'Yoga Mat'
                    ],
                    isPremium: false,
                    participantCount: 89,
                    totalWeeks: 4,
                    totalSessions: 20,
                    totalExercises: 12,
                    totalEnrollments: 89
                }
            ];
            return enhancedPrograms;
        } catch (error) {
            console.error('Error fetching public programs:', error);
            throw error;
        }
    }
    /**
   * Get exercises from workout-cool backend
   */ async getExercises(search) {
        if (!this.isAvailable) {
            throw new Error('workout-cool backend not available');
        }
        try {
            // Enhanced mock data representing real workout-cool exercise structure
            const allExercises = [
                {
                    id: 'wc-ex-001',
                    name: 'Pompes',
                    nameEn: 'Push-ups',
                    description: 'Exercice de base pour le haut du corps',
                    descriptionEn: 'Basic upper body exercise',
                    instructions: 'Placez-vous en position de planche, descendez et remontez',
                    instructionsEn: 'Get into plank position, lower and push back up',
                    tips: 'Gardez le corps aligné',
                    tipsEn: 'Keep your body aligned',
                    imageUrl: '/images/exercises/push-ups.jpg',
                    videoUrl: '/videos/exercises/push-ups.mp4',
                    attributes: [
                        {
                            id: 'attr-1',
                            attributeName: {
                                id: 'muscle-group',
                                name: 'Groupe Musculaire',
                                nameEn: 'Muscle Group'
                            },
                            attributeValue: {
                                id: 'chest',
                                value: 'Pectoraux',
                                valueEn: 'Chest'
                            }
                        },
                        {
                            id: 'attr-2',
                            attributeName: {
                                id: 'equipment',
                                name: 'Équipement',
                                nameEn: 'Equipment'
                            },
                            attributeValue: {
                                id: 'bodyweight',
                                value: 'Poids du corps',
                                valueEn: 'Bodyweight'
                            }
                        },
                        {
                            id: 'attr-3',
                            attributeName: {
                                id: 'difficulty',
                                name: 'Difficulté',
                                nameEn: 'Difficulty'
                            },
                            attributeValue: {
                                id: 'beginner',
                                value: 'Débutant',
                                valueEn: 'Beginner'
                            }
                        }
                    ]
                },
                {
                    id: 'wc-ex-002',
                    name: 'Squats',
                    nameEn: 'Squats',
                    description: 'Exercice fondamental pour les jambes',
                    descriptionEn: 'Fundamental leg exercise',
                    instructions: 'Descendez en fléchissant les genoux, remontez',
                    instructionsEn: 'Lower by bending knees, then stand back up',
                    tips: 'Gardez le dos droit',
                    tipsEn: 'Keep your back straight',
                    imageUrl: '/images/exercises/squats.jpg',
                    videoUrl: '/videos/exercises/squats.mp4',
                    attributes: [
                        {
                            id: 'attr-4',
                            attributeName: {
                                id: 'muscle-group',
                                name: 'Groupe Musculaire',
                                nameEn: 'Muscle Group'
                            },
                            attributeValue: {
                                id: 'legs',
                                value: 'Jambes',
                                valueEn: 'Legs'
                            }
                        },
                        {
                            id: 'attr-5',
                            attributeName: {
                                id: 'equipment',
                                name: 'Équipement',
                                nameEn: 'Equipment'
                            },
                            attributeValue: {
                                id: 'bodyweight',
                                value: 'Poids du corps',
                                valueEn: 'Bodyweight'
                            }
                        },
                        {
                            id: 'attr-6',
                            attributeName: {
                                id: 'difficulty',
                                name: 'Difficulté',
                                nameEn: 'Difficulty'
                            },
                            attributeValue: {
                                id: 'beginner',
                                value: 'Débutant',
                                valueEn: 'Beginner'
                            }
                        }
                    ]
                },
                {
                    id: 'wc-ex-003',
                    name: 'Développé Couché',
                    nameEn: 'Bench Press',
                    description: 'Exercice de musculation pour les pectoraux',
                    descriptionEn: 'Strength exercise for chest muscles',
                    instructions: 'Allongé sur le banc, poussez la barre vers le haut',
                    instructionsEn: 'Lying on bench, push the barbell upward',
                    tips: 'Contrôlez la descente',
                    tipsEn: 'Control the descent',
                    imageUrl: '/images/exercises/bench-press.jpg',
                    videoUrl: '/videos/exercises/bench-press.mp4',
                    attributes: [
                        {
                            id: 'attr-7',
                            attributeName: {
                                id: 'muscle-group',
                                name: 'Groupe Musculaire',
                                nameEn: 'Muscle Group'
                            },
                            attributeValue: {
                                id: 'chest',
                                value: 'Pectoraux',
                                valueEn: 'Chest'
                            }
                        },
                        {
                            id: 'attr-8',
                            attributeName: {
                                id: 'equipment',
                                name: 'Équipement',
                                nameEn: 'Equipment'
                            },
                            attributeValue: {
                                id: 'barbell',
                                value: 'Barre',
                                valueEn: 'Barbell'
                            }
                        },
                        {
                            id: 'attr-9',
                            attributeName: {
                                id: 'difficulty',
                                name: 'Difficulté',
                                nameEn: 'Difficulty'
                            },
                            attributeValue: {
                                id: 'intermediate',
                                value: 'Intermédiaire',
                                valueEn: 'Intermediate'
                            }
                        }
                    ]
                }
            ];
            // Apply search filter if provided
            if (search) {
                const searchLower = search.toLowerCase();
                return allExercises.filter((exercise)=>exercise.name.toLowerCase().includes(searchLower) || exercise.nameEn.toLowerCase().includes(searchLower) || exercise.description.toLowerCase().includes(searchLower) || exercise.descriptionEn.toLowerCase().includes(searchLower));
            }
            return allExercises;
        } catch (error) {
            console.error('Error fetching exercises:', error);
            throw error;
        }
    }
    /**
   * Check if workout-cool backend is available
   */ isConnectedToDatabase() {
        return this.isAvailable;
    }
    /**
   * Refresh availability status
   */ async refreshAvailability() {
        await this.checkAvailability();
    }
}
const workoutCoolClient = new WorkoutCoolClient();
}}),
"[project]/src/lib/api/services/progress.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Progress Tracking API Service
 * Handles all progress and analytics related API calls
 * Integrated with workout-cool backend
 */ __turbopack_context__.s({
    "ProgressService": (()=>ProgressService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$workout$2d$cool$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/workout-cool-client.ts [app-ssr] (ecmascript)");
;
;
;
class ProgressService {
    /**
   * Get user's progress records with optional filtering
   * Integrates with workout-cool's workout session data
   */ static async getProgressRecords(params = {}) {
        try {
            // Try to get real data from workout-cool if available
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$workout$2d$cool$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workoutCoolClient"].isConnectedToDatabase()) {
                return await this.getWorkoutCoolProgressRecords(params);
            }
            // Fallback to original API
            const searchParams = new URLSearchParams();
            if (params.limit) searchParams.append('limit', params.limit.toString());
            if (params.offset) searchParams.append('offset', params.offset.toString());
            if (params.type) searchParams.append('type', params.type);
            if (params.startDate) searchParams.append('startDate', params.startDate);
            if (params.endDate) searchParams.append('endDate', params.endDate);
            if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);
            if (params.workoutId) searchParams.append('workoutId', params.workoutId);
            const queryString = searchParams.toString();
            const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.LIST;
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        } catch (error) {
            console.error('Error fetching progress records:', error);
            throw error;
        }
    }
    /**
   * Get progress records from workout-cool backend
   */ static async getWorkoutCoolProgressRecords(params) {
        // Mock data representing workout-cool workout sessions
        const mockWorkoutSessions = [
            {
                id: 'session-001',
                userId: 'user-001',
                programId: 'wc-prog-001',
                weekNumber: 1,
                sessionNumber: 1,
                startedAt: new Date('2024-01-15T09:00:00Z'),
                completedAt: new Date('2024-01-15T09:45:00Z'),
                status: 'COMPLETED',
                notes: 'Great workout, felt strong',
                exercises: [
                    {
                        id: 'session-ex-001',
                        exerciseId: 'wc-ex-001',
                        order: 1,
                        sets: [
                            {
                                id: 'set-001',
                                setIndex: 1,
                                reps: 12,
                                weight: 0,
                                duration: 0,
                                distance: 0,
                                completed: true
                            },
                            {
                                id: 'set-002',
                                setIndex: 2,
                                reps: 10,
                                weight: 0,
                                duration: 0,
                                distance: 0,
                                completed: true
                            },
                            {
                                id: 'set-003',
                                setIndex: 3,
                                reps: 8,
                                weight: 0,
                                duration: 0,
                                distance: 0,
                                completed: true
                            }
                        ]
                    }
                ]
            }
        ];
        // Convert workout-cool sessions to progress records
        const progressRecords = mockWorkoutSessions.map((session)=>({
                id: session.id,
                userId: session.userId,
                type: 'workout',
                date: session.completedAt?.toISOString() || session.startedAt.toISOString(),
                value: session.exercises.reduce((total, ex)=>total + ex.sets.length, 0),
                unit: 'sets',
                exerciseId: session.exercises[0]?.exerciseId,
                workoutId: session.programId,
                notes: session.notes,
                metadata: {
                    weekNumber: session.weekNumber,
                    sessionNumber: session.sessionNumber,
                    status: session.status,
                    duration: session.completedAt && session.startedAt ? Math.round((session.completedAt.getTime() - session.startedAt.getTime()) / 60000) : 0
                },
                createdAt: session.startedAt.toISOString(),
                updatedAt: session.completedAt?.toISOString() || session.startedAt.toISOString()
            }));
        // Apply filtering
        let filteredRecords = progressRecords;
        if (params.type) {
            filteredRecords = filteredRecords.filter((record)=>record.type === params.type);
        }
        if (params.startDate) {
            filteredRecords = filteredRecords.filter((record)=>record.date >= params.startDate);
        }
        if (params.endDate) {
            filteredRecords = filteredRecords.filter((record)=>record.date <= params.endDate);
        }
        // Apply pagination
        const offset = params.offset || 0;
        const limit = params.limit || 20;
        const paginatedRecords = filteredRecords.slice(offset, offset + limit);
        return {
            data: paginatedRecords,
            total: filteredRecords.length,
            page: Math.floor(offset / limit) + 1,
            limit,
            hasNext: offset + limit < filteredRecords.length,
            hasPrev: offset > 0
        };
    }
    /**
   * Get progress record by ID
   */ static async getProgressRecord(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.DETAILS(id));
    }
    /**
   * Create a new progress record
   */ static async createProgressRecord(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.CREATE, data);
    }
    /**
   * Update progress record
   */ static async updateProgressRecord(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.UPDATE(id), data);
    }
    /**
   * Delete progress record
   */ static async deleteProgressRecord(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.DELETE(id));
    }
    /**
   * Get comprehensive progress statistics
   * Integrates with workout-cool's progress tracking
   */ static async getProgressStats(period = 'month') {
        try {
            // Try to get real data from workout-cool if available
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$workout$2d$cool$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["workoutCoolClient"].isConnectedToDatabase()) {
                return await this.getWorkoutCoolProgressStats(period);
            }
            // Fallback to original API
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.STATS}?period=${period}`);
        } catch (error) {
            console.error('Error fetching progress stats:', error);
            throw error;
        }
    }
    /**
   * Get progress statistics from workout-cool backend
   */ static async getWorkoutCoolProgressStats(period) {
        // Mock data representing workout-cool progress statistics
        const mockStats = {
            totalWorkouts: 24,
            totalExercises: 156,
            totalSets: 468,
            totalReps: 3744,
            totalWeight: 18720,
            totalDuration: 1080,
            averageWorkoutDuration: 45,
            streakDays: 7,
            lastWorkoutDate: new Date('2024-01-15T09:45:00Z')
        };
        // Convert to ProgressStats format
        const progressStats = {
            totalRecords: mockStats.totalWorkouts,
            totalValue: mockStats.totalSets,
            averageValue: Math.round(mockStats.totalSets / mockStats.totalWorkouts),
            bestValue: 25,
            currentStreak: mockStats.streakDays,
            longestStreak: 14,
            lastRecordDate: mockStats.lastWorkoutDate?.toISOString(),
            improvementRate: 12.5,
            weeklyAverage: Math.round(mockStats.totalWorkouts / 4),
            monthlyAverage: mockStats.totalWorkouts,
            progressTrend: 'increasing',
            achievements: [
                {
                    id: '1',
                    name: 'First Workout',
                    description: 'Completed your first workout',
                    unlockedAt: '2024-01-01T00:00:00Z'
                },
                {
                    id: '2',
                    name: 'Week Warrior',
                    description: 'Completed 7 days in a row',
                    unlockedAt: '2024-01-08T00:00:00Z'
                },
                {
                    id: '3',
                    name: 'Strength Builder',
                    description: 'Lifted 10,000kg total',
                    unlockedAt: '2024-01-12T00:00:00Z'
                }
            ],
            chartData: [
                {
                    date: '2024-01-08',
                    value: 18
                },
                {
                    date: '2024-01-09',
                    value: 20
                },
                {
                    date: '2024-01-10',
                    value: 22
                },
                {
                    date: '2024-01-11',
                    value: 19
                },
                {
                    date: '2024-01-12',
                    value: 24
                },
                {
                    date: '2024-01-13',
                    value: 25
                },
                {
                    date: '2024-01-14',
                    value: 23
                },
                {
                    date: '2024-01-15',
                    value: 25
                }
            ]
        };
        return progressStats;
    }
    /**
   * Get workout completion statistics
   */ static async getWorkoutStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/workout-stats?period=${period}`);
    }
    /**
   * Get exercise performance data
   */ static async getExerciseProgress(exerciseId, period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/exercise/${exerciseId}?period=${period}`);
    }
    /**
   * Get body measurements progress
   */ static async getBodyMeasurements(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/body-measurements?period=${period}`);
    }
    /**
   * Add body measurement record
   */ static async addBodyMeasurement(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/progress/body-measurements', data);
    }
    /**
   * Get fitness goals and progress
   */ static async getFitnessGoals() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/goals');
    }
    /**
   * Create a new fitness goal
   */ static async createFitnessGoal(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/progress/goals', data);
    }
    /**
   * Update fitness goal progress
   */ static async updateGoalProgress(goalId, currentValue) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(`/api/progress/goals/${goalId}`, {
            currentValue
        });
    }
    /**
   * Get achievement badges and milestones
   */ static async getAchievements() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/achievements');
    }
    /**
   * Get workout calendar data
   */ static async getWorkoutCalendar(year, month) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/calendar?year=${year}&month=${month}`);
    }
    /**
   * Get personal records (PRs)
   */ static async getPersonalRecords() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/personal-records');
    }
    /**
   * Get strength progression data
   */ static async getStrengthProgression(exerciseIds) {
        const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/strength-progression${params}`);
    }
    /**
   * Export progress data
   */ static async exportProgressData(format = 'csv', period = 'all') {
        const response = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL}/api/progress/export?format=${format}&period=${period}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
            }
        });
        if (!response.ok) {
            throw new Error('Failed to export data');
        }
        return response.blob();
    }
    /**
   * Get workout intensity analysis
   */ static async getWorkoutIntensity(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/workout-intensity?period=${period}`);
    }
}
}}),
"[project]/src/lib/query/config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * React Query Configuration and Cache Management
 * Centralized configuration for data fetching, caching, and synchronization
 */ __turbopack_context__.s({
    "cacheUtils": (()=>cacheUtils),
    "default": (()=>queryClient),
    "offlineUtils": (()=>offlineUtils),
    "queryClient": (()=>queryClient),
    "queryKeys": (()=>queryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$sync$2d$storage$2d$persister$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-sync-storage-persister/build/modern/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)");
;
;
;
;
// ============================================================================
// QUERY CLIENT CONFIGURATION
// ============================================================================
const queryConfig = {
    queries: {
        // Global defaults for all queries
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: (failureCount, error)=>{
            // Don't retry on 4xx errors (client errors)
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry up to 3 times for other errors
            return failureCount < 3;
        },
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: true
    },
    mutations: {
        // Global defaults for all mutations
        retry: (failureCount, error)=>{
            // Don't retry mutations on client errors
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry once for server errors
            return failureCount < 1;
        },
        onError: (error)=>{
            // Global error handling for mutations
            console.error('Mutation error:', error);
            // Add error notification
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'error',
                title: 'Operation Failed',
                message: error?.message || 'An unexpected error occurred'
            });
        }
    }
};
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
    defaultOptions: queryConfig
});
// ============================================================================
// PERSISTENCE CONFIGURATION
// ============================================================================
const persister = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$sync$2d$storage$2d$persister$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSyncStoragePersister"])({
    storage: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : undefined,
    key: 'ai-fitness-query-cache',
    serialize: JSON.stringify,
    deserialize: JSON.parse
});
// Persist query client (only in browser)
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
const cacheUtils = {
    /**
   * Invalidate all queries for a specific entity type
   */ invalidateEntity: (entityType)=>{
        queryClient.invalidateQueries({
            queryKey: [
                entityType
            ]
        });
    },
    /**
   * Remove all cached data for a specific entity
   */ removeEntity: (entityType, id)=>{
        if (id) {
            queryClient.removeQueries({
                queryKey: [
                    entityType,
                    id
                ]
            });
        } else {
            queryClient.removeQueries({
                queryKey: [
                    entityType
                ]
            });
        }
    },
    /**
   * Prefetch data for better UX
   */ prefetch: async (queryKey, queryFn)=>{
        await queryClient.prefetchQuery({
            queryKey,
            queryFn,
            staleTime: 10 * 60 * 1000
        });
    },
    /**
   * Set query data manually (for optimistic updates)
   */ setQueryData: (queryKey, data)=>{
        queryClient.setQueryData(queryKey, data);
    },
    /**
   * Get cached query data
   */ getQueryData: (queryKey)=>{
        return queryClient.getQueryData(queryKey);
    },
    /**
   * Clear all cached data
   */ clearAll: ()=>{
        queryClient.clear();
    },
    /**
   * Reset queries to refetch fresh data
   */ resetQueries: (queryKey)=>{
        if (queryKey) {
            queryClient.resetQueries({
                queryKey
            });
        } else {
            queryClient.resetQueries();
        }
    },
    /**
   * Cancel ongoing queries
   */ cancelQueries: (queryKey)=>{
        if (queryKey) {
            queryClient.cancelQueries({
                queryKey
            });
        } else {
            queryClient.cancelQueries();
        }
    }
};
const queryKeys = {
    // Auth
    auth: {
        all: [
            'auth'
        ],
        session: ()=>[
                ...queryKeys.auth.all,
                'session'
            ],
        user: ()=>[
                ...queryKeys.auth.all,
                'user'
            ]
    },
    // Exercises
    exercises: {
        all: [
            'exercises'
        ],
        lists: ()=>[
                ...queryKeys.exercises.all,
                'list'
            ],
        list: (filters)=>[
                ...queryKeys.exercises.lists(),
                filters
            ],
        details: ()=>[
                ...queryKeys.exercises.all,
                'detail'
            ],
        detail: (id)=>[
                ...queryKeys.exercises.details(),
                id
            ],
        search: (query)=>[
                ...queryKeys.exercises.all,
                'search',
                query
            ],
        attributes: ()=>[
                ...queryKeys.exercises.all,
                'attributes'
            ]
    },
    // Workouts
    workouts: {
        all: [
            'workouts'
        ],
        sessions: ()=>[
                ...queryKeys.workouts.all,
                'sessions'
            ],
        session: (id)=>[
                ...queryKeys.workouts.sessions(),
                id
            ],
        programs: ()=>[
                ...queryKeys.workouts.all,
                'programs'
            ],
        program: (id)=>[
                ...queryKeys.workouts.programs(),
                id
            ],
        history: (filters)=>[
                ...queryKeys.workouts.all,
                'history',
                filters
            ],
        stats: (period)=>[
                ...queryKeys.workouts.all,
                'stats',
                period
            ]
    },
    // Progress
    progress: {
        all: [
            'progress'
        ],
        records: ()=>[
                ...queryKeys.progress.all,
                'records'
            ],
        record: (id)=>[
                ...queryKeys.progress.records(),
                id
            ],
        stats: (period)=>[
                ...queryKeys.progress.all,
                'stats',
                period
            ],
        goals: ()=>[
                ...queryKeys.progress.all,
                'goals'
            ],
        achievements: ()=>[
                ...queryKeys.progress.all,
                'achievements'
            ],
        calendar: (year, month)=>[
                ...queryKeys.progress.all,
                'calendar',
                year,
                month
            ]
    },
    // User
    user: {
        all: [
            'user'
        ],
        profile: ()=>[
                ...queryKeys.user.all,
                'profile'
            ],
        preferences: ()=>[
                ...queryKeys.user.all,
                'preferences'
            ],
        subscription: ()=>[
                ...queryKeys.user.all,
                'subscription'
            ]
    }
};
const offlineUtils = {
    /**
   * Check if we're online
   */ isOnline: ()=>{
        return typeof navigator !== 'undefined' ? navigator.onLine : true;
    },
    /**
   * Setup online/offline event listeners
   */ setupNetworkListeners: ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const handleOnline = undefined;
        const handleOffline = undefined;
    },
    /**
   * Queue mutation for offline sync
   */ queueOfflineMutation: (type, action, data)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addPendingSync({
            type: type,
            action: action,
            data
        });
    }
};
// ============================================================================
// QUERY CLIENT EVENTS
// ============================================================================
// Setup global query client event listeners
queryClient.getQueryCache().subscribe((event)=>{
    // Log query events in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Query event:', event);
    }
    // Handle specific events
    switch(event.type){
        case 'added':
            break;
        case 'removed':
            break;
        case 'updated':
            break;
    }
});
queryClient.getMutationCache().subscribe((event)=>{
    // Log mutation events in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Mutation event:', event);
    }
    // Handle mutation success/error globally
    if (event.type === 'updated') {
        const mutation = event.mutation;
        if (mutation.state.status === 'success') {
            // Global success handling
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'success',
                title: 'Success',
                message: 'Operation completed successfully'
            });
        }
    }
});
;
}}),
"[project]/src/lib/sync/sync-manager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Sync Manager for Offline Data Synchronization
 * Handles syncing offline data when connection is restored
 */ __turbopack_context__.s({
    "SyncManager": (()=>SyncManager),
    "default": (()=>__TURBOPACK__default__export__),
    "optimisticUpdates": (()=>optimisticUpdates),
    "useSyncManager": (()=>useSyncManager),
    "useSyncStatus": (()=>useSyncStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/workouts.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/progress.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/config.ts [app-ssr] (ecmascript)");
;
;
;
;
class SyncManager {
    static instance;
    syncInProgress = false;
    syncQueue = [];
    constructor(){
        this.setupNetworkListeners();
    }
    static getInstance() {
        if (!SyncManager.instance) {
            SyncManager.instance = new SyncManager();
        }
        return SyncManager.instance;
    }
    /**
   * Setup network event listeners
   */ setupNetworkListeners() {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const handleOnline = undefined;
        const handleOffline = undefined;
    }
    /**
   * Add data to sync queue for offline processing
   */ addToSyncQueue(item) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addPendingSync(item);
        // Try to sync immediately if online
        if (navigator.onLine) {
            this.syncPendingData();
        }
    }
    /**
   * Sync all pending data
   */ async syncPendingData() {
        if (this.syncInProgress || !navigator.onLine) {
            return;
        }
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState();
        const pendingItems = state.offline.pendingSync;
        if (pendingItems.length === 0) {
            return;
        }
        this.syncInProgress = true;
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'info',
                title: 'Syncing Data',
                message: `Syncing ${pendingItems.length} pending items...`
            });
            const syncResults = await Promise.allSettled(pendingItems.map((item)=>this.syncSingleItem(item)));
            // Process results
            let successCount = 0;
            let failureCount = 0;
            syncResults.forEach((result, index)=>{
                const item = pendingItems[index];
                if (result.status === 'fulfilled') {
                    successCount++;
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].removePendingSync(item.id);
                } else {
                    failureCount++;
                    console.error(`Failed to sync item ${item.id}:`, result.reason);
                }
            });
            // Update last sync time
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].updateLastSyncTime();
            // Show result notification
            if (failureCount === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                    type: 'success',
                    title: 'Sync Complete',
                    message: `Successfully synced ${successCount} items`
                });
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                    type: 'warning',
                    title: 'Sync Partially Complete',
                    message: `Synced ${successCount} items, ${failureCount} failed`
                });
            }
            // Invalidate relevant queries to refetch fresh data
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].invalidateEntity('workouts');
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].invalidateEntity('progress');
        } catch (error) {
            console.error('Sync failed:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'error',
                title: 'Sync Failed',
                message: 'Failed to sync offline data. Will retry later.'
            });
        } finally{
            this.syncInProgress = false;
        }
    }
    /**
   * Sync a single item
   */ async syncSingleItem(item) {
        switch(item.type){
            case 'workout':
                return this.syncWorkoutItem(item);
            case 'progress':
                return this.syncProgressItem(item);
            case 'goal':
                return this.syncGoalItem(item);
            default:
                throw new Error(`Unknown sync item type: ${item.type}`);
        }
    }
    /**
   * Sync workout-related items
   */ async syncWorkoutItem(item) {
        switch(item.action){
            case 'create':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].createWorkoutSession(item.data);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].createWorkoutProgram(item.data);
                }
                break;
            case 'update':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].updateWorkoutSession(item.data.id, item.data);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].updateWorkoutProgram(item.data.id, item.data);
                }
                break;
            case 'delete':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].deleteWorkoutSession(item.data.id);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].deleteWorkoutProgram(item.data.id);
                }
                break;
        }
        throw new Error(`Unknown workout sync action: ${item.action}`);
    }
    /**
   * Sync progress-related items
   */ async syncProgressItem(item) {
        switch(item.action){
            case 'create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].createProgressRecord(item.data);
            case 'update':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].updateProgressRecord(item.data.id, item.data);
            case 'delete':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].deleteProgressRecord(item.data.id);
        }
        throw new Error(`Unknown progress sync action: ${item.action}`);
    }
    /**
   * Sync goal-related items
   */ async syncGoalItem(item) {
        switch(item.action){
            case 'create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].createFitnessGoal(item.data);
            case 'update':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].updateGoalProgress(item.data.id, item.data.currentValue);
            default:
                throw new Error(`Unknown goal sync action: ${item.action}`);
        }
    }
    /**
   * Force sync all data
   */ async forceSyncAll() {
        await this.syncPendingData();
    }
    /**
   * Clear all pending sync data
   */ clearPendingSync() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].clearPendingSync();
    }
    /**
   * Get sync status
   */ getSyncStatus() {
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState();
        return {
            isOnline: state.offline.isOnline,
            pendingCount: state.offline.pendingSync.length,
            lastSyncTime: state.offline.lastSyncTime,
            syncInProgress: this.syncInProgress
        };
    }
}
function useSyncManager() {
    const syncManager = SyncManager.getInstance();
    return {
        addToSyncQueue: (item)=>syncManager.addToSyncQueue(item),
        syncPendingData: ()=>syncManager.syncPendingData(),
        forceSyncAll: ()=>syncManager.forceSyncAll(),
        clearPendingSync: ()=>syncManager.clearPendingSync(),
        getSyncStatus: ()=>syncManager.getSyncStatus()
    };
}
function useSyncStatus() {
    const syncManager = SyncManager.getInstance();
    const status = syncManager.getSyncStatus();
    return status;
}
const optimisticUpdates = {
    /**
   * Optimistically update workout session
   */ updateWorkoutSession: (sessionId, updates)=>{
        const queryKey = [
            'workouts',
            'sessions',
            sessionId
        ];
        const previousData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].getQueryData(queryKey);
        // Apply optimistic update
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, {
            ...previousData || {},
            ...updates,
            updatedAt: new Date().toISOString()
        });
        return previousData;
    },
    /**
   * Optimistically add progress record
   */ addProgressRecord: (record)=>{
        const queryKey = [
            'progress',
            'records'
        ];
        const previousData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].getQueryData(queryKey);
        if (previousData?.data) {
            const newRecord = {
                ...record,
                id: `temp-${Date.now()}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, {
                ...previousData,
                data: [
                    newRecord,
                    ...previousData.data
                ]
            });
        }
        return previousData;
    },
    /**
   * Revert optimistic update
   */ revertUpdate: (queryKey, previousData)=>{
        if (previousData !== undefined) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, previousData);
        }
    }
};
const __TURBOPACK__default__export__ = SyncManager;
}}),
"[project]/src/lib/theme/mui-theme.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fitnessTheme": (()=>fitnessTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$createTheme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__createTheme$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/styles/createTheme.js [app-ssr] (ecmascript) <export default as createTheme>");
;
const fitnessTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$createTheme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__createTheme$3e$__["createTheme"])({
    palette: {
        primary: {
            main: '#FF6B35',
            light: '#FF8A65',
            dark: '#E64A19',
            contrastText: '#FFFFFF'
        },
        secondary: {
            main: '#4CAF50',
            light: '#81C784',
            dark: '#388E3C',
            contrastText: '#FFFFFF'
        },
        background: {
            default: '#FAFAFA',
            paper: '#FFFFFF'
        },
        text: {
            primary: '#212121',
            secondary: '#757575'
        },
        error: {
            main: '#F44336'
        },
        warning: {
            main: '#FF9800'
        },
        info: {
            main: '#2196F3'
        },
        success: {
            main: '#4CAF50'
        }
    },
    typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
            fontSize: '3rem',
            fontWeight: 700,
            lineHeight: 1.2
        },
        h2: {
            fontSize: '2.5rem',
            fontWeight: 600,
            lineHeight: 1.3
        },
        h3: {
            fontSize: '2rem',
            fontWeight: 600,
            lineHeight: 1.4
        },
        h4: {
            fontSize: '1.5rem',
            fontWeight: 500,
            lineHeight: 1.4
        },
        h5: {
            fontSize: '1.25rem',
            fontWeight: 500,
            lineHeight: 1.5
        },
        h6: {
            fontSize: '1rem',
            fontWeight: 500,
            lineHeight: 1.5
        },
        body1: {
            fontSize: '1rem',
            lineHeight: 1.6
        },
        body2: {
            fontSize: '0.875rem',
            lineHeight: 1.5
        },
        button: {
            textTransform: 'none',
            fontWeight: 600
        }
    },
    shape: {
        borderRadius: 12
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: 25,
                    padding: '12px 24px',
                    fontSize: '1rem',
                    fontWeight: 600,
                    textTransform: 'none',
                    boxShadow: 'none',
                    '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        transform: 'translateY(-2px)',
                        transition: 'all 0.3s ease'
                    }
                },
                contained: {
                    background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',
                    '&:hover': {
                        background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)'
                    }
                },
                outlined: {
                    borderWidth: 2,
                    '&:hover': {
                        borderWidth: 2
                    }
                }
            }
        },
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: 16,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    '&:hover': {
                        boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                        transform: 'translateY(-4px)',
                        transition: 'all 0.3s ease'
                    }
                }
            }
        },
        MuiAppBar: {
            styleOverrides: {
                root: {
                    backgroundColor: '#FFFFFF',
                    color: '#212121',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }
            }
        },
        MuiChip: {
            styleOverrides: {
                root: {
                    borderRadius: 20,
                    fontWeight: 500
                }
            }
        },
        MuiFab: {
            styleOverrides: {
                root: {
                    background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',
                    '&:hover': {
                        background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)',
                        transform: 'scale(1.1)'
                    }
                }
            }
        }
    }
});
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/i18n/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Simple custom i18n implementation for Next.js 15 compatibility
__turbopack_context__.s({
    "addLocaleToPathname": (()=>addLocaleToPathname),
    "defaultLocale": (()=>defaultLocale),
    "getLocale": (()=>getLocale),
    "getLocaleFromPathname": (()=>getLocaleFromPathname),
    "locales": (()=>locales),
    "removeLocaleFromPathname": (()=>removeLocaleFromPathname),
    "setLocale": (()=>setLocale)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-ssr] (ecmascript)");
;
const locales = [
    'en',
    'zh'
];
const defaultLocale = 'en';
function getLocale() {
    // In server components, we can't access URL directly
    // So we'll use cookies as fallback
    const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cookies"])();
    const localeCookie = cookieStore.get('locale')?.value;
    if (localeCookie && locales.includes(localeCookie)) {
        return localeCookie;
    }
    return defaultLocale;
}
function setLocale(locale) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function getLocaleFromPathname(pathname) {
    const segments = pathname.split('/');
    const localeSegment = segments[1];
    if (locales.includes(localeSegment)) {
        return localeSegment;
    }
    return defaultLocale;
}
function removeLocaleFromPathname(pathname) {
    const segments = pathname.split('/');
    const localeSegment = segments[1];
    if (locales.includes(localeSegment)) {
        return '/' + segments.slice(2).join('/');
    }
    return pathname;
}
function addLocaleToPathname(pathname, locale) {
    if (locale === defaultLocale) {
        return pathname;
    }
    const cleanPath = removeLocaleFromPathname(pathname);
    return `/${locale}${cleanPath}`;
}
}}),
"[project]/messages/en.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Loading...\",\"error\":\"Error\",\"retry\":\"Retry\",\"cancel\":\"Cancel\",\"save\":\"Save\",\"delete\":\"Delete\",\"edit\":\"Edit\",\"view\":\"View\",\"search\":\"Search\",\"filter\":\"Filter\",\"clear\":\"Clear\",\"submit\":\"Submit\",\"back\":\"Back\",\"next\":\"Next\",\"previous\":\"Previous\",\"close\":\"Close\"},\"navigation\":{\"home\":\"Home\",\"workouts\":\"Workout Plans\",\"exercises\":\"Exercise Database\",\"progress\":\"Progress Tracking\",\"profile\":\"Profile\",\"settings\":\"Settings\",\"logout\":\"Logout\"},\"homepage\":{\"title\":\"AI-fitness-singles\",\"subtitle\":\"Smart Fitness Platform for Singles\",\"description\":\"AI-powered fitness platform designed for singles. Create personalized workout plans, access comprehensive exercise database, track progress with detailed analytics, and achieve your fitness goals.\",\"getStarted\":\"Get Started\",\"learnMore\":\"Learn More\",\"features\":{\"title\":\"Core Features\",\"workouts\":{\"title\":\"Smart Workout Plans\",\"description\":\"AI-generated personalized training programs\"},\"exercises\":{\"title\":\"Exercise Library\",\"description\":\"Comprehensive database with detailed instructions\"},\"progress\":{\"title\":\"Progress Analytics\",\"description\":\"Track your fitness journey with detailed insights\"}},\"stats\":{\"title\":\"Quick Stats\",\"totalWorkouts\":\"Total Workouts\",\"completedSessions\":\"Completed Sessions\",\"totalExercises\":\"Total Exercises\",\"progressScore\":\"Progress Score\"}},\"workouts\":{\"title\":\"Workout Plans\",\"subtitle\":\"Discover and create personalized training programs\",\"createNew\":\"Create New Workout\",\"searchPlaceholder\":\"Search workout plans...\",\"filters\":{\"all\":\"All Levels\",\"beginner\":\"Beginner\",\"intermediate\":\"Intermediate\",\"advanced\":\"Advanced\"},\"card\":{\"level\":\"Level\",\"duration\":\"Duration\",\"exercises\":\"Exercises\",\"startWorkout\":\"Start Workout\",\"viewDetails\":\"View Details\"},\"empty\":{\"title\":\"No workout plans found\",\"description\":\"Try adjusting your search or filters\"}},\"exercises\":{\"title\":\"Exercise Database\",\"subtitle\":\"Comprehensive library of exercises with detailed instructions\",\"searchPlaceholder\":\"Search exercises...\",\"filters\":{\"all\":\"All Categories\",\"strength\":\"Strength\",\"cardio\":\"Cardio\",\"flexibility\":\"Flexibility\",\"balance\":\"Balance\"},\"card\":{\"muscleGroup\":\"Muscle Group\",\"equipment\":\"Equipment\",\"difficulty\":\"Difficulty\",\"viewExercise\":\"View Exercise\",\"addToWorkout\":\"Add to Workout\"},\"attributes\":{\"muscleGroup\":\"Muscle Group\",\"equipment\":\"Equipment\",\"difficulty\":\"Difficulty\"},\"empty\":{\"title\":\"No exercises found\",\"description\":\"Try adjusting your search or filters\"}},\"progress\":{\"title\":\"Progress Tracking\",\"subtitle\":\"Monitor your fitness journey with detailed analytics\",\"overview\":{\"title\":\"Progress Overview\",\"thisWeek\":\"This Week\",\"thisMonth\":\"This Month\",\"allTime\":\"All Time\"},\"charts\":{\"workoutFrequency\":\"Workout Frequency\",\"progressTrend\":\"Progress Trend\",\"exerciseDistribution\":\"Exercise Distribution\"},\"stats\":{\"totalWorkouts\":\"Total Workouts\",\"totalTime\":\"Total Time\",\"averageSession\":\"Average Session\",\"caloriesBurned\":\"Calories Burned\"},\"empty\":{\"title\":\"No progress data yet\",\"description\":\"Start working out to see your progress here\"}},\"apiTest\":{\"title\":\"API Integration Test\",\"subtitle\":\"Testing workout-cool API integration\",\"programsTest\":\"Programs API Test\",\"exercisesTest\":\"Exercises API Test\",\"testPrograms\":\"Test Programs API\",\"testExercises\":\"Test Exercises API\",\"testing\":\"Testing...\",\"endpoint\":\"Endpoint\",\"foundPrograms\":\"Found {count} programs:\",\"foundExercises\":\"Found {count} exercises:\",\"weeks\":\"{count} weeks\",\"instructions\":\"Instructions\",\"noProgramsLoaded\":\"No programs loaded yet. Click \\\"Test Programs API\\\" to load data.\",\"noExercisesLoaded\":\"No exercises loaded yet. Click \\\"Test Exercises API\\\" to load data.\"},\"language\":{\"switch\":\"Switch Language\",\"english\":\"English\",\"chinese\":\"中文\"}}"));}}),
"[project]/messages/zh.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"加载中...\",\"error\":\"错误\",\"retry\":\"重试\",\"cancel\":\"取消\",\"save\":\"保存\",\"delete\":\"删除\",\"edit\":\"编辑\",\"view\":\"查看\",\"search\":\"搜索\",\"filter\":\"筛选\",\"clear\":\"清除\",\"submit\":\"提交\",\"back\":\"返回\",\"next\":\"下一步\",\"previous\":\"上一步\",\"close\":\"关闭\"},\"navigation\":{\"home\":\"首页\",\"workouts\":\"训练计划\",\"exercises\":\"运动库\",\"progress\":\"进度追踪\",\"profile\":\"个人资料\",\"settings\":\"设置\",\"logout\":\"退出登录\"},\"homepage\":{\"title\":\"AI-fitness-singles\",\"subtitle\":\"专为单身人士设计的智能健身平台\",\"description\":\"AI驱动的健身平台，专为单身人士设计。创建个性化训练计划，访问全面的运动数据库，通过详细分析追踪进度，实现您的健身目标。\",\"getStarted\":\"开始使用\",\"learnMore\":\"了解更多\",\"features\":{\"title\":\"核心功能\",\"workouts\":{\"title\":\"智能训练计划\",\"description\":\"AI生成的个性化训练方案\"},\"exercises\":{\"title\":\"运动库\",\"description\":\"包含详细指导的全面运动数据库\"},\"progress\":{\"title\":\"进度分析\",\"description\":\"通过详细洞察追踪您的健身之旅\"}},\"stats\":{\"title\":\"快速统计\",\"totalWorkouts\":\"总训练次数\",\"completedSessions\":\"完成会话\",\"totalExercises\":\"总运动数\",\"progressScore\":\"进度评分\"}},\"workouts\":{\"title\":\"训练计划\",\"subtitle\":\"发现并创建个性化训练方案\",\"createNew\":\"创建新训练\",\"searchPlaceholder\":\"搜索训练计划...\",\"filters\":{\"all\":\"所有级别\",\"beginner\":\"初级\",\"intermediate\":\"中级\",\"advanced\":\"高级\"},\"card\":{\"level\":\"级别\",\"duration\":\"时长\",\"exercises\":\"运动项目\",\"startWorkout\":\"开始训练\",\"viewDetails\":\"查看详情\"},\"empty\":{\"title\":\"未找到训练计划\",\"description\":\"尝试调整您的搜索或筛选条件\"}},\"exercises\":{\"title\":\"运动库\",\"subtitle\":\"包含详细指导的全面运动库\",\"searchPlaceholder\":\"搜索运动...\",\"filters\":{\"all\":\"所有类别\",\"strength\":\"力量训练\",\"cardio\":\"有氧运动\",\"flexibility\":\"柔韧性\",\"balance\":\"平衡训练\"},\"card\":{\"muscleGroup\":\"肌肉群\",\"equipment\":\"器械\",\"difficulty\":\"难度\",\"viewExercise\":\"查看运动\",\"addToWorkout\":\"添加到训练\"},\"attributes\":{\"muscleGroup\":\"肌肉群\",\"equipment\":\"器械\",\"difficulty\":\"难度\"},\"empty\":{\"title\":\"未找到运动\",\"description\":\"尝试调整您的搜索或筛选条件\"}},\"progress\":{\"title\":\"进度追踪\",\"subtitle\":\"通过详细分析监控您的健身之旅\",\"overview\":{\"title\":\"进度概览\",\"thisWeek\":\"本周\",\"thisMonth\":\"本月\",\"allTime\":\"全部时间\"},\"charts\":{\"workoutFrequency\":\"训练频率\",\"progressTrend\":\"进度趋势\",\"exerciseDistribution\":\"运动分布\"},\"stats\":{\"totalWorkouts\":\"总训练次数\",\"totalTime\":\"总时长\",\"averageSession\":\"平均会话\",\"caloriesBurned\":\"消耗卡路里\"},\"empty\":{\"title\":\"暂无进度数据\",\"description\":\"开始训练以查看您的进度\"}},\"apiTest\":{\"title\":\"API 集成测试\",\"subtitle\":\"测试 workout-cool API 集成\",\"programsTest\":\"训练计划 API 测试\",\"exercisesTest\":\"运动 API 测试\",\"testPrograms\":\"测试训练计划 API\",\"testExercises\":\"测试运动 API\",\"testing\":\"测试中...\",\"endpoint\":\"接口\",\"foundPrograms\":\"找到 {count} 个训练计划：\",\"foundExercises\":\"找到 {count} 个运动：\",\"weeks\":\"{count} 周\",\"instructions\":\"说明\",\"noProgramsLoaded\":\"尚未加载训练计划。点击\\\\\\\"测试训练计划 API\\\\\\\"加载数据。\",\"noExercisesLoaded\":\"尚未加载运动。点击\\\\\\\"测试运动 API\\\\\\\"加载数据。\"},\"language\":{\"switch\":\"切换语言\",\"english\":\"English\",\"chinese\":\"中文\"}}"));}}),
"[project]/src/lib/i18n/translations.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Translation utilities
__turbopack_context__.s({
    "getMessages": (()=>getMessages),
    "getTranslations": (()=>getTranslations),
    "t": (()=>t),
    "useTranslations": (()=>useTranslations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$en$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/messages/en.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$zh$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/messages/zh.json (json)");
;
;
const messages = {
    en: __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$en$2e$json__$28$json$29$__["default"],
    zh: __TURBOPACK__imported__module__$5b$project$5d2f$messages$2f$zh$2e$json__$28$json$29$__["default"]
};
function getMessages(locale) {
    return messages[locale] || messages.en;
}
function t(key, locale, params) {
    const msgs = getMessages(locale);
    // Navigate through nested object using dot notation
    const keys = key.split('.');
    let value = msgs;
    for (const k of keys){
        if (value && typeof value === 'object' && k in value) {
            value = value[k];
        } else {
            console.warn(`Translation key not found: ${key}`);
            return key; // Return key as fallback
        }
    }
    if (typeof value !== 'string') {
        console.warn(`Translation value is not a string: ${key}`);
        return key;
    }
    // Replace parameters if provided
    if (params) {
        return value.replace(/\{(\w+)\}/g, (match, paramKey)=>{
            return params[paramKey]?.toString() || match;
        });
    }
    return value;
}
function useTranslations(locale) {
    return (key, params)=>t(key, locale, params);
}
function getTranslations(locale) {
    return (key, params)=>t(key, locale, params);
}
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/i18n/context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "I18nProvider": (()=>I18nProvider),
    "useI18n": (()=>useI18n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
// I18n Context for client components
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$translations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n/translations.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const I18nContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function I18nProvider({ children, initialLocale }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [locale, setLocaleState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialLocale || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLocaleFromPathname"])(pathname) || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultLocale"]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$translations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTranslations"])(locale);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Update locale based on pathname changes
        const pathLocale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLocaleFromPathname"])(pathname);
        if (pathLocale !== locale) {
            setLocaleState(pathLocale);
        }
    }, [
        pathname,
        locale
    ]);
    const setLocale = (newLocale)=>{
        setLocaleState(newLocale);
        // Set cookie
        document.cookie = `locale=${newLocale}; path=/; max-age=31536000`;
        // Navigate to new locale URL
        const currentPath = pathname;
        const pathWithoutLocale = currentPath.replace(/^\/[a-z]{2}/, '') || '/';
        const newPath = newLocale === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultLocale"] ? pathWithoutLocale : `/${newLocale}${pathWithoutLocale}`;
        window.location.href = newPath;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(I18nContext.Provider, {
        value: {
            locale,
            setLocale,
            t
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/i18n/context.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
function useI18n() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(I18nContext);
    if (context === undefined) {
        throw new Error('useI18n must be used within an I18nProvider');
    }
    return context;
}
}}),
"[project]/src/components/providers/app-providers.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppProviders": (()=>AppProviders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$ThemeProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ThemeProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/styles/ThemeProvider.js [app-ssr] (ecmascript) <export default as ThemeProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CssBaseline$2f$CssBaseline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CssBaseline$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/CssBaseline/CssBaseline.js [app-ssr] (ecmascript) <export default as CssBaseline>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/query-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/auth-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sync$2f$sync$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/sync/sync-manager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2f$mui$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/theme/mui-theme.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n/context.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
function AppProviders({ children }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize sync manager
        const syncManager = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sync$2f$sync$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SyncManager"].getInstance();
        // Setup network listeners
        const cleanupNetworkListeners = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["offlineUtils"].setupNetworkListeners();
        // Cleanup on unmount
        return ()=>{
            if (cleanupNetworkListeners) {
                cleanupNetworkListeners();
            }
        };
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["I18nProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$ThemeProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ThemeProvider$3e$__["ThemeProvider"], {
            theme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2f$mui$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fitnessTheme"],
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CssBaseline$2f$CssBaseline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CssBaseline$3e$__["CssBaseline"], {}, void 0, false, {
                    fileName: "[project]/src/components/providers/app-providers.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthProvider"], {
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/providers/app-providers.tsx",
                        lineNumber: 38,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/providers/app-providers.tsx",
                    lineNumber: 37,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/providers/app-providers.tsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/providers/app-providers.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a2857782._.js.map