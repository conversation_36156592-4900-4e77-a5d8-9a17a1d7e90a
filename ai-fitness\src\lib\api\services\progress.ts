/**
 * Progress Tracking API Service
 * Handles all progress and analytics related API calls
 */

import { apiClient } from '../client';
import { API_CONFIG } from '../config';
import type { 
  ProgressRecord, 
  ProgressStats,
  CreateProgressRecordData,
  UpdateProgressRecordData,
  PaginatedResponse 
} from '../types';

export class ProgressService {
  /**
   * Get user's progress records with optional filtering
   */
  static async getProgressRecords(params: {
    limit?: number;
    offset?: number;
    type?: string;
    startDate?: string;
    endDate?: string;
    exerciseId?: string;
    workoutId?: string;
  } = {}): Promise<PaginatedResponse<ProgressRecord>> {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());
    if (params.type) searchParams.append('type', params.type);
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);
    if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);
    if (params.workoutId) searchParams.append('workoutId', params.workoutId);

    const queryString = searchParams.toString();
    const url = queryString 
      ? `${API_CONFIG.ENDPOINTS.PROGRESS.LIST}?${queryString}`
      : API_CONFIG.ENDPOINTS.PROGRESS.LIST;

    return apiClient.get<PaginatedResponse<ProgressRecord>>(url);
  }

  /**
   * Get progress record by ID
   */
  static async getProgressRecord(id: string): Promise<ProgressRecord> {
    return apiClient.get<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.DETAILS(id));
  }

  /**
   * Create a new progress record
   */
  static async createProgressRecord(data: CreateProgressRecordData): Promise<ProgressRecord> {
    return apiClient.post<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.CREATE, data);
  }

  /**
   * Update progress record
   */
  static async updateProgressRecord(
    id: string, 
    data: UpdateProgressRecordData
  ): Promise<ProgressRecord> {
    return apiClient.patch<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.UPDATE(id), data);
  }

  /**
   * Delete progress record
   */
  static async deleteProgressRecord(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRESS.DELETE(id));
  }

  /**
   * Get comprehensive progress statistics
   */
  static async getProgressStats(period: 'week' | 'month' | 'year' | 'all' = 'month'): Promise<ProgressStats> {
    return apiClient.get<ProgressStats>(`${API_CONFIG.ENDPOINTS.PROGRESS.STATS}?period=${period}`);
  }

  /**
   * Get workout completion statistics
   */
  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    totalWorkouts: number;
    totalDuration: number;
    averageDuration: number;
    caloriesBurned: number;
    streakDays: number;
    completionRate: number;
    favoriteExercises: Array<{ name: string; count: number }>;
    weeklyProgress: Array<{ date: string; workouts: number; duration: number; calories: number }>;
    monthlyProgress: Array<{ month: string; workouts: number; duration: number; calories: number }>;
  }> {
    return apiClient.get(`/api/progress/workout-stats?period=${period}`);
  }

  /**
   * Get exercise performance data
   */
  static async getExerciseProgress(exerciseId: string, period: 'week' | 'month' | 'year' = 'month'): Promise<{
    exerciseId: string;
    exerciseName: string;
    totalSessions: number;
    bestPerformance: {
      weight?: number;
      reps?: number;
      duration?: number;
      distance?: number;
      date: string;
    };
    averagePerformance: {
      weight?: number;
      reps?: number;
      duration?: number;
      distance?: number;
    };
    progressData: Array<{
      date: string;
      weight?: number;
      reps?: number;
      duration?: number;
      distance?: number;
      volume?: number;
    }>;
    improvements: {
      weightIncrease?: number;
      repsIncrease?: number;
      durationIncrease?: number;
      distanceIncrease?: number;
    };
  }> {
    return apiClient.get(`/api/progress/exercise/${exerciseId}?period=${period}`);
  }

  /**
   * Get body measurements progress
   */
  static async getBodyMeasurements(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    measurements: Array<{
      date: string;
      weight?: number;
      bodyFat?: number;
      muscleMass?: number;
      chest?: number;
      waist?: number;
      hips?: number;
      arms?: number;
      thighs?: number;
    }>;
    trends: {
      weight?: { change: number; percentage: number };
      bodyFat?: { change: number; percentage: number };
      muscleMass?: { change: number; percentage: number };
    };
  }> {
    return apiClient.get(`/api/progress/body-measurements?period=${period}`);
  }

  /**
   * Add body measurement record
   */
  static async addBodyMeasurement(data: {
    date: string;
    weight?: number;
    bodyFat?: number;
    muscleMass?: number;
    chest?: number;
    waist?: number;
    hips?: number;
    arms?: number;
    thighs?: number;
    notes?: string;
  }): Promise<{ message: string }> {
    return apiClient.post('/api/progress/body-measurements', data);
  }

  /**
   * Get fitness goals and progress
   */
  static async getFitnessGoals(): Promise<{
    goals: Array<{
      id: string;
      title: string;
      description: string;
      targetValue: number;
      currentValue: number;
      unit: string;
      deadline: string;
      category: string;
      progress: number;
      status: 'active' | 'completed' | 'paused';
    }>;
  }> {
    return apiClient.get('/api/progress/goals');
  }

  /**
   * Create a new fitness goal
   */
  static async createFitnessGoal(data: {
    title: string;
    description: string;
    targetValue: number;
    unit: string;
    deadline: string;
    category: string;
  }): Promise<{ message: string; goalId: string }> {
    return apiClient.post('/api/progress/goals', data);
  }

  /**
   * Update fitness goal progress
   */
  static async updateGoalProgress(goalId: string, currentValue: number): Promise<{ message: string }> {
    return apiClient.patch(`/api/progress/goals/${goalId}`, { currentValue });
  }

  /**
   * Get achievement badges and milestones
   */
  static async getAchievements(): Promise<{
    badges: Array<{
      id: string;
      name: string;
      description: string;
      icon: string;
      category: string;
      earnedDate?: string;
      progress?: number;
      requirement: number;
    }>;
    milestones: Array<{
      id: string;
      title: string;
      description: string;
      achievedDate: string;
      category: string;
    }>;
  }> {
    return apiClient.get('/api/progress/achievements');
  }

  /**
   * Get workout calendar data
   */
  static async getWorkoutCalendar(year: number, month: number): Promise<{
    calendar: Array<{
      date: string;
      workouts: number;
      duration: number;
      calories: number;
      hasWorkout: boolean;
    }>;
    monthStats: {
      totalWorkouts: number;
      totalDuration: number;
      totalCalories: number;
      activeDays: number;
    };
  }> {
    return apiClient.get(`/api/progress/calendar?year=${year}&month=${month}`);
  }

  /**
   * Get personal records (PRs)
   */
  static async getPersonalRecords(): Promise<{
    records: Array<{
      exerciseId: string;
      exerciseName: string;
      recordType: 'weight' | 'reps' | 'duration' | 'distance';
      value: number;
      unit: string;
      achievedDate: string;
      workoutId?: string;
    }>;
    recentPRs: Array<{
      exerciseId: string;
      exerciseName: string;
      recordType: string;
      value: number;
      unit: string;
      achievedDate: string;
      improvement: number;
    }>;
  }> {
    return apiClient.get('/api/progress/personal-records');
  }

  /**
   * Get strength progression data
   */
  static async getStrengthProgression(exerciseIds?: string[]): Promise<{
    exercises: Array<{
      exerciseId: string;
      exerciseName: string;
      progressData: Array<{
        date: string;
        maxWeight: number;
        totalVolume: number;
        oneRepMax: number;
      }>;
      trends: {
        weightTrend: number;
        volumeTrend: number;
        oneRepMaxTrend: number;
      };
    }>;
  }> {
    const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';
    return apiClient.get(`/api/progress/strength-progression${params}`);
  }

  /**
   * Export progress data
   */
  static async exportProgressData(format: 'csv' | 'json' = 'csv', period: 'month' | 'year' | 'all' = 'all'): Promise<Blob> {
    const response = await fetch(`${API_CONFIG.BASE_URL}/api/progress/export?format=${format}&period=${period}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to export data');
    }

    return response.blob();
  }

  /**
   * Get workout intensity analysis
   */
  static async getWorkoutIntensity(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    averageIntensity: number;
    intensityDistribution: Array<{
      level: 'low' | 'moderate' | 'high' | 'very_high';
      count: number;
      percentage: number;
    }>;
    weeklyIntensity: Array<{
      week: string;
      averageIntensity: number;
      workoutCount: number;
    }>;
  }> {
    return apiClient.get(`/api/progress/workout-intensity?period=${period}`);
  }
}
