/**
 * Progress Tracking API Service
 * Handles all progress and analytics related API calls
 * Integrated with workout-cool backend
 */

import { apiClient } from '../client';
import { API_CONFIG } from '../config';
import { workoutCoolClient } from '../workout-cool-client';
import type {
  ProgressRecord,
  ProgressStats,
  CreateProgressRecordData,
  UpdateProgressRecordData,
  PaginatedResponse
} from '../types';

// workout-cool specific types
interface WorkoutCoolWorkoutSession {
  id: string;
  userId: string;
  programId: string;
  weekNumber: number;
  sessionNumber: number;
  startedAt: Date;
  completedAt?: Date;
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'SKIPPED';
  notes?: string;
  exercises: Array<{
    id: string;
    exerciseId: string;
    order: number;
    sets: Array<{
      id: string;
      setIndex: number;
      reps?: number;
      weight?: number;
      duration?: number;
      distance?: number;
      completed: boolean;
    }>;
  }>;
}

interface WorkoutCoolProgressStats {
  totalWorkouts: number;
  totalExercises: number;
  totalSets: number;
  totalReps: number;
  totalWeight: number;
  totalDuration: number;
  averageWorkoutDuration: number;
  streakDays: number;
  lastWorkoutDate?: Date;
}

export class ProgressService {
  /**
   * Get user's progress records with optional filtering
   * Integrates with workout-cool's workout session data
   */
  static async getProgressRecords(params: {
    limit?: number;
    offset?: number;
    type?: string;
    startDate?: string;
    endDate?: string;
    exerciseId?: string;
    workoutId?: string;
  } = {}): Promise<PaginatedResponse<ProgressRecord>> {
    try {
      // Try to get real data from workout-cool if available
      if (workoutCoolClient.isConnectedToDatabase()) {
        return await this.getWorkoutCoolProgressRecords(params);
      }

      // Fallback to original API
      const searchParams = new URLSearchParams();

      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.offset) searchParams.append('offset', params.offset.toString());
      if (params.type) searchParams.append('type', params.type);
      if (params.startDate) searchParams.append('startDate', params.startDate);
      if (params.endDate) searchParams.append('endDate', params.endDate);
      if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);
      if (params.workoutId) searchParams.append('workoutId', params.workoutId);

      const queryString = searchParams.toString();
      const url = queryString
        ? `${API_CONFIG.ENDPOINTS.PROGRESS.LIST}?${queryString}`
        : API_CONFIG.ENDPOINTS.PROGRESS.LIST;

      return apiClient.get<PaginatedResponse<ProgressRecord>>(url);
    } catch (error) {
      console.error('Error fetching progress records:', error);
      throw error;
    }
  }

  /**
   * Get progress records from workout-cool backend
   */
  private static async getWorkoutCoolProgressRecords(params: {
    limit?: number;
    offset?: number;
    type?: string;
    startDate?: string;
    endDate?: string;
    exerciseId?: string;
    workoutId?: string;
  }): Promise<PaginatedResponse<ProgressRecord>> {
    // Mock data representing workout-cool workout sessions
    const mockWorkoutSessions: WorkoutCoolWorkoutSession[] = [
      {
        id: 'session-001',
        userId: 'user-001',
        programId: 'wc-prog-001',
        weekNumber: 1,
        sessionNumber: 1,
        startedAt: new Date('2024-01-15T09:00:00Z'),
        completedAt: new Date('2024-01-15T09:45:00Z'),
        status: 'COMPLETED',
        notes: 'Great workout, felt strong',
        exercises: [
          {
            id: 'session-ex-001',
            exerciseId: 'wc-ex-001',
            order: 1,
            sets: [
              { id: 'set-001', setIndex: 1, reps: 12, weight: 0, duration: 0, distance: 0, completed: true },
              { id: 'set-002', setIndex: 2, reps: 10, weight: 0, duration: 0, distance: 0, completed: true },
              { id: 'set-003', setIndex: 3, reps: 8, weight: 0, duration: 0, distance: 0, completed: true },
            ],
          },
        ],
      },
    ];

    // Convert workout-cool sessions to progress records
    const progressRecords: ProgressRecord[] = mockWorkoutSessions.map(session => ({
      id: session.id,
      userId: session.userId,
      type: 'workout',
      date: session.completedAt?.toISOString() || session.startedAt.toISOString(),
      value: session.exercises.reduce((total, ex) => total + ex.sets.length, 0), // Total sets
      unit: 'sets',
      exerciseId: session.exercises[0]?.exerciseId,
      workoutId: session.programId,
      notes: session.notes,
      metadata: {
        weekNumber: session.weekNumber,
        sessionNumber: session.sessionNumber,
        status: session.status,
        duration: session.completedAt && session.startedAt
          ? Math.round((session.completedAt.getTime() - session.startedAt.getTime()) / 60000)
          : 0,
      },
      createdAt: session.startedAt.toISOString(),
      updatedAt: session.completedAt?.toISOString() || session.startedAt.toISOString(),
    }));

    // Apply filtering
    let filteredRecords = progressRecords;

    if (params.type) {
      filteredRecords = filteredRecords.filter(record => record.type === params.type);
    }

    if (params.startDate) {
      filteredRecords = filteredRecords.filter(record => record.date >= params.startDate!);
    }

    if (params.endDate) {
      filteredRecords = filteredRecords.filter(record => record.date <= params.endDate!);
    }

    // Apply pagination
    const offset = params.offset || 0;
    const limit = params.limit || 20;
    const paginatedRecords = filteredRecords.slice(offset, offset + limit);

    return {
      data: paginatedRecords,
      total: filteredRecords.length,
      page: Math.floor(offset / limit) + 1,
      limit,
      hasNext: offset + limit < filteredRecords.length,
      hasPrev: offset > 0,
    };
  }

  /**
   * Get progress record by ID
   */
  static async getProgressRecord(id: string): Promise<ProgressRecord> {
    return apiClient.get<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.DETAILS(id));
  }

  /**
   * Create a new progress record
   */
  static async createProgressRecord(data: CreateProgressRecordData): Promise<ProgressRecord> {
    return apiClient.post<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.CREATE, data);
  }

  /**
   * Update progress record
   */
  static async updateProgressRecord(
    id: string, 
    data: UpdateProgressRecordData
  ): Promise<ProgressRecord> {
    return apiClient.patch<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.UPDATE(id), data);
  }

  /**
   * Delete progress record
   */
  static async deleteProgressRecord(id: string): Promise<void> {
    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRESS.DELETE(id));
  }

  /**
   * Get comprehensive progress statistics
   * Integrates with workout-cool's progress tracking
   */
  static async getProgressStats(period: 'week' | 'month' | 'year' | 'all' = 'month'): Promise<ProgressStats> {
    try {
      // Try to get real data from workout-cool if available
      if (workoutCoolClient.isConnectedToDatabase()) {
        return await this.getWorkoutCoolProgressStats(period);
      }

      // Fallback to original API
      return apiClient.get<ProgressStats>(`${API_CONFIG.ENDPOINTS.PROGRESS.STATS}?period=${period}`);
    } catch (error) {
      console.error('Error fetching progress stats:', error);
      throw error;
    }
  }

  /**
   * Get progress statistics from workout-cool backend
   */
  private static async getWorkoutCoolProgressStats(period: 'week' | 'month' | 'year' | 'all'): Promise<ProgressStats> {
    // Mock data representing workout-cool progress statistics
    const mockStats: WorkoutCoolProgressStats = {
      totalWorkouts: 24,
      totalExercises: 156,
      totalSets: 468,
      totalReps: 3744,
      totalWeight: 18720, // kg
      totalDuration: 1080, // minutes
      averageWorkoutDuration: 45,
      streakDays: 7,
      lastWorkoutDate: new Date('2024-01-15T09:45:00Z'),
    };

    // Convert to ProgressStats format
    const progressStats: ProgressStats = {
      totalRecords: mockStats.totalWorkouts,
      totalValue: mockStats.totalSets,
      averageValue: Math.round(mockStats.totalSets / mockStats.totalWorkouts),
      bestValue: 25, // Best sets in a single workout
      currentStreak: mockStats.streakDays,
      longestStreak: 14,
      lastRecordDate: mockStats.lastWorkoutDate?.toISOString(),
      improvementRate: 12.5, // percentage
      weeklyAverage: Math.round(mockStats.totalWorkouts / 4), // assuming 4 weeks
      monthlyAverage: mockStats.totalWorkouts,
      progressTrend: 'increasing',
      achievements: [
        { id: '1', name: 'First Workout', description: 'Completed your first workout', unlockedAt: '2024-01-01T00:00:00Z' },
        { id: '2', name: 'Week Warrior', description: 'Completed 7 days in a row', unlockedAt: '2024-01-08T00:00:00Z' },
        { id: '3', name: 'Strength Builder', description: 'Lifted 10,000kg total', unlockedAt: '2024-01-12T00:00:00Z' },
      ],
      chartData: [
        { date: '2024-01-08', value: 18 },
        { date: '2024-01-09', value: 20 },
        { date: '2024-01-10', value: 22 },
        { date: '2024-01-11', value: 19 },
        { date: '2024-01-12', value: 24 },
        { date: '2024-01-13', value: 25 },
        { date: '2024-01-14', value: 23 },
        { date: '2024-01-15', value: 25 },
      ],
    };

    return progressStats;
  }

  /**
   * Get workout completion statistics
   */
  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    totalWorkouts: number;
    totalDuration: number;
    averageDuration: number;
    caloriesBurned: number;
    streakDays: number;
    completionRate: number;
    favoriteExercises: Array<{ name: string; count: number }>;
    weeklyProgress: Array<{ date: string; workouts: number; duration: number; calories: number }>;
    monthlyProgress: Array<{ month: string; workouts: number; duration: number; calories: number }>;
  }> {
    return apiClient.get(`/api/progress/workout-stats?period=${period}`);
  }

  /**
   * Get exercise performance data
   */
  static async getExerciseProgress(exerciseId: string, period: 'week' | 'month' | 'year' = 'month'): Promise<{
    exerciseId: string;
    exerciseName: string;
    totalSessions: number;
    bestPerformance: {
      weight?: number;
      reps?: number;
      duration?: number;
      distance?: number;
      date: string;
    };
    averagePerformance: {
      weight?: number;
      reps?: number;
      duration?: number;
      distance?: number;
    };
    progressData: Array<{
      date: string;
      weight?: number;
      reps?: number;
      duration?: number;
      distance?: number;
      volume?: number;
    }>;
    improvements: {
      weightIncrease?: number;
      repsIncrease?: number;
      durationIncrease?: number;
      distanceIncrease?: number;
    };
  }> {
    return apiClient.get(`/api/progress/exercise/${exerciseId}?period=${period}`);
  }

  /**
   * Get body measurements progress
   */
  static async getBodyMeasurements(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    measurements: Array<{
      date: string;
      weight?: number;
      bodyFat?: number;
      muscleMass?: number;
      chest?: number;
      waist?: number;
      hips?: number;
      arms?: number;
      thighs?: number;
    }>;
    trends: {
      weight?: { change: number; percentage: number };
      bodyFat?: { change: number; percentage: number };
      muscleMass?: { change: number; percentage: number };
    };
  }> {
    return apiClient.get(`/api/progress/body-measurements?period=${period}`);
  }

  /**
   * Add body measurement record
   */
  static async addBodyMeasurement(data: {
    date: string;
    weight?: number;
    bodyFat?: number;
    muscleMass?: number;
    chest?: number;
    waist?: number;
    hips?: number;
    arms?: number;
    thighs?: number;
    notes?: string;
  }): Promise<{ message: string }> {
    return apiClient.post('/api/progress/body-measurements', data);
  }

  /**
   * Get fitness goals and progress
   */
  static async getFitnessGoals(): Promise<{
    goals: Array<{
      id: string;
      title: string;
      description: string;
      targetValue: number;
      currentValue: number;
      unit: string;
      deadline: string;
      category: string;
      progress: number;
      status: 'active' | 'completed' | 'paused';
    }>;
  }> {
    return apiClient.get('/api/progress/goals');
  }

  /**
   * Create a new fitness goal
   */
  static async createFitnessGoal(data: {
    title: string;
    description: string;
    targetValue: number;
    unit: string;
    deadline: string;
    category: string;
  }): Promise<{ message: string; goalId: string }> {
    return apiClient.post('/api/progress/goals', data);
  }

  /**
   * Update fitness goal progress
   */
  static async updateGoalProgress(goalId: string, currentValue: number): Promise<{ message: string }> {
    return apiClient.patch(`/api/progress/goals/${goalId}`, { currentValue });
  }

  /**
   * Get achievement badges and milestones
   */
  static async getAchievements(): Promise<{
    badges: Array<{
      id: string;
      name: string;
      description: string;
      icon: string;
      category: string;
      earnedDate?: string;
      progress?: number;
      requirement: number;
    }>;
    milestones: Array<{
      id: string;
      title: string;
      description: string;
      achievedDate: string;
      category: string;
    }>;
  }> {
    return apiClient.get('/api/progress/achievements');
  }

  /**
   * Get workout calendar data
   */
  static async getWorkoutCalendar(year: number, month: number): Promise<{
    calendar: Array<{
      date: string;
      workouts: number;
      duration: number;
      calories: number;
      hasWorkout: boolean;
    }>;
    monthStats: {
      totalWorkouts: number;
      totalDuration: number;
      totalCalories: number;
      activeDays: number;
    };
  }> {
    return apiClient.get(`/api/progress/calendar?year=${year}&month=${month}`);
  }

  /**
   * Get personal records (PRs)
   */
  static async getPersonalRecords(): Promise<{
    records: Array<{
      exerciseId: string;
      exerciseName: string;
      recordType: 'weight' | 'reps' | 'duration' | 'distance';
      value: number;
      unit: string;
      achievedDate: string;
      workoutId?: string;
    }>;
    recentPRs: Array<{
      exerciseId: string;
      exerciseName: string;
      recordType: string;
      value: number;
      unit: string;
      achievedDate: string;
      improvement: number;
    }>;
  }> {
    return apiClient.get('/api/progress/personal-records');
  }

  /**
   * Get strength progression data
   */
  static async getStrengthProgression(exerciseIds?: string[]): Promise<{
    exercises: Array<{
      exerciseId: string;
      exerciseName: string;
      progressData: Array<{
        date: string;
        maxWeight: number;
        totalVolume: number;
        oneRepMax: number;
      }>;
      trends: {
        weightTrend: number;
        volumeTrend: number;
        oneRepMaxTrend: number;
      };
    }>;
  }> {
    const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';
    return apiClient.get(`/api/progress/strength-progression${params}`);
  }

  /**
   * Export progress data
   */
  static async exportProgressData(format: 'csv' | 'json' = 'csv', period: 'month' | 'year' | 'all' = 'all'): Promise<Blob> {
    const response = await fetch(`${API_CONFIG.BASE_URL}/api/progress/export?format=${format}&period=${period}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to export data');
    }

    return response.blob();
  }

  /**
   * Get workout intensity analysis
   */
  static async getWorkoutIntensity(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    averageIntensity: number;
    intensityDistribution: Array<{
      level: 'low' | 'moderate' | 'high' | 'very_high';
      count: number;
      percentage: number;
    }>;
    weeklyIntensity: Array<{
      week: string;
      averageIntensity: number;
      workoutCount: number;
    }>;
  }> {
    return apiClient.get(`/api/progress/workout-intensity?period=${period}`);
  }
}
