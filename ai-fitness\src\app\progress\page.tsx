"use client"

import { useState } from "react"
import { Navigation } from "@/components/navigation"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  TrendingUp,
  Calendar,
  Target,
  Award,
  Activity,
  Flame,
  Clock,
  BarChart3,
  Download,
  Settings,
  Plus,
  Eye,
  TrendingDown,
  Users,
  Zap,
  Heart,
  X,
  Edit,
  Check
} from "lucide-react"

// Mock data for enhanced functionality
const progressData = {
  weeklyActivity: [
    { week: 'W1', workouts: 3, calories: 450, time: 2.5 },
    { week: 'W2', workouts: 5, calories: 750, time: 4.2 },
    { week: 'W3', workouts: 4, calories: 600, time: 3.1 },
    { week: 'W4', workouts: 6, calories: 900, time: 5.0 },
    { week: 'W5', workouts: 5, calories: 750, time: 4.5 },
    { week: 'W6', workouts: 7, calories: 1050, time: 6.2 },
    { week: 'W7', workouts: 4, calories: 600, time: 3.8 },
    { week: 'W8', workouts: 6, calories: 900, time: 5.1 }
  ],
  goals: [
    { id: 1, name: 'Weekly Workouts', current: 4, target: 5, unit: 'workouts', color: 'blue' },
    { id: 2, name: 'Monthly Calories', current: 2340, target: 3000, unit: 'cal', color: 'green' },
    { id: 3, name: 'Workout Streak', current: 12, target: 30, unit: 'days', color: 'purple' },
    { id: 4, name: 'Monthly Time', current: 8.5, target: 12, unit: 'hours', color: 'orange' }
  ],
  achievements: [
    { id: 1, title: '100 Workouts', description: 'Completed 100 total workouts', icon: '🏆', date: '2024-12-15', category: 'milestone' },
    { id: 2, title: '10-Day Streak', description: 'Worked out 10 days in a row', icon: '🔥', date: '2024-12-10', category: 'streak' },
    { id: 3, title: 'Strength Master', description: 'Completed 25 strength workouts', icon: '💪', date: '2024-12-05', category: 'category' },
    { id: 4, title: 'Cardio King', description: 'Burned 5000 calories in cardio', icon: '❤️', date: '2024-11-28', category: 'category' },
    { id: 5, title: 'Early Bird', description: 'Completed 10 morning workouts', icon: '🌅', date: '2024-11-20', category: 'habit' }
  ],
  workoutHistory: [
    { date: '2024-12-20', type: 'Strength Training', duration: 45, calories: 320, exercises: 8 },
    { date: '2024-12-19', type: 'Cardio Blast', duration: 30, calories: 280, exercises: 6 },
    { date: '2024-12-18', type: 'Full Body', duration: 60, calories: 420, exercises: 12 },
    { date: '2024-12-17', type: 'HIIT', duration: 25, calories: 350, exercises: 5 },
    { date: '2024-12-16', type: 'Yoga Flow', duration: 40, calories: 180, exercises: 10 }
  ]
}

export default function Progress() {
  const [timeRange, setTimeRange] = useState('8weeks')
  const [selectedMetric, setSelectedMetric] = useState('workouts')
  const [showGoalModal, setShowGoalModal] = useState(false)
  const [showHistoryModal, setShowHistoryModal] = useState(false)
  const [goals, setGoals] = useState(progressData.goals)

  const timeRanges = [
    { value: '1week', label: '1 Week' },
    { value: '4weeks', label: '4 Weeks' },
    { value: '8weeks', label: '8 Weeks' },
    { value: '6months', label: '6 Months' },
    { value: '1year', label: '1 Year' }
  ]

  const metrics = [
    { value: 'workouts', label: 'Workouts', icon: Activity },
    { value: 'calories', label: 'Calories', icon: Flame },
    { value: 'time', label: 'Time', icon: Clock }
  ]

  const toggleFavoriteGoal = (goalId: number) => {
    setGoals(goals.map(goal =>
      goal.id === goalId
        ? { ...goal, isFavorite: !goal.isFavorite }
        : goal
    ))
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      {/* Header Section */}
      <section className="bg-gradient-to-br from-purple-50 to-indigo-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              Your Progress
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Track your fitness journey with detailed analytics and insights
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">

        {/* Controls Section */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-700 py-2">Time Range:</span>
              {timeRanges.map((range) => (
                <Button
                  key={range.value}
                  variant={timeRange === range.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeRange(range.value)}
                >
                  {range.label}
                </Button>
              ))}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowGoalModal(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Set Goal
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-gray-700 py-2">View:</span>
            {metrics.map((metric) => {
              const IconComponent = metric.icon
              return (
                <Button
                  key={metric.value}
                  variant={selectedMetric === metric.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedMetric(metric.value)}
                >
                  <IconComponent className="h-4 w-4 mr-2" />
                  {metric.label}
                </Button>
              )
            })}
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Workouts
              </CardTitle>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <Activity className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">127</div>
              <p className="text-xs text-muted-foreground">
                +12 this month (+10.4%)
              </p>
              <div className="mt-2">
                <Badge variant="secondary" className="text-xs">
                  Above Average
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Time
              </CardTitle>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <Clock className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">89h</div>
              <p className="text-xs text-muted-foreground">
                +8h this month (+9.9%)
              </p>
              <div className="mt-2">
                <Badge variant="secondary" className="text-xs">
                  On Track
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Calories Burned
              </CardTitle>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <Flame className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24,567</div>
              <p className="text-xs text-muted-foreground">
                +2,340 this month (+10.5%)
              </p>
              <div className="mt-2">
                <Badge variant="secondary" className="text-xs">
                  Excellent
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Current Streak
              </CardTitle>
              <div className="flex items-center gap-2">
                <Flame className="h-4 w-4 text-orange-500" />
                <Target className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12 days</div>
              <p className="text-xs text-muted-foreground">
                Personal best! 🎉
              </p>
              <div className="mt-2">
                <Badge className="text-xs bg-orange-100 text-orange-800">
                  Hot Streak
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Progress Chart */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Weekly Activity
                  </div>
                  <Button variant="outline" size="sm" onClick={() => setShowHistoryModal(true)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </CardTitle>
                <CardDescription>
                  Your {selectedMetric} over the past {timeRange === '8weeks' ? '8 weeks' : timeRange}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Enhanced interactive bar chart */}
                  <div className="flex items-end justify-between h-48 border-b border-gray-200 px-2">
                    {progressData.weeklyActivity.map((data, index) => {
                      const value = selectedMetric === 'workouts' ? data.workouts :
                                   selectedMetric === 'calories' ? data.calories / 150 :
                                   data.time
                      const maxValue = selectedMetric === 'workouts' ? 7 :
                                      selectedMetric === 'calories' ? 7 :
                                      7
                      const height = (value / maxValue) * 100
                      const color = selectedMetric === 'workouts' ? 'bg-blue-500' :
                                   selectedMetric === 'calories' ? 'bg-green-500' :
                                   'bg-purple-500'

                      return (
                        <div key={index} className="flex flex-col items-center group cursor-pointer">
                          <div className="relative">
                            <div
                              className={`${color} w-10 rounded-t transition-all duration-300 group-hover:opacity-80 group-hover:scale-105`}
                              style={{ height: `${Math.max(height, 8)}%` }}
                            ></div>
                            {/* Tooltip on hover */}
                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                              {selectedMetric === 'workouts' && `${data.workouts} workouts`}
                              {selectedMetric === 'calories' && `${data.calories} cal`}
                              {selectedMetric === 'time' && `${data.time}h`}
                            </div>
                          </div>
                          <span className="text-xs text-gray-500 mt-2 font-medium">
                            {data.week}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>0 {selectedMetric}</span>
                    <span>
                      {selectedMetric === 'workouts' && '7 workouts'}
                      {selectedMetric === 'calories' && '1000+ cal'}
                      {selectedMetric === 'time' && '7+ hours'}
                    </span>
                  </div>

                  {/* Chart summary */}
                  <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-blue-600">
                        {progressData.weeklyActivity.reduce((sum, data) => sum + data.workouts, 0)}
                      </div>
                      <div className="text-xs text-gray-500">Total Workouts</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">
                        {progressData.weeklyActivity.reduce((sum, data) => sum + data.calories, 0)}
                      </div>
                      <div className="text-xs text-gray-500">Total Calories</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-purple-600">
                        {progressData.weeklyActivity.reduce((sum, data) => sum + data.time, 0).toFixed(1)}h
                      </div>
                      <div className="text-xs text-gray-500">Total Time</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Goals & Achievements */}
          <div className="space-y-6">
            {/* Current Goals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Current Goals
                  </div>
                  <Button variant="outline" size="sm" onClick={() => setShowGoalModal(true)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {goals.map((goal) => {
                  const percentage = Math.min((goal.current / goal.target) * 100, 100)
                  const colorClass = goal.color === 'blue' ? 'bg-blue-600' :
                                    goal.color === 'green' ? 'bg-green-600' :
                                    goal.color === 'purple' ? 'bg-purple-600' :
                                    'bg-orange-600'

                  return (
                    <div key={goal.id} className="space-y-2">
                      <div className="flex justify-between items-center text-sm">
                        <span className="font-medium">{goal.name}</span>
                        <div className="flex items-center gap-2">
                          <span>{goal.current}/{goal.target} {goal.unit}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => toggleFavoriteGoal(goal.id)}
                          >
                            <Heart className={`h-3 w-3 ${goal.isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}`} />
                          </Button>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`${colorClass} h-2 rounded-full transition-all duration-500`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{percentage.toFixed(0)}% complete</span>
                        {percentage >= 100 ? (
                          <Badge className="text-xs bg-green-100 text-green-800">
                            <Check className="h-3 w-3 mr-1" />
                            Achieved!
                          </Badge>
                        ) : (
                          <span>{(goal.target - goal.current).toFixed(1)} {goal.unit} to go</span>
                        )}
                      </div>
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* Recent Achievements */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    Recent Achievements
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {progressData.achievements.length} Total
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 max-h-80 overflow-y-auto">
                {progressData.achievements.map((achievement) => {
                  const bgColor = achievement.category === 'milestone' ? 'bg-yellow-50 border-yellow-200' :
                                 achievement.category === 'streak' ? 'bg-orange-50 border-orange-200' :
                                 achievement.category === 'category' ? 'bg-green-50 border-green-200' :
                                 'bg-blue-50 border-blue-200'

                  return (
                    <div key={achievement.id} className={`flex items-center gap-3 p-3 rounded-lg border transition-all hover:shadow-md cursor-pointer ${bgColor}`}>
                      <div className="text-2xl">{achievement.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{achievement.title}</h4>
                        <p className="text-xs text-gray-600">{achievement.description}</p>
                        <p className="text-xs text-gray-400 mt-1">{achievement.date}</p>
                      </div>
                      <Badge
                        variant="outline"
                        className="text-xs capitalize"
                      >
                        {achievement.category}
                      </Badge>
                    </div>
                  )
                })}

                <Button variant="outline" className="w-full text-sm" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View All Achievements
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Monthly Summary & Recent Workouts */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Monthly Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                This Month's Summary
              </CardTitle>
              <CardDescription>
                December 2024 - Your fitness highlights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">12</div>
                  <p className="text-sm text-gray-600">Workouts</p>
                  <p className="text-xs text-green-600">+20% vs last month</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">8.5h</div>
                  <p className="text-sm text-gray-600">Exercise Time</p>
                  <p className="text-xs text-green-600">+15% vs last month</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">2,340</div>
                  <p className="text-sm text-gray-600">Calories</p>
                  <p className="text-xs text-green-600">+25% vs last month</p>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">4.2</div>
                  <p className="text-sm text-gray-600">Avg/Week</p>
                  <p className="text-xs text-green-600">Above target</p>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-sm">Monthly Insights</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2 text-green-600">
                    <TrendingUp className="h-4 w-4" />
                    <span>Best month for consistency this year</span>
                  </div>
                  <div className="flex items-center gap-2 text-blue-600">
                    <Users className="h-4 w-4" />
                    <span>Top 15% among similar users</span>
                  </div>
                  <div className="flex items-center gap-2 text-purple-600">
                    <Zap className="h-4 w-4" />
                    <span>Strength training improved by 18%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Workout History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Workouts
                </div>
                <Button variant="outline" size="sm" onClick={() => setShowHistoryModal(true)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </CardTitle>
              <CardDescription>
                Your last 5 workout sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {progressData.workoutHistory.map((workout, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Activity className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm">{workout.type}</h4>
                        <p className="text-xs text-gray-500">{workout.date}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{workout.duration}min</div>
                      <div className="text-xs text-gray-500">{workout.calories} cal</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Goal Setting Modal */}
        {showGoalModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Set New Goal</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowGoalModal(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Goal Type</label>
                    <select className="w-full p-2 border border-gray-300 rounded-lg">
                      <option>Weekly Workouts</option>
                      <option>Monthly Calories</option>
                      <option>Workout Streak</option>
                      <option>Monthly Time</option>
                      <option>Custom Goal</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Target Value</label>
                    <input
                      type="number"
                      placeholder="Enter target value"
                      className="w-full p-2 border border-gray-300 rounded-lg"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Time Frame</label>
                    <select className="w-full p-2 border border-gray-300 rounded-lg">
                      <option>Weekly</option>
                      <option>Monthly</option>
                      <option>Quarterly</option>
                      <option>Yearly</option>
                    </select>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => setShowGoalModal(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="flex-1"
                      onClick={() => setShowGoalModal(false)}
                    >
                      Create Goal
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Workout History Modal */}
        {showHistoryModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Workout History</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowHistoryModal(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-3">
                  {progressData.workoutHistory.map((workout, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <Activity className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">{workout.type}</h4>
                          <p className="text-sm text-gray-500">{workout.date}</p>
                          <p className="text-xs text-gray-400">{workout.exercises} exercises</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{workout.duration} min</div>
                        <div className="text-sm text-gray-500">{workout.calories} cal</div>
                        <Badge variant="outline" className="text-xs mt-1">
                          Completed
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2 pt-4 mt-6 border-t">
                  <Button variant="outline" className="flex-1">
                    <Download className="h-4 w-4 mr-2" />
                    Export History
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => setShowHistoryModal(false)}
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
