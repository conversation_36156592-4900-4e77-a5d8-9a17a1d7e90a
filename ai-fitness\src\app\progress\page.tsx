"use client"

import { useState } from "react"
import { Navigation } from "@/components/Navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingPage, LoadingCard } from "@/components/ui/loading"
import { ErrorMessage } from "@/components/ui/error"
import { EmptyProgress, EmptyGoals, EmptyHistory } from "@/components/ui/empty-state"
import {
  TrendingUp,
  Calendar,
  Target,
  Award,
  Activity,
  Flame,
  Clock,
  BarChart3,
  Download,
  Settings,
  Plus,
  Eye,
  TrendingDown,
  Users,
  Zap,
  Heart,
  X,
  Edit,
  Check
} from "lucide-react"
import {
  useProgressStats,
  useWorkoutStats,
  useBodyMeasurements,
  useFitnessGoals,
  useAchievements,
  useWorkoutCalendar,
  usePersonalRecords,
  useWorkoutIntensity,
  useExportProgressData
} from "@/lib/hooks/use-progress"
import { useAuth } from "@/lib/hooks/use-auth"

export default function ProgressPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'goals' | 'achievements' | 'history'>('overview')
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month')
  
  const { isAuthenticated } = useAuth()
  
  // API hooks
  const { data: progressStats, isLoading: isLoadingProgress } = useProgressStats(selectedPeriod)
  const { data: workoutStats, isLoading: isLoadingWorkouts } = useWorkoutStats(selectedPeriod)
  const { data: bodyMeasurements, isLoading: isLoadingBody } = useBodyMeasurements(selectedPeriod)
  const { data: fitnessGoals, isLoading: isLoadingGoals } = useFitnessGoals()
  const { data: achievements, isLoading: isLoadingAchievements } = useAchievements()
  const { data: personalRecords, isLoading: isLoadingPRs } = usePersonalRecords()
  const { data: workoutIntensity, isLoading: isLoadingIntensity } = useWorkoutIntensity(selectedPeriod)
  
  // Current month for calendar
  const currentDate = new Date()
  const { data: calendarData, isLoading: isLoadingCalendar } = useWorkoutCalendar(
    currentDate.getFullYear(), 
    currentDate.getMonth() + 1
  )
  
  const exportDataMutation = useExportProgressData()

  const handleExportData = (format: 'csv' | 'json') => {
    exportDataMutation.mutate({ format, period: 'all' })
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Sign in to view your progress</h1>
            <p className="text-gray-600 mb-6">Track your fitness journey with detailed analytics and insights</p>
            <Button asChild>
              <a href="/auth/signin">Sign In</a>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Progress Dashboard
            </h1>
            <p className="text-xl text-gray-600">
              Track your fitness journey and celebrate your achievements
            </p>
          </div>
          
          <div className="flex gap-3">
            {/* Period Selector */}
            <div className="flex bg-white rounded-lg p-1 shadow-sm">
              {(['week', 'month', 'year'] as const).map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    selectedPeriod === period
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </button>
              ))}
            </div>
            
            {/* Export Button */}
            <Button 
              variant="outline" 
              onClick={() => handleExportData('csv')}
              disabled={exportDataMutation.isPending}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {exportDataMutation.isPending ? 'Exporting...' : 'Export'}
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'goals', label: 'Goals', icon: Target },
            { id: 'achievements', label: 'Achievements', icon: Award },
            { id: 'history', label: 'History', icon: Calendar }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Workouts</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {isLoadingWorkouts ? '...' : workoutStats?.totalWorkouts || 0}
                      </p>
                      <p className="text-xs text-green-600 flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        +12% from last {selectedPeriod}
                      </p>
                    </div>
                    <Activity className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Duration</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {isLoadingWorkouts ? '...' : `${Math.round((workoutStats?.totalDuration || 0) / 60)}h`}
                      </p>
                      <p className="text-xs text-green-600 flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        +8% from last {selectedPeriod}
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Calories Burned</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {isLoadingWorkouts ? '...' : workoutStats?.caloriesBurned || 0}
                      </p>
                      <p className="text-xs text-green-600 flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        +15% from last {selectedPeriod}
                      </p>
                    </div>
                    <Flame className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Current Streak</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {isLoadingWorkouts ? '...' : workoutStats?.streakDays || 0}
                      </p>
                      <p className="text-xs text-gray-600">days</p>
                    </div>
                    <Zap className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Progress Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Weekly Progress Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Weekly Progress</CardTitle>
                  <CardDescription>Your workout activity over time</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingWorkouts ? (
                    <LoadingCard />
                  ) : workoutStats?.weeklyProgress ? (
                    <div className="space-y-4">
                      {workoutStats.weeklyProgress.slice(-8).map((week, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">{week.date}</span>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Activity className="h-4 w-4 text-blue-600" />
                              <span className="text-sm">{week.workouts}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-green-600" />
                              <span className="text-sm">{Math.round(week.duration / 60)}h</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Flame className="h-4 w-4 text-orange-600" />
                              <span className="text-sm">{week.calories}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <EmptyProgress />
                  )}
                </CardContent>
              </Card>

              {/* Workout Calendar */}
              <Card>
                <CardHeader>
                  <CardTitle>Workout Calendar</CardTitle>
                  <CardDescription>This month's activity</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingCalendar ? (
                    <LoadingCard />
                  ) : calendarData ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-7 gap-2 text-center">
                        {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day) => (
                          <div key={day} className="text-xs font-medium text-gray-500 p-2">
                            {day}
                          </div>
                        ))}
                        {calendarData.calendar.slice(0, 35).map((day, index) => (
                          <div
                            key={index}
                            className={`p-2 text-xs rounded ${
                              day.hasWorkout
                                ? 'bg-blue-100 text-blue-800 font-medium'
                                : 'text-gray-400'
                            }`}
                          >
                            {new Date(day.date).getDate()}
                          </div>
                        ))}
                      </div>
                      <div className="pt-4 border-t">
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="text-lg font-bold text-blue-600">
                              {calendarData.monthStats.totalWorkouts}
                            </div>
                            <div className="text-xs text-gray-600">Workouts</div>
                          </div>
                          <div>
                            <div className="text-lg font-bold text-green-600">
                              {Math.round(calendarData.monthStats.totalDuration / 60)}h
                            </div>
                            <div className="text-xs text-gray-600">Duration</div>
                          </div>
                          <div>
                            <div className="text-lg font-bold text-orange-600">
                              {calendarData.monthStats.activeDays}
                            </div>
                            <div className="text-xs text-gray-600">Active Days</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <EmptyProgress />
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Personal Records */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Recent Personal Records</CardTitle>
                    <CardDescription>Your latest achievements</CardDescription>
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isLoadingPRs ? (
                  <LoadingCard />
                ) : personalRecords?.recentPRs && personalRecords.recentPRs.length > 0 ? (
                  <div className="space-y-4">
                    {personalRecords.recentPRs.slice(0, 5).map((record, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-medium text-gray-900">{record.exerciseName}</h4>
                          <p className="text-sm text-gray-600">
                            {record.recordType}: {record.value} {record.unit}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-green-600">
                            +{record.improvement} {record.unit}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(record.achievedDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <EmptyHistory />
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Goals Tab */}
        {activeTab === 'goals' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Fitness Goals</h2>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Goal
              </Button>
            </div>

            {isLoadingGoals ? (
              <LoadingCard />
            ) : fitnessGoals?.goals && fitnessGoals.goals.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {fitnessGoals.goals.map((goal) => (
                  <Card key={goal.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{goal.title}</CardTitle>
                          <CardDescription>{goal.description}</CardDescription>
                        </div>
                        <Badge 
                          variant={goal.status === 'completed' ? 'default' : 'secondary'}
                        >
                          {goal.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{goal.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(goal.progress, 100)}%` }}
                          />
                        </div>
                        <div className="flex justify-between text-sm text-gray-600">
                          <span>{goal.currentValue} / {goal.targetValue} {goal.unit}</span>
                          <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <EmptyGoals />
            )}
          </div>
        )}

        {/* Achievements Tab */}
        {activeTab === 'achievements' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Achievements & Badges</h2>

            {isLoadingAchievements ? (
              <LoadingCard />
            ) : achievements ? (
              <div className="space-y-8">
                {/* Recent Milestones */}
                {achievements.milestones && achievements.milestones.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Milestones</h3>
                    <div className="space-y-3">
                      {achievements.milestones.slice(0, 5).map((milestone) => (
                        <div key={milestone.id} className="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
                          <Award className="h-8 w-8 text-green-600" />
                          <div>
                            <h4 className="font-medium text-gray-900">{milestone.title}</h4>
                            <p className="text-sm text-gray-600">{milestone.description}</p>
                            <p className="text-xs text-gray-500">
                              Achieved on {new Date(milestone.achievedDate).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Badges */}
                {achievements.badges && achievements.badges.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Badges</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {achievements.badges.map((badge) => (
                        <Card key={badge.id} className={badge.earnedDate ? 'border-green-200' : 'opacity-60'}>
                          <CardContent className="p-6 text-center">
                            <div className="text-4xl mb-2">{badge.icon}</div>
                            <h4 className="font-medium text-gray-900 mb-1">{badge.name}</h4>
                            <p className="text-sm text-gray-600 mb-3">{badge.description}</p>
                            {badge.earnedDate ? (
                              <Badge variant="default">
                                Earned {new Date(badge.earnedDate).toLocaleDateString()}
                              </Badge>
                            ) : (
                              <div className="space-y-2">
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-blue-600 h-2 rounded-full"
                                    style={{ width: `${(badge.progress || 0) / badge.requirement * 100}%` }}
                                  />
                                </div>
                                <p className="text-xs text-gray-500">
                                  {badge.progress || 0} / {badge.requirement}
                                </p>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No achievements yet</h3>
                <p className="text-gray-600">Start working out to earn your first badges!</p>
              </div>
            )}
          </div>
        )}

        {/* History Tab */}
        {activeTab === 'history' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Workout History</h2>
              <Button variant="outline" onClick={() => handleExportData('csv')}>
                <Download className="h-4 w-4 mr-2" />
                Export History
              </Button>
            </div>

            {/* History content would go here */}
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Workout history</h3>
                  <p className="text-gray-600">Your detailed workout history will appear here</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
