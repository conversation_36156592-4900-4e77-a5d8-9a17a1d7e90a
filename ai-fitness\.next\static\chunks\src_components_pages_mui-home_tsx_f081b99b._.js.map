{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-home.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Grid,\n  Chip,\n  Fab,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  FitnessCenter,\n  DirectionsRun,\n  Assessment,\n  PlayArrow,\n  TrendingUp,\n  Timer,\n  EmojiEvents,\n  Add,\n} from '@mui/icons-material';\nimport Link from 'next/link';\n\nconst features = [\n  {\n    icon: <FitnessCenter />,\n    title: '智能训练计划',\n    description: 'AI 定制个性化训练方案',\n    color: '#FF6B35',\n    href: '/workouts',\n  },\n  {\n    icon: <DirectionsRun />,\n    title: '运动动作库',\n    description: '千种运动动作详细指导',\n    color: '#4CAF50',\n    href: '/exercises',\n  },\n  {\n    icon: <Assessment />,\n    title: '进度分析',\n    description: '数据驱动的健身追踪',\n    color: '#2196F3',\n    href: '/progress',\n  },\n];\n\nconst quickStats = [\n  { label: '今日训练', value: '45分钟', icon: <Timer />, color: '#FF6B35' },\n  { label: '本周目标', value: '80%', icon: <TrendingUp />, color: '#4CAF50' },\n  { label: '连续天数', value: '12天', icon: <EmojiEvents />, color: '#2196F3' },\n];\n\nexport function MuiHome() {\n  const theme = useTheme();\n\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,\n          py: { xs: 8, md: 12 },\n          position: 'relative',\n          overflow: 'hidden',\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Box textAlign=\"center\" sx={{ position: 'relative', zIndex: 1 }}>\n            <Typography\n              variant=\"h1\"\n              sx={{\n                fontSize: { xs: '2.5rem', md: '4rem' },\n                fontWeight: 700,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                mb: 2,\n              }}\n            >\n              AI-fitness-singles\n            </Typography>\n            <Typography\n              variant=\"h4\"\n              sx={{\n                fontSize: { xs: '1.25rem', md: '1.75rem' },\n                color: 'text.secondary',\n                mb: 4,\n                fontWeight: 400,\n              }}\n            >\n              智能健身平台，专为单身人士打造\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n              <Button\n                variant=\"contained\"\n                size=\"large\"\n                startIcon={<PlayArrow />}\n                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}\n              >\n                开始训练\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                startIcon={<DirectionsRun />}\n                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}\n                component={Link}\n                href=\"/exercises\"\n              >\n                浏览动作\n              </Button>\n            </Box>\n          </Box>\n        </Container>\n\n        {/* Decorative elements */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '20%',\n            right: '10%',\n            width: 100,\n            height: 100,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.2)}, ${alpha(theme.palette.secondary.main, 0.2)})`,\n            animation: 'float 6s ease-in-out infinite',\n          }}\n        />\n        <Box\n          sx={{\n            position: 'absolute',\n            bottom: '20%',\n            left: '5%',\n            width: 60,\n            height: 60,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,\n            animation: 'float 4s ease-in-out infinite reverse',\n          }}\n        />\n      </Box>\n\n      {/* Quick Stats */}\n      <Container maxWidth=\"lg\" sx={{ mt: -4, position: 'relative', zIndex: 2 }}>\n        <Grid container spacing={3}>\n          {quickStats.map((stat, index) => (\n            <Grid item xs={12} md={4} key={index}>\n              <Card\n                sx={{\n                  textAlign: 'center',\n                  py: 3,\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',\n                  backdropFilter: 'blur(10px)',\n                  border: '1px solid rgba(255,255,255,0.2)',\n                }}\n              >\n                <CardContent>\n                  <Box\n                    sx={{\n                      display: 'inline-flex',\n                      p: 2,\n                      borderRadius: '50%',\n                      bgcolor: alpha(stat.color, 0.1),\n                      color: stat.color,\n                      mb: 2,\n                    }}\n                  >\n                    {stat.icon}\n                  </Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>\n                    {stat.value}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {stat.label}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n\n      {/* Features Section */}\n      <Container maxWidth=\"lg\" sx={{ py: 8 }}>\n        <Typography\n          variant=\"h2\"\n          textAlign=\"center\"\n          sx={{ mb: 2, fontWeight: 600 }}\n        >\n          核心功能\n        </Typography>\n        <Typography\n          variant=\"h6\"\n          textAlign=\"center\"\n          color=\"text.secondary\"\n          sx={{ mb: 6 }}\n        >\n          一站式健身解决方案\n        </Typography>\n\n        <Grid container spacing={4}>\n          {features.map((feature, index) => (\n            <Grid item xs={12} md={4} key={index}>\n              <Card\n                component={Link}\n                href={feature.href}\n                sx={{\n                  height: '100%',\n                  textDecoration: 'none',\n                  cursor: 'pointer',\n                  '&:hover': {\n                    transform: 'translateY(-8px)',\n                  },\n                }}\n              >\n                <CardContent sx={{ textAlign: 'center', p: 4 }}>\n                  <Box\n                    sx={{\n                      display: 'inline-flex',\n                      p: 3,\n                      borderRadius: '50%',\n                      bgcolor: alpha(feature.color, 0.1),\n                      color: feature.color,\n                      mb: 3,\n                      fontSize: '2rem',\n                    }}\n                  >\n                    {feature.icon}\n                  </Box>\n                  <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 2 }}>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"text.secondary\">\n                    {feature.description}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n\n      {/* Quick Actions */}\n      <Box sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}>\n        <Fab\n          color=\"primary\"\n          aria-label=\"add workout\"\n          sx={{\n            width: 64,\n            height: 64,\n            '&:hover': {\n              transform: 'scale(1.1)',\n            },\n          }}\n        >\n          <Add sx={{ fontSize: '2rem' }} />\n        </Fab>\n      </Box>\n\n      {/* CSS for animations */}\n      <style jsx global>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-20px); }\n        }\n      `}</style>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AA1BA;;;;;;;;;;;;AA4BA,MAAM,WAAW;IACf;QACE,oBAAM,6LAAC,qKAAA,CAAA,UAAa;;;;;QACpB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,6LAAC,qKAAA,CAAA,UAAa;;;;;QACpB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;QACjB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAQ,OAAO;QAAQ,oBAAM,6LAAC,6JAAA,CAAA,UAAK;;;;;QAAK,OAAO;IAAU;IAClE;QAAE,OAAO;QAAQ,OAAO;QAAO,oBAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;QAAK,OAAO;IAAU;IACtE;QAAE,OAAO;QAAQ,OAAO;QAAO,oBAAM,6LAAC,mKAAA,CAAA,UAAW;;;;;QAAK,OAAO;IAAU;CACxE;AAEM,SAAS;;IACd,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IAErB,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,WAAW;YAAS,SAAS;QAAqB;;0BAE3D,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;oBACrI,IAAI;wBAAE,IAAI;wBAAG,IAAI;oBAAG;oBACpB,UAAU;oBACV,UAAU;gBACZ;;kCAEA,6LAAC,6MAAA,CAAA,YAAS;wBAAC,UAAS;kCAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;4BAAC,WAAU;4BAAS,IAAI;gCAAE,UAAU;gCAAY,QAAQ;4BAAE;;8CAC5D,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,UAAU;4CAAE,IAAI;4CAAU,IAAI;wCAAO;wCACrC,YAAY;wCACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACpG,gBAAgB;wCAChB,sBAAsB;wCACtB,qBAAqB;wCACrB,IAAI;oCACN;8CACD;;;;;;8CAGD,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,UAAU;4CAAE,IAAI;4CAAW,IAAI;wCAAU;wCACzC,OAAO;wCACP,IAAI;wCACJ,YAAY;oCACd;8CACD;;;;;;8CAGD,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,SAAS;wCAAQ,KAAK;wCAAG,gBAAgB;wCAAU,UAAU;oCAAO;;sDAC7E,6LAAC,oMAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,yBAAW,6LAAC,iKAAA,CAAA,UAAS;;;;;4CACrB,IAAI;gDAAE,IAAI;gDAAG,IAAI;gDAAK,UAAU;4CAAS;sDAC1C;;;;;;sDAGD,6LAAC,oMAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,yBAAW,6LAAC,qKAAA,CAAA,UAAa;;;;;4CACzB,IAAI;gDAAE,IAAI;gDAAG,IAAI;gDAAK,UAAU;4CAAS;4CACzC,WAAW,+JAAA,CAAA,UAAI;4CACf,MAAK;sDACN;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;kCAEF,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;;;;;;;0BAKJ,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI,CAAC;oBAAG,UAAU;oBAAY,QAAQ;gBAAE;0BACrE,cAAA,6LAAC,8LAAA,CAAA,OAAI;oBAAC,SAAS;oBAAC,SAAS;8BACtB,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,8LAAA,CAAA,OAAI;4BAAC,IAAI;4BAAC,IAAI;4BAAI,IAAI;sCACrB,cAAA,6LAAC,8LAAA,CAAA,OAAI;gCACH,IAAI;oCACF,WAAW;oCACX,IAAI;oCACJ,YAAY;oCACZ,gBAAgB;oCAChB,QAAQ;gCACV;0CAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;;sDACV,6LAAC,2LAAA,CAAA,MAAG;4CACF,IAAI;gDACF,SAAS;gDACT,GAAG;gDACH,cAAc;gDACd,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE;gDAC3B,OAAO,KAAK,KAAK;gDACjB,IAAI;4CACN;sDAEC,KAAK,IAAI;;;;;;sDAEZ,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAK,IAAI;gDAAE,YAAY;gDAAK,OAAO,KAAK,KAAK;gDAAE,IAAI;4CAAE;sDACtE,KAAK,KAAK;;;;;;sDAEb,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAQ,OAAM;sDAC/B,KAAK,KAAK;;;;;;;;;;;;;;;;;2BA3BY;;;;;;;;;;;;;;;0BAqCrC,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;;kCACnC,6LAAC,gNAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,WAAU;wBACV,IAAI;4BAAE,IAAI;4BAAG,YAAY;wBAAI;kCAC9B;;;;;;kCAGD,6LAAC,gNAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,WAAU;wBACV,OAAM;wBACN,IAAI;4BAAE,IAAI;wBAAE;kCACb;;;;;;kCAID,6LAAC,8LAAA,CAAA,OAAI;wBAAC,SAAS;wBAAC,SAAS;kCACtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,8LAAA,CAAA,OAAI;gCAAC,IAAI;gCAAC,IAAI;gCAAI,IAAI;0CACrB,cAAA,6LAAC,8LAAA,CAAA,OAAI;oCACH,WAAW,+JAAA,CAAA,UAAI;oCACf,MAAM,QAAQ,IAAI;oCAClB,IAAI;wCACF,QAAQ;wCACR,gBAAgB;wCAChB,QAAQ;wCACR,WAAW;4CACT,WAAW;wCACb;oCACF;8CAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;wCAAC,IAAI;4CAAE,WAAW;4CAAU,GAAG;wCAAE;;0DAC3C,6LAAC,2LAAA,CAAA,MAAG;gDACF,IAAI;oDACF,SAAS;oDACT,GAAG;oDACH,cAAc;oDACd,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK,EAAE;oDAC9B,OAAO,QAAQ,KAAK;oDACpB,IAAI;oDACJ,UAAU;gDACZ;0DAEC,QAAQ,IAAI;;;;;;0DAEf,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,YAAY;oDAAK,IAAI;gDAAE;0DACnD,QAAQ,KAAK;;;;;;0DAEhB,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAQ,OAAM;0DAC/B,QAAQ,WAAW;;;;;;;;;;;;;;;;;+BA/BG;;;;;;;;;;;;;;;;0BAyCrC,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,UAAU;oBAAS,QAAQ;oBAAI,OAAO;oBAAI,QAAQ;gBAAK;0BAChE,cAAA,6LAAC,2LAAA,CAAA,MAAG;oBACF,OAAM;oBACN,cAAW;oBACX,IAAI;wBACF,OAAO;wBACP,QAAQ;wBACR,WAAW;4BACT,WAAW;wBACb;oBACF;8BAEA,cAAA,6LAAC,2JAAA,CAAA,UAAG;wBAAC,IAAI;4BAAE,UAAU;wBAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AAatC;GAzNgB;;QACA,wMAAA,CAAA,WAAQ;;;KADR", "debugId": null}}]}