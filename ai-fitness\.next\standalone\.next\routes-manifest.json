{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/login", "destination": "/auth/signin", "statusCode": 308, "regex": "^(?!/_next)/login(?:/)?$"}, {"source": "/register", "destination": "/auth/signup", "statusCode": 308, "regex": "^(?!/_next)/register(?:/)?$"}, {"source": "/dashboard", "destination": "/", "statusCode": 307, "regex": "^(?!/_next)/dashboard(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubDomains; preload"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}], "regex": "^/api(?:/(.*))(?:/)?$"}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/auth/[...all]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPall": "nxtPall"}, "namedRegex": "^/api/auth/(?<nxtPall>.+?)(?:/)?$"}, {"page": "/exercises/[id]", "regex": "^/exercises/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/exercises/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/workouts/[id]", "regex": "^/workouts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/workouts/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/demo", "regex": "^/demo(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo(?:/)?$"}, {"page": "/exercises", "regex": "^/exercises(?:/)?$", "routeKeys": {}, "namedRegex": "^/exercises(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/progress", "regex": "^/progress(?:/)?$", "routeKeys": {}, "namedRegex": "^/progress(?:/)?$"}, {"page": "/workouts", "regex": "^/workouts(?:/)?$", "routeKeys": {}, "namedRegex": "^/workouts(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/api/auth/:path*", "destination": "/api/auth/:path*", "regex": "^/api/auth(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}