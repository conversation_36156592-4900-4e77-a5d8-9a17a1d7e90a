import { render, screen } from '@testing-library/react'
import Home from '@/app/page'

// Mock the navigation component
jest.mock('@/components/navigation', () => ({
  Navigation: () => <nav data-testid="navigation">Navigation</nav>
}))

describe('Home Page', () => {
  it('renders the main heading', () => {
    render(<Home />)
    
    const heading = screen.getByText(/Transform Your Fitness/i)
    expect(heading).toBeInTheDocument()
  })

  it('renders the AI Guidance text', () => {
    render(<Home />)
    
    const aiText = screen.getByText(/with AI Guidance/i)
    expect(aiText).toBeInTheDocument()
  })

  it('renders the navigation component', () => {
    render(<Home />)
    
    const navigation = screen.getByTestId('navigation')
    expect(navigation).toBeInTheDocument()
  })

  it('renders the Start Your Journey button', () => {
    render(<Home />)
    
    const button = screen.getByText(/Start Your Journey/i)
    expect(button).toBeInTheDocument()
  })

  it('renders the features section', () => {
    render(<Home />)
    
    const featuresHeading = screen.getByText(/Why Choose AI Fitness?/i)
    expect(featuresHeading).toBeInTheDocument()
  })

  it('renders feature cards', () => {
    render(<Home />)
    
    expect(screen.getByText(/Personalized Plans/i)).toBeInTheDocument()
    expect(screen.getByText(/Progress Tracking/i)).toBeInTheDocument()
    expect(screen.getByText(/Smart Coaching/i)).toBeInTheDocument()
  })

  it('renders the CTA section', () => {
    render(<Home />)
    
    const ctaHeading = screen.getByText(/Ready to Transform Your Fitness?/i)
    expect(ctaHeading).toBeInTheDocument()
    
    const ctaButton = screen.getByText(/Get Started Free/i)
    expect(ctaButton).toBeInTheDocument()
  })
})
