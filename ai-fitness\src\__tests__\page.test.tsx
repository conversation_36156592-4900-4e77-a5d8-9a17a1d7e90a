import { render, screen } from '@testing-library/react'
import Home from '@/app/page'

// Mock the navigation component
jest.mock('@/components/navigation', () => ({
  Navigation: () => <nav data-testid="navigation">Navigation</nav>
}))

describe('Home Page', () => {
  it('renders the main heading', () => {
    render(<Home />)

    const heading = screen.getByText(/Modern Fitness/i)
    expect(heading).toBeInTheDocument()
  })

  it('renders the coaching platform text', () => {
    render(<Home />)

    const platformText = screen.getByText(/Coaching Platform/i)
    expect(platformText).toBeInTheDocument()
  })

  it('renders the navigation component', () => {
    render(<Home />)

    const navigation = screen.getByTestId('navigation')
    expect(navigation).toBeInTheDocument()
  })

  it('renders the Start Training button', () => {
    render(<Home />)

    const buttons = screen.getAllByText(/Start Training/i)
    expect(buttons.length).toBeGreaterThan(0)
  })

  it('renders the features section', () => {
    render(<Home />)

    const featuresHeading = screen.getByText(/Everything You Need to Train/i)
    expect(featuresHeading).toBeInTheDocument()
  })

  it('renders core feature cards', () => {
    render(<Home />)

    // Use getAllByText for elements that appear multiple times
    expect(screen.getAllByText(/Workout Builder/i)[0]).toBeInTheDocument()
    expect(screen.getAllByText(/Exercise Database/i)[0]).toBeInTheDocument()
    expect(screen.getAllByText(/Progress Tracking/i)[0]).toBeInTheDocument()
    expect(screen.getByText(/Workout Sessions/i)).toBeInTheDocument()
  })

  it('renders the CTA section', () => {
    render(<Home />)

    const ctaHeading = screen.getByText(/Start Training Today/i)
    expect(ctaHeading).toBeInTheDocument()

    const ctaButton = screen.getByText(/Create Workout Plan/i)
    expect(ctaButton).toBeInTheDocument()
  })
})
