{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from \"lucide-react\"\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const toggleMenu = () => setIsOpen(!isOpen)\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">AI-fitness-singles</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/workouts\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Workouts\n            </Link>\n            <Link\n              href=\"/exercises\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Exercises\n            </Link>\n            <Link\n              href=\"/progress\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Progress\n            </Link>\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button variant=\"ghost\" size=\"icon\" onClick={toggleMenu}>\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/workouts\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Dumbbell className=\"h-4 w-4\" />\n                <span>Workouts</span>\n              </Link>\n              <Link\n                href=\"/exercises\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BookOpen className=\"h-4 w-4\" />\n                <span>Exercises</span>\n              </Link>\n              <Link\n                href=\"/progress\"\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <BarChart3 className=\"h-4 w-4\" />\n                <span>Progress</span>\n              </Link>\n              <div className=\"pt-4 border-t\">\n                <div className=\"flex flex-col space-y-2\">\n                  <Button variant=\"outline\" className=\"justify-start\">\n                    Sign In\n                  </Button>\n                  <Button className=\"justify-start bg-blue-600 hover:bg-blue-700\">\n                    Get Started\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,SAAS;0CAC1C,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDAGpD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlF", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/exercises/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Navigation } from \"@/components/navigation\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  Search,\n  Filter,\n  Clock,\n  Target,\n  Play,\n  Heart,\n  Zap,\n  Star,\n  BookOpen,\n  Users,\n  Dumbbell,\n  X,\n  Eye,\n  Plus\n} from \"lucide-react\"\n\nconst exercises = [\n  {\n    id: 1,\n    name: \"Push-ups\",\n    category: \"Chest\",\n    difficulty: \"Beginner\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"Classic bodyweight exercise targeting chest, shoulders, and triceps\",\n    image: \"💪\",\n    muscles: [\"Chest\", \"Shoulders\", \"Triceps\"],\n    rating: 4.8,\n    views: 12547,\n    isFavorite: false,\n    instructions: [\"Start in plank position\", \"Lower body to ground\", \"Push back up\", \"Repeat\"],\n    tips: \"Keep your core tight and maintain straight line from head to heels\",\n    calories: 8\n  },\n  {\n    id: 2,\n    name: \"Squats\",\n    category: \"Legs\",\n    difficulty: \"Beginner\",\n    duration: \"45 seconds\",\n    equipment: \"None\",\n    description: \"Fundamental lower body exercise for strength and mobility\",\n    image: \"🦵\",\n    muscles: [\"Quadriceps\", \"Glutes\", \"Hamstrings\"],\n    rating: 4.9,\n    views: 18923,\n    isFavorite: true,\n    instructions: [\"Stand with feet shoulder-width apart\", \"Lower hips back and down\", \"Keep chest up\", \"Return to standing\"],\n    tips: \"Keep your weight on your heels and knees tracking over toes\",\n    calories: 12\n  },\n  {\n    id: 3,\n    name: \"Burpees\",\n    category: \"Full Body\",\n    difficulty: \"Advanced\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"High-intensity full-body exercise combining strength and cardio\",\n    image: \"🔥\",\n    muscles: [\"Full Body\", \"Cardio\"],\n    rating: 4.6,\n    views: 8934,\n    isFavorite: false,\n    instructions: [\"Start standing\", \"Drop to squat\", \"Jump back to plank\", \"Do push-up\", \"Jump feet forward\", \"Jump up\"],\n    tips: \"Maintain good form even when tired - quality over speed\",\n    calories: 15\n  },\n  {\n    id: 4,\n    name: \"Plank\",\n    category: \"Core\",\n    difficulty: \"Intermediate\",\n    duration: \"60 seconds\",\n    equipment: \"None\",\n    description: \"Isometric core exercise for stability and strength\",\n    image: \"🏋️\",\n    muscles: [\"Core\", \"Shoulders\", \"Back\"],\n    rating: 4.7,\n    views: 15632,\n    isFavorite: true,\n    instructions: [\"Start in push-up position\", \"Hold body straight\", \"Engage core\", \"Breathe normally\"],\n    tips: \"Don't let hips sag or pike up - maintain neutral spine\",\n    calories: 5\n  },\n  {\n    id: 5,\n    name: \"Mountain Climbers\",\n    category: \"Cardio\",\n    difficulty: \"Intermediate\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"Dynamic cardio exercise that targets core and improves endurance\",\n    image: \"⛰️\",\n    muscles: [\"Core\", \"Shoulders\", \"Legs\"],\n    rating: 4.5,\n    views: 11245,\n    isFavorite: false,\n    instructions: [\"Start in plank position\", \"Bring one knee to chest\", \"Switch legs quickly\", \"Keep hips level\"],\n    tips: \"Start slow and build up speed while maintaining form\",\n    calories: 10\n  },\n  {\n    id: 6,\n    name: \"Lunges\",\n    category: \"Legs\",\n    difficulty: \"Beginner\",\n    duration: \"45 seconds\",\n    equipment: \"None\",\n    description: \"Unilateral leg exercise for strength and balance\",\n    image: \"🚶\",\n    muscles: [\"Quadriceps\", \"Glutes\", \"Calves\"],\n    rating: 4.8,\n    views: 14567,\n    isFavorite: false,\n    instructions: [\"Step forward with one leg\", \"Lower hips until both knees at 90°\", \"Push back to start\", \"Alternate legs\"],\n    tips: \"Keep front knee over ankle and back knee pointing down\",\n    calories: 9\n  },\n  {\n    id: 7,\n    name: \"Jumping Jacks\",\n    category: \"Cardio\",\n    difficulty: \"Beginner\",\n    duration: \"30 seconds\",\n    equipment: \"None\",\n    description: \"Classic cardio exercise to get your heart rate up\",\n    image: \"🤸\",\n    muscles: [\"Full Body\", \"Cardio\"],\n    rating: 4.4,\n    views: 9876,\n    isFavorite: false,\n    instructions: [\"Start with feet together\", \"Jump feet apart while raising arms\", \"Jump back to start\", \"Repeat rhythmically\"],\n    tips: \"Land softly on balls of feet to reduce impact\",\n    calories: 8\n  },\n  {\n    id: 8,\n    name: \"Deadlifts\",\n    category: \"Back\",\n    difficulty: \"Advanced\",\n    duration: \"45 seconds\",\n    equipment: \"Dumbbells\",\n    description: \"Compound exercise targeting posterior chain muscles\",\n    image: \"🏋️‍♂️\",\n    muscles: [\"Back\", \"Glutes\", \"Hamstrings\"],\n    rating: 4.9,\n    views: 13456,\n    isFavorite: true,\n    instructions: [\"Stand with weights in front\", \"Hinge at hips\", \"Lower weights to mid-shin\", \"Drive hips forward to stand\"],\n    tips: \"Keep back straight and weights close to body throughout movement\",\n    calories: 14\n  },\n  {\n    id: 9,\n    name: \"Bicycle Crunches\",\n    category: \"Core\",\n    difficulty: \"Intermediate\",\n    duration: \"45 seconds\",\n    equipment: \"None\",\n    description: \"Dynamic core exercise targeting obliques and abs\",\n    image: \"🚴\",\n    muscles: [\"Core\", \"Obliques\"],\n    rating: 4.6,\n    views: 10234,\n    isFavorite: false,\n    instructions: [\"Lie on back, hands behind head\", \"Bring opposite elbow to knee\", \"Alternate sides\", \"Keep core engaged\"],\n    tips: \"Focus on rotation from core, not pulling on neck\",\n    calories: 7\n  },\n  {\n    id: 10,\n    name: \"Pull-ups\",\n    category: \"Back\",\n    difficulty: \"Advanced\",\n    duration: \"30 seconds\",\n    equipment: \"Pull-up bar\",\n    description: \"Upper body pulling exercise for back and arm strength\",\n    image: \"🏃‍♂️\",\n    muscles: [\"Back\", \"Biceps\", \"Shoulders\"],\n    rating: 4.8,\n    views: 16789,\n    isFavorite: true,\n    instructions: [\"Hang from bar with overhand grip\", \"Pull body up until chin over bar\", \"Lower with control\", \"Repeat\"],\n    tips: \"Engage lats and avoid swinging - use assistance if needed\",\n    calories: 12\n  }\n]\n\nconst categories = [\n  { name: \"All\", count: exercises.length },\n  { name: \"Chest\", count: exercises.filter(e => e.category === \"Chest\").length },\n  { name: \"Legs\", count: exercises.filter(e => e.category === \"Legs\").length },\n  { name: \"Core\", count: exercises.filter(e => e.category === \"Core\").length },\n  { name: \"Cardio\", count: exercises.filter(e => e.category === \"Cardio\").length },\n  { name: \"Back\", count: exercises.filter(e => e.category === \"Back\").length },\n  { name: \"Full Body\", count: exercises.filter(e => e.category === \"Full Body\").length }\n]\n\nconst getDifficultyColor = (difficulty: string) => {\n  switch (difficulty) {\n    case \"Beginner\": return \"bg-green-100 text-green-800\"\n    case \"Intermediate\": return \"bg-yellow-100 text-yellow-800\"\n    case \"Advanced\": return \"bg-red-100 text-red-800\"\n    default: return \"bg-gray-100 text-gray-800\"\n  }\n}\n\nconst getDifficultyIcon = (difficulty: string) => {\n  switch (difficulty) {\n    case \"Beginner\": return <Heart className=\"h-4 w-4\" />\n    case \"Intermediate\": return <Target className=\"h-4 w-4\" />\n    case \"Advanced\": return <Zap className=\"h-4 w-4\" />\n    default: return <Target className=\"h-4 w-4\" />\n  }\n}\n\nexport default function Exercises() {\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [selectedCategory, setSelectedCategory] = useState(\"All\")\n  const [selectedDifficulty, setSelectedDifficulty] = useState(\"All\")\n  const [selectedEquipment, setSelectedEquipment] = useState(\"All\")\n  const [sortBy, setSortBy] = useState(\"name\")\n  const [showExerciseModal, setShowExerciseModal] = useState(false)\n  const [selectedExercise, setSelectedExercise] = useState(null)\n  const [exerciseList, setExerciseList] = useState(exercises)\n\n  // Get unique equipment types\n  const equipmentTypes = [\"All\", ...Array.from(new Set(exercises.map(e => e.equipment)))]\n  const difficulties = [\"All\", \"Beginner\", \"Intermediate\", \"Advanced\"]\n\n  // Filter and sort exercises\n  const filteredExercises = exerciseList\n    .filter(exercise => {\n      const matchesSearch = exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           exercise.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           exercise.muscles.some(muscle => muscle.toLowerCase().includes(searchTerm.toLowerCase()))\n      const matchesCategory = selectedCategory === \"All\" || exercise.category === selectedCategory\n      const matchesDifficulty = selectedDifficulty === \"All\" || exercise.difficulty === selectedDifficulty\n      const matchesEquipment = selectedEquipment === \"All\" || exercise.equipment === selectedEquipment\n      return matchesSearch && matchesCategory && matchesDifficulty && matchesEquipment\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case \"name\": return a.name.localeCompare(b.name)\n        case \"difficulty\":\n          const difficultyOrder = { \"Beginner\": 1, \"Intermediate\": 2, \"Advanced\": 3 }\n          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]\n        case \"rating\": return b.rating - a.rating\n        case \"popular\": return b.views - a.views\n        case \"calories\": return b.calories - a.calories\n        default: return 0\n      }\n    })\n\n  const toggleFavorite = (id: number) => {\n    setExerciseList(exerciseList.map(exercise =>\n      exercise.id === id ? { ...exercise, isFavorite: !exercise.isFavorite } : exercise\n    ))\n  }\n\n  const openExerciseModal = (exercise) => {\n    setSelectedExercise(exercise)\n    setShowExerciseModal(true)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n\n      {/* Header Section */}\n      <section className=\"bg-gradient-to-br from-green-50 to-emerald-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\">\n              Exercise Database\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover thousands of exercises with detailed instructions and video guides\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"container mx-auto px-4 py-8\">\n\n        {/* Search and Filters */}\n        <div className=\"mb-8 space-y-4\">\n          {/* Search Bar and Sort */}\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search exercises...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n              />\n            </div>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            >\n              <option value=\"name\">Sort by Name</option>\n              <option value=\"difficulty\">Sort by Difficulty</option>\n              <option value=\"rating\">Sort by Rating</option>\n              <option value=\"popular\">Sort by Popularity</option>\n              <option value=\"calories\">Sort by Calories</option>\n            </select>\n          </div>\n\n          {/* Filter Buttons */}\n          <div className=\"space-y-3\">\n            <div className=\"flex flex-wrap gap-2\">\n              <span className=\"text-sm font-medium text-gray-700 py-2\">Difficulty:</span>\n              {difficulties.map(difficulty => (\n                <Button\n                  key={difficulty}\n                  variant={selectedDifficulty === difficulty ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setSelectedDifficulty(difficulty)}\n                  className=\"text-xs\"\n                >\n                  {difficulty}\n                </Button>\n              ))}\n            </div>\n\n            <div className=\"flex flex-wrap gap-2\">\n              <span className=\"text-sm font-medium text-gray-700 py-2\">Equipment:</span>\n              {equipmentTypes.map(equipment => (\n                <Button\n                  key={equipment}\n                  variant={selectedEquipment === equipment ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setSelectedEquipment(equipment)}\n                  className=\"text-xs\"\n                >\n                  {equipment}\n                </Button>\n              ))}\n            </div>\n          </div>\n\n          {/* Results Count */}\n          <div className=\"text-sm text-gray-600\">\n            Showing {filteredExercises.length} of {exerciseList.length} exercises\n          </div>\n        </div>\n\n        {/* Categories */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Categories</h2>\n          <div className=\"flex flex-wrap gap-2\">\n            {categories.map((category) => (\n              <Button\n                key={category.name}\n                variant={selectedCategory === category.name ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.name)}\n                className=\"text-sm\"\n              >\n                {category.name} ({category.count})\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Exercise Grid */}\n        {filteredExercises.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No exercises found</h3>\n            <p className=\"text-gray-600 mb-4\">Try adjusting your search or filters</p>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setSearchTerm(\"\")\n                setSelectedCategory(\"All\")\n                setSelectedDifficulty(\"All\")\n                setSelectedEquipment(\"All\")\n              }}\n            >\n              Clear all filters\n            </Button>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredExercises.map((exercise) => (\n              <Card key={exercise.id} className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader>\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"text-4xl mb-2\">{exercise.image}</div>\n                    <div className=\"flex items-center gap-2\">\n                      <button\n                        onClick={() => toggleFavorite(exercise.id)}\n                        className={`p-1 rounded-full transition-colors ${\n                          exercise.isFavorite\n                            ? 'text-red-500 hover:text-red-600'\n                            : 'text-gray-400 hover:text-red-500'\n                        }`}\n                      >\n                        <Heart className={`h-4 w-4 ${exercise.isFavorite ? 'fill-current' : ''}`} />\n                      </button>\n                      <div className=\"flex items-center gap-1\">\n                        {getDifficultyIcon(exercise.difficulty)}\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                          {exercise.difficulty}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <CardTitle className=\"text-lg\">{exercise.name}</CardTitle>\n                  <CardDescription className=\"text-sm\">\n                    {exercise.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    {/* Rating and Views */}\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                        <span className=\"font-medium\">{exercise.rating}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1 text-gray-500\">\n                        <Eye className=\"h-4 w-4\" />\n                        <span>{exercise.views.toLocaleString()}</span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                      <div className=\"flex items-center gap-1\">\n                        <Clock className=\"h-4 w-4\" />\n                        <span>{exercise.duration}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Zap className=\"h-4 w-4\" />\n                        <span>{exercise.calories} cal</span>\n                      </div>\n                    </div>\n\n                    <Badge variant=\"secondary\" className=\"w-fit\">\n                      {exercise.category}\n                    </Badge>\n\n                    <div>\n                      <p className=\"text-xs text-gray-500 mb-1\">Target Muscles:</p>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {exercise.muscles.map((muscle, index) => (\n                          <span\n                            key={index}\n                            className=\"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs\"\n                          >\n                            {muscle}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"text-xs text-gray-500\">\n                      Equipment: {exercise.equipment}\n                    </div>\n\n                    <Button\n                      className=\"w-full\"\n                      size=\"sm\"\n                      onClick={() => openExerciseModal(exercise)}\n                    >\n                      <Play className=\"h-4 w-4 mr-2\" />\n                      View Exercise\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        {/* Quick Stats */}\n        <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card className=\"text-center\">\n            <CardContent className=\"p-6\">\n              <div className=\"text-3xl mb-2\">💪</div>\n              <h3 className=\"font-semibold text-gray-900\">500+ Exercises</h3>\n              <p className=\"text-sm text-gray-600\">Comprehensive exercise library</p>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"text-center\">\n            <CardContent className=\"p-6\">\n              <div className=\"text-3xl mb-2\">🎯</div>\n              <h3 className=\"font-semibold text-gray-900\">All Skill Levels</h3>\n              <p className=\"text-sm text-gray-600\">From beginner to advanced</p>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"text-center\">\n            <CardContent className=\"p-6\">\n              <div className=\"text-3xl mb-2\">📱</div>\n              <h3 className=\"font-semibold text-gray-900\">Video Guides</h3>\n              <p className=\"text-sm text-gray-600\">Step-by-step instructions</p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Exercise Detail Modal */}\n        {showExerciseModal && selectedExercise && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                {/* Modal Header */}\n                <div className=\"flex items-start justify-between mb-6\">\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"text-5xl\">{selectedExercise.image}</div>\n                    <div>\n                      <h2 className=\"text-2xl font-bold text-gray-900\">{selectedExercise.name}</h2>\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        <div className=\"flex items-center gap-1\">\n                          <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                          <span className=\"font-medium\">{selectedExercise.rating}</span>\n                        </div>\n                        <span className=\"text-gray-300\">•</span>\n                        <div className=\"flex items-center gap-1 text-gray-500\">\n                          <Eye className=\"h-4 w-4\" />\n                          <span>{selectedExercise.views.toLocaleString()} views</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setShowExerciseModal(false)}\n                    className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n                  >\n                    <X className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                {/* Exercise Info */}\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n                  <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n                    <Clock className=\"h-5 w-5 mx-auto mb-1 text-gray-600\" />\n                    <div className=\"text-sm font-medium\">{selectedExercise.duration}</div>\n                    <div className=\"text-xs text-gray-500\">Duration</div>\n                  </div>\n                  <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n                    {getDifficultyIcon(selectedExercise.difficulty)}\n                    <div className=\"text-sm font-medium mt-1\">{selectedExercise.difficulty}</div>\n                    <div className=\"text-xs text-gray-500\">Difficulty</div>\n                  </div>\n                  <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n                    <Zap className=\"h-5 w-5 mx-auto mb-1 text-gray-600\" />\n                    <div className=\"text-sm font-medium\">{selectedExercise.calories} cal</div>\n                    <div className=\"text-xs text-gray-500\">Calories</div>\n                  </div>\n                  <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n                    <Dumbbell className=\"h-5 w-5 mx-auto mb-1 text-gray-600\" />\n                    <div className=\"text-sm font-medium\">{selectedExercise.equipment}</div>\n                    <div className=\"text-xs text-gray-500\">Equipment</div>\n                  </div>\n                </div>\n\n                {/* Description */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Description</h3>\n                  <p className=\"text-gray-600\">{selectedExercise.description}</p>\n                </div>\n\n                {/* Instructions */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold mb-3\">Instructions</h3>\n                  <ol className=\"space-y-2\">\n                    {selectedExercise.instructions.map((instruction, index) => (\n                      <li key={index} className=\"flex items-start gap-3\">\n                        <span className=\"flex-shrink-0 w-6 h-6 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-sm font-medium\">\n                          {index + 1}\n                        </span>\n                        <span className=\"text-gray-700\">{instruction}</span>\n                      </li>\n                    ))}\n                  </ol>\n                </div>\n\n                {/* Tips */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Tips</h3>\n                  <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4\">\n                    <p className=\"text-blue-800\">{selectedExercise.tips}</p>\n                  </div>\n                </div>\n\n                {/* Target Muscles */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold mb-3\">Target Muscles</h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {selectedExercise.muscles.map((muscle, index) => (\n                      <Badge key={index} variant=\"outline\">\n                        {muscle}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex gap-3\">\n                  <Button className=\"flex-1\">\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    Start Exercise\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => toggleFavorite(selectedExercise.id)}\n                    className={selectedExercise.isFavorite ? 'text-red-500 border-red-500' : ''}\n                  >\n                    <Heart className={`h-4 w-4 ${selectedExercise.isFavorite ? 'fill-current' : ''}`} />\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAwBA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAS;YAAa;SAAU;QAC1C,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAA2B;YAAwB;YAAgB;SAAS;QAC3F,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAc;YAAU;SAAa;QAC/C,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAAwC;YAA4B;YAAiB;SAAqB;QACzH,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAa;SAAS;QAChC,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAAkB;YAAiB;YAAsB;YAAc;YAAqB;SAAU;QACrH,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAa;SAAO;QACtC,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAA6B;YAAsB;YAAe;SAAmB;QACpG,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAa;SAAO;QACtC,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAA2B;YAA2B;YAAuB;SAAkB;QAC9G,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAc;YAAU;SAAS;QAC3C,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAA6B;YAAsC;YAAsB;SAAiB;QACzH,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAa;SAAS;QAChC,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAA4B;YAAsC;YAAsB;SAAsB;QAC7H,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAU;SAAa;QACzC,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAA+B;YAAiB;YAA6B;SAA8B;QAC1H,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;SAAW;QAC7B,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAAkC;YAAgC;YAAmB;SAAoB;QACxH,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,SAAS;YAAC;YAAQ;YAAU;SAAY;QACxC,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,cAAc;YAAC;YAAoC;YAAoC;YAAsB;SAAS;QACtH,MAAM;QACN,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,OAAO,UAAU,MAAM;IAAC;IACvC;QAAE,MAAM;QAAS,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,MAAM;IAAC;IAC7E;QAAE,MAAM;QAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IAC3E;QAAE,MAAM;QAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IAC3E;QAAE,MAAM;QAAU,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;IAAC;IAC/E;QAAE,MAAM;QAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;IAAC;IAC3E;QAAE,MAAM;QAAa,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,MAAM;IAAC;CACtF;AAED,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YAAY,OAAO;QACxB,KAAK;YAAgB,OAAO;QAC5B,KAAK;YAAY,OAAO;QACxB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YAAY,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzC,KAAK;YAAgB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC9C,KAAK;YAAY,qBAAO,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvC;YAAS,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;IACpC;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,6BAA6B;IAC7B,MAAM,iBAAiB;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS;KAAI;IACvF,MAAM,eAAe;QAAC;QAAO;QAAY;QAAgB;KAAW;IAEpE,4BAA4B;IAC5B,MAAM,oBAAoB,aACvB,MAAM,CAAC,CAAA;QACN,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,SAAS,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACzG,MAAM,kBAAkB,qBAAqB,SAAS,SAAS,QAAQ,KAAK;QAC5E,MAAM,oBAAoB,uBAAuB,SAAS,SAAS,UAAU,KAAK;QAClF,MAAM,mBAAmB,sBAAsB,SAAS,SAAS,SAAS,KAAK;QAC/E,OAAO,iBAAiB,mBAAmB,qBAAqB;IAClE,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBAAQ,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YAC/C,KAAK;gBACH,MAAM,kBAAkB;oBAAE,YAAY;oBAAG,gBAAgB;oBAAG,YAAY;gBAAE;gBAC1E,OAAO,eAAe,CAAC,EAAE,UAAU,CAAC,GAAG,eAAe,CAAC,EAAE,UAAU,CAAC;YACtE,KAAK;gBAAU,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;YACzC,KAAK;gBAAW,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YACxC,KAAK;gBAAY,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAC/C;gBAAS,OAAO;QAClB;IACF;IAEF,MAAM,iBAAiB,CAAC;QACtB,gBAAgB,aAAa,GAAG,CAAC,CAAA,WAC/B,SAAS,EAAE,KAAK,KAAK;gBAAE,GAAG,QAAQ;gBAAE,YAAY,CAAC,SAAS,UAAU;YAAC,IAAI;IAE7E;IAEA,MAAM,oBAAoB,CAAC;QACzB,oBAAoB;QACpB,qBAAqB;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO7D,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;4CACxD,aAAa,GAAG,CAAC,CAAA,2BAChB,8OAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,uBAAuB,aAAa,YAAY;oDACzD,MAAK;oDACL,SAAS,IAAM,sBAAsB;oDACrC,WAAU;8DAET;mDANI;;;;;;;;;;;kDAWX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;4CACxD,eAAe,GAAG,CAAC,CAAA,0BAClB,8OAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,sBAAsB,YAAY,YAAY;oDACvD,MAAK;oDACL,SAAS,IAAM,qBAAqB;oDACpC,WAAU;8DAET;mDANI;;;;;;;;;;;;;;;;;0CAab,8OAAC;gCAAI,WAAU;;oCAAwB;oCAC5B,kBAAkB,MAAM;oCAAC;oCAAK,aAAa,MAAM;oCAAC;;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,qBAAqB,SAAS,IAAI,GAAG,YAAY;wCAC1D,MAAK;wCACL,SAAS,IAAM,oBAAoB,SAAS,IAAI;wCAChD,WAAU;;4CAET,SAAS,IAAI;4CAAC;4CAAG,SAAS,KAAK;4CAAC;;uCAN5B,SAAS,IAAI;;;;;;;;;;;;;;;;oBAazB,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;oCACP,cAAc;oCACd,oBAAoB;oCACpB,sBAAsB;oCACtB,qBAAqB;gCACvB;0CACD;;;;;;;;;;;6CAKH,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,gIAAA,CAAA,OAAI;gCAAmB,WAAU;;kDAChC,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,SAAS,KAAK;;;;;;kEAC9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,eAAe,SAAS,EAAE;gEACzC,WAAW,CAAC,mCAAmC,EAC7C,SAAS,UAAU,GACf,oCACA,oCACJ;0EAEF,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,UAAU,GAAG,iBAAiB,IAAI;;;;;;;;;;;0EAE1E,8OAAC;gEAAI,WAAU;;oEACZ,kBAAkB,SAAS,UAAU;kFACtC,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,SAAS,UAAU,GAAG;kFACrG,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAK5B,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,SAAS,IAAI;;;;;;0DAC7C,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,SAAS,WAAW;;;;;;;;;;;;kDAGzB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAe,SAAS,MAAM;;;;;;;;;;;;sEAEhD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;8EAAM,SAAS,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;;8DAIxC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM,SAAS,QAAQ;;;;;;;;;;;;sEAE1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;;wEAAM,SAAS,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;;8DAI7B,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,SAAS,QAAQ;;;;;;8DAGpB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;;;;;;;;;;;;8DASb,8OAAC;oDAAI,WAAU;;wDAAwB;wDACzB,SAAS,SAAS;;;;;;;8DAGhC,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,MAAK;oDACL,SAAS,IAAM,kBAAkB;;sEAEjC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;+BAhF9B,SAAS,EAAE;;;;;;;;;;kCA2F5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;oBAM1C,qBAAqB,kCACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAY,iBAAiB,KAAK;;;;;;kEACjD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAoC,iBAAiB,IAAI;;;;;;0EACvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;gFAAK,WAAU;0FAAe,iBAAiB,MAAM;;;;;;;;;;;;kFAExD,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;0FACf,8OAAC;;oFAAM,iBAAiB,KAAK,CAAC,cAAc;oFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKvD,8OAAC;gDACC,SAAS,IAAM,qBAAqB;gDACpC,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAI,WAAU;kEAAuB,iBAAiB,QAAQ;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;oDACZ,kBAAkB,iBAAiB,UAAU;kEAC9C,8OAAC;wDAAI,WAAU;kEAA4B,iBAAiB,UAAU;;;;;;kEACtE,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;4DAAuB,iBAAiB,QAAQ;4DAAC;;;;;;;kEAChE,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;kEAAuB,iBAAiB,SAAS;;;;;;kEAChE,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAK3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAiB,iBAAiB,WAAW;;;;;;;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;0DACX,iBAAiB,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAC/C,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEAAK,WAAU;0EACb,QAAQ;;;;;;0EAEX,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAJ1B;;;;;;;;;;;;;;;;kDAWf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAiB,iBAAiB,IAAI;;;;;;;;;;;;;;;;;kDAKvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACrC,8OAAC,iIAAA,CAAA,QAAK;wDAAa,SAAQ;kEACxB;uDADS;;;;;;;;;;;;;;;;kDAQlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe,iBAAiB,EAAE;gDACjD,WAAW,iBAAiB,UAAU,GAAG,gCAAgC;0DAEzE,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAW,CAAC,QAAQ,EAAE,iBAAiB,UAAU,GAAG,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpG", "debugId": null}}]}