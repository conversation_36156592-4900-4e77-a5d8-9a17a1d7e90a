"use client"

import { useEffect } from 'react';
import { QueryProvider } from '@/lib/providers/query-provider';
import { AuthProvider } from '@/lib/providers/auth-provider';
import { SyncManager } from '@/lib/sync/sync-manager';
import { offlineUtils } from '@/lib/query/config';

/**
 * Main App Providers Component
 * Combines all providers and initializes app-wide functionality
 */
export function AppProviders({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize sync manager
    const syncManager = SyncManager.getInstance();
    
    // Setup network listeners
    const cleanupNetworkListeners = offlineUtils.setupNetworkListeners();
    
    // Cleanup on unmount
    return () => {
      if (cleanupNetworkListeners) {
        cleanupNetworkListeners();
      }
    };
  }, []);

  return (
    <QueryProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryProvider>
  );
}
