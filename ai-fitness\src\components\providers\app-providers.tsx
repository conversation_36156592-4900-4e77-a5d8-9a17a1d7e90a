"use client"

import { useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { QueryProvider } from '@/lib/providers/query-provider';
import { AuthProvider } from '@/lib/providers/auth-provider';
import { SyncManager } from '@/lib/sync/sync-manager';
import { offlineUtils } from '@/lib/query/config';
import { fitnessTheme } from '@/lib/theme/mui-theme';
import { I18nProvider } from '@/lib/i18n/context';

/**
 * Main App Providers Component
 * Combines all providers and initializes app-wide functionality
 */
export function AppProviders({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize sync manager
    const syncManager = SyncManager.getInstance();
    
    // Setup network listeners
    const cleanupNetworkListeners = offlineUtils.setupNetworkListeners();
    
    // Cleanup on unmount
    return () => {
      if (cleanupNetworkListeners) {
        cleanupNetworkListeners();
      }
    };
  }, []);

  return (
    <I18nProvider>
      <ThemeProvider theme={fitnessTheme}>
        <CssBaseline />
        <QueryProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </QueryProvider>
      </ThemeProvider>
    </I18nProvider>
  );
}
