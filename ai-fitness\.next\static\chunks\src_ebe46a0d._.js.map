{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-progress.ts"], "sourcesContent": ["/**\n * React Query hooks for progress tracking data\n */\n\nimport { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';\nimport { ProgressService } from '../api/services/progress';\nimport type { \n  CreateProgressRecordData, \n  UpdateProgressRecordData \n} from '../api/types';\n\n// Query keys for consistent caching\nexport const progressKeys = {\n  all: ['progress'] as const,\n  records: () => [...progressKeys.all, 'records'] as const,\n  record: (id: string) => [...progressKeys.records(), id] as const,\n  recordsList: (params: any) => [...progressKeys.records(), 'list', params] as const,\n  stats: (period: string) => [...progressKeys.all, 'stats', period] as const,\n  workoutStats: (period: string) => [...progressKeys.all, 'workout-stats', period] as const,\n  exerciseProgress: (exerciseId: string, period: string) => [...progressKeys.all, 'exercise', exerciseId, period] as const,\n  bodyMeasurements: (period: string) => [...progressKeys.all, 'body-measurements', period] as const,\n  goals: () => [...progressKeys.all, 'goals'] as const,\n  achievements: () => [...progressKeys.all, 'achievements'] as const,\n  calendar: (year: number, month: number) => [...progressKeys.all, 'calendar', year, month] as const,\n  personalRecords: () => [...progressKeys.all, 'personal-records'] as const,\n  strengthProgression: (exerciseIds?: string[]) => [...progressKeys.all, 'strength-progression', exerciseIds] as const,\n  workoutIntensity: (period: string) => [...progressKeys.all, 'workout-intensity', period] as const,\n};\n\n/**\n * Hook to get progress records\n */\nexport function useProgressRecords(params: Parameters<typeof ProgressService.getProgressRecords>[0] = {}) {\n  return useQuery({\n    queryKey: progressKeys.recordsList(params),\n    queryFn: () => ProgressService.getProgressRecords(params),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Hook to get a specific progress record\n */\nexport function useProgressRecord(id: string, enabled = true) {\n  return useQuery({\n    queryKey: progressKeys.record(id),\n    queryFn: () => ProgressService.getProgressRecord(id),\n    enabled: enabled && !!id,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to create a progress record\n */\nexport function useCreateProgressRecord() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateProgressRecordData) => ProgressService.createProgressRecord(data),\n    onSuccess: () => {\n      // Invalidate progress records and stats\n      queryClient.invalidateQueries({ queryKey: progressKeys.records() });\n      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });\n      queryClient.invalidateQueries({ queryKey: progressKeys.workoutStats('month') });\n    },\n  });\n}\n\n/**\n * Hook to update a progress record\n */\nexport function useUpdateProgressRecord() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateProgressRecordData }) =>\n      ProgressService.updateProgressRecord(id, data),\n    onSuccess: (updatedRecord) => {\n      // Update the specific record cache\n      queryClient.setQueryData(progressKeys.record(updatedRecord.id), updatedRecord);\n      \n      // Invalidate records list and stats\n      queryClient.invalidateQueries({ queryKey: progressKeys.records() });\n      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });\n    },\n  });\n}\n\n/**\n * Hook to delete a progress record\n */\nexport function useDeleteProgressRecord() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (id: string) => ProgressService.deleteProgressRecord(id),\n    onSuccess: (_, deletedId) => {\n      // Remove from cache\n      queryClient.removeQueries({ queryKey: progressKeys.record(deletedId) });\n      \n      // Invalidate records list and stats\n      queryClient.invalidateQueries({ queryKey: progressKeys.records() });\n      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });\n    },\n  });\n}\n\n/**\n * Hook to get progress statistics\n */\nexport function useProgressStats(period: 'week' | 'month' | 'year' | 'all' = 'month') {\n  return useQuery({\n    queryKey: progressKeys.stats(period),\n    queryFn: () => ProgressService.getProgressStats(period),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to get workout statistics\n */\nexport function useWorkoutStats(period: 'week' | 'month' | 'year' = 'month') {\n  return useQuery({\n    queryKey: progressKeys.workoutStats(period),\n    queryFn: () => ProgressService.getWorkoutStats(period),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to get exercise progress data\n */\nexport function useExerciseProgress(exerciseId: string, period: 'week' | 'month' | 'year' = 'month', enabled = true) {\n  return useQuery({\n    queryKey: progressKeys.exerciseProgress(exerciseId, period),\n    queryFn: () => ProgressService.getExerciseProgress(exerciseId, period),\n    enabled: enabled && !!exerciseId,\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get body measurements\n */\nexport function useBodyMeasurements(period: 'week' | 'month' | 'year' = 'month') {\n  return useQuery({\n    queryKey: progressKeys.bodyMeasurements(period),\n    queryFn: () => ProgressService.getBodyMeasurements(period),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to add body measurement\n */\nexport function useAddBodyMeasurement() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: Parameters<typeof ProgressService.addBodyMeasurement>[0]) =>\n      ProgressService.addBodyMeasurement(data),\n    onSuccess: () => {\n      // Invalidate body measurements\n      queryClient.invalidateQueries({ queryKey: progressKeys.bodyMeasurements('month') });\n      queryClient.invalidateQueries({ queryKey: progressKeys.stats('month') });\n    },\n  });\n}\n\n/**\n * Hook to get fitness goals\n */\nexport function useFitnessGoals() {\n  return useQuery({\n    queryKey: progressKeys.goals(),\n    queryFn: () => ProgressService.getFitnessGoals(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook to create fitness goal\n */\nexport function useCreateFitnessGoal() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: Parameters<typeof ProgressService.createFitnessGoal>[0]) =>\n      ProgressService.createFitnessGoal(data),\n    onSuccess: () => {\n      // Invalidate goals\n      queryClient.invalidateQueries({ queryKey: progressKeys.goals() });\n    },\n  });\n}\n\n/**\n * Hook to update goal progress\n */\nexport function useUpdateGoalProgress() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ goalId, currentValue }: { goalId: string; currentValue: number }) =>\n      ProgressService.updateGoalProgress(goalId, currentValue),\n    onSuccess: () => {\n      // Invalidate goals\n      queryClient.invalidateQueries({ queryKey: progressKeys.goals() });\n    },\n  });\n}\n\n/**\n * Hook to get achievements\n */\nexport function useAchievements() {\n  return useQuery({\n    queryKey: progressKeys.achievements(),\n    queryFn: () => ProgressService.getAchievements(),\n    staleTime: 15 * 60 * 1000, // 15 minutes\n  });\n}\n\n/**\n * Hook to get workout calendar\n */\nexport function useWorkoutCalendar(year: number, month: number) {\n  return useQuery({\n    queryKey: progressKeys.calendar(year, month),\n    queryFn: () => ProgressService.getWorkoutCalendar(year, month),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get personal records\n */\nexport function usePersonalRecords() {\n  return useQuery({\n    queryKey: progressKeys.personalRecords(),\n    queryFn: () => ProgressService.getPersonalRecords(),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get strength progression\n */\nexport function useStrengthProgression(exerciseIds?: string[]) {\n  return useQuery({\n    queryKey: progressKeys.strengthProgression(exerciseIds),\n    queryFn: () => ProgressService.getStrengthProgression(exerciseIds),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to get workout intensity analysis\n */\nexport function useWorkoutIntensity(period: 'week' | 'month' | 'year' = 'month') {\n  return useQuery({\n    queryKey: progressKeys.workoutIntensity(period),\n    queryFn: () => ProgressService.getWorkoutIntensity(period),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n}\n\n/**\n * Hook to export progress data\n */\nexport function useExportProgressData() {\n  return useMutation({\n    mutationFn: ({ format, period }: { format: 'csv' | 'json'; period: 'month' | 'year' | 'all' }) =>\n      ProgressService.exportProgressData(format, period),\n    onSuccess: (blob, { format }) => {\n      // Create download link\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `progress-data.${format}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    },\n  });\n}\n\n/**\n * Hook to get progress records with infinite scrolling\n */\nexport function useInfiniteProgressRecords(params: Omit<Parameters<typeof ProgressService.getProgressRecords>[0], 'offset'> = {}) {\n  return useInfiniteQuery({\n    queryKey: progressKeys.recordsList(params),\n    queryFn: ({ pageParam = 0 }) => \n      ProgressService.getProgressRecords({ ...params, offset: pageParam }),\n    initialPageParam: 0,\n    getNextPageParam: (lastPage) => {\n      const { pagination } = lastPage;\n      return pagination.hasNext ? pagination.page * pagination.limit : undefined;\n    },\n    staleTime: 5 * 60 * 1000,\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;;;AAED;AAAA;AAAA;AAAA;AACA;;;;AAOO,MAAM,eAAe;IAC1B,KAAK;QAAC;KAAW;IACjB,SAAS,IAAM;eAAI,aAAa,GAAG;YAAE;SAAU;IAC/C,QAAQ,CAAC,KAAe;eAAI,aAAa,OAAO;YAAI;SAAG;IACvD,aAAa,CAAC,SAAgB;eAAI,aAAa,OAAO;YAAI;YAAQ;SAAO;IACzE,OAAO,CAAC,SAAmB;eAAI,aAAa,GAAG;YAAE;YAAS;SAAO;IACjE,cAAc,CAAC,SAAmB;eAAI,aAAa,GAAG;YAAE;YAAiB;SAAO;IAChF,kBAAkB,CAAC,YAAoB,SAAmB;eAAI,aAAa,GAAG;YAAE;YAAY;YAAY;SAAO;IAC/G,kBAAkB,CAAC,SAAmB;eAAI,aAAa,GAAG;YAAE;YAAqB;SAAO;IACxF,OAAO,IAAM;eAAI,aAAa,GAAG;YAAE;SAAQ;IAC3C,cAAc,IAAM;eAAI,aAAa,GAAG;YAAE;SAAe;IACzD,UAAU,CAAC,MAAc,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAY;YAAM;SAAM;IACzF,iBAAiB,IAAM;eAAI,aAAa,GAAG;YAAE;SAAmB;IAChE,qBAAqB,CAAC,cAA2B;eAAI,aAAa,GAAG;YAAE;YAAwB;SAAY;IAC3G,kBAAkB,CAAC,SAAmB;eAAI,aAAa,GAAG;YAAE;YAAqB;SAAO;AAC1F;AAKO,SAAS,mBAAmB,SAAmE,CAAC,CAAC;;IACtG,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,WAAW,CAAC;QACnC,OAAO;2CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;;QAClD,WAAW,IAAI,KAAK;IACtB;AACF;GANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,kBAAkB,EAAU,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;0CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;;QACjD,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,OAAmC,4IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC;;QACrF,SAAS;mDAAE;gBACT,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,OAAO;gBAAG;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK,CAAC;gBAAS;gBACtE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,YAAY,CAAC;gBAAS;YAC/E;;IACF;AACF;IAZgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAkD,GACvE,4IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,IAAI;;QAC3C,SAAS;mDAAE,CAAC;gBACV,mCAAmC;gBACnC,YAAY,YAAY,CAAC,aAAa,MAAM,CAAC,cAAc,EAAE,GAAG;gBAEhE,oCAAoC;gBACpC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,OAAO;gBAAG;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK,CAAC;gBAAS;YACxE;;IACF;AACF;IAfgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,KAAe,4IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC;;QACjE,SAAS;mDAAE,CAAC,GAAG;gBACb,oBAAoB;gBACpB,YAAY,aAAa,CAAC;oBAAE,UAAU,aAAa,MAAM,CAAC;gBAAW;gBAErE,oCAAoC;gBACpC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,OAAO;gBAAG;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK,CAAC;gBAAS;YACxE;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,SAAS,iBAAiB,SAA4C,OAAO;;IAClF,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,KAAK,CAAC;QAC7B,OAAO;yCAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;;QAChD,WAAW,IAAI,KAAK;IACtB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,gBAAgB,SAAoC,OAAO;;IACzE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,YAAY,CAAC;QACpC,OAAO;wCAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;;QAC/C,WAAW,IAAI,KAAK;IACtB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,oBAAoB,UAAkB,EAAE,SAAoC,OAAO,EAAE,UAAU,IAAI;;IACjH,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,gBAAgB,CAAC,YAAY;QACpD,OAAO;4CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,YAAY;;QAC/D,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,oBAAoB,SAAoC,OAAO;;IAC7E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,gBAAgB,CAAC;QACxC,OAAO;4CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;;QACnD,WAAW,KAAK,KAAK;IACvB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,OACX,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;;QACrC,SAAS;iDAAE;gBACT,+BAA+B;gBAC/B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,gBAAgB,CAAC;gBAAS;gBACjF,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK,CAAC;gBAAS;YACxE;;IACF;AACF;IAZgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,KAAK;QAC5B,OAAO;wCAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,eAAe;;QAC9C,WAAW,IAAI,KAAK;IACtB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,CAAC,OACX,4IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;;QACpC,SAAS;gDAAE;gBACT,mBAAmB;gBACnB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;YACjE;;IACF;AACF;KAXgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,EAAE,MAAM,EAAE,YAAY,EAA4C,GAC7E,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,QAAQ;;QAC7C,SAAS;iDAAE;gBACT,mBAAmB;gBACnB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;YACjE;;IACF;AACF;KAXgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,YAAY;QACnC,OAAO;wCAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,eAAe;;QAC9C,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,mBAAmB,IAAY,EAAE,KAAa;;IAC5D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,QAAQ,CAAC,MAAM;QACtC,OAAO;2CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,MAAM;;QACxD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,eAAe;QACtC,OAAO;2CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,kBAAkB;;QACjD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,uBAAuB,WAAsB;;IAC3D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,mBAAmB,CAAC;QAC3C,OAAO;+CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC;;QACtD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,oBAAoB,SAAoC,OAAO;;IAC7E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,gBAAgB,CAAC;QACxC,OAAO;4CAAE,IAAM,4IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;;QACnD,WAAW,KAAK,KAAK;IACvB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAgE,GAC3F,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,QAAQ;;QAC7C,SAAS;iDAAE,CAAC,MAAM,EAAE,MAAM,EAAE;gBAC1B,uBAAuB;gBACvB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,CAAC,cAAc,EAAE,QAAQ;gBACzC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAC7B;;IACF;AACF;KAhBgB;;QACP,iLAAA,CAAA,cAAW;;;AAoBb,SAAS,2BAA2B,SAAmF,CAAC,CAAC;;IAC9H,OAAO,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,aAAa,WAAW,CAAC;QACnC,OAAO;2DAAE,CAAC,EAAE,YAAY,CAAC,EAAE,GACzB,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU;;QACpE,kBAAkB;QAClB,gBAAgB;2DAAE,CAAC;gBACjB,MAAM,EAAE,UAAU,EAAE,GAAG;gBACvB,OAAO,WAAW,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,KAAK,GAAG;YACnE;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;KAZgB;;QACP,sLAAA,CAAA,mBAAgB", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-progress.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  Button,\n  Tabs,\n  Tab,\n  Paper,\n  LinearProgress,\n  Chip,\n  alpha,\n  useTheme,\n  Avatar,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n} from '@mui/material';\nimport {\n  TrendingUp as TrendingUpIcon,\n  CalendarToday as CalendarIcon,\n  Target as TargetIcon,\n  EmojiEvents as AwardIcon,\n  LocalFireDepartment as FlameIcon,\n  AccessTime as ClockIcon,\n  BarChart as BarChartIcon,\n  Download as DownloadIcon,\n  Settings as SettingsIcon,\n  Add as AddIcon,\n  Visibility as EyeIcon,\n  TrendingDown as TrendingDownIcon,\n  Group as UsersIcon,\n  Bolt as ZapIcon,\n  Favorite as HeartIcon,\n  Clear as ClearIcon,\n  Edit as EditIcon,\n  Check as CheckIcon,\n  Timer as TimerIcon,\n  FitnessCenter as FitnessCenterIcon,\n} from '@mui/icons-material';\nimport {\n  useProgressStats,\n  useWorkoutStats,\n  useBodyMeasurements,\n  useFitnessGoals,\n  useAchievements,\n  useWorkoutCalendar,\n  usePersonalRecords,\n  useWorkoutIntensity,\n  useExportProgressData\n} from \"@/lib/hooks/use-progress\";\nimport { useAuth } from \"@/lib/hooks/use-auth\";\nimport { useI18n } from '@/lib/i18n/context';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`progress-tabpanel-${index}`}\n      aria-labelledby={`progress-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nexport function MuiProgress() {\n  const { t } = useI18n();\n  const theme = useTheme();\n  const [activeTab, setActiveTab] = useState(0);\n  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');\n  \n  const { isAuthenticated } = useAuth();\n  \n  // API hooks\n  const { data: progressStats, isLoading: isLoadingProgress } = useProgressStats(selectedPeriod);\n  const { data: workoutStats, isLoading: isLoadingWorkouts } = useWorkoutStats(selectedPeriod);\n  const { data: bodyMeasurements, isLoading: isLoadingMeasurements } = useBodyMeasurements();\n  const { data: fitnessGoals, isLoading: isLoadingGoals } = useFitnessGoals();\n  const { data: achievements, isLoading: isLoadingAchievements } = useAchievements();\n  const { data: workoutCalendar } = useWorkoutCalendar();\n  const { data: personalRecords } = usePersonalRecords();\n  const { data: workoutIntensity } = useWorkoutIntensity(selectedPeriod);\n\n  // Mock data for demonstration\n  const mockStats = [\n    { \n      label: '本月训练', \n      value: '18次', \n      change: '+12%',\n      trend: 'up',\n      icon: <CalendarIcon />, \n      color: theme.palette.primary.main \n    },\n    { \n      label: '总时长', \n      value: '720分钟', \n      change: '+8%',\n      trend: 'up',\n      icon: <TimerIcon />, \n      color: theme.palette.secondary.main \n    },\n    { \n      label: '消耗卡路里', \n      value: '2,340', \n      change: '+15%',\n      trend: 'up',\n      icon: <FlameIcon />, \n      color: '#FF5722' \n    },\n    { \n      label: '连续天数', \n      value: '12天', \n      change: '+3天',\n      trend: 'up',\n      icon: <AwardIcon />, \n      color: '#9C27B0' \n    },\n  ];\n\n  const mockGoals = [\n    { id: 1, title: '每周训练4次', progress: 75, target: 4, current: 3, unit: '次' },\n    { id: 2, title: '月度卡路里消耗', progress: 60, target: 5000, current: 3000, unit: '卡' },\n    { id: 3, title: '体重目标', progress: 40, target: 70, current: 72, unit: 'kg' },\n  ];\n\n  const mockAchievements = [\n    { id: 1, title: '连续训练7天', description: '坚持一周训练', icon: '🔥', earned: true, date: '2024-01-15' },\n    { id: 2, title: '首次完成HIIT', description: '完成高强度间歇训练', icon: '⚡', earned: true, date: '2024-01-10' },\n    { id: 3, title: '训练达人', description: '完成50次训练', icon: '🏆', earned: false, progress: 32 },\n    { id: 4, title: '卡路里燃烧王', description: '单次训练消耗500卡路里', icon: '🔥', earned: false, progress: 80 },\n  ];\n\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,\n          py: { xs: 6, md: 8 },\n          position: 'relative',\n          overflow: 'hidden',\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>\n            <Typography\n              variant=\"h2\"\n              sx={{\n                fontWeight: 700,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                mb: 2,\n                fontSize: { xs: '2.5rem', md: '3.5rem' }\n              }}\n            >\n              {t('progress.title')}\n            </Typography>\n            <Typography\n              variant=\"h5\"\n              sx={{\n                color: 'text.secondary',\n                mb: 4,\n                fontWeight: 400,\n                fontSize: { xs: '1.25rem', md: '1.5rem' }\n              }}\n            >\n              {t('progress.subtitle')}\n            </Typography>\n          </Box>\n        </Container>\n\n        {/* Floating Elements */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '20%',\n            right: '10%',\n            width: 80,\n            height: 80,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.secondary.main, 0.3)})`,\n            animation: 'float 6s ease-in-out infinite',\n          }}\n        />\n        <Box\n          sx={{\n            position: 'absolute',\n            bottom: '30%',\n            left: '5%',\n            width: 60,\n            height: 60,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,\n            animation: 'float 4s ease-in-out infinite reverse',\n          }}\n        />\n      </Box>\n\n      {/* Quick Stats */}\n      <Container maxWidth=\"lg\" sx={{ mt: -2, position: 'relative', zIndex: 2, py: 4 }}>\n        <Grid container spacing={3}>\n          {mockStats.map((stat, index) => (\n            <Grid size={{ xs: 12, sm: 6, lg: 3 }} key={index}>\n              <Card\n                sx={{\n                  p: 3,\n                  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',\n                  backdropFilter: 'blur(15px)',\n                  border: '1px solid rgba(255,255,255,0.3)',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-4px)',\n                    boxShadow: `0 8px 20px ${alpha(stat.color, 0.15)}`,\n                    border: `1px solid ${alpha(stat.color, 0.2)}`,\n                  }\n                }}\n              >\n                <CardContent sx={{ p: '0 !important' }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\n                    <Box\n                      sx={{\n                        display: 'inline-flex',\n                        p: 1.5,\n                        borderRadius: '50%',\n                        bgcolor: alpha(stat.color, 0.12),\n                        color: stat.color,\n                      }}\n                    >\n                      {stat.icon}\n                    </Box>\n                    <Chip\n                      label={stat.change}\n                      size=\"small\"\n                      icon={stat.trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}\n                      color={stat.trend === 'up' ? 'success' : 'error'}\n                      variant=\"outlined\"\n                    />\n                  </Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>\n                    {stat.value}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: 'text.secondary', fontWeight: 500 }}>\n                    {stat.label}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        {/* Period Selector */}\n        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>\n          <Paper sx={{ p: 1, borderRadius: 3 }}>\n            <Button\n              variant={selectedPeriod === 'week' ? 'contained' : 'text'}\n              onClick={() => setSelectedPeriod('week')}\n              sx={{ borderRadius: 2, textTransform: 'none', mx: 0.5 }}\n            >\n              本周\n            </Button>\n            <Button\n              variant={selectedPeriod === 'month' ? 'contained' : 'text'}\n              onClick={() => setSelectedPeriod('month')}\n              sx={{ borderRadius: 2, textTransform: 'none', mx: 0.5 }}\n            >\n              本月\n            </Button>\n            <Button\n              variant={selectedPeriod === 'year' ? 'contained' : 'text'}\n              onClick={() => setSelectedPeriod('year')}\n              sx={{ borderRadius: 2, textTransform: 'none', mx: 0.5 }}\n            >\n              本年\n            </Button>\n          </Paper>\n        </Box>\n\n        {/* Tabs */}\n        <Paper sx={{ mb: 4, borderRadius: 3, overflow: 'hidden' }}>\n          <Tabs\n            value={activeTab}\n            onChange={(_, newValue) => setActiveTab(newValue)}\n            variant=\"fullWidth\"\n            sx={{\n              '& .MuiTab-root': {\n                fontWeight: 600,\n                fontSize: '1rem',\n                textTransform: 'none',\n                py: 2,\n              },\n              '& .Mui-selected': {\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                color: 'white !important',\n              }\n            }}\n          >\n            <Tab label=\"概览\" />\n            <Tab label=\"目标\" />\n            <Tab label=\"成就\" />\n            <Tab label=\"历史\" />\n          </Tabs>\n        </Paper>\n\n        {/* Overview Tab */}\n        <TabPanel value={activeTab} index={0}>\n          <Grid container spacing={3}>\n            {/* Workout Chart */}\n            <Grid size={{ xs: 12, lg: 8 }}>\n              <Card sx={{ p: 3, borderRadius: 3, height: 400 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 3 }}>\n                  训练趋势\n                </Typography>\n                <Box\n                  sx={{\n                    height: 300,\n                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,\n                    borderRadius: 2,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                  }}\n                >\n                  <BarChartIcon sx={{ fontSize: 64, color: 'text.secondary', opacity: 0.5 }} />\n                  <Typography variant=\"body1\" color=\"text.secondary\" sx={{ ml: 2 }}>\n                    图表数据加载中...\n                  </Typography>\n                </Box>\n              </Card>\n            </Grid>\n\n            {/* Recent Activities */}\n            <Grid size={{ xs: 12, lg: 4 }}>\n              <Card sx={{ p: 3, borderRadius: 3, height: 400 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 3 }}>\n                  最近活动\n                </Typography>\n                <List sx={{ p: 0 }}>\n                  {[\n                    { activity: '完成HIIT训练', time: '2小时前', icon: <ZapIcon />, color: theme.palette.primary.main },\n                    { activity: '达成周目标', time: '1天前', icon: <TargetIcon />, color: theme.palette.secondary.main },\n                    { activity: '力量训练', time: '2天前', icon: <FitnessCenterIcon />, color: '#FF5722' },\n                    { activity: '获得新成就', time: '3天前', icon: <AwardIcon />, color: '#9C27B0' },\n                  ].map((item, index) => (\n                    <React.Fragment key={index}>\n                      <ListItem sx={{ px: 0 }}>\n                        <ListItemIcon>\n                          <Avatar sx={{ bgcolor: alpha(item.color, 0.1), color: item.color, width: 40, height: 40 }}>\n                            {item.icon}\n                          </Avatar>\n                        </ListItemIcon>\n                        <ListItemText\n                          primary={item.activity}\n                          secondary={item.time}\n                          primaryTypographyProps={{ fontWeight: 500 }}\n                        />\n                      </ListItem>\n                      {index < 3 && <Divider />}\n                    </React.Fragment>\n                  ))}\n                </List>\n              </Card>\n            </Grid>\n          </Grid>\n        </TabPanel>\n\n        {/* Goals Tab */}\n        <TabPanel value={activeTab} index={1}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>\n              健身目标\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              sx={{\n                borderRadius: 3,\n                textTransform: 'none',\n                fontWeight: 600,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n              }}\n            >\n              添加目标\n            </Button>\n          </Box>\n\n          <Grid container spacing={3}>\n            {mockGoals.map((goal) => (\n              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={goal.id}>\n                <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, flex: 1 }}>\n                      {goal.title}\n                    </Typography>\n                    <Button variant=\"text\" size=\"small\" sx={{ minWidth: 'auto', p: 0.5 }}>\n                      <EditIcon fontSize=\"small\" />\n                    </Button>\n                  </Box>\n\n                  <Box sx={{ mb: 3 }}>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        进度\n                      </Typography>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                        {goal.current}/{goal.target} {goal.unit}\n                      </Typography>\n                    </Box>\n                    <LinearProgress\n                      variant=\"determinate\"\n                      value={goal.progress}\n                      sx={{\n                        height: 8,\n                        borderRadius: 4,\n                        bgcolor: alpha(theme.palette.primary.main, 0.1),\n                        '& .MuiLinearProgress-bar': {\n                          borderRadius: 4,\n                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                        }\n                      }}\n                    />\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                      {goal.progress}% 完成\n                    </Typography>\n                  </Box>\n\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      startIcon={<EyeIcon />}\n                      sx={{ flex: 1, borderRadius: 2, textTransform: 'none' }}\n                    >\n                      查看详情\n                    </Button>\n                    <Button\n                      variant=\"text\"\n                      size=\"small\"\n                      sx={{ minWidth: 'auto', borderRadius: 2 }}\n                    >\n                      <CheckIcon />\n                    </Button>\n                  </Box>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </TabPanel>\n\n        {/* Achievements Tab */}\n        <TabPanel value={activeTab} index={2}>\n          <Typography variant=\"h5\" sx={{ fontWeight: 700, mb: 3 }}>\n            成就系统\n          </Typography>\n\n          <Grid container spacing={3}>\n            {mockAchievements.map((achievement) => (\n              <Grid size={{ xs: 12, sm: 6, lg: 3 }} key={achievement.id}>\n                <Card\n                  sx={{\n                    p: 3,\n                    borderRadius: 3,\n                    height: '100%',\n                    opacity: achievement.earned ? 1 : 0.7,\n                    background: achievement.earned\n                      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`\n                      : 'background.paper',\n                    border: achievement.earned ? `2px solid ${alpha(theme.palette.primary.main, 0.2)}` : '1px solid',\n                    borderColor: achievement.earned ? 'transparent' : 'divider',\n                  }}\n                >\n                  <Box sx={{ textAlign: 'center', mb: 2 }}>\n                    <Typography variant=\"h2\" sx={{ mb: 1 }}>\n                      {achievement.icon}\n                    </Typography>\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                      {achievement.title}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {achievement.description}\n                    </Typography>\n                  </Box>\n\n                  {achievement.earned ? (\n                    <Box sx={{ textAlign: 'center' }}>\n                      <Chip\n                        label={`获得于 ${achievement.date}`}\n                        color=\"primary\"\n                        size=\"small\"\n                        icon={<CheckIcon />}\n                      />\n                    </Box>\n                  ) : (\n                    <Box>\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          进度\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                          {achievement.progress}%\n                        </Typography>\n                      </Box>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={achievement.progress}\n                        sx={{\n                          height: 6,\n                          borderRadius: 3,\n                          bgcolor: alpha(theme.palette.grey[500], 0.2),\n                        }}\n                      />\n                    </Box>\n                  )}\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </TabPanel>\n\n        {/* History Tab */}\n        <TabPanel value={activeTab} index={3}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>\n              训练历史\n            </Typography>\n            <Button\n              variant=\"outlined\"\n              startIcon={<DownloadIcon />}\n              sx={{ borderRadius: 3, textTransform: 'none', fontWeight: 600 }}\n            >\n              导出数据\n            </Button>\n          </Box>\n\n          <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>\n            <CalendarIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              训练历史记录\n            </Typography>\n            <Typography color=\"text.secondary\" sx={{ mb: 3 }}>\n              这里将显示您的详细训练历史和数据分析\n            </Typography>\n            <Button\n              variant=\"contained\"\n              sx={{\n                borderRadius: 3,\n                textTransform: 'none',\n                fontWeight: 600,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n              }}\n            >\n              查看详细记录\n            </Button>\n          </Paper>\n        </TabPanel>\n      </Container>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAAA;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAWA;AACA;;;AA3DA;;;;;;;;;;;;;;;;;;;;;AAmEA,SAAS,SAAS,KAAoB;IACpC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,GAAG;IAE7C,qBACE,6LAAC;QACC,MAAK;QACL,QAAQ,UAAU;QAClB,IAAI,CAAC,kBAAkB,EAAE,OAAO;QAChC,mBAAiB,CAAC,aAAa,EAAE,OAAO;QACvC,GAAG,KAAK;kBAER,UAAU,uBAAS,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,IAAI;YAAE;sBAAI;;;;;;;;;;;AAG/C;KAdS;AAgBF,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEhF,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAElC,YAAY;IACZ,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD,EAAE;IAC/E,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD,EAAE;IAC7E,MAAM,EAAE,MAAM,gBAAgB,EAAE,WAAW,qBAAqB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD;IACvF,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;IACxE,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,qBAAqB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;IAC/E,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD;IACnD,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD;IACnD,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD,EAAE;IAEvD,8BAA8B;IAC9B,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,qKAAA,CAAA,UAAY;;;;;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QACnC;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;YAChB,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI;QACrC;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,2KAAA,CAAA,UAAS;;;;;YAChB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,oBAAM,6LAAC,mKAAA,CAAA,UAAS;;;;;YAChB,OAAO;QACT;KACD;IAED,MAAM,YAAY;QAChB;YAAE,IAAI;YAAG,OAAO;YAAU,UAAU;YAAI,QAAQ;YAAG,SAAS;YAAG,MAAM;QAAI;QACzE;YAAE,IAAI;YAAG,OAAO;YAAW,UAAU;YAAI,QAAQ;YAAM,SAAS;YAAM,MAAM;QAAI;QAChF;YAAE,IAAI;YAAG,OAAO;YAAQ,UAAU;YAAI,QAAQ;YAAI,SAAS;YAAI,MAAM;QAAK;KAC3E;IAED,MAAM,mBAAmB;QACvB;YAAE,IAAI;YAAG,OAAO;YAAU,aAAa;YAAU,MAAM;YAAM,QAAQ;YAAM,MAAM;QAAa;QAC9F;YAAE,IAAI;YAAG,OAAO;YAAY,aAAa;YAAa,MAAM;YAAK,QAAQ;YAAM,MAAM;QAAa;QAClG;YAAE,IAAI;YAAG,OAAO;YAAQ,aAAa;YAAW,MAAM;YAAM,QAAQ;YAAO,UAAU;QAAG;QACxF;YAAE,IAAI;YAAG,OAAO;YAAU,aAAa;YAAgB,MAAM;YAAM,QAAQ;YAAO,UAAU;QAAG;KAChG;IAED,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,WAAW;YAAS,SAAS;QAAqB;;0BAE3D,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;oBACrI,IAAI;wBAAE,IAAI;wBAAG,IAAI;oBAAE;oBACnB,UAAU;oBACV,UAAU;gBACZ;;kCAEA,6LAAC,6MAAA,CAAA,YAAS;wBAAC,UAAS;kCAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,WAAW;gCAAU,UAAU;gCAAY,QAAQ;4BAAE;;8CAC9D,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,YAAY;wCACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACpG,gBAAgB;wCAChB,sBAAsB;wCACtB,qBAAqB;wCACrB,IAAI;wCACJ,UAAU;4CAAE,IAAI;4CAAU,IAAI;wCAAS;oCACzC;8CAEC,EAAE;;;;;;8CAEL,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,OAAO;wCACP,IAAI;wCACJ,YAAY;wCACZ,UAAU;4CAAE,IAAI;4CAAW,IAAI;wCAAS;oCAC1C;8CAEC,EAAE;;;;;;;;;;;;;;;;;kCAMT,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;kCAEF,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;;;;;;;0BAKJ,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI,CAAC;oBAAG,UAAU;oBAAY,QAAQ;oBAAG,IAAI;gBAAE;0BAC5E,cAAA,6LAAC,8LAAA,CAAA,OAAI;oBAAC,SAAS;oBAAC,SAAS;8BACtB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,8LAAA,CAAA,OAAI;4BAAC,MAAM;gCAAE,IAAI;gCAAI,IAAI;gCAAG,IAAI;4BAAE;sCACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;gCACH,IAAI;oCACF,GAAG;oCACH,YAAY;oCACZ,gBAAgB;oCAChB,QAAQ;oCACR,cAAc;oCACd,YAAY;oCACZ,WAAW;wCACT,WAAW;wCACX,WAAW,CAAC,WAAW,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,OAAO;wCAClD,QAAQ,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,MAAM;oCAC/C;gCACF;0CAEA,cAAA,6LAAC,mNAAA,CAAA,cAAW;oCAAC,IAAI;wCAAE,GAAG;oCAAe;;sDACnC,6LAAC,2LAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,SAAS;gDAAQ,YAAY;gDAAU,gBAAgB;gDAAiB,IAAI;4CAAE;;8DACvF,6LAAC,2LAAA,CAAA,MAAG;oDACF,IAAI;wDACF,SAAS;wDACT,GAAG;wDACH,cAAc;wDACd,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE;wDAC3B,OAAO,KAAK,KAAK;oDACnB;8DAEC,KAAK,IAAI;;;;;;8DAEZ,6LAAC,8LAAA,CAAA,OAAI;oDACH,OAAO,KAAK,MAAM;oDAClB,MAAK;oDACL,MAAM,KAAK,KAAK,KAAK,qBAAO,6LAAC,kKAAA,CAAA,UAAc;;;;+EAAM,6LAAC,oKAAA,CAAA,UAAgB;;;;;oDAClE,OAAO,KAAK,KAAK,KAAK,OAAO,YAAY;oDACzC,SAAQ;;;;;;;;;;;;sDAGZ,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAK,IAAI;gDAAE,YAAY;gDAAK,OAAO,KAAK,KAAK;gDAAE,IAAI;4CAAE;sDACtE,KAAK,KAAK;;;;;;sDAEb,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAQ,IAAI;gDAAE,OAAO;gDAAkB,YAAY;4CAAI;sDACxE,KAAK,KAAK;;;;;;;;;;;;;;;;;2BAzCwB;;;;;;;;;;;;;;;0BAkDjD,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;;kCAEnC,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,SAAS;4BAAQ,gBAAgB;4BAAU,IAAI;wBAAE;kCAC1D,cAAA,6LAAC,iMAAA,CAAA,QAAK;4BAAC,IAAI;gCAAE,GAAG;gCAAG,cAAc;4BAAE;;8CACjC,6LAAC,oMAAA,CAAA,SAAM;oCACL,SAAS,mBAAmB,SAAS,cAAc;oCACnD,SAAS,IAAM,kBAAkB;oCACjC,IAAI;wCAAE,cAAc;wCAAG,eAAe;wCAAQ,IAAI;oCAAI;8CACvD;;;;;;8CAGD,6LAAC,oMAAA,CAAA,SAAM;oCACL,SAAS,mBAAmB,UAAU,cAAc;oCACpD,SAAS,IAAM,kBAAkB;oCACjC,IAAI;wCAAE,cAAc;wCAAG,eAAe;wCAAQ,IAAI;oCAAI;8CACvD;;;;;;8CAGD,6LAAC,oMAAA,CAAA,SAAM;oCACL,SAAS,mBAAmB,SAAS,cAAc;oCACnD,SAAS,IAAM,kBAAkB;oCACjC,IAAI;wCAAE,cAAc;wCAAG,eAAe;wCAAQ,IAAI;oCAAI;8CACvD;;;;;;;;;;;;;;;;;kCAOL,6LAAC,iMAAA,CAAA,QAAK;wBAAC,IAAI;4BAAE,IAAI;4BAAG,cAAc;4BAAG,UAAU;wBAAS;kCACtD,cAAA,6LAAC,8LAAA,CAAA,OAAI;4BACH,OAAO;4BACP,UAAU,CAAC,GAAG,WAAa,aAAa;4BACxC,SAAQ;4BACR,IAAI;gCACF,kBAAkB;oCAChB,YAAY;oCACZ,UAAU;oCACV,eAAe;oCACf,IAAI;gCACN;gCACA,mBAAmB;oCACjB,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oCACpG,OAAO;gCACT;4BACF;;8CAEA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;8CACX,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;8CACX,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;8CACX,6LAAC,2LAAA,CAAA,MAAG;oCAAC,OAAM;;;;;;;;;;;;;;;;;kCAKf,6LAAC;wBAAS,OAAO;wBAAW,OAAO;kCACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4BAAC,SAAS;4BAAC,SAAS;;8CAEvB,6LAAC,8LAAA,CAAA,OAAI;oCAAC,MAAM;wCAAE,IAAI;wCAAI,IAAI;oCAAE;8CAC1B,cAAA,6LAAC,8LAAA,CAAA,OAAI;wCAAC,IAAI;4CAAE,GAAG;4CAAG,cAAc;4CAAG,QAAQ;wCAAI;;0DAC7C,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,YAAY;oDAAK,IAAI;gDAAE;0DAAG;;;;;;0DAGzD,6LAAC,2LAAA,CAAA,MAAG;gDACF,IAAI;oDACF,QAAQ;oDACR,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oDAC/H,cAAc;oDACd,SAAS;oDACT,YAAY;oDACZ,gBAAgB;gDAClB;;kEAEA,6LAAC,gKAAA,CAAA,UAAY;wDAAC,IAAI;4DAAE,UAAU;4DAAI,OAAO;4DAAkB,SAAS;wDAAI;;;;;;kEACxE,6LAAC,gNAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,OAAM;wDAAiB,IAAI;4DAAE,IAAI;wDAAE;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAQxE,6LAAC,8LAAA,CAAA,OAAI;oCAAC,MAAM;wCAAE,IAAI;wCAAI,IAAI;oCAAE;8CAC1B,cAAA,6LAAC,8LAAA,CAAA,OAAI;wCAAC,IAAI;4CAAE,GAAG;4CAAG,cAAc;4CAAG,QAAQ;wCAAI;;0DAC7C,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,YAAY;oDAAK,IAAI;gDAAE;0DAAG;;;;;;0DAGzD,6LAAC,8LAAA,CAAA,OAAI;gDAAC,IAAI;oDAAE,GAAG;gDAAE;0DACd;oDACC;wDAAE,UAAU;wDAAY,MAAM;wDAAQ,oBAAM,6LAAC,4JAAA,CAAA,UAAO;;;;;wDAAK,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;oDAAC;oDAC3F;wDAAE,UAAU;wDAAS,MAAM;wDAAO,oBAAM,6LAAC;;;;;wDAAe,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI;oDAAC;oDAC5F;wDAAE,UAAU;wDAAQ,MAAM;wDAAO,oBAAM,6LAAC,qKAAA,CAAA,UAAiB;;;;;wDAAK,OAAO;oDAAU;oDAC/E;wDAAE,UAAU;wDAAS,MAAM;wDAAO,oBAAM,6LAAC,mKAAA,CAAA,UAAS;;;;;wDAAK,OAAO;oDAAU;iDACzE,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;0EACb,6LAAC,0MAAA,CAAA,WAAQ;gEAAC,IAAI;oEAAE,IAAI;gEAAE;;kFACpB,6LAAC,sNAAA,CAAA,eAAY;kFACX,cAAA,6LAAC,oMAAA,CAAA,SAAM;4EAAC,IAAI;gFAAE,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE;gFAAM,OAAO,KAAK,KAAK;gFAAE,OAAO;gFAAI,QAAQ;4EAAG;sFACrF,KAAK,IAAI;;;;;;;;;;;kFAGd,6LAAC,sNAAA,CAAA,eAAY;wEACX,SAAS,KAAK,QAAQ;wEACtB,WAAW,KAAK,IAAI;wEACpB,wBAAwB;4EAAE,YAAY;wEAAI;;;;;;;;;;;;4DAG7C,QAAQ,mBAAK,6LAAC,uMAAA,CAAA,UAAO;;;;;;uDAbH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAuBjC,6LAAC;wBAAS,OAAO;wBAAW,OAAO;;0CACjC,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,gBAAgB;oCAAiB,YAAY;oCAAU,IAAI;gCAAE;;kDACvF,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,IAAI;4CAAE,YAAY;wCAAI;kDAAG;;;;;;kDAGlD,6LAAC,oMAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,yBAAW,6LAAC,2JAAA,CAAA,UAAO;;;;;wCACnB,IAAI;4CACF,cAAc;4CACd,eAAe;4CACf,YAAY;4CACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACtG;kDACD;;;;;;;;;;;;0CAKH,6LAAC,8LAAA,CAAA,OAAI;gCAAC,SAAS;gCAAC,SAAS;0CACtB,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,8LAAA,CAAA,OAAI;wCAAC,MAAM;4CAAE,IAAI;4CAAI,IAAI;4CAAG,IAAI;wCAAE;kDACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4CAAC,IAAI;gDAAE,GAAG;gDAAG,cAAc;gDAAG,QAAQ;4CAAO;;8DAChD,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,SAAS;wDAAQ,gBAAgB;wDAAiB,YAAY;wDAAc,IAAI;oDAAE;;sEAC3F,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAK,IAAI;gEAAE,YAAY;gEAAK,MAAM;4DAAE;sEACrD,KAAK,KAAK;;;;;;sEAEb,6LAAC,oMAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAO,MAAK;4DAAQ,IAAI;gEAAE,UAAU;gEAAQ,GAAG;4DAAI;sEACjE,cAAA,6LAAC,4JAAA,CAAA,UAAQ;gEAAC,UAAS;;;;;;;;;;;;;;;;;8DAIvB,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,IAAI;oDAAE;;sEACf,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,gBAAgB;gEAAiB,IAAI;4DAAE;;8EACjE,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,OAAM;8EAAiB;;;;;;8EAGnD,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,IAAI;wEAAE,YAAY;oEAAI;;wEAC/C,KAAK,OAAO;wEAAC;wEAAE,KAAK,MAAM;wEAAC;wEAAE,KAAK,IAAI;;;;;;;;;;;;;sEAG3C,6LAAC,4NAAA,CAAA,iBAAc;4DACb,SAAQ;4DACR,OAAO,KAAK,QAAQ;4DACpB,IAAI;gEACF,QAAQ;gEACR,cAAc;gEACd,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;gEAC3C,4BAA4B;oEAC1B,cAAc;oEACd,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gEACtG;4DACF;;;;;;sEAEF,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAQ,OAAM;4DAAiB,IAAI;gEAAE,IAAI;4DAAE;;gEAC5D,KAAK,QAAQ;gEAAC;;;;;;;;;;;;;8DAInB,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,SAAS;wDAAQ,KAAK;oDAAE;;sEACjC,6LAAC,oMAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,yBAAW,6LAAC,kKAAA,CAAA,UAAO;;;;;4DACnB,IAAI;gEAAE,MAAM;gEAAG,cAAc;gEAAG,eAAe;4DAAO;sEACvD;;;;;;sEAGD,6LAAC,oMAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,IAAI;gEAAE,UAAU;gEAAQ,cAAc;4DAAE;sEAExC,cAAA,6LAAC,6JAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;uCApDyB,KAAK,EAAE;;;;;;;;;;;;;;;;kCA8DxD,6LAAC;wBAAS,OAAO;wBAAW,OAAO;;0CACjC,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,IAAI;oCAAE,YAAY;oCAAK,IAAI;gCAAE;0CAAG;;;;;;0CAIzD,6LAAC,8LAAA,CAAA,OAAI;gCAAC,SAAS;gCAAC,SAAS;0CACtB,iBAAiB,GAAG,CAAC,CAAC,4BACrB,6LAAC,8LAAA,CAAA,OAAI;wCAAC,MAAM;4CAAE,IAAI;4CAAI,IAAI;4CAAG,IAAI;wCAAE;kDACjC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4CACH,IAAI;gDACF,GAAG;gDACH,cAAc;gDACd,QAAQ;gDACR,SAAS,YAAY,MAAM,GAAG,IAAI;gDAClC,YAAY,YAAY,MAAM,GAC1B,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GACnH;gDACJ,QAAQ,YAAY,MAAM,GAAG,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG;gDACrF,aAAa,YAAY,MAAM,GAAG,gBAAgB;4CACpD;;8DAEA,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,WAAW;wDAAU,IAAI;oDAAE;;sEACpC,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAK,IAAI;gEAAE,IAAI;4DAAE;sEAClC,YAAY,IAAI;;;;;;sEAEnB,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAK,IAAI;gEAAE,YAAY;gEAAK,IAAI;4DAAE;sEACnD,YAAY,KAAK;;;;;;sEAEpB,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAQ,OAAM;sEAC/B,YAAY,WAAW;;;;;;;;;;;;gDAI3B,YAAY,MAAM,iBACjB,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,WAAW;oDAAS;8DAC7B,cAAA,6LAAC,8LAAA,CAAA,OAAI;wDACH,OAAO,CAAC,IAAI,EAAE,YAAY,IAAI,EAAE;wDAChC,OAAM;wDACN,MAAK;wDACL,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;;;;;;;;;;yEAIpB,6LAAC,2LAAA,CAAA,MAAG;;sEACF,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,gBAAgB;gEAAiB,IAAI;4DAAE;;8EACjE,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,OAAM;8EAAiB;;;;;;8EAGnD,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,IAAI;wEAAE,YAAY;oEAAI;;wEAC/C,YAAY,QAAQ;wEAAC;;;;;;;;;;;;;sEAG1B,6LAAC,4NAAA,CAAA,iBAAc;4DACb,SAAQ;4DACR,OAAO,YAAY,QAAQ;4DAC3B,IAAI;gEACF,QAAQ;gEACR,cAAc;gEACd,SAAS,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;4DAC1C;;;;;;;;;;;;;;;;;;uCApDiC,YAAY,EAAE;;;;;;;;;;;;;;;;kCA+D/D,6LAAC;wBAAS,OAAO;wBAAW,OAAO;;0CACjC,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,gBAAgB;oCAAiB,YAAY;oCAAU,IAAI;gCAAE;;kDACvF,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,IAAI;4CAAE,YAAY;wCAAI;kDAAG;;;;;;kDAGlD,6LAAC,oMAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,yBAAW,6LAAC,gKAAA,CAAA,UAAY;;;;;wCACxB,IAAI;4CAAE,cAAc;4CAAG,eAAe;4CAAQ,YAAY;wCAAI;kDAC/D;;;;;;;;;;;;0CAKH,6LAAC,iMAAA,CAAA,QAAK;gCAAC,IAAI;oCAAE,GAAG;oCAAG,WAAW;oCAAU,cAAc;gCAAE;;kDACtD,6LAAC,qKAAA,CAAA,UAAY;wCAAC,IAAI;4CAAE,UAAU;4CAAI,OAAO;4CAAkB,IAAI;wCAAE;;;;;;kDACjE,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,IAAI;4CAAE,IAAI;wCAAE;kDAAG;;;;;;kDAGxC,6LAAC,gNAAA,CAAA,aAAU;wCAAC,OAAM;wCAAiB,IAAI;4CAAE,IAAI;wCAAE;kDAAG;;;;;;kDAGlD,6LAAC,oMAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,IAAI;4CACF,cAAc;4CACd,eAAe;4CACf,YAAY;4CACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACtG;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjfgB;;QACA,iIAAA,CAAA,UAAO;QACP,wMAAA,CAAA,WAAQ;QAIM,qIAAA,CAAA,UAAO;QAG2B,yIAAA,CAAA,mBAAgB;QACjB,yIAAA,CAAA,kBAAe;QACP,yIAAA,CAAA,sBAAmB;QAC9B,yIAAA,CAAA,kBAAe;QACR,yIAAA,CAAA,kBAAe;QAC9C,yIAAA,CAAA,qBAAkB;QAClB,yIAAA,CAAA,qBAAkB;QACjB,yIAAA,CAAA,sBAAmB;;;MAhBxC", "debugId": null}}]}