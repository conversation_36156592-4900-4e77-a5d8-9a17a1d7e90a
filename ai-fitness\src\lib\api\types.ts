/**
 * Type definitions for AI-fitness API
 * Based on workout-cool backend data models
 */

// ========================================
// USER & AUTHENTICATION TYPES
// ========================================

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  locale?: string;
  role?: 'user' | 'admin';
  banned?: boolean;
  banReason?: string;
  banExpires?: string;
  isPremium?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  user: User;
  token: string;
  expiresAt: string;
}

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface SignUpData extends AuthCredentials {
  firstName: string;
  lastName: string;
}

// ========================================
// EXERCISE TYPES
// ========================================

export interface ExerciseAttribute {
  id: string;
  attributeName: {
    id: string;
    name: string;
    nameEn: string;
  };
  attributeValue: {
    id: string;
    value: string;
    valueEn: string;
  };
}

export interface Exercise {
  id: string;
  name: string;
  nameEn?: string;
  description?: string;
  descriptionEn?: string;
  fullVideoUrl?: string;
  fullVideoImageUrl?: string;
  introduction?: string;
  introductionEn?: string;
  slug?: string;
  slugEn?: string;
  attributes: ExerciseAttribute[];
  createdAt: string;
  updatedAt: string;
}

export interface ExerciseSearchParams {
  search?: string;
  equipment?: string[];
  muscles?: string[];
  difficulty?: string[];
  category?: string[];
  limit?: number;
  offset?: number;
}

// ========================================
// WORKOUT SESSION TYPES
// ========================================

export interface WorkoutSet {
  id: string;
  type: 'NORMAL' | 'WARMUP' | 'DROPSET' | 'SUPERSET' | 'REST';
  reps?: number;
  weight?: number;
  duration?: number; // in seconds
  distance?: number; // in meters
  restTime?: number; // in seconds
  completed: boolean;
  notes?: string;
}

export interface WorkoutSessionExercise {
  id: string;
  exerciseId: string;
  exercise: Exercise;
  order: number;
  sets: WorkoutSet[];
  notes?: string;
}

export interface WorkoutSession {
  id: string;
  userId: string;
  startedAt: string;
  endedAt?: string;
  duration?: number; // in seconds
  exercises: WorkoutSessionExercise[];
  muscles: string[];
  notes?: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
}

export interface CreateWorkoutSessionData {
  exercises: {
    exerciseId: string;
    order: number;
    sets: Omit<WorkoutSet, 'id' | 'completed'>[];
  }[];
  notes?: string;
}

// ========================================
// PROGRAM TYPES
// ========================================

export interface ProgramCoach {
  id: string;
  name: string;
  nameEn: string;
  bio: string;
  bioEn: string;
  image?: string;
  order: number;
}

export interface ProgramSuggestedSet {
  id: string;
  setIndex: number;
  types: string[];
  valuesInt?: number[];
  valuesSec?: number[];
  units?: string[];
}

export interface ProgramSessionExercise {
  id: string;
  exerciseId: string;
  exercise: Exercise;
  order: number;
  instructions: string;
  instructionsEn: string;
  suggestedSets: ProgramSuggestedSet[];
}

export interface ProgramSession {
  id: string;
  sessionNumber: number;
  title: string;
  titleEn: string;
  slug: string;
  slugEn: string;
  description: string;
  descriptionEn: string;
  equipment: string[];
  estimatedMinutes: number;
  isPremium: boolean;
  exercises: ProgramSessionExercise[];
}

export interface ProgramWeek {
  id: string;
  weekNumber: number;
  title: string;
  titleEn: string;
  description: string;
  descriptionEn: string;
  sessions: ProgramSession[];
}

export interface Program {
  id: string;
  slug: string;
  slugEn: string;
  title: string;
  titleEn: string;
  description: string;
  descriptionEn: string;
  shortDescription: string;
  shortDescriptionEn: string;
  image?: string;
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  duration: number; // in weeks
  sessionsPerWeek: number;
  equipment: string[];
  goals: string[];
  isPremium: boolean;
  isPublished: boolean;
  participantCount: number;
  rating?: number;
  coaches: ProgramCoach[];
  weeks: ProgramWeek[];
  createdAt: string;
  updatedAt: string;
}

export interface UserProgramEnrollment {
  id: string;
  userId: string;
  programId: string;
  program: Program;
  enrolledAt: string;
  completedAt?: string;
  currentWeek: number;
  currentSession: number;
  progress: number; // percentage
}

// ========================================
// PROGRESS TYPES
// ========================================

export interface ProgressStats {
  totalWorkouts: number;
  totalDuration: number; // in minutes
  totalCalories: number;
  currentStreak: number;
  longestStreak: number;
  averageWorkoutDuration: number;
  workoutsThisWeek: number;
  workoutsThisMonth: number;
}

export interface ProgressGoal {
  id: string;
  type: 'WORKOUTS_PER_WEEK' | 'MINUTES_PER_WEEK' | 'CALORIES_PER_WEEK' | 'WEIGHT_LOSS' | 'WEIGHT_GAIN' | 'CUSTOM';
  title: string;
  description?: string;
  target: number;
  current: number;
  unit: string;
  deadline?: string;
  isActive: boolean;
  createdAt: string;
}

export interface ProgressHistory {
  date: string;
  workouts: number;
  duration: number; // in minutes
  calories: number;
  weight?: number; // in kg
}

// ========================================
// PREMIUM TYPES
// ========================================

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  priceMonthly?: number;
  priceYearly?: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  isPopular?: boolean;
  isActive: boolean;
}

export interface UserSubscription {
  id: string;
  planId: string;
  plan: SubscriptionPlan;
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'PAST_DUE';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

// ========================================
// API RESPONSE TYPES
// ========================================

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiError {
  message: string;
  code?: string;
  status: number;
  details?: any;
}

// ========================================
// ENHANCED PROGRESS TRACKING TYPES
// ========================================

export interface ProgressRecord {
  id: string;
  userId: string;
  type: 'workout' | 'exercise' | 'measurement' | 'goal';
  exerciseId?: string;
  workoutId?: string;
  date: string;
  value: number;
  unit: string;
  notes?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProgressRecordData {
  type: 'workout' | 'exercise' | 'measurement' | 'goal';
  exerciseId?: string;
  workoutId?: string;
  date: string;
  value: number;
  unit: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface UpdateProgressRecordData {
  value?: number;
  unit?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface ProgressStats {
  totalWorkouts: number;
  totalDuration: number;
  totalCalories: number;
  averageWorkoutDuration: number;
  workoutFrequency: number;
  currentStreak: number;
  longestStreak: number;
  favoriteExercises: Array<{
    exerciseId: string;
    exerciseName: string;
    count: number;
  }>;
  weeklyStats: Array<{
    week: string;
    workouts: number;
    duration: number;
    calories: number;
  }>;
  monthlyStats: Array<{
    month: string;
    workouts: number;
    duration: number;
    calories: number;
  }>;
}
