# Workout.cool - Modern Fitness Platform

A simplified, modern fitness coaching platform inspired by the original workout-cool project. Built with Next.js 15, TypeScript, and Tailwind CSS.

## Core Features

- **🏋️ Workout Builder**: Create custom workout plans with intuitive interface
- **📚 Exercise Database**: Access comprehensive exercise library with instructions
- **📊 Progress Tracking**: Monitor your fitness journey with detailed analytics  
- **▶️ Workout Sessions**: Execute workouts with guided sessions and tracking

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + Custom Components
- **State Management**: Zustand
- **Icons**: Lucide React

## Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## Project Philosophy

This project focuses on the core functionality of fitness coaching:
- **Simplicity**: Clean, focused interface without unnecessary features
- **Functionality**: Essential tools for workout planning and tracking
- **Performance**: Fast, responsive experience built on modern web technologies

## Available Pages

- `/` - Landing page with feature overview
- `/workouts` - Workout plans and session management
- `/exercises` - Exercise database and library
- `/progress` - Progress tracking and analytics

## Design Principles

- **Minimalist UI**: Focus on content and functionality
- **Core Features Only**: Workout builder, exercise library, progress tracking
- **Modern Stack**: Latest Next.js, TypeScript, and Tailwind CSS
- **Responsive Design**: Works seamlessly across all devices

## License

MIT
