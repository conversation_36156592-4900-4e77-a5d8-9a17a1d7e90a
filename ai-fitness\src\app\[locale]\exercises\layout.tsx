import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo/utils';
import { Breadcrumb } from '@/components/ui/breadcrumb';

export const metadata: Metadata = generatePageMetadata({
  title: "Exercise Database - Comprehensive Fitness Exercise Library",
  description: "Explore our comprehensive database of fitness exercises with detailed instructions, target muscles, equipment requirements, and difficulty levels. Perfect for building custom workout plans.",
  path: "/exercises",
});

export default function ExercisesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Exercises', href: '/exercises' }
  ];

  return (
    <div>
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      {children}
    </div>
  );
}
