{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/exercises.ts"], "sourcesContent": ["/**\n * Exercise API Service\n * Handles all exercise-related API calls\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport { WorkoutCoolAdapter, type WorkoutCoolExercise } from '../adapters/workout-cool.adapter';\nimport type { Exercise, ExerciseSearchParams, PaginatedResponse } from '../types';\n\nexport class ExerciseService {\n  /**\n   * Get all exercises with optional filtering\n   */\n  static async getExercises(params: ExerciseSearchParams = {}): Promise<PaginatedResponse<Exercise>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.search) {\n      searchParams.append('search', params.search);\n    }\n    \n    if (params.equipment?.length) {\n      params.equipment.forEach(eq => searchParams.append('equipment', eq));\n    }\n    \n    if (params.muscles?.length) {\n      params.muscles.forEach(muscle => searchParams.append('muscles', muscle));\n    }\n    \n    if (params.difficulty?.length) {\n      params.difficulty.forEach(diff => searchParams.append('difficulty', diff));\n    }\n    \n    if (params.category?.length) {\n      params.category.forEach(cat => searchParams.append('category', cat));\n    }\n    \n    if (params.limit) {\n      searchParams.append('limit', params.limit.toString());\n    }\n    \n    if (params.offset) {\n      searchParams.append('offset', params.offset.toString());\n    }\n\n    const queryString = searchParams.toString();\n    const url = queryString\n      ? `${API_CONFIG.ENDPOINTS.EXERCISES.PUBLIC_LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.EXERCISES.PUBLIC_LIST;\n\n    const response = await apiClient.get<WorkoutCoolExercise[]>(url);\n\n    // Adapt workout-cool exercises to our format\n    const adaptedExercises = response.map(exercise =>\n      WorkoutCoolAdapter.adaptExercise(exercise)\n    );\n\n    return {\n      data: adaptedExercises,\n      pagination: {\n        page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,\n        limit: params.limit || 10,\n        total: adaptedExercises.length,\n        totalPages: Math.ceil(adaptedExercises.length / (params.limit || 10)),\n      },\n    };\n  }\n\n  /**\n   * Search exercises by name or description - adapted for workout-cool API\n   */\n  static async searchExercises(query: string, limit = 20): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      search: query,\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.SEARCH}?${searchParams.toString()}`;\n\n    const response = await apiClient.get<WorkoutCoolExercise[]>(url);\n    return response.map(exercise => WorkoutCoolAdapter.adaptExercise(exercise));\n  }\n\n  /**\n   * Get exercise details by ID - adapted for workout-cool API\n   */\n  static async getExerciseById(id: string): Promise<Exercise> {\n    const url = API_CONFIG.ENDPOINTS.EXERCISES.DETAILS(id);\n    const response = await apiClient.get<WorkoutCoolExercise>(url);\n    return WorkoutCoolAdapter.adaptExercise(response);\n  }\n\n  /**\n   * Get exercise attributes (categories, equipment, muscles, etc.)\n   */\n  static async getExerciseAttributes(): Promise<{\n    categories: Array<{ id: string; name: string; nameEn: string }>;\n    equipment: Array<{ id: string; name: string; nameEn: string }>;\n    muscles: Array<{ id: string; name: string; nameEn: string }>;\n    difficulties: Array<{ id: string; name: string; nameEn: string }>;\n  }> {\n    return apiClient.get(API_CONFIG.ENDPOINTS.EXERCISES.ATTRIBUTES);\n  }\n\n  /**\n   * Get exercises by equipment and muscles (for workout builder)\n   */\n  static async getExercisesByFilters(\n    equipment: string[],\n    muscles: string[],\n    limit = 3\n  ): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams();\n    \n    equipment.forEach(eq => searchParams.append('equipment', eq));\n    muscles.forEach(muscle => searchParams.append('muscles', muscle));\n    searchParams.append('limit', limit.toString());\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get random exercises for suggestions\n   */\n  static async getRandomExercises(count = 6): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      random: 'true',\n      limit: count.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get popular exercises\n   */\n  static async getPopularExercises(limit = 10): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      sort: 'popular',\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get exercises by muscle group\n   */\n  static async getExercisesByMuscleGroup(muscleGroup: string, limit = 20): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      muscles: muscleGroup,\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get exercises by equipment type\n   */\n  static async getExercisesByEquipment(equipment: string, limit = 20): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      equipment: equipment,\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AAGO,MAAM;IACX;;GAEC,GACD,aAAa,aAAa,SAA+B,CAAC,CAAC,EAAwC;QACjG,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,MAAM,EAAE;YACjB,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7C;QAEA,IAAI,OAAO,SAAS,EAAE,QAAQ;YAC5B,OAAO,SAAS,CAAC,OAAO,CAAC,CAAA,KAAM,aAAa,MAAM,CAAC,aAAa;QAClE;QAEA,IAAI,OAAO,OAAO,EAAE,QAAQ;YAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA,SAAU,aAAa,MAAM,CAAC,WAAW;QAClE;QAEA,IAAI,OAAO,UAAU,EAAE,QAAQ;YAC7B,OAAO,UAAU,CAAC,OAAO,CAAC,CAAA,OAAQ,aAAa,MAAM,CAAC,cAAc;QACtE;QAEA,IAAI,OAAO,QAAQ,EAAE,QAAQ;YAC3B,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA,MAAO,aAAa,MAAM,CAAC,YAAY;QACjE;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpD;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtD;QAEA,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,aAAa,GAC9D,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW;QAE9C,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAwB;QAE5D,6CAA6C;QAC7C,MAAM,mBAAmB,SAAS,GAAG,CAAC,CAAA,WACpC,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;QAGnC,OAAO;YACL,MAAM;YACN,YAAY;gBACV,MAAM,KAAK,KAAK,CAAC,CAAC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,KAAK;gBAChE,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,iBAAiB,MAAM;gBAC9B,YAAY,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,CAAC,OAAO,KAAK,IAAI,EAAE;YACrE;QACF;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,KAAa,EAAE,QAAQ,EAAE,EAAuB;QAC3E,MAAM,eAAe,IAAI,gBAAgB;YACvC,QAAQ;YACR,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAEjF,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAwB;QAC5D,OAAO,SAAS,GAAG,CAAC,CAAA,WAAY,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;IACnE;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAqB;QAC1D,MAAM,MAAM,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC;QACnD,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAsB;QAC1D,OAAO,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;IAC1C;IAEA;;GAEC,GACD,aAAa,wBAKV;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU;IAChE;IAEA;;GAEC,GACD,aAAa,sBACX,SAAmB,EACnB,OAAiB,EACjB,QAAQ,CAAC,EACY;QACrB,MAAM,eAAe,IAAI;QAEzB,UAAU,OAAO,CAAC,CAAA,KAAM,aAAa,MAAM,CAAC,aAAa;QACzD,QAAQ,OAAO,CAAC,CAAA,SAAU,aAAa,MAAM,CAAC,WAAW;QACzD,aAAa,MAAM,CAAC,SAAS,MAAM,QAAQ;QAE3C,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,mBAAmB,QAAQ,CAAC,EAAuB;QAC9D,MAAM,eAAe,IAAI,gBAAgB;YACvC,QAAQ;YACR,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,oBAAoB,QAAQ,EAAE,EAAuB;QAChE,MAAM,eAAe,IAAI,gBAAgB;YACvC,MAAM;YACN,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,0BAA0B,WAAmB,EAAE,QAAQ,EAAE,EAAuB;QAC3F,MAAM,eAAe,IAAI,gBAAgB;YACvC,SAAS;YACT,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,wBAAwB,SAAiB,EAAE,QAAQ,EAAE,EAAuB;QACvF,MAAM,eAAe,IAAI,gBAAgB;YACvC,WAAW;YACX,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-exercises.ts"], "sourcesContent": ["/**\n * React Query hooks for exercise data\n */\n\nimport { useQuery, useInfiniteQuery } from '@tanstack/react-query';\nimport { ExerciseService } from '../api/services/exercises';\nimport type { ExerciseSearchParams } from '../api/types';\n\n// Query keys for consistent caching\nexport const exerciseKeys = {\n  all: ['exercises'] as const,\n  lists: () => [...exerciseKeys.all, 'list'] as const,\n  list: (params: ExerciseSearchParams) => [...exerciseKeys.lists(), params] as const,\n  details: () => [...exerciseKeys.all, 'detail'] as const,\n  detail: (id: string) => [...exerciseKeys.details(), id] as const,\n  search: (query: string) => [...exerciseKeys.all, 'search', query] as const,\n  attributes: () => [...exerciseKeys.all, 'attributes'] as const,\n  random: (count: number) => [...exerciseKeys.all, 'random', count] as const,\n  popular: (limit: number) => [...exerciseKeys.all, 'popular', limit] as const,\n  byMuscle: (muscle: string, limit: number) => [...exerciseKeys.all, 'muscle', muscle, limit] as const,\n  byEquipment: (equipment: string, limit: number) => [...exerciseKeys.all, 'equipment', equipment, limit] as const,\n  byFilters: (equipment: string[], muscles: string[], limit: number) => \n    [...exerciseKeys.all, 'filters', { equipment, muscles, limit }] as const,\n};\n\n/**\n * Hook to get exercises with filtering and pagination\n */\nexport function useExercises(params: ExerciseSearchParams = {}, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.list(params),\n    queryFn: () => ExerciseService.getExercises(params),\n    enabled,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook for infinite scrolling exercises list\n */\nexport function useInfiniteExercises(params: Omit<ExerciseSearchParams, 'offset'> = {}) {\n  return useInfiniteQuery({\n    queryKey: exerciseKeys.list(params),\n    queryFn: ({ pageParam = 0 }) => \n      ExerciseService.getExercises({ ...params, offset: pageParam }),\n    initialPageParam: 0,\n    getNextPageParam: (lastPage) => {\n      const { pagination } = lastPage;\n      return pagination.hasNext ? pagination.page * pagination.limit : undefined;\n    },\n    staleTime: 5 * 60 * 1000,\n  });\n}\n\n/**\n * Hook to search exercises by name\n */\nexport function useExerciseSearch(query: string, limit = 20, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.search(query),\n    queryFn: () => ExerciseService.searchExercises(query, limit),\n    enabled: enabled && query.length > 0,\n    staleTime: 2 * 60 * 1000, // 2 minutes for search results\n  });\n}\n\n/**\n * Hook to get exercise details by ID\n */\nexport function useExercise(id: string, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.detail(id),\n    queryFn: () => ExerciseService.getExerciseById(id),\n    enabled: enabled && !!id,\n    staleTime: 10 * 60 * 1000, // 10 minutes for exercise details\n  });\n}\n\n/**\n * Hook to get exercise attributes (categories, equipment, muscles, etc.)\n */\nexport function useExerciseAttributes() {\n  return useQuery({\n    queryKey: exerciseKeys.attributes(),\n    queryFn: () => ExerciseService.getExerciseAttributes(),\n    staleTime: 30 * 60 * 1000, // 30 minutes for attributes (rarely change)\n  });\n}\n\n/**\n * Hook to get exercises by equipment and muscles (for workout builder)\n */\nexport function useExercisesByFilters(\n  equipment: string[],\n  muscles: string[],\n  limit = 3,\n  enabled = true\n) {\n  return useQuery({\n    queryKey: exerciseKeys.byFilters(equipment, muscles, limit),\n    queryFn: () => ExerciseService.getExercisesByFilters(equipment, muscles, limit),\n    enabled: enabled && equipment.length > 0 && muscles.length > 0,\n    staleTime: 5 * 60 * 1000,\n  });\n}\n\n/**\n * Hook to get random exercises for suggestions\n */\nexport function useRandomExercises(count = 6, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.random(count),\n    queryFn: () => ExerciseService.getRandomExercises(count),\n    enabled,\n    staleTime: 2 * 60 * 1000, // 2 minutes for random exercises\n  });\n}\n\n/**\n * Hook to get popular exercises\n */\nexport function usePopularExercises(limit = 10, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.popular(limit),\n    queryFn: () => ExerciseService.getPopularExercises(limit),\n    enabled,\n    staleTime: 15 * 60 * 1000, // 15 minutes for popular exercises\n  });\n}\n\n/**\n * Hook to get exercises by muscle group\n */\nexport function useExercisesByMuscleGroup(muscleGroup: string, limit = 20, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.byMuscle(muscleGroup, limit),\n    queryFn: () => ExerciseService.getExercisesByMuscleGroup(muscleGroup, limit),\n    enabled: enabled && !!muscleGroup,\n    staleTime: 10 * 60 * 1000,\n  });\n}\n\n/**\n * Hook to get exercises by equipment type\n */\nexport function useExercisesByEquipment(equipment: string, limit = 20, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.byEquipment(equipment, limit),\n    queryFn: () => ExerciseService.getExercisesByEquipment(equipment, limit),\n    enabled: enabled && !!equipment,\n    staleTime: 10 * 60 * 1000,\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;AAED;AAAA;AACA;;;;AAIO,MAAM,eAAe;IAC1B,KAAK;QAAC;KAAY;IAClB,OAAO,IAAM;eAAI,aAAa,GAAG;YAAE;SAAO;IAC1C,MAAM,CAAC,SAAiC;eAAI,aAAa,KAAK;YAAI;SAAO;IACzE,SAAS,IAAM;eAAI,aAAa,GAAG;YAAE;SAAS;IAC9C,QAAQ,CAAC,KAAe;eAAI,aAAa,OAAO;YAAI;SAAG;IACvD,QAAQ,CAAC,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAU;SAAM;IACjE,YAAY,IAAM;eAAI,aAAa,GAAG;YAAE;SAAa;IACrD,QAAQ,CAAC,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAU;SAAM;IACjE,SAAS,CAAC,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAW;SAAM;IACnE,UAAU,CAAC,QAAgB,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAU;YAAQ;SAAM;IAC3F,aAAa,CAAC,WAAmB,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAa;YAAW;SAAM;IACvG,WAAW,CAAC,WAAqB,SAAmB,QAClD;eAAI,aAAa,GAAG;YAAE;YAAW;gBAAE;gBAAW;gBAAS;YAAM;SAAE;AACnE;AAKO,SAAS,aAAa,SAA+B,CAAC,CAAC,EAAE,UAAU,IAAI;;IAC5E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,IAAI,CAAC;QAC5B,OAAO;qCAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;;QAC5C;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,qBAAqB,SAA+C,CAAC,CAAC;;IACpF,OAAO,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,aAAa,IAAI,CAAC;QAC5B,OAAO;qDAAE,CAAC,EAAE,YAAY,CAAC,EAAE,GACzB,6IAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU;;QAC9D,kBAAkB;QAClB,gBAAgB;qDAAE,CAAC;gBACjB,MAAM,EAAE,UAAU,EAAE,GAAG;gBACvB,OAAO,WAAW,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,KAAK,GAAG;YACnE;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IAZgB;;QACP,sLAAA,CAAA,mBAAgB;;;AAgBlB,SAAS,kBAAkB,KAAa,EAAE,QAAQ,EAAE,EAAE,UAAU,IAAI;;IACzE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;0CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,OAAO;;QACtD,SAAS,WAAW,MAAM,MAAM,GAAG;QACnC,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,YAAY,EAAU,EAAE,UAAU,IAAI;;IACpD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;oCAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;;QAC/C,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,UAAU;QACjC,OAAO;8CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,qBAAqB;;QACpD,WAAW,KAAK,KAAK;IACvB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,sBACd,SAAmB,EACnB,OAAiB,EACjB,QAAQ,CAAC,EACT,UAAU,IAAI;;IAEd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,SAAS,CAAC,WAAW,SAAS;QACrD,OAAO;8CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,WAAW,SAAS;;QACzE,SAAS,WAAW,UAAU,MAAM,GAAG,KAAK,QAAQ,MAAM,GAAG;QAC7D,WAAW,IAAI,KAAK;IACtB;AACF;IAZgB;;QAMP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,mBAAmB,QAAQ,CAAC,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;2CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;;QAClD;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,oBAAoB,QAAQ,EAAE,EAAE,UAAU,IAAI;;IAC5D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,OAAO,CAAC;QAC/B,OAAO;4CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;;QACnD;QACA,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,0BAA0B,WAAmB,EAAE,QAAQ,EAAE,EAAE,UAAU,IAAI;;IACvF,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,QAAQ,CAAC,aAAa;QAC7C,OAAO;kDAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,aAAa;;QACtE,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,wBAAwB,SAAiB,EAAE,QAAQ,EAAE,EAAE,UAAU,IAAI;;IACnF,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,WAAW,CAAC,WAAW;QAC9C,OAAO;gDAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,uBAAuB,CAAC,WAAW;;QAClE,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-exercises.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Grid,\n  Button,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  Badge,\n  Paper,\n  Collapse,\n  FormGroup,\n  FormControlLabel,\n  Checkbox,\n  alpha,\n  useTheme,\n  CardMedia,\n  Avatar,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PlayArrow as PlayIcon,\n  Add as AddIcon,\n  Favorite as FavoriteIcon,\n  FavoriteBorder as FavoriteBorderIcon,\n  AccessTime as ClockIcon,\n  TrendingUp as TargetIcon,\n  Clear as ClearIcon,\n  FitnessCenter as FitnessCenterIcon,\n  Timer as TimerIcon,\n  Star as StarIcon,\n  Visibility as EyeIcon,\n  SportsMartialArts as MuscleIcon,\n  Build as EquipmentIcon,\n} from '@mui/icons-material';\nimport { \n  useExercises, \n  useExerciseSearch, \n  useExerciseAttributes,\n  useInfiniteExercises \n} from \"@/lib/hooks/use-exercises\";\nimport type { ExerciseSearchParams } from \"@/lib/api/types\";\nimport { useI18n } from '@/lib/i18n/context';\n\nexport function MuiExercises() {\n  const { t } = useI18n();\n  const theme = useTheme();\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [showFilters, setShowFilters] = useState(false);\n  const [selectedFilters, setSelectedFilters] = useState<ExerciseSearchParams>({\n    equipment: [],\n    muscles: [],\n    difficulty: [],\n    category: []\n  });\n\n  // Use search when there's a query, otherwise use filtered exercises\n  const { data: searchResults, isLoading: isSearching } = useExerciseSearch(\n    searchQuery, \n    20, \n    searchQuery ? selectedFilters : undefined\n  );\n\n  const { data: exercisesData, isLoading: isLoadingExercises } = useExercises({\n    limit: 20,\n    ...(!searchQuery ? selectedFilters : {})\n  });\n\n  const { data: attributesData } = useExerciseAttributes();\n\n  const exercises = searchQuery ? searchResults?.data : exercisesData?.data;\n  const isLoading = searchQuery ? isSearching : isLoadingExercises;\n\n  const handleFilterChange = (type: keyof ExerciseSearchParams, value: string) => {\n    setSelectedFilters(prev => {\n      const currentValues = prev[type] || [];\n      const newValues = currentValues.includes(value)\n        ? currentValues.filter(v => v !== value)\n        : [...currentValues, value];\n      \n      return {\n        ...prev,\n        [type]: newValues\n      };\n    });\n  };\n\n  const clearFilters = () => {\n    setSelectedFilters({\n      equipment: [],\n      muscles: [],\n      difficulty: [],\n      category: []\n    });\n  };\n\n  const hasActiveFilters = Object.values(selectedFilters).some(arr => arr && arr.length > 0);\n\n  const filteredExercises = exercises?.filter(exercise =>\n    searchQuery === \"\" ||\n    exercise.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    exercise.description?.toLowerCase().includes(searchQuery.toLowerCase())\n  ) || [];\n\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,\n          py: { xs: 6, md: 8 },\n          position: 'relative',\n          overflow: 'hidden',\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>\n            <Typography\n              variant=\"h2\"\n              sx={{\n                fontWeight: 700,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                mb: 2,\n                fontSize: { xs: '2.5rem', md: '3.5rem' }\n              }}\n            >\n              {t('exercises.title')}\n            </Typography>\n            <Typography\n              variant=\"h5\"\n              sx={{\n                color: 'text.secondary',\n                mb: 4,\n                fontWeight: 400,\n                fontSize: { xs: '1.25rem', md: '1.5rem' }\n              }}\n            >\n              {t('exercises.subtitle')}\n            </Typography>\n          </Box>\n        </Container>\n\n        {/* Floating Elements */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '20%',\n            right: '10%',\n            width: 80,\n            height: 80,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.secondary.main, 0.3)})`,\n            animation: 'float 6s ease-in-out infinite',\n          }}\n        />\n        <Box\n          sx={{\n            position: 'absolute',\n            bottom: '30%',\n            left: '5%',\n            width: 60,\n            height: 60,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,\n            animation: 'float 4s ease-in-out infinite reverse',\n          }}\n        />\n      </Box>\n\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        {/* Search and Filters */}\n        <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>\n          <Box sx={{ display: 'flex', gap: 2, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>\n            <TextField\n              fullWidth\n              placeholder={t('exercises.searchPlaceholder')}\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n              sx={{\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 3,\n                }\n              }}\n            />\n            <Button\n              variant=\"outlined\"\n              onClick={() => setShowFilters(!showFilters)}\n              startIcon={<FilterIcon />}\n              endIcon={hasActiveFilters && (\n                <Badge badgeContent={Object.values(selectedFilters).reduce((acc, arr) => acc + (arr?.length || 0), 0)} color=\"primary\">\n                  <Box />\n                </Badge>\n              )}\n              sx={{\n                borderRadius: 3,\n                minWidth: 120,\n                fontWeight: 600,\n                textTransform: 'none',\n              }}\n            >\n              筛选\n            </Button>\n          </Box>\n\n          <Collapse in={showFilters}>\n            <Box sx={{ pt: 3, borderTop: 1, borderColor: 'divider' }}>\n              <Grid container spacing={3}>\n                <Grid size={{ xs: 12, md: 3 }}>\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                    器械\n                  </Typography>\n                  <FormGroup>\n                    {['哑铃', '杠铃', '器械', '自重', '弹力带'].map((equipment) => (\n                      <FormControlLabel\n                        key={equipment}\n                        control={\n                          <Checkbox\n                            checked={selectedFilters.equipment?.includes(equipment) || false}\n                            onChange={() => handleFilterChange('equipment', equipment)}\n                            sx={{ color: theme.palette.primary.main }}\n                          />\n                        }\n                        label={equipment}\n                      />\n                    ))}\n                  </FormGroup>\n                </Grid>\n                <Grid size={{ xs: 12, md: 3 }}>\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                    目标肌群\n                  </Typography>\n                  <FormGroup>\n                    {['胸部', '背部', '肩部', '手臂', '腿部', '核心'].map((muscle) => (\n                      <FormControlLabel\n                        key={muscle}\n                        control={\n                          <Checkbox\n                            checked={selectedFilters.muscles?.includes(muscle) || false}\n                            onChange={() => handleFilterChange('muscles', muscle)}\n                            sx={{ color: theme.palette.secondary.main }}\n                          />\n                        }\n                        label={muscle}\n                      />\n                    ))}\n                  </FormGroup>\n                </Grid>\n                <Grid size={{ xs: 12, md: 3 }}>\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                    难度\n                  </Typography>\n                  <FormGroup>\n                    {['初级', '中级', '高级'].map((difficulty) => (\n                      <FormControlLabel\n                        key={difficulty}\n                        control={\n                          <Checkbox\n                            checked={selectedFilters.difficulty?.includes(difficulty) || false}\n                            onChange={() => handleFilterChange('difficulty', difficulty)}\n                            sx={{ color: '#9C27B0' }}\n                          />\n                        }\n                        label={difficulty}\n                      />\n                    ))}\n                  </FormGroup>\n                </Grid>\n                <Grid size={{ xs: 12, md: 3 }}>\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\n                    类别\n                  </Typography>\n                  <FormGroup>\n                    {['力量', '有氧', '柔韧', '平衡'].map((category) => (\n                      <FormControlLabel\n                        key={category}\n                        control={\n                          <Checkbox\n                            checked={selectedFilters.category?.includes(category) || false}\n                            onChange={() => handleFilterChange('category', category)}\n                            sx={{ color: '#FF9800' }}\n                          />\n                        }\n                        label={category}\n                      />\n                    ))}\n                  </FormGroup>\n                </Grid>\n              </Grid>\n              {hasActiveFilters && (\n                <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={clearFilters}\n                    startIcon={<ClearIcon />}\n                    sx={{ borderRadius: 3, textTransform: 'none' }}\n                  >\n                    清除筛选\n                  </Button>\n                </Box>\n              )}\n            </Box>\n          </Collapse>\n        </Paper>\n\n        {/* Exercises Grid */}\n        <Box>\n          <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>\n            {searchQuery ? `搜索结果: \"${searchQuery}\"` : '所有动作'}\n          </Typography>\n\n          <Grid container spacing={3}>\n            {isLoading ? (\n              Array.from({ length: 12 }).map((_, index) => (\n                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={index}>\n                  <Card sx={{ height: 320, borderRadius: 3 }}>\n                    <Box sx={{ height: 180, bgcolor: 'grey.200', animation: 'pulse 1.5s ease-in-out infinite' }} />\n                    <CardContent sx={{ p: 2 }}>\n                      <Box sx={{ bgcolor: 'grey.200', height: 20, borderRadius: 1, mb: 1, animation: 'pulse 1.5s ease-in-out infinite' }} />\n                      <Box sx={{ bgcolor: 'grey.200', height: 16, borderRadius: 1, animation: 'pulse 1.5s ease-in-out infinite' }} />\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))\n            ) : filteredExercises.length === 0 ? (\n              <Grid size={12}>\n                <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>\n                  <FitnessCenterIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n                  <Typography variant=\"h6\" sx={{ mb: 1 }}>\n                    {searchQuery ? '未找到相关动作' : '暂无健身动作'}\n                  </Typography>\n                  <Typography color=\"text.secondary\">\n                    {searchQuery ? '尝试调整搜索条件' : '敬请期待更多精彩内容'}\n                  </Typography>\n                </Paper>\n              </Grid>\n            ) : (\n              filteredExercises.map((exercise) => (\n                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={exercise.id}>\n                  <Card\n                    sx={{\n                      height: '100%',\n                      borderRadius: 3,\n                      transition: 'all 0.3s ease',\n                      '&:hover': {\n                        transform: 'translateY(-4px)',\n                        boxShadow: theme.shadows[8],\n                      }\n                    }}\n                  >\n                    {/* Exercise Image/Video Placeholder */}\n                    <Box\n                      sx={{\n                        height: 180,\n                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        position: 'relative',\n                      }}\n                    >\n                      <FitnessCenterIcon sx={{ fontSize: 48, color: theme.palette.primary.main, opacity: 0.7 }} />\n                      <IconButton\n                        sx={{\n                          position: 'absolute',\n                          top: 8,\n                          right: 8,\n                          bgcolor: 'rgba(255,255,255,0.9)',\n                          '&:hover': { bgcolor: 'white' }\n                        }}\n                        size=\"small\"\n                      >\n                        <FavoriteBorderIcon fontSize=\"small\" />\n                      </IconButton>\n                      <Box\n                        sx={{\n                          position: 'absolute',\n                          bottom: 8,\n                          right: 8,\n                          bgcolor: 'rgba(0,0,0,0.7)',\n                          color: 'white',\n                          px: 1,\n                          py: 0.5,\n                          borderRadius: 1,\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 0.5,\n                        }}\n                      >\n                        <PlayIcon fontSize=\"small\" />\n                        <Typography variant=\"caption\">演示</Typography>\n                      </Box>\n                    </Box>\n\n                    <CardContent sx={{ p: 2, height: 140, display: 'flex', flexDirection: 'column' }}>\n                      <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1, fontSize: '1rem' }}>\n                        {exercise.name}\n                      </Typography>\n                      <Typography\n                        variant=\"body2\"\n                        color=\"text.secondary\"\n                        sx={{\n                          mb: 2,\n                          flex: 1,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          display: '-webkit-box',\n                          WebkitLineClamp: 2,\n                          WebkitBoxOrient: 'vertical',\n                        }}\n                      >\n                        {exercise.description || '专业健身动作指导'}\n                      </Typography>\n\n                      <Box sx={{ display: 'flex', gap: 0.5, mb: 2, flexWrap: 'wrap' }}>\n                        <Chip\n                          icon={<MuscleIcon />}\n                          label={exercise.primaryMuscles?.[0] || '全身'}\n                          size=\"small\"\n                          variant=\"outlined\"\n                          sx={{ fontSize: '0.7rem', height: 24 }}\n                        />\n                        <Chip\n                          icon={<EquipmentIcon />}\n                          label={exercise.equipment || '自重'}\n                          size=\"small\"\n                          variant=\"outlined\"\n                          sx={{ fontSize: '0.7rem', height: 24 }}\n                        />\n                      </Box>\n\n                      <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>\n                        <Button\n                          variant=\"contained\"\n                          size=\"small\"\n                          startIcon={<EyeIcon />}\n                          sx={{\n                            flex: 1,\n                            borderRadius: 2,\n                            textTransform: 'none',\n                            fontWeight: 600,\n                            fontSize: '0.8rem',\n                            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                          }}\n                        >\n                          查看\n                        </Button>\n                        <Button\n                          variant=\"outlined\"\n                          size=\"small\"\n                          sx={{ borderRadius: 2, minWidth: 'auto', px: 1 }}\n                        >\n                          <AddIcon fontSize=\"small\" />\n                        </Button>\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))\n            )}\n          </Grid>\n\n          {/* Load More Button */}\n          {!isLoading && filteredExercises.length > 0 && (\n            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                sx={{\n                  borderRadius: 3,\n                  textTransform: 'none',\n                  fontWeight: 600,\n                  px: 4,\n                }}\n              >\n                加载更多\n              </Button>\n            </Box>\n          )}\n        </Box>\n      </Container>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAOA;;;AAlDA;;;;;;;;;;;;;;;AAoDO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QAC3E,WAAW,EAAE;QACb,SAAS,EAAE;QACX,YAAY,EAAE;QACd,UAAU,EAAE;IACd;IAEA,oEAAoE;IACpE,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EACtE,aACA,IACA,cAAc,kBAAkB;IAGlC,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,eAAY,AAAD,EAAE;QAC1E,OAAO;QACP,GAAI,CAAC,cAAc,kBAAkB,CAAC,CAAC;IACzC;IAEA,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,wBAAqB,AAAD;IAErD,MAAM,YAAY,cAAc,eAAe,OAAO,eAAe;IACrE,MAAM,YAAY,cAAc,cAAc;IAE9C,MAAM,qBAAqB,CAAC,MAAkC;QAC5D,mBAAmB,CAAA;YACjB,MAAM,gBAAgB,IAAI,CAAC,KAAK,IAAI,EAAE;YACtC,MAAM,YAAY,cAAc,QAAQ,CAAC,SACrC,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM,SAChC;mBAAI;gBAAe;aAAM;YAE7B,OAAO;gBACL,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,eAAe;QACnB,mBAAmB;YACjB,WAAW,EAAE;YACb,SAAS,EAAE;YACX,YAAY,EAAE;YACd,UAAU,EAAE;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAA,MAAO,OAAO,IAAI,MAAM,GAAG;IAExF,MAAM,oBAAoB,WAAW,OAAO,CAAA,WAC1C,gBAAgB,MAChB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,QACjE,EAAE;IAEP,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,WAAW;YAAS,SAAS;QAAqB;;0BAE3D,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;oBACrI,IAAI;wBAAE,IAAI;wBAAG,IAAI;oBAAE;oBACnB,UAAU;oBACV,UAAU;gBACZ;;kCAEA,6LAAC,6MAAA,CAAA,YAAS;wBAAC,UAAS;kCAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,WAAW;gCAAU,UAAU;gCAAY,QAAQ;4BAAE;;8CAC9D,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,YAAY;wCACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACpG,gBAAgB;wCAChB,sBAAsB;wCACtB,qBAAqB;wCACrB,IAAI;wCACJ,UAAU;4CAAE,IAAI;4CAAU,IAAI;wCAAS;oCACzC;8CAEC,EAAE;;;;;;8CAEL,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,OAAO;wCACP,IAAI;wCACJ,YAAY;wCACZ,UAAU;4CAAE,IAAI;4CAAW,IAAI;wCAAS;oCAC1C;8CAEC,EAAE;;;;;;;;;;;;;;;;;kCAMT,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;kCAEF,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;;;;;;;0BAIJ,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;;kCAEnC,6LAAC,iMAAA,CAAA,QAAK;wBAAC,IAAI;4BAAE,GAAG;4BAAG,IAAI;4BAAG,cAAc;wBAAE;;0CACxC,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,KAAK;oCAAG,IAAI;oCAAG,eAAe;wCAAE,IAAI;wCAAU,IAAI;oCAAM;gCAAE;;kDACpF,6LAAC,6MAAA,CAAA,YAAS;wCACR,SAAS;wCACT,aAAa,EAAE;wCACf,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,YAAY;4CACV,8BACE,6LAAC,4NAAA,CAAA,iBAAc;gDAAC,UAAS;0DACvB,cAAA,6LAAC,8JAAA,CAAA,UAAU;oDAAC,OAAM;;;;;;;;;;;wCAGxB;wCACA,IAAI;4CACF,4BAA4B;gDAC1B,cAAc;4CAChB;wCACF;;;;;;kDAEF,6LAAC,oMAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe,CAAC;wCAC/B,yBAAW,6LAAC,kKAAA,CAAA,UAAU;;;;;wCACtB,SAAS,kCACP,6LAAC,iMAAA,CAAA,QAAK;4CAAC,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,KAAK,UAAU,CAAC,GAAG;4CAAI,OAAM;sDAC3G,cAAA,6LAAC,2LAAA,CAAA,MAAG;;;;;;;;;;wCAGR,IAAI;4CACF,cAAc;4CACd,UAAU;4CACV,YAAY;4CACZ,eAAe;wCACjB;kDACD;;;;;;;;;;;;0CAKH,6LAAC,0MAAA,CAAA,WAAQ;gCAAC,IAAI;0CACZ,cAAA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,IAAI;wCAAG,WAAW;wCAAG,aAAa;oCAAU;;sDACrD,6LAAC,8LAAA,CAAA,OAAI;4CAAC,SAAS;4CAAC,SAAS;;8DACvB,6LAAC,8LAAA,CAAA,OAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAY,IAAI;gEAAE,YAAY;gEAAK,IAAI;4DAAE;sEAAG;;;;;;sEAGhE,6LAAC,6MAAA,CAAA,YAAS;sEACP;gEAAC;gEAAM;gEAAM;gEAAM;gEAAM;6DAAM,CAAC,GAAG,CAAC,CAAC,0BACpC,6LAAC,kOAAA,CAAA,mBAAgB;oEAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;wEACP,SAAS,gBAAgB,SAAS,EAAE,SAAS,cAAc;wEAC3D,UAAU,IAAM,mBAAmB,aAAa;wEAChD,IAAI;4EAAE,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;wEAAC;;;;;;oEAG5C,OAAO;mEARF;;;;;;;;;;;;;;;;8DAab,6LAAC,8LAAA,CAAA,OAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAY,IAAI;gEAAE,YAAY;gEAAK,IAAI;4DAAE;sEAAG;;;;;;sEAGhE,6LAAC,6MAAA,CAAA,YAAS;sEACP;gEAAC;gEAAM;gEAAM;gEAAM;gEAAM;gEAAM;6DAAK,CAAC,GAAG,CAAC,CAAC,uBACzC,6LAAC,kOAAA,CAAA,mBAAgB;oEAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;wEACP,SAAS,gBAAgB,OAAO,EAAE,SAAS,WAAW;wEACtD,UAAU,IAAM,mBAAmB,WAAW;wEAC9C,IAAI;4EAAE,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI;wEAAC;;;;;;oEAG9C,OAAO;mEARF;;;;;;;;;;;;;;;;8DAab,6LAAC,8LAAA,CAAA,OAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAY,IAAI;gEAAE,YAAY;gEAAK,IAAI;4DAAE;sEAAG;;;;;;sEAGhE,6LAAC,6MAAA,CAAA,YAAS;sEACP;gEAAC;gEAAM;gEAAM;6DAAK,CAAC,GAAG,CAAC,CAAC,2BACvB,6LAAC,kOAAA,CAAA,mBAAgB;oEAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;wEACP,SAAS,gBAAgB,UAAU,EAAE,SAAS,eAAe;wEAC7D,UAAU,IAAM,mBAAmB,cAAc;wEACjD,IAAI;4EAAE,OAAO;wEAAU;;;;;;oEAG3B,OAAO;mEARF;;;;;;;;;;;;;;;;8DAab,6LAAC,8LAAA,CAAA,OAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAY,IAAI;gEAAE,YAAY;gEAAK,IAAI;4DAAE;sEAAG;;;;;;sEAGhE,6LAAC,6MAAA,CAAA,YAAS;sEACP;gEAAC;gEAAM;gEAAM;gEAAM;6DAAK,CAAC,GAAG,CAAC,CAAC,yBAC7B,6LAAC,kOAAA,CAAA,mBAAgB;oEAEf,uBACE,6LAAC,0MAAA,CAAA,WAAQ;wEACP,SAAS,gBAAgB,QAAQ,EAAE,SAAS,aAAa;wEACzD,UAAU,IAAM,mBAAmB,YAAY;wEAC/C,IAAI;4EAAE,OAAO;wEAAU;;;;;;oEAG3B,OAAO;mEARF;;;;;;;;;;;;;;;;;;;;;;wCAcd,kCACC,6LAAC,2LAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,IAAI;gDAAG,IAAI;gDAAG,WAAW;gDAAG,aAAa;4CAAU;sDAC5D,cAAA,6LAAC,oMAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,yBAAW,6LAAC,6JAAA,CAAA,UAAS;;;;;gDACrB,IAAI;oDAAE,cAAc;oDAAG,eAAe;gDAAO;0DAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,6LAAC,2LAAA,CAAA,MAAG;;0CACF,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,IAAI;oCAAE,YAAY;oCAAK,IAAI;oCAAG,OAAO;gCAAe;0CAC1E,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,GAAG;;;;;;0CAG5C,6LAAC,8LAAA,CAAA,OAAI;gCAAC,SAAS;gCAAC,SAAS;0CACtB,YACC,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,8LAAA,CAAA,OAAI;wCAAC,MAAM;4CAAE,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;wCAAE;kDACxC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4CAAC,IAAI;gDAAE,QAAQ;gDAAK,cAAc;4CAAE;;8DACvC,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,QAAQ;wDAAK,SAAS;wDAAY,WAAW;oDAAkC;;;;;;8DAC1F,6LAAC,mNAAA,CAAA,cAAW;oDAAC,IAAI;wDAAE,GAAG;oDAAE;;sEACtB,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAY,QAAQ;gEAAI,cAAc;gEAAG,IAAI;gEAAG,WAAW;4DAAkC;;;;;;sEACjH,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAY,QAAQ;gEAAI,cAAc;gEAAG,WAAW;4DAAkC;;;;;;;;;;;;;;;;;;uCAL9D;;;;gDAUlD,kBAAkB,MAAM,KAAK,kBAC/B,6LAAC,8LAAA,CAAA,OAAI;oCAAC,MAAM;8CACV,cAAA,6LAAC,iMAAA,CAAA,QAAK;wCAAC,IAAI;4CAAE,GAAG;4CAAG,WAAW;4CAAU,cAAc;wCAAE;;0DACtD,6LAAC,qKAAA,CAAA,UAAiB;gDAAC,IAAI;oDAAE,UAAU;oDAAI,OAAO;oDAAkB,IAAI;gDAAE;;;;;;0DACtE,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,IAAI;gDAAE;0DAClC,cAAc,YAAY;;;;;;0DAE7B,6LAAC,gNAAA,CAAA,aAAU;gDAAC,OAAM;0DACf,cAAc,aAAa;;;;;;;;;;;;;;;;2CAKlC,kBAAkB,GAAG,CAAC,CAAC,yBACrB,6LAAC,8LAAA,CAAA,OAAI;wCAAC,MAAM;4CAAE,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;wCAAE;kDACxC,cAAA,6LAAC,8LAAA,CAAA,OAAI;4CACH,IAAI;gDACF,QAAQ;gDACR,cAAc;gDACd,YAAY;gDACZ,WAAW;oDACT,WAAW;oDACX,WAAW,MAAM,OAAO,CAAC,EAAE;gDAC7B;4CACF;;8DAGA,6LAAC,2LAAA,CAAA,MAAG;oDACF,IAAI;wDACF,QAAQ;wDACR,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wDAC7H,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,UAAU;oDACZ;;sEAEA,6LAAC,qKAAA,CAAA,UAAiB;4DAAC,IAAI;gEAAE,UAAU;gEAAI,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;gEAAE,SAAS;4DAAI;;;;;;sEACvF,6LAAC,gNAAA,CAAA,aAAU;4DACT,IAAI;gEACF,UAAU;gEACV,KAAK;gEACL,OAAO;gEACP,SAAS;gEACT,WAAW;oEAAE,SAAS;gEAAQ;4DAChC;4DACA,MAAK;sEAEL,cAAA,6LAAC,sKAAA,CAAA,UAAkB;gEAAC,UAAS;;;;;;;;;;;sEAE/B,6LAAC,2LAAA,CAAA,MAAG;4DACF,IAAI;gEACF,UAAU;gEACV,QAAQ;gEACR,OAAO;gEACP,SAAS;gEACT,OAAO;gEACP,IAAI;gEACJ,IAAI;gEACJ,cAAc;gEACd,SAAS;gEACT,YAAY;gEACZ,KAAK;4DACP;;8EAEA,6LAAC,iKAAA,CAAA,UAAQ;oEAAC,UAAS;;;;;;8EACnB,6LAAC,gNAAA,CAAA,aAAU;oEAAC,SAAQ;8EAAU;;;;;;;;;;;;;;;;;;8DAIlC,6LAAC,mNAAA,CAAA,cAAW;oDAAC,IAAI;wDAAE,GAAG;wDAAG,QAAQ;wDAAK,SAAS;wDAAQ,eAAe;oDAAS;;sEAC7E,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAK,IAAI;gEAAE,YAAY;gEAAK,IAAI;gEAAG,UAAU;4DAAO;sEACrE,SAAS,IAAI;;;;;;sEAEhB,6LAAC,gNAAA,CAAA,aAAU;4DACT,SAAQ;4DACR,OAAM;4DACN,IAAI;gEACF,IAAI;gEACJ,MAAM;gEACN,UAAU;gEACV,cAAc;gEACd,SAAS;gEACT,iBAAiB;gEACjB,iBAAiB;4DACnB;sEAEC,SAAS,WAAW,IAAI;;;;;;sEAG3B,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,KAAK;gEAAK,IAAI;gEAAG,UAAU;4DAAO;;8EAC5D,6LAAC,8LAAA,CAAA,OAAI;oEACH,oBAAM,6LAAC,yKAAA,CAAA,UAAU;;;;;oEACjB,OAAO,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;oEACvC,MAAK;oEACL,SAAQ;oEACR,IAAI;wEAAE,UAAU;wEAAU,QAAQ;oEAAG;;;;;;8EAEvC,6LAAC,8LAAA,CAAA,OAAI;oEACH,oBAAM,6LAAC,6JAAA,CAAA,UAAa;;;;;oEACpB,OAAO,SAAS,SAAS,IAAI;oEAC7B,MAAK;oEACL,SAAQ;oEACR,IAAI;wEAAE,UAAU;wEAAU,QAAQ;oEAAG;;;;;;;;;;;;sEAIzC,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,KAAK;gEAAG,IAAI;4DAAO;;8EAC7C,6LAAC,oMAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,yBAAW,6LAAC,kKAAA,CAAA,UAAO;;;;;oEACnB,IAAI;wEACF,MAAM;wEACN,cAAc;wEACd,eAAe;wEACf,YAAY;wEACZ,UAAU;wEACV,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oEACtG;8EACD;;;;;;8EAGD,6LAAC,oMAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,IAAI;wEAAE,cAAc;wEAAG,UAAU;wEAAQ,IAAI;oEAAE;8EAE/C,cAAA,6LAAC,2JAAA,CAAA,UAAO;wEAAC,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAlHsB,SAAS,EAAE;;;;;;;;;;4BA6HlE,CAAC,aAAa,kBAAkB,MAAM,GAAG,mBACxC,6LAAC,2LAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,gBAAgB;oCAAU,IAAI;gCAAE;0CAC1D,cAAA,6LAAC,oMAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,IAAI;wCACF,cAAc;wCACd,eAAe;wCACf,YAAY;wCACZ,IAAI;oCACN;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhcgB;;QACA,iIAAA,CAAA,UAAO;QACP,wMAAA,CAAA,WAAQ;QAWkC,0IAAA,CAAA,oBAAiB;QAMV,0IAAA,CAAA,eAAY;QAK1C,0IAAA,CAAA,wBAAqB;;;KAxBxC", "debugId": null}}]}