/**
 * Exercise API Service
 * Handles all exercise-related API calls
 */

import { apiClient } from '../client';
import { API_CONFIG } from '../config';
import { WorkoutCoolAdapter, type WorkoutCoolExercise } from '../adapters/workout-cool.adapter';
import type { Exercise, ExerciseSearchParams, PaginatedResponse } from '../types';

export class ExerciseService {
  /**
   * Get all exercises with optional filtering
   */
  static async getExercises(params: ExerciseSearchParams = {}): Promise<PaginatedResponse<Exercise>> {
    const searchParams = new URLSearchParams();
    
    if (params.search) {
      searchParams.append('search', params.search);
    }
    
    if (params.equipment?.length) {
      params.equipment.forEach(eq => searchParams.append('equipment', eq));
    }
    
    if (params.muscles?.length) {
      params.muscles.forEach(muscle => searchParams.append('muscles', muscle));
    }
    
    if (params.difficulty?.length) {
      params.difficulty.forEach(diff => searchParams.append('difficulty', diff));
    }
    
    if (params.category?.length) {
      params.category.forEach(cat => searchParams.append('category', cat));
    }
    
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }
    
    if (params.offset) {
      searchParams.append('offset', params.offset.toString());
    }

    const queryString = searchParams.toString();
    const url = queryString
      ? `${API_CONFIG.ENDPOINTS.EXERCISES.PUBLIC_LIST}?${queryString}`
      : API_CONFIG.ENDPOINTS.EXERCISES.PUBLIC_LIST;

    const response = await apiClient.get<WorkoutCoolExercise[]>(url);

    // Adapt workout-cool exercises to our format
    const adaptedExercises = response.map(exercise =>
      WorkoutCoolAdapter.adaptExercise(exercise)
    );

    return {
      data: adaptedExercises,
      pagination: {
        page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,
        limit: params.limit || 10,
        total: adaptedExercises.length,
        totalPages: Math.ceil(adaptedExercises.length / (params.limit || 10)),
      },
    };
  }

  /**
   * Search exercises by name or description - adapted for workout-cool API
   */
  static async searchExercises(query: string, limit = 20): Promise<Exercise[]> {
    const searchParams = new URLSearchParams({
      search: query,
      limit: limit.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.SEARCH}?${searchParams.toString()}`;

    const response = await apiClient.get<WorkoutCoolExercise[]>(url);
    return response.map(exercise => WorkoutCoolAdapter.adaptExercise(exercise));
  }

  /**
   * Get exercise details by ID - adapted for workout-cool API
   */
  static async getExerciseById(id: string): Promise<Exercise> {
    const url = API_CONFIG.ENDPOINTS.EXERCISES.DETAILS(id);
    const response = await apiClient.get<WorkoutCoolExercise>(url);
    return WorkoutCoolAdapter.adaptExercise(response);
  }

  /**
   * Get exercise attributes (categories, equipment, muscles, etc.)
   */
  static async getExerciseAttributes(): Promise<{
    categories: Array<{ id: string; name: string; nameEn: string }>;
    equipment: Array<{ id: string; name: string; nameEn: string }>;
    muscles: Array<{ id: string; name: string; nameEn: string }>;
    difficulties: Array<{ id: string; name: string; nameEn: string }>;
  }> {
    return apiClient.get(API_CONFIG.ENDPOINTS.EXERCISES.ATTRIBUTES);
  }

  /**
   * Get exercises by equipment and muscles (for workout builder)
   */
  static async getExercisesByFilters(
    equipment: string[],
    muscles: string[],
    limit = 3
  ): Promise<Exercise[]> {
    const searchParams = new URLSearchParams();
    
    equipment.forEach(eq => searchParams.append('equipment', eq));
    muscles.forEach(muscle => searchParams.append('muscles', muscle));
    searchParams.append('limit', limit.toString());

    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;
    
    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);
    return response.data || [];
  }

  /**
   * Get random exercises for suggestions
   */
  static async getRandomExercises(count = 6): Promise<Exercise[]> {
    const searchParams = new URLSearchParams({
      random: 'true',
      limit: count.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;
    
    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);
    return response.data || [];
  }

  /**
   * Get popular exercises
   */
  static async getPopularExercises(limit = 10): Promise<Exercise[]> {
    const searchParams = new URLSearchParams({
      sort: 'popular',
      limit: limit.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;
    
    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);
    return response.data || [];
  }

  /**
   * Get exercises by muscle group
   */
  static async getExercisesByMuscleGroup(muscleGroup: string, limit = 20): Promise<Exercise[]> {
    const searchParams = new URLSearchParams({
      muscles: muscleGroup,
      limit: limit.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;
    
    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);
    return response.data || [];
  }

  /**
   * Get exercises by equipment type
   */
  static async getExercisesByEquipment(equipment: string, limit = 20): Promise<Exercise[]> {
    const searchParams = new URLSearchParams({
      equipment: equipment,
      limit: limit.toString(),
    });

    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;
    
    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);
    return response.data || [];
  }
}
