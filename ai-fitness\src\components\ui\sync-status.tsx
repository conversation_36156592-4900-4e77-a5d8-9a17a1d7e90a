"use client"

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Wifi,
  WifiOff,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Database,
  Settings,
  Bell,
  BellOff
} from 'lucide-react';
import {
  useOfflineState,
  useNotifications,
  useUnreadNotificationCount,
  useHasPendingSync,
  useSettings,
  useAppStore
} from '@/lib/store/app-store';
import { useSyncManager, useSyncStatus } from '@/lib/sync/sync-manager';

/**
 * Sync Status Component
 * Shows current sync status and allows manual sync
 */
export function SyncStatus() {
  const [isExpanded, setIsExpanded] = useState(false);
  const offlineState = useOfflineState();
  const syncStatus = useSyncStatus();
  const hasPendingSync = useHasPendingSync();
  const { syncPendingData, forceSyncAll, clearPendingSync } = useSyncManager();

  const handleSync = async () => {
    try {
      await syncPendingData();
    } catch (error) {
      console.error('Manual sync failed:', error);
    }
  };

  const handleForceSync = async () => {
    try {
      await forceSyncAll();
    } catch (error) {
      console.error('Force sync failed:', error);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {offlineState.isOnline ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
            <CardTitle className="text-sm">
              {offlineState.isOnline ? 'Online' : 'Offline'}
            </CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>
          {hasPendingSync ? (
            <span className="text-orange-600">
              {offlineState.pendingSync.length} items pending sync
            </span>
          ) : (
            'All data synchronized'
          )}
        </CardDescription>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Sync Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Sync Status:</span>
              <Badge variant={hasPendingSync ? 'destructive' : 'default'}>
                {hasPendingSync ? 'Pending' : 'Up to date'}
              </Badge>
            </div>
            
            {offlineState.lastSyncTime && (
              <div className="flex items-center justify-between text-sm">
                <span>Last Sync:</span>
                <span className="text-gray-600">
                  {new Date(offlineState.lastSyncTime).toLocaleTimeString()}
                </span>
              </div>
            )}

            <div className="flex items-center justify-between text-sm">
              <span>Pending Items:</span>
              <span className="text-gray-600">
                {offlineState.pendingSync.length}
              </span>
            </div>
          </div>

          {/* Sync Actions */}
          <div className="space-y-2">
            <Button
              onClick={handleSync}
              disabled={!offlineState.isOnline || syncStatus.syncInProgress}
              className="w-full"
              size="sm"
            >
              {syncStatus.syncInProgress ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Sync Now
                </>
              )}
            </Button>

            {hasPendingSync && (
              <div className="flex gap-2">
                <Button
                  onClick={handleForceSync}
                  disabled={!offlineState.isOnline}
                  variant="outline"
                  size="sm"
                  className="flex-1"
                >
                  Force Sync
                </Button>
                <Button
                  onClick={clearPendingSync}
                  variant="destructive"
                  size="sm"
                  className="flex-1"
                >
                  Clear Queue
                </Button>
              </div>
            )}
          </div>

          {/* Pending Items List */}
          {offlineState.pendingSync.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Pending Items:</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {offlineState.pendingSync.slice(0, 5).map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between text-xs p-2 bg-gray-50 rounded"
                  >
                    <span className="capitalize">
                      {item.action} {item.type}
                    </span>
                    <Clock className="h-3 w-3 text-gray-400" />
                  </div>
                ))}
                {offlineState.pendingSync.length > 5 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{offlineState.pendingSync.length - 5} more items
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Notification Center Component
 * Shows app notifications and allows management
 */
export function NotificationCenter() {
  const [isOpen, setIsOpen] = useState(false);
  const notifications = useNotifications();
  const unreadCount = useUnreadNotificationCount();
  const settings = useSettings();
  const { markNotificationRead, clearNotifications, updateSettings } = useAppStore();

  const toggleNotifications = () => {
    updateSettings({
      notifications: {
        ...settings.notifications,
        workoutReminders: !settings.notifications.workoutReminders,
      },
    });
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {settings.notifications.workoutReminders ? (
              <Bell className="h-4 w-4 text-blue-600" />
            ) : (
              <BellOff className="h-4 w-4 text-gray-400" />
            )}
            <CardTitle className="text-sm">Notifications</CardTitle>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      {isOpen && (
        <CardContent className="space-y-4">
          {/* Notification Settings */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Workout Reminders</span>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleNotifications}
              >
                {settings.notifications.workoutReminders ? 'On' : 'Off'}
              </Button>
            </div>
          </div>

          {/* Recent Notifications */}
          {notifications.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Recent</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearNotifications}
                >
                  Clear All
                </Button>
              </div>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {notifications.slice(0, 5).map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-2 rounded text-xs ${
                      notification.read ? 'bg-gray-50' : 'bg-blue-50'
                    }`}
                    onClick={() => markNotificationRead(notification.id)}
                  >
                    <div className="flex items-start gap-2">
                      {notification.type === 'success' && (
                        <CheckCircle className="h-3 w-3 text-green-600 mt-0.5" />
                      )}
                      {notification.type === 'error' && (
                        <AlertCircle className="h-3 w-3 text-red-600 mt-0.5" />
                      )}
                      {notification.type === 'warning' && (
                        <AlertCircle className="h-3 w-3 text-orange-600 mt-0.5" />
                      )}
                      {notification.type === 'info' && (
                        <AlertCircle className="h-3 w-3 text-blue-600 mt-0.5" />
                      )}
                      <div className="flex-1">
                        <div className="font-medium">{notification.title}</div>
                        <div className="text-gray-600">{notification.message}</div>
                        <div className="text-gray-400 mt-1">
                          {new Date(notification.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {notifications.length === 0 && (
            <div className="text-center py-4 text-gray-500 text-sm">
              No notifications
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
