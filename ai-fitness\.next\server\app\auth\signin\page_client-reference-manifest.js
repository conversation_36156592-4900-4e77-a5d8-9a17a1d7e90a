globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/signin/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"86346","name":"*","chunks":[],"async":false}},"1169":{"*":{"id":"85343","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"97173","name":"*","chunks":[],"async":false}},"2985":{"*":{"id":"38926","name":"*","chunks":[],"async":false}},"3262":{"*":{"id":"13966","name":"*","chunks":[],"async":false}},"3738":{"*":{"id":"43476","name":"*","chunks":[],"async":false}},"3792":{"*":{"id":"75694","name":"*","chunks":[],"async":false}},"4281":{"*":{"id":"67997","name":"*","chunks":[],"async":false}},"4403":{"*":{"id":"50482","name":"*","chunks":[],"async":false}},"4820":{"*":{"id":"70058","name":"*","chunks":[],"async":false}},"4879":{"*":{"id":"58061","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"28827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"27924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"35656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"40099","name":"*","chunks":[],"async":false}},"7080":{"*":{"id":"26608","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"38243","name":"*","chunks":[],"async":false}},"8035":{"*":{"id":"57858","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"62763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":5356,"name":"*","chunks":["177","static/chunks/app/layout-6606529bb353912a.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-6606529bb353912a.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\providers\\app-providers.tsx":{"id":7080,"name":"*","chunks":["177","static/chunks/app/layout-6606529bb353912a.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signin\\page.tsx":{"id":3738,"name":"*","chunks":["680","static/chunks/app/auth/signin/page-8f5e3fab00084e47.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signup\\page.tsx":{"id":4820,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\dashboard\\page.tsx":{"id":4879,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\demo\\page.tsx":{"id":8035,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\[id]\\page.tsx":{"id":3262,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\page.tsx":{"id":4403,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page.tsx":{"id":3792,"name":"*","chunks":["974","static/chunks/app/page-d89ce877e7d770a7.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\progress\\page.tsx":{"id":2985,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\[id]\\page.tsx":{"id":1169,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\page.tsx":{"id":4281,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout":[{"inlined":false,"path":"static/css/89f2b2231ada20b1.css"}],"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signin\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"61135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"16444","name":"*","chunks":[],"async":false}},"1169":{"*":{"id":"14633","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"31307","name":"*","chunks":[],"async":false}},"2985":{"*":{"id":"39200","name":"*","chunks":[],"async":false}},"3262":{"*":{"id":"44916","name":"*","chunks":[],"async":false}},"3738":{"*":{"id":"87578","name":"*","chunks":[],"async":false}},"3792":{"*":{"id":"21204","name":"*","chunks":[],"async":false}},"4281":{"*":{"id":"43523","name":"*","chunks":[],"async":false}},"4403":{"*":{"id":"25548","name":"*","chunks":[],"async":false}},"4820":{"*":{"id":"94796","name":"*","chunks":[],"async":false}},"4879":{"*":{"id":"80559","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"12089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"16042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"88170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"49477","name":"*","chunks":[],"async":false}},"7080":{"*":{"id":"8195","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"29345","name":"*","chunks":[],"async":false}},"8035":{"*":{"id":"23920","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"46577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}