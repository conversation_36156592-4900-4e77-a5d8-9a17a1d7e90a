import type { IntlMessageFormat } from 'intl-messageformat';
export type IntlCache = {
    dateTime: Record<string, Intl.DateTimeFormat>;
    number: Record<string, Intl.NumberFormat>;
    message: Record<string, IntlMessageFormat>;
    relativeTime: Record<string, Intl.RelativeTimeFormat>;
    pluralRules: Record<string, Intl.PluralRules>;
    list: Record<string, Intl.ListFormat>;
    displayNames: Record<string, Intl.DisplayNames>;
};
export declare function createCache(): IntlCache;
export declare function memoFn<Fn extends (...args: Array<any>) => any>(fn: Fn, cache: Record<string, ReturnType<Fn> | undefined>): Fn;
export type IntlFormatters = {
    getDateTimeFormat(...args: ConstructorParameters<typeof Intl.DateTimeFormat>): Intl.DateTimeFormat;
    getNumberFormat(...args: ConstructorParameters<typeof Intl.NumberFormat>): Intl.NumberFormat;
    getPluralRules(...args: ConstructorParameters<typeof Intl.PluralRules>): Intl.PluralRules;
    getRelativeTimeFormat(...args: ConstructorParameters<typeof Intl.RelativeTimeFormat>): Intl.RelativeTimeFormat;
    getListFormat(...args: ConstructorParameters<typeof Intl.ListFormat>): Intl.ListFormat;
    getDisplayNames(...args: ConstructorParameters<typeof Intl.DisplayNames>): Intl.DisplayNames;
};
export declare function createIntlFormatters(cache: IntlCache): IntlFormatters;
export type MessageFormatter = (...args: ConstructorParameters<typeof IntlMessageFormat>) => IntlMessageFormat;
export type Formatters = IntlFormatters & {
    getMessageFormat?: MessageFormatter;
};
