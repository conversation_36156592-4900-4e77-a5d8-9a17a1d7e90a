{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-exercises.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MuiExercises = registerClientReference(\n    function() { throw new Error(\"Attempted to call MuiExercises() from the server but MuiExercises is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/mui-exercises.tsx <module evaluation>\",\n    \"MuiExercises\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-exercises.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MuiExercises = registerClientReference(\n    function() { throw new Error(\"Attempted to call MuiExercises() from the server but MuiExercises is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/mui-exercises.tsx\",\n    \"MuiExercises\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oDACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/%5Blocale%5D/exercises/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { getTranslations } from 'next-intl/server';\nimport { generatePageMetadata } from \"@/lib/seo/utils\";\nimport { MuiExercises } from \"@/components/pages/mui-exercises\";\n\ntype Props = {\n  params: { locale: string };\n};\n\nexport async function generateMetadata({ params }: Props): Promise<Metadata> {\n  const { locale } = await params;\n  const t = await getTranslations({ locale, namespace: 'exercises' });\n\n  return generatePageMetadata({\n    title: t('title'),\n    description: t('subtitle'),\n    path: \"/exercises\",\n  });\n}\n\nexport default function ExercisesPage({ params }: Props) {\n  return <MuiExercises />;\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAS;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAY;IAEjE,OAAO,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1B,OAAO,EAAE;QACT,aAAa,EAAE;QACf,MAAM;IACR;AACF;AAEe,SAAS,cAAc,EAAE,MAAM,EAAS;IACrD,qBAAO,8OAAC,+IAAA,CAAA,eAAY;;;;;AACtB", "debugId": null}}]}