(()=>{var e={};e.id=43,e.ids=[43],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14633:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\workouts\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\[id]\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21942:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["workouts",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,14633)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\[id]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/workouts/[id]/page",pathname:"/workouts/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53326:(e,s,t)=>{Promise.resolve().then(t.bind(t,85343))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67238:(e,s,t)=>{Promise.resolve().then(t.bind(t,14633))},79551:e=>{"use strict";e.exports=require("url")},85343:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var r=t(60687),i=t(16189),a=t(86246),n=t(44493),l=t(29523),c=t(96834),d=t(52027),o=t(70293),x=t(28559),m=t(67760),h=t(81620),u=t(97840),p=t(96474),j=t(82080),g=t(28947),f=t(5336),N=t(48730),v=t(64398),w=t(41312),y=t(40228),b=t(95698),k=t(29908),A=t(85814),P=t.n(A);function C(){let e=(0,i.useParams)().id,{isAuthenticated:s}=(0,k.As)(),{data:t,isLoading:A,error:C}=(0,b.Rc)(e),_=(0,b.t2)();(0,b.ij)();let q=(0,b.bR)(),R=async()=>{try{await _.mutateAsync(e)}catch(e){console.error("Failed to join program:",e)}},W=async()=>{try{let e=await q.mutateAsync({exercises:[],notes:`Started from program: ${t?.title}`});window.location.href=`/workouts/sessions/${e.id}`}catch(e){console.error("Failed to start workout:",e)}};return A?(0,r.jsx)(d.AV,{}):C||!t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(a.V,{}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)(o.Kw,{title:"Program not found",message:"The workout program you're looking for doesn't exist or has been removed.",onRetry:()=>window.location.reload()})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(a.V,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(P(),{href:"/workouts",children:(0,r.jsxs)(l.$,{variant:"ghost",className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),"Back to Workouts"]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-2xl mb-2",children:t.title}),(0,r.jsx)(n.BT,{className:"text-lg",children:t.description})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{variant:"outline",size:"icon",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"outline",size:"icon",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})]})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.duration||"N/A"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Minutes"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.difficulty||"N/A"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Difficulty"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:t.weeks?.reduce((e,s)=>e+s.sessions.reduce((e,s)=>e+s.exercises.length,0),0)||0}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Exercises"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:t.participantCount||0}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Participants"})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)(l.$,{size:"lg",className:"flex items-center gap-2",onClick:W,disabled:q.isPending,children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),q.isPending?"Starting...":"Start Workout"]}),s&&(0,r.jsxs)(l.$,{variant:"outline",size:"lg",className:"flex items-center gap-2",onClick:R,disabled:_.isPending,children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),_.isPending?"Joining...":"Join Program"]})]})]})]}),t.description&&(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5"}),"About This Program"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:t.description})})]}),t.weeks&&t.weeks.length>0&&(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{children:["Program Weeks (",t.weeks.length,")"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:t.weeks.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("span",{className:"flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium",children:s+1}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-medium text-gray-900",children:["Week ",e.weekNumber||s+1]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.sessions?.length||0," sessions"]})]})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"View Week"})]},e.id||s))})})]}),t.goals&&t.goals.length>0&&(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),"Program Goals"]})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:t.goals.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Program Info"})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Category"}),(0,r.jsx)(c.E,{variant:"outline",children:"Fitness Program"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),"Duration"]}),(0,r.jsxs)("span",{className:"font-medium",children:[t.duration||"N/A"," min"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),"Difficulty"]}),(0,r.jsx)("span",{className:"font-medium",children:t.difficulty||"N/A"})]}),t.equipment&&t.equipment.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 block mb-2",children:"Equipment Needed"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:t.equipment.map((e,s)=>(0,r.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},s))})]}),t.rating&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"Rating"]}),(0,r.jsxs)("span",{className:"font-medium",children:[t.rating,"/5"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,r.jsx)(w.A,{className:"h-4 w-4"}),"Participants"]}),(0,r.jsx)("span",{className:"font-medium",children:t.participantCount||0})]})]})]}),!1,(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Quick Actions"})}),(0,r.jsxs)(n.Wu,{className:"space-y-3",children:[(0,r.jsxs)(l.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Schedule Workout"]}),(0,r.jsxs)(l.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Share Program"]}),(0,r.jsxs)(l.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"View Similar"]})]})]})]})]})]})]})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[96,76],()=>t(21942));module.exports=r})();