import{match as e}from"@formatjs/intl-localematcher";import o from"negotiator";import{getPathnameMatch as l,isLocaleSupportedOnDomain as t,getHost as a}from"./utils.js";function c(l,t,a){let c;const n=new o({headers:{"accept-language":l.get("accept-language")||void 0}}).languages();try{const o=function(e){return e.slice().sort(((e,o)=>o.length-e.length))}(t);c=e(n,o,a)}catch{}return c}function n(e,o){if(e.localeCookie&&o.has(e.localeCookie.name)){const l=o.get(e.localeCookie.name)?.value;if(l&&e.locales.includes(l))return l}}function i(e,o,t,a){let i;return a&&(i=l(a,e.locales,e.localePrefix)?.locale),!i&&e.localeDetection&&(i=n(e,t)),!i&&e.localeDetection&&(i=c(o,e.locales,e.defaultLocale)),i||(i=e.defaultLocale),i}function r(e,o,r,f){const u=function(e,o){const l=a(e);if(l)return o.find((e=>e.domain===l))}(o,e.domains);if(!u)return{locale:i(e,o,r,f)};let s;if(f){const o=l(f,e.locales,e.localePrefix,u)?.locale;if(o){if(!t(o,u))return{locale:o,domain:u};s=o}}if(!s&&e.localeDetection){const o=n(e,r);o&&t(o,u)&&(s=o)}if(!s&&e.localeDetection){const e=c(o,u.locales,u.defaultLocale);e&&(s=e)}return s||(s=u.defaultLocale),{locale:s,domain:u}}function f(e,o,l,t){return e.domains?r(e,o,l,t):{locale:i(e,o,l,t)}}export{f as default,c as getAcceptLanguageLocale};
