(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{4505:(s,e,l)=>{Promise.resolve().then(l.bind(l,4879))},4879:(s,e,l)=>{"use strict";l.r(e),l.d(e,{default:()=>o});var a=l(5155),r=l(6695),t=l(5220),c=l(9397),i=l(5607),d=l(6785),n=l(9037),x=l(9074),m=l(4186),h=l(5690),j=l(3109);function o(){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome back, <PERSON>! \uD83D\uDC4B"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Ready to crush your fitness goals today?"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Workouts This Week"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"4"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2 from last week"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Calories Burned"}),(0,a.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"1,247"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+180 from yesterday"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Current Streak"}),(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"12 days"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Personal best!"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Workouts"}),(0,a.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"127"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Since joining"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"Today's Workout"]}),(0,a.jsx)(r.BT,{children:"Upper Body Strength Training"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Push-up Variations"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"3 sets \xd7 12 reps"})]}),(0,a.jsx)(m.A,{className:"h-5 w-5 text-blue-600"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Dumbbell Rows"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"3 sets \xd7 10 reps"})]}),(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Shoulder Press"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"3 sets \xd7 8 reps"})]}),(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)(t.$,{className:"w-full",size:"lg",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]})]})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),"Weekly Progress"]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{children:"Workout Goal"}),(0,a.jsx)("span",{children:"4/5"})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"80%"}})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{children:"Calorie Goal"}),(0,a.jsx)("span",{children:"1247/1500"})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"83%"}})})]})]})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Quick Actions"})}),(0,a.jsxs)(r.Wu,{className:"space-y-3",children:[(0,a.jsxs)(t.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Log Quick Workout"]}),(0,a.jsxs)(t.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Update Goals"]}),(0,a.jsxs)(t.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"View Progress"]})]})]})]})]})]})})}}},s=>{var e=e=>s(s.s=e);s.O(0,[76,96,358],()=>e(4505)),_N_E=s.O()}]);