{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Box/boxClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nconst boxClasses = generateUtilityClasses('MuiBox', ['root']);\nexport default boxClasses;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,aAAa,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,UAAU;IAAC;CAAO;uCAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Box/Box.js"], "sourcesContent": ["'use client';\n\nimport { createBox } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from \"../className/index.js\";\nimport { createTheme } from \"../styles/index.js\";\nimport THEME_ID from \"../styles/identifier.js\";\nimport boxClasses from \"./boxClasses.js\";\nconst defaultTheme = createTheme();\nconst Box = createBox({\n  themeId: THEME_ID,\n  defaultTheme,\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAQA,MAAM,eAAe,CAAA,GAAA,2MAAA,CAAA,cAAW,AAAD;AAC/B,MAAM,MAAM,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE;IACpB,SAAS,gKAAA,CAAA,UAAQ;IACjB;IACA,kBAAkB,6JAAA,CAAA,UAAU,CAAC,IAAI;IACjC,mBAAmB,2OAAA,CAAA,8BAAkB,CAAC,QAAQ;AAChD;AACA,uCAAwC,IAAI,SAAS,GAA0B;IAC7E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AACxJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/capitalize.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nexport default capitalize;"], "names": [], "mappings": ";;;AAAA;;uCACe,iKAAA,CAAA,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/styles/slotShouldForwardProp.js"], "sourcesContent": ["// copied from @mui/system/createStyled\nfunction slotShouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nexport default slotShouldForwardProp;"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC,SAAS,sBAAsB,IAAI;IACjC,OAAO,SAAS,gBAAgB,SAAS,WAAW,SAAS,QAAQ,SAAS;AAChF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/styles/rootShouldForwardProp.js"], "sourcesContent": ["import slotShouldForwardProp from \"./slotShouldForwardProp.js\";\nconst rootShouldForwardProp = prop => slotShouldForwardProp(prop) && prop !== 'classes';\nexport default rootShouldForwardProp;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,wBAAwB,CAAA,OAAQ,CAAA,GAAA,2KAAA,CAAA,UAAqB,AAAD,EAAE,SAAS,SAAS;uCAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/styles/styled.js"], "sourcesContent": ["'use client';\n\nimport createStyled from '@mui/system/createStyled';\nimport defaultTheme from \"./defaultTheme.js\";\nimport THEME_ID from \"./identifier.js\";\nimport rootShouldForwardProp from \"./rootShouldForwardProp.js\";\nexport { default as slotShouldForwardProp } from \"./slotShouldForwardProp.js\";\nexport { default as rootShouldForwardProp } from \"./rootShouldForwardProp.js\";\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;;;AAQA,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD,EAAE;IAC1B,SAAS,gKAAA,CAAA,UAAQ;IACjB,cAAA,kKAAA,CAAA,UAAY;IACZ,uBAAA,2KAAA,CAAA,UAAqB;AACvB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Container/Container.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from \"../utils/capitalize.js\";\nimport styled from \"../styles/styled.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AAOA,MAAM,YAAY,CAAA,GAAA,oNAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,uBAAuB,CAAA,GAAA,4KAAA,CAAA,UAAM,AAAD,EAAE,OAAO;QACnC,MAAM;QACN,MAAM;QACN,mBAAmB,CAAC,OAAO;YACzB,MAAM,EACJ,UAAU,EACX,GAAG;YACJ,OAAO;gBAAC,OAAO,IAAI;gBAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO,WAAW,QAAQ,IAAI,CAAC;gBAAE,WAAW,KAAK,IAAI,OAAO,KAAK;gBAAE,WAAW,cAAc,IAAI,OAAO,cAAc;aAAC;QAC1K;IACF;IACA,eAAe,CAAA,UAAW,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;YACxC,OAAO;YACP,MAAM;QACR;AACF;AACA,uCAAwC,UAAU,SAAS,GAA0B;IACnF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;;GAGC,GACD,gBAAgB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC9B;;;;;;GAMC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,IAAI;IACrB;;;;;GAKC,GACD,UAAU,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAM;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC9I;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AACxJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/memoTheme.js"], "sourcesContent": ["import { unstable_memoTheme } from '@mui/system';\nconst memoTheme = unstable_memoTheme;\nexport default memoTheme;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY,oMAAA,CAAA,qBAAkB;uCACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/createSimplePaletteValueFilter.js"], "sourcesContent": ["/**\n * Type guard to check if the object has a \"main\" property of type string.\n *\n * @param obj - the object to check\n * @returns boolean\n */\nfunction hasCorrectMainProperty(obj) {\n  return typeof obj.main === 'string';\n}\n/**\n * Checks if the object conforms to the SimplePaletteColorOptions type.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param obj - The object to check\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns boolean\n */\nfunction checkSimplePaletteColorValues(obj, additionalPropertiesToCheck = []) {\n  if (!hasCorrectMainProperty(obj)) {\n    return false;\n  }\n  for (const value of additionalPropertiesToCheck) {\n    if (!obj.hasOwnProperty(value) || typeof obj[value] !== 'string') {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Creates a filter function used to filter simple palette color options.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns ([, value]: [any, PaletteColorOptions]) => boolean\n */\nexport default function createSimplePaletteValueFilter(additionalPropertiesToCheck = []) {\n  return ([, value]) => value && checkSimplePaletteColorValues(value, additionalPropertiesToCheck);\n}"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACD,SAAS,uBAAuB,GAAG;IACjC,OAAO,OAAO,IAAI,IAAI,KAAK;AAC7B;AACA;;;;;;;;CAQC,GACD,SAAS,8BAA8B,GAAG,EAAE,8BAA8B,EAAE;IAC1E,IAAI,CAAC,uBAAuB,MAAM;QAChC,OAAO;IACT;IACA,KAAK,MAAM,SAAS,4BAA6B;QAC/C,IAAI,CAAC,IAAI,cAAc,CAAC,UAAU,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU;YAChE,OAAO;QACT;IACF;IACA,OAAO;AACT;AAUe,SAAS,+BAA+B,8BAA8B,EAAE;IACrF,OAAO,CAAC,GAAG,MAAM,GAAK,SAAS,8BAA8B,OAAO;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Typography/typographyClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTypographyUtilityClass(slot) {\n  return generateUtilityClass('MuiTypography', slot);\n}\nconst typographyClasses = generateUtilityClasses('MuiTypography', ['root', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'inherit', 'button', 'caption', 'overline', 'alignLeft', 'alignRight', 'alignCenter', 'alignJustify', 'noWrap', 'gutterBottom', 'paragraph']);\nexport default typographyClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,0BAA0B,IAAI;IAC5C,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,iBAAiB;AAC/C;AACA,MAAM,oBAAoB,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,iBAAiB;IAAC;IAAQ;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAa;IAAa;IAAS;IAAS;IAAW;IAAU;IAAW;IAAY;IAAa;IAAc;IAAe;IAAgB;IAAU;IAAgB;CAAY;uCACxR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Typography/Typography.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, internal_createExtendSxProp } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { getTypographyUtilityClass } from \"./typographyClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst v6Colors = {\n  primary: true,\n  secondary: true,\n  error: true,\n  info: true,\n  success: true,\n  warning: true,\n  textPrimary: true,\n  textSecondary: true,\n  textDisabled: true\n};\nconst extendSxProp = internal_createExtendSxProp();\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, ownerState.align !== 'inherit' && `align${capitalize(align)}`, gutterBottom && 'gutterBottom', noWrap && 'noWrap', paragraph && 'paragraph']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, classes);\n};\nexport const TypographyRoot = styled('span', {\n  name: 'MuiTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.variant && styles[ownerState.variant], ownerState.align !== 'inherit' && styles[`align${capitalize(ownerState.align)}`], ownerState.noWrap && styles.noWrap, ownerState.gutterBottom && styles.gutterBottom, ownerState.paragraph && styles.paragraph];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  variants: [{\n    props: {\n      variant: 'inherit'\n    },\n    style: {\n      // Some elements, like <button> on Chrome have default font that doesn't inherit, reset this.\n      font: 'inherit',\n      lineHeight: 'inherit',\n      letterSpacing: 'inherit'\n    }\n  }, ...Object.entries(theme.typography).filter(([variant, value]) => variant !== 'inherit' && value && typeof value === 'object').map(([variant, value]) => ({\n    props: {\n      variant\n    },\n    style: value\n  })), ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      color: (theme.vars || theme).palette[color].main\n    }\n  })), ...Object.entries(theme.palette?.text || {}).filter(([, value]) => typeof value === 'string').map(([color]) => ({\n    props: {\n      color: `text${capitalize(color)}`\n    },\n    style: {\n      color: (theme.vars || theme).palette.text[color]\n    }\n  })), {\n    props: ({\n      ownerState\n    }) => ownerState.align !== 'inherit',\n    style: {\n      textAlign: 'var(--Typography-textAlign)'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.noWrap,\n    style: {\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.gutterBottom,\n    style: {\n      marginBottom: '0.35em'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.paragraph,\n    style: {\n      marginBottom: 16\n    }\n  }]\n})));\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  h5: 'h5',\n  h6: 'h6',\n  subtitle1: 'h6',\n  subtitle2: 'h6',\n  body1: 'p',\n  body2: 'p',\n  inherit: 'p'\n};\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  const {\n    color,\n    ...themeProps\n  } = useDefaultProps({\n    props: inProps,\n    name: 'MuiTypography'\n  });\n  const isSxColor = !v6Colors[color];\n  // TODO: Remove `extendSxProp` in v7\n  const props = extendSxProp({\n    ...themeProps,\n    ...(isSxColor && {\n      color\n    })\n  });\n  const {\n    align = 'inherit',\n    className,\n    component,\n    gutterBottom = false,\n    noWrap = false,\n    paragraph = false,\n    variant = 'body1',\n    variantMapping = defaultVariantMapping,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    align,\n    color,\n    className,\n    component,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    variantMapping\n  };\n  const Component = component || (paragraph ? 'p' : variantMapping[variant] || defaultVariantMapping[variant]) || 'span';\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TypographyRoot, {\n    as: Component,\n    ref: ref,\n    className: clsx(classes.root, className),\n    ...other,\n    ownerState: ownerState,\n    style: {\n      ...(align !== 'inherit' && {\n        '--Typography-textAlign': align\n      }),\n      ...other.style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the text-align on the component.\n   * @default 'inherit'\n   */\n  align: PropTypes.oneOf(['center', 'inherit', 'justify', 'left', 'right']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'success', 'error', 'info', 'warning', 'textPrimary', 'textSecondary', 'textDisabled']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * If `true`, the element will be a paragraph element.\n   * @default false\n   * @deprecated Use the `component` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  paragraph: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Applies the theme typography styles.\n   * @default 'body1'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, subtitle1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   h5: 'h5',\n   *   h6: 'h6',\n   *   subtitle1: 'h6',\n   *   subtitle2: 'h6',\n   *   body1: 'p',\n   *   body2: 'p',\n   *   inherit: 'p',\n   * }\n   */\n  variantMapping: PropTypes /* @typescript-to-proptypes-ignore */.object\n} : void 0;\nexport default Typography;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAaA,MAAM,WAAW;IACf,SAAS;IACT,WAAW;IACX,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,aAAa;IACb,eAAe;IACf,cAAc;AAChB;AACA,MAAM,eAAe,CAAA,GAAA,mLAAA,CAAA,8BAA2B,AAAD;AAC/C,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,KAAK,EACL,YAAY,EACZ,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ;YAAS,WAAW,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,QAAQ;YAAE,gBAAgB;YAAgB,UAAU;YAAU,aAAa;SAAY;IACtK;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,2KAAA,CAAA,4BAAyB,EAAE;AAC1D;AACO,MAAM,iBAAiB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAC3C,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,WAAW,OAAO,IAAI,MAAM,CAAC,WAAW,OAAO,CAAC;YAAE,WAAW,KAAK,KAAK,aAAa,MAAM,CAAC,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,KAAK,GAAG,CAAC;YAAE,WAAW,MAAM,IAAI,OAAO,MAAM;YAAE,WAAW,YAAY,IAAI,OAAO,YAAY;YAAE,WAAW,SAAS,IAAI,OAAO,SAAS;SAAC;IACxR;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN,GAAK,CAAC;QACL,QAAQ;QACR,UAAU;YAAC;gBACT,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,6FAA6F;oBAC7F,MAAM;oBACN,YAAY;oBACZ,eAAe;gBACjB;YACF;eAAM,OAAO,OAAO,CAAC,MAAM,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,MAAM,GAAK,YAAY,aAAa,SAAS,OAAO,UAAU,UAAU,GAAG,CAAC,CAAC,CAAC,SAAS,MAAM,GAAK,CAAC;oBAC1J,OAAO;wBACL;oBACF;oBACA,OAAO;gBACT,CAAC;eAAO,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,MAAM,CAAC,CAAA,GAAA,mLAAA,CAAA,UAA8B,AAAD,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK,CAAC;oBAC/F,OAAO;wBACL;oBACF;oBACA,OAAO;wBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;oBAClD;gBACF,CAAC;eAAO,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,OAAO,UAAU,UAAU,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK,CAAC;oBACnH,OAAO;wBACL,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,QAAQ;oBACnC;oBACA,OAAO;wBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;oBAClD;gBACF,CAAC;YAAI;gBACH,OAAO,CAAC,EACN,UAAU,EACX,GAAK,WAAW,KAAK,KAAK;gBAC3B,OAAO;oBACL,WAAW;gBACb;YACF;YAAG;gBACD,OAAO,CAAC,EACN,UAAU,EACX,GAAK,WAAW,MAAM;gBACvB,OAAO;oBACL,UAAU;oBACV,cAAc;oBACd,YAAY;gBACd;YACF;YAAG;gBACD,OAAO,CAAC,EACN,UAAU,EACX,GAAK,WAAW,YAAY;gBAC7B,OAAO;oBACL,cAAc;gBAChB;YACF;YAAG;gBACD,OAAO,CAAC,EACN,UAAU,EACX,GAAK,WAAW,SAAS;gBAC1B,OAAO;oBACL,cAAc;gBAChB;YACF;SAAE;IACJ,CAAC;AACD,MAAM,wBAAwB;IAC5B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,WAAW;IACX,OAAO;IACP,OAAO;IACP,SAAS;AACX;AACA,MAAM,aAAa,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,WAAW,OAAO,EAAE,GAAG;IAC/E,MAAM,EACJ,KAAK,EACL,GAAG,YACJ,GAAG,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAClB,OAAO;QACP,MAAM;IACR;IACA,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM;IAClC,oCAAoC;IACpC,MAAM,QAAQ,aAAa;QACzB,GAAG,UAAU;QACb,GAAI,aAAa;YACf;QACF,CAAC;IACH;IACA,MAAM,EACJ,QAAQ,SAAS,EACjB,SAAS,EACT,SAAS,EACT,eAAe,KAAK,EACpB,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,UAAU,OAAO,EACjB,iBAAiB,qBAAqB,EACtC,GAAG,OACJ,GAAG;IACJ,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,YAAY,aAAa,CAAC,YAAY,MAAM,cAAc,CAAC,QAAQ,IAAI,qBAAqB,CAAC,QAAQ,KAAK;IAChH,MAAM,UAAU,kBAAkB;IAClC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,gBAAgB;QACvC,IAAI;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,GAAG,KAAK;QACR,YAAY;QACZ,OAAO;YACL,GAAI,UAAU,aAAa;gBACzB,0BAA0B;YAC5B,CAAC;YACD,GAAG,MAAM,KAAK;QAChB;IACF;AACF;AACA,uCAAwC,WAAW,SAAS,GAA0B;IACpF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;;GAGC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAAC;QAAU;QAAW;QAAW;QAAQ;KAAQ;IACxE;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;GAIC,GACD,OAAO,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAW;YAAa;YAAW;YAAS;YAAQ;YAAW;YAAe;YAAiB;SAAe;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACrN;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;;GAGC,GACD,cAAc,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B;;;;;;GAMC,GACD,QAAQ,sIAAA,CAAA,UAAS,CAAC,IAAI;IACtB;;;;GAIC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;IACzB;;GAEC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvB;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;;GAGC,GACD,SAAS,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAS;YAAS;YAAU;YAAW;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAW;YAAY;YAAa;SAAY;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACpO;;;;;;;;;;;;;;;;;;GAkBC,GACD,gBAAgB,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,MAAM;AACxE;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/useId.js"], "sourcesContent": ["'use client';\n\nimport useId from '@mui/utils/useId';\nexport default useId;"], "names": [], "mappings": ";;;AAEA;AAFA;;uCAGe,uJAAA,CAAA,UAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nexport default useForkRef;"], "names": [], "mappings": ";;;AAEA;AAFA;;uCAGe,iKAAA,CAAA,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/useEventCallback.js"], "sourcesContent": ["'use client';\n\nimport useEventCallback from '@mui/utils/useEventCallback';\nexport default useEventCallback;"], "names": [], "mappings": ";;;AAEA;AAFA;;uCAGe,6KAAA,CAAA,UAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/useLazyRipple/useLazyRipple.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\n/**\n * Lazy initialization container for the Ripple instance. This improves\n * performance by delaying mounting the ripple until it's needed.\n */\nexport class LazyRipple {\n  /** React ref to the ripple instance */\n\n  /** If the ripple component should be mounted */\n\n  /** Promise that resolves when the ripple component is mounted */\n\n  /** If the ripple component has been mounted */\n\n  /** React state hook setter */\n\n  static create() {\n    return new LazyRipple();\n  }\n  static use() {\n    /* eslint-disable */\n    const ripple = useLazyRef(LazyRipple.create).current;\n    const [shouldMount, setShouldMount] = React.useState(false);\n    ripple.shouldMount = shouldMount;\n    ripple.setShouldMount = setShouldMount;\n    React.useEffect(ripple.mountEffect, [shouldMount]);\n    /* eslint-enable */\n\n    return ripple;\n  }\n  constructor() {\n    this.ref = {\n      current: null\n    };\n    this.mounted = null;\n    this.didMount = false;\n    this.shouldMount = false;\n    this.setShouldMount = null;\n  }\n  mount() {\n    if (!this.mounted) {\n      this.mounted = createControlledPromise();\n      this.shouldMount = true;\n      this.setShouldMount(this.shouldMount);\n    }\n    return this.mounted;\n  }\n  mountEffect = () => {\n    if (this.shouldMount && !this.didMount) {\n      if (this.ref.current !== null) {\n        this.didMount = true;\n        this.mounted.resolve();\n      }\n    }\n  };\n\n  /* Ripple API */\n\n  start(...args) {\n    this.mount().then(() => this.ref.current?.start(...args));\n  }\n  stop(...args) {\n    this.mount().then(() => this.ref.current?.stop(...args));\n  }\n  pulsate(...args) {\n    this.mount().then(() => this.ref.current?.pulsate(...args));\n  }\n}\nexport default function useLazyRipple() {\n  return LazyRipple.use();\n}\nfunction createControlledPromise() {\n  let resolve;\n  let reject;\n  const p = new Promise((resolveFn, rejectFn) => {\n    resolve = resolveFn;\n    reject = rejectFn;\n  });\n  p.resolve = resolve;\n  p.reject = reject;\n  return p;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAQO,MAAM;IACX,qCAAqC,GAErC,8CAA8C,GAE9C,+DAA+D,GAE/D,6CAA6C,GAE7C,4BAA4B,GAE5B,OAAO,SAAS;QACd,OAAO,IAAI;IACb;IACA,OAAO,MAAM;QACX,kBAAkB,GAClB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,WAAW,MAAM,EAAE,OAAO;QACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;QACrD,OAAO,WAAW,GAAG;QACrB,OAAO,cAAc,GAAG;QACxB,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE,OAAO,WAAW,EAAE;YAAC;SAAY;QACjD,iBAAiB,GAEjB,OAAO;IACT;IACA,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;YACT,SAAS;QACX;QACA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG;IACxB;IACA,QAAQ;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW;QACtC;QACA,OAAO,IAAI,CAAC,OAAO;IACrB;IACA,cAAc;QACZ,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM;gBAC7B,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,OAAO,CAAC,OAAO;YACtB;QACF;IACF,EAAE;IAEF,cAAc,GAEd,MAAM,GAAG,IAAI,EAAE;QACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS;IACrD;IACA,KAAK,GAAG,IAAI,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ;IACpD;IACA,QAAQ,GAAG,IAAI,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW;IACvD;AACF;AACe,SAAS;IACtB,OAAO,WAAW,GAAG;AACvB;AACA,SAAS;IACP,IAAI;IACJ,IAAI;IACJ,MAAM,IAAI,IAAI,QAAQ,CAAC,WAAW;QAChC,UAAU;QACV,SAAS;IACX;IACA,EAAE,OAAO,GAAG;IACZ,EAAE,MAAM,GAAG;IACX,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonBase/Ripple.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Ripple(props) {\n  const {\n    className,\n    classes,\n    pulsate = false,\n    rippleX,\n    rippleY,\n    rippleSize,\n    in: inProp,\n    onExited,\n    timeout\n  } = props;\n  const [leaving, setLeaving] = React.useState(false);\n  const rippleClassName = clsx(className, classes.ripple, classes.rippleVisible, pulsate && classes.ripplePulsate);\n  const rippleStyles = {\n    width: rippleSize,\n    height: rippleSize,\n    top: -(rippleSize / 2) + rippleY,\n    left: -(rippleSize / 2) + rippleX\n  };\n  const childClassName = clsx(classes.child, leaving && classes.childLeaving, pulsate && classes.childPulsate);\n  if (!inProp && !leaving) {\n    setLeaving(true);\n  }\n  React.useEffect(() => {\n    if (!inProp && onExited != null) {\n      // react-transition-group#onExited\n      const timeoutId = setTimeout(onExited, timeout);\n      return () => {\n        clearTimeout(timeoutId);\n      };\n    }\n    return undefined;\n  }, [onExited, inProp, timeout]);\n  return /*#__PURE__*/_jsx(\"span\", {\n    className: rippleClassName,\n    style: rippleStyles,\n    children: /*#__PURE__*/_jsx(\"span\", {\n      className: childClassName\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Ripple.propTypes /* remove-proptypes */ = {\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object.isRequired,\n  className: PropTypes.string,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  onExited: PropTypes.func,\n  /**\n   * If `true`, the ripple pulsates, typically indicating the keyboard focus state of an element.\n   */\n  pulsate: PropTypes.bool,\n  /**\n   * Diameter of the ripple.\n   */\n  rippleSize: PropTypes.number,\n  /**\n   * Horizontal position of the ripple center.\n   */\n  rippleX: PropTypes.number,\n  /**\n   * Vertical position of the ripple center.\n   */\n  rippleY: PropTypes.number,\n  /**\n   * exit delay\n   */\n  timeout: PropTypes.number.isRequired\n} : void 0;\nexport default Ripple;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAEA;;CAEC,GACD;AATA;;;;;AAUA,SAAS,OAAO,KAAK;IACnB,MAAM,EACJ,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,OAAO,EACP,OAAO,EACP,UAAU,EACV,IAAI,MAAM,EACV,QAAQ,EACR,OAAO,EACR,GAAG;IACJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,QAAQ,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,QAAQ,aAAa;IAC/G,MAAM,eAAe;QACnB,OAAO;QACP,QAAQ;QACR,KAAK,CAAC,CAAC,aAAa,CAAC,IAAI;QACzB,MAAM,CAAC,CAAC,aAAa,CAAC,IAAI;IAC5B;IACA,MAAM,iBAAiB,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,KAAK,EAAE,WAAW,QAAQ,YAAY,EAAE,WAAW,QAAQ,YAAY;IAC3G,IAAI,CAAC,UAAU,CAAC,SAAS;QACvB,WAAW;IACb;IACA,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,UAAU,YAAY,MAAM;YAC/B,kCAAkC;YAClC,MAAM,YAAY,WAAW,UAAU;YACvC,OAAO;gBACL,aAAa;YACf;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAU;QAAQ;KAAQ;IAC9B,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;QAC/B,WAAW;QACX,OAAO;QACP,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,WAAW;QACb;IACF;AACF;AACA,uCAAwC,OAAO,SAAS,GAA0B;IAChF;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM,CAAC,UAAU;IACpC,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;IAClB;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI;IACvB;;GAEC,GACD,YAAY,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC5B;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM,CAAC,UAAU;AACtC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonBase/touchRippleClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTouchRippleUtilityClass(slot) {\n  return generateUtilityClass('MuiTouchRipple', slot);\n}\nconst touchRippleClasses = generateUtilityClasses('MuiTouchRipple', ['root', 'ripple', 'rippleVisible', 'ripplePulsate', 'child', 'childLeaving', 'childPulsate']);\nexport default touchRippleClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,2BAA2B,IAAI;IAC7C,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,kBAAkB;AAChD;AACA,MAAM,qBAAqB,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,kBAAkB;IAAC;IAAQ;IAAU;IAAiB;IAAiB;IAAS;IAAgB;CAAe;uCAClJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonBase/TouchRipple.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { TransitionGroup } from 'react-transition-group';\nimport clsx from 'clsx';\nimport useTimeout from '@mui/utils/useTimeout';\nimport { keyframes, styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Ripple from \"./Ripple.js\";\nimport touchRippleClasses from \"./touchRippleClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DURATION = 550;\nexport const DELAY_RIPPLE = 80;\nconst enterKeyframe = keyframes`\n  0% {\n    transform: scale(0);\n    opacity: 0.1;\n  }\n\n  100% {\n    transform: scale(1);\n    opacity: 0.3;\n  }\n`;\nconst exitKeyframe = keyframes`\n  0% {\n    opacity: 1;\n  }\n\n  100% {\n    opacity: 0;\n  }\n`;\nconst pulsateKeyframe = keyframes`\n  0% {\n    transform: scale(1);\n  }\n\n  50% {\n    transform: scale(0.92);\n  }\n\n  100% {\n    transform: scale(1);\n  }\n`;\nexport const TouchRippleRoot = styled('span', {\n  name: 'MuiTouchRipple',\n  slot: 'Root'\n})({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  zIndex: 0,\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit'\n});\n\n// This `styled()` function invokes keyframes. `styled-components` only supports keyframes\n// in string templates. Do not convert these styles in JS object as it will break.\nexport const TouchRippleRipple = styled(Ripple, {\n  name: 'MuiTouchRipple',\n  slot: 'Ripple'\n})`\n  opacity: 0;\n  position: absolute;\n\n  &.${touchRippleClasses.rippleVisible} {\n    opacity: 0.3;\n    transform: scale(1);\n    animation-name: ${enterKeyframe};\n    animation-duration: ${DURATION}ms;\n    animation-timing-function: ${({\n  theme\n}) => theme.transitions.easing.easeInOut};\n  }\n\n  &.${touchRippleClasses.ripplePulsate} {\n    animation-duration: ${({\n  theme\n}) => theme.transitions.duration.shorter}ms;\n  }\n\n  & .${touchRippleClasses.child} {\n    opacity: 1;\n    display: block;\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    background-color: currentColor;\n  }\n\n  & .${touchRippleClasses.childLeaving} {\n    opacity: 0;\n    animation-name: ${exitKeyframe};\n    animation-duration: ${DURATION}ms;\n    animation-timing-function: ${({\n  theme\n}) => theme.transitions.easing.easeInOut};\n  }\n\n  & .${touchRippleClasses.childPulsate} {\n    position: absolute;\n    /* @noflip */\n    left: 0px;\n    top: 0;\n    animation-name: ${pulsateKeyframe};\n    animation-duration: 2500ms;\n    animation-timing-function: ${({\n  theme\n}) => theme.transitions.easing.easeInOut};\n    animation-iteration-count: infinite;\n    animation-delay: 200ms;\n  }\n`;\n\n/**\n * @ignore - internal component.\n *\n * TODO v5: Make private\n */\nconst TouchRipple = /*#__PURE__*/React.forwardRef(function TouchRipple(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTouchRipple'\n  });\n  const {\n    center: centerProp = false,\n    classes = {},\n    className,\n    ...other\n  } = props;\n  const [ripples, setRipples] = React.useState([]);\n  const nextKey = React.useRef(0);\n  const rippleCallback = React.useRef(null);\n  React.useEffect(() => {\n    if (rippleCallback.current) {\n      rippleCallback.current();\n      rippleCallback.current = null;\n    }\n  }, [ripples]);\n\n  // Used to filter out mouse emulated events on mobile.\n  const ignoringMouseDown = React.useRef(false);\n  // We use a timer in order to only show the ripples for touch \"click\" like events.\n  // We don't want to display the ripple for touch scroll events.\n  const startTimer = useTimeout();\n\n  // This is the hook called once the previous timeout is ready.\n  const startTimerCommit = React.useRef(null);\n  const container = React.useRef(null);\n  const startCommit = React.useCallback(params => {\n    const {\n      pulsate,\n      rippleX,\n      rippleY,\n      rippleSize,\n      cb\n    } = params;\n    setRipples(oldRipples => [...oldRipples, /*#__PURE__*/_jsx(TouchRippleRipple, {\n      classes: {\n        ripple: clsx(classes.ripple, touchRippleClasses.ripple),\n        rippleVisible: clsx(classes.rippleVisible, touchRippleClasses.rippleVisible),\n        ripplePulsate: clsx(classes.ripplePulsate, touchRippleClasses.ripplePulsate),\n        child: clsx(classes.child, touchRippleClasses.child),\n        childLeaving: clsx(classes.childLeaving, touchRippleClasses.childLeaving),\n        childPulsate: clsx(classes.childPulsate, touchRippleClasses.childPulsate)\n      },\n      timeout: DURATION,\n      pulsate: pulsate,\n      rippleX: rippleX,\n      rippleY: rippleY,\n      rippleSize: rippleSize\n    }, nextKey.current)]);\n    nextKey.current += 1;\n    rippleCallback.current = cb;\n  }, [classes]);\n  const start = React.useCallback((event = {}, options = {}, cb = () => {}) => {\n    const {\n      pulsate = false,\n      center = centerProp || options.pulsate,\n      fakeElement = false // For test purposes\n    } = options;\n    if (event?.type === 'mousedown' && ignoringMouseDown.current) {\n      ignoringMouseDown.current = false;\n      return;\n    }\n    if (event?.type === 'touchstart') {\n      ignoringMouseDown.current = true;\n    }\n    const element = fakeElement ? null : container.current;\n    const rect = element ? element.getBoundingClientRect() : {\n      width: 0,\n      height: 0,\n      left: 0,\n      top: 0\n    };\n\n    // Get the size of the ripple\n    let rippleX;\n    let rippleY;\n    let rippleSize;\n    if (center || event === undefined || event.clientX === 0 && event.clientY === 0 || !event.clientX && !event.touches) {\n      rippleX = Math.round(rect.width / 2);\n      rippleY = Math.round(rect.height / 2);\n    } else {\n      const {\n        clientX,\n        clientY\n      } = event.touches && event.touches.length > 0 ? event.touches[0] : event;\n      rippleX = Math.round(clientX - rect.left);\n      rippleY = Math.round(clientY - rect.top);\n    }\n    if (center) {\n      rippleSize = Math.sqrt((2 * rect.width ** 2 + rect.height ** 2) / 3);\n\n      // For some reason the animation is broken on Mobile Chrome if the size is even.\n      if (rippleSize % 2 === 0) {\n        rippleSize += 1;\n      }\n    } else {\n      const sizeX = Math.max(Math.abs((element ? element.clientWidth : 0) - rippleX), rippleX) * 2 + 2;\n      const sizeY = Math.max(Math.abs((element ? element.clientHeight : 0) - rippleY), rippleY) * 2 + 2;\n      rippleSize = Math.sqrt(sizeX ** 2 + sizeY ** 2);\n    }\n\n    // Touche devices\n    if (event?.touches) {\n      // check that this isn't another touchstart due to multitouch\n      // otherwise we will only clear a single timer when unmounting while two\n      // are running\n      if (startTimerCommit.current === null) {\n        // Prepare the ripple effect.\n        startTimerCommit.current = () => {\n          startCommit({\n            pulsate,\n            rippleX,\n            rippleY,\n            rippleSize,\n            cb\n          });\n        };\n        // Delay the execution of the ripple effect.\n        // We have to make a tradeoff with this delay value.\n        startTimer.start(DELAY_RIPPLE, () => {\n          if (startTimerCommit.current) {\n            startTimerCommit.current();\n            startTimerCommit.current = null;\n          }\n        });\n      }\n    } else {\n      startCommit({\n        pulsate,\n        rippleX,\n        rippleY,\n        rippleSize,\n        cb\n      });\n    }\n  }, [centerProp, startCommit, startTimer]);\n  const pulsate = React.useCallback(() => {\n    start({}, {\n      pulsate: true\n    });\n  }, [start]);\n  const stop = React.useCallback((event, cb) => {\n    startTimer.clear();\n\n    // The touch interaction occurs too quickly.\n    // We still want to show ripple effect.\n    if (event?.type === 'touchend' && startTimerCommit.current) {\n      startTimerCommit.current();\n      startTimerCommit.current = null;\n      startTimer.start(0, () => {\n        stop(event, cb);\n      });\n      return;\n    }\n    startTimerCommit.current = null;\n    setRipples(oldRipples => {\n      if (oldRipples.length > 0) {\n        return oldRipples.slice(1);\n      }\n      return oldRipples;\n    });\n    rippleCallback.current = cb;\n  }, [startTimer]);\n  React.useImperativeHandle(ref, () => ({\n    pulsate,\n    start,\n    stop\n  }), [pulsate, start, stop]);\n  return /*#__PURE__*/_jsx(TouchRippleRoot, {\n    className: clsx(touchRippleClasses.root, classes.root, className),\n    ref: container,\n    ...other,\n    children: /*#__PURE__*/_jsx(TransitionGroup, {\n      component: null,\n      exit: true,\n      children: ripples\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TouchRipple.propTypes /* remove-proptypes */ = {\n  /**\n   * If `true`, the ripple starts at the center of the component\n   * rather than at the point of interaction.\n   */\n  center: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string\n} : void 0;\nexport default TouchRipple;"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAYA,MAAM,WAAW;AACV,MAAM,eAAe;AAC5B,MAAM,gBAAgB,oMAAA,CAAA,YAAS,CAAC;;;;;;;;;;AAUhC,CAAC;AACD,MAAM,eAAe,oMAAA,CAAA,YAAS,CAAC;;;;;;;;AAQ/B,CAAC;AACD,MAAM,kBAAkB,oMAAA,CAAA,YAAS,CAAC;;;;;;;;;;;;AAYlC,CAAC;AACM,MAAM,kBAAkB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAC5C,MAAM;IACN,MAAM;AACR,GAAG;IACD,UAAU;IACV,eAAe;IACf,UAAU;IACV,QAAQ;IACR,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,cAAc;AAChB;AAIO,MAAM,oBAAoB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,gKAAA,CAAA,UAAM,EAAE;IAC9C,MAAM;IACN,MAAM;AACR,EAAE,CAAC;;;;IAIC,EAAE,4KAAA,CAAA,UAAkB,CAAC,aAAa,CAAC;;;oBAGnB,EAAE,cAAc;wBACZ,EAAE,SAAS;+BACJ,EAAE,CAAC,EAChC,KAAK,EACN,GAAK,MAAM,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;;;IAGrC,EAAE,4KAAA,CAAA,UAAkB,CAAC,aAAa,CAAC;wBACf,EAAE,CAAC,EACzB,KAAK,EACN,GAAK,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;;;KAGpC,EAAE,4KAAA,CAAA,UAAkB,CAAC,KAAK,CAAC;;;;;;;;;KAS3B,EAAE,4KAAA,CAAA,UAAkB,CAAC,YAAY,CAAC;;oBAEnB,EAAE,aAAa;wBACX,EAAE,SAAS;+BACJ,EAAE,CAAC,EAChC,KAAK,EACN,GAAK,MAAM,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;;;KAGpC,EAAE,4KAAA,CAAA,UAAkB,CAAC,YAAY,CAAC;;;;;oBAKnB,EAAE,gBAAgB;;+BAEP,EAAE,CAAC,EAChC,KAAK,EACN,GAAK,MAAM,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;;;;AAIzC,CAAC;AAED;;;;CAIC,GACD,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,YAAY,OAAO,EAAE,GAAG;IACjF,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,QAAQ,aAAa,KAAK,EAC1B,UAAU,CAAC,CAAC,EACZ,SAAS,EACT,GAAG,OACJ,GAAG;IACJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,EAAE;IAC/C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO;YACtB,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC;KAAQ;IAEZ,sDAAsD;IACtD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACvC,kFAAkF;IAClF,+DAA+D;IAC/D,MAAM,aAAa,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD;IAE5B,8DAA8D;IAC9D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACtC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAA;QACpC,MAAM,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,UAAU,EACV,EAAE,EACH,GAAG;QACJ,WAAW,CAAA,aAAc;mBAAI;gBAAY,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,mBAAmB;oBAC5E,SAAS;wBACP,QAAQ,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,MAAM,EAAE,4KAAA,CAAA,UAAkB,CAAC,MAAM;wBACtD,eAAe,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,aAAa,EAAE,4KAAA,CAAA,UAAkB,CAAC,aAAa;wBAC3E,eAAe,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,aAAa,EAAE,4KAAA,CAAA,UAAkB,CAAC,aAAa;wBAC3E,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,KAAK,EAAE,4KAAA,CAAA,UAAkB,CAAC,KAAK;wBACnD,cAAc,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,YAAY,EAAE,4KAAA,CAAA,UAAkB,CAAC,YAAY;wBACxE,cAAc,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,YAAY,EAAE,4KAAA,CAAA,UAAkB,CAAC,YAAY;oBAC1E;oBACA,SAAS;oBACT,SAAS;oBACT,SAAS;oBACT,SAAS;oBACT,YAAY;gBACd,GAAG,QAAQ,OAAO;aAAE;QACpB,QAAQ,OAAO,IAAI;QACnB,eAAe,OAAO,GAAG;IAC3B,GAAG;QAAC;KAAQ;IACZ,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,KAAO,CAAC;QACtE,MAAM,EACJ,UAAU,KAAK,EACf,SAAS,cAAc,QAAQ,OAAO,EACtC,cAAc,MAAM,oBAAoB;QAArB,EACpB,GAAG;QACJ,IAAI,OAAO,SAAS,eAAe,kBAAkB,OAAO,EAAE;YAC5D,kBAAkB,OAAO,GAAG;YAC5B;QACF;QACA,IAAI,OAAO,SAAS,cAAc;YAChC,kBAAkB,OAAO,GAAG;QAC9B;QACA,MAAM,UAAU,cAAc,OAAO,UAAU,OAAO;QACtD,MAAM,OAAO,UAAU,QAAQ,qBAAqB,KAAK;YACvD,OAAO;YACP,QAAQ;YACR,MAAM;YACN,KAAK;QACP;QAEA,6BAA6B;QAC7B,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,UAAU,UAAU,aAAa,MAAM,OAAO,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;YACnH,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG;YAClC,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;QACrC,OAAO;YACL,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,MAAM,OAAO,CAAC,EAAE,GAAG;YACnE,UAAU,KAAK,KAAK,CAAC,UAAU,KAAK,IAAI;YACxC,UAAU,KAAK,KAAK,CAAC,UAAU,KAAK,GAAG;QACzC;QACA,IAAI,QAAQ;YACV,aAAa,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI;YAElE,gFAAgF;YAChF,IAAI,aAAa,MAAM,GAAG;gBACxB,cAAc;YAChB;QACF,OAAO;YACL,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,QAAQ,WAAW,GAAG,CAAC,IAAI,UAAU,WAAW,IAAI;YAC/F,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,QAAQ,YAAY,GAAG,CAAC,IAAI,UAAU,WAAW,IAAI;YAChG,aAAa,KAAK,IAAI,CAAC,SAAS,IAAI,SAAS;QAC/C;QAEA,iBAAiB;QACjB,IAAI,OAAO,SAAS;YAClB,6DAA6D;YAC7D,wEAAwE;YACxE,cAAc;YACd,IAAI,iBAAiB,OAAO,KAAK,MAAM;gBACrC,6BAA6B;gBAC7B,iBAAiB,OAAO,GAAG;oBACzB,YAAY;wBACV;wBACA;wBACA;wBACA;wBACA;oBACF;gBACF;gBACA,4CAA4C;gBAC5C,oDAAoD;gBACpD,WAAW,KAAK,CAAC,cAAc;oBAC7B,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,iBAAiB,OAAO;wBACxB,iBAAiB,OAAO,GAAG;oBAC7B;gBACF;YACF;QACF,OAAO;YACL,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAY;QAAa;KAAW;IACxC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QAChC,MAAM,CAAC,GAAG;YACR,SAAS;QACX;IACF,GAAG;QAAC;KAAM;IACV,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC,OAAO;QACrC,WAAW,KAAK;QAEhB,4CAA4C;QAC5C,uCAAuC;QACvC,IAAI,OAAO,SAAS,cAAc,iBAAiB,OAAO,EAAE;YAC1D,iBAAiB,OAAO;YACxB,iBAAiB,OAAO,GAAG;YAC3B,WAAW,KAAK,CAAC,GAAG;gBAClB,KAAK,OAAO;YACd;YACA;QACF;QACA,iBAAiB,OAAO,GAAG;QAC3B,WAAW,CAAA;YACT,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,OAAO,WAAW,KAAK,CAAC;YAC1B;YACA,OAAO;QACT;QACA,eAAe,OAAO,GAAG;IAC3B,GAAG;QAAC;KAAW;IACf,CAAA,GAAA,qMAAA,CAAA,sBAAyB,AAAD,EAAE,KAAK,IAAM,CAAC;YACpC;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAS;QAAO;KAAK;IAC1B,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,iBAAiB;QACxC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,4KAAA,CAAA,UAAkB,CAAC,IAAI,EAAE,QAAQ,IAAI,EAAE;QACvD,KAAK;QACL,GAAG,KAAK;QACR,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,oNAAA,CAAA,kBAAe,EAAE;YAC3C,WAAW;YACX,MAAM;YACN,UAAU;QACZ;IACF;AACF;AACA,uCAAwC,YAAY,SAAS,GAA0B;IACrF;;;GAGC,GACD,QAAQ,sIAAA,CAAA,UAAS,CAAC,IAAI;IACtB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;AAC7B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonBase/buttonBaseClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getButtonBaseUtilityClass(slot) {\n  return generateUtilityClass('MuiButtonBase', slot);\n}\nconst buttonBaseClasses = generateUtilityClasses('MuiButtonBase', ['root', 'disabled', 'focusVisible']);\nexport default buttonBaseClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,0BAA0B,IAAI;IAC5C,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,iBAAiB;AAC/C;AACA,MAAM,oBAAoB,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,iBAAiB;IAAC;IAAQ;IAAY;CAAe;uCACvF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonBase/ButtonBase.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useLazyRipple from \"../useLazyRipple/index.js\";\nimport TouchRipple from \"./TouchRipple.js\";\nimport buttonBaseClasses, { getButtonBaseUtilityClass } from \"./buttonBaseClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    focusVisible,\n    focusVisibleClassName,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible']\n  };\n  const composedClasses = composeClasses(slots, getButtonBaseUtilityClass, classes);\n  if (focusVisible && focusVisibleClassName) {\n    composedClasses.root += ` ${focusVisibleClassName}`;\n  }\n  return composedClasses;\n};\nexport const ButtonBaseRoot = styled('button', {\n  name: 'MuiButtonBase',\n  slot: 'Root'\n})({\n  display: 'inline-flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  position: 'relative',\n  boxSizing: 'border-box',\n  WebkitTapHighlightColor: 'transparent',\n  backgroundColor: 'transparent',\n  // Reset default value\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0,\n  border: 0,\n  margin: 0,\n  // Remove the margin in Safari\n  borderRadius: 0,\n  padding: 0,\n  // Remove the padding in Firefox\n  cursor: 'pointer',\n  userSelect: 'none',\n  verticalAlign: 'middle',\n  MozAppearance: 'none',\n  // Reset\n  WebkitAppearance: 'none',\n  // Reset\n  textDecoration: 'none',\n  // So we take precedent over the style of a native <a /> element.\n  color: 'inherit',\n  '&::-moz-focus-inner': {\n    borderStyle: 'none' // Remove Firefox dotted outline.\n  },\n  [`&.${buttonBaseClasses.disabled}`]: {\n    pointerEvents: 'none',\n    // Disable link interactions\n    cursor: 'default'\n  },\n  '@media print': {\n    colorAdjust: 'exact'\n  }\n});\n\n/**\n * `ButtonBase` contains as few styles as possible.\n * It aims to be a simple building block for creating a button.\n * It contains a load of style reset and some focus/ripple logic.\n */\nconst ButtonBase = /*#__PURE__*/React.forwardRef(function ButtonBase(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiButtonBase'\n  });\n  const {\n    action,\n    centerRipple = false,\n    children,\n    className,\n    component = 'button',\n    disabled = false,\n    disableRipple = false,\n    disableTouchRipple = false,\n    focusRipple = false,\n    focusVisibleClassName,\n    LinkComponent = 'a',\n    onBlur,\n    onClick,\n    onContextMenu,\n    onDragLeave,\n    onFocus,\n    onFocusVisible,\n    onKeyDown,\n    onKeyUp,\n    onMouseDown,\n    onMouseLeave,\n    onMouseUp,\n    onTouchEnd,\n    onTouchMove,\n    onTouchStart,\n    tabIndex = 0,\n    TouchRippleProps,\n    touchRippleRef,\n    type,\n    ...other\n  } = props;\n  const buttonRef = React.useRef(null);\n  const ripple = useLazyRipple();\n  const handleRippleRef = useForkRef(ripple.ref, touchRippleRef);\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  if (disabled && focusVisible) {\n    setFocusVisible(false);\n  }\n  React.useImperativeHandle(action, () => ({\n    focusVisible: () => {\n      setFocusVisible(true);\n      buttonRef.current.focus();\n    }\n  }), []);\n  const enableTouchRipple = ripple.shouldMount && !disableRipple && !disabled;\n  React.useEffect(() => {\n    if (focusVisible && focusRipple && !disableRipple) {\n      ripple.pulsate();\n    }\n  }, [disableRipple, focusRipple, focusVisible, ripple]);\n  const handleMouseDown = useRippleHandler(ripple, 'start', onMouseDown, disableTouchRipple);\n  const handleContextMenu = useRippleHandler(ripple, 'stop', onContextMenu, disableTouchRipple);\n  const handleDragLeave = useRippleHandler(ripple, 'stop', onDragLeave, disableTouchRipple);\n  const handleMouseUp = useRippleHandler(ripple, 'stop', onMouseUp, disableTouchRipple);\n  const handleMouseLeave = useRippleHandler(ripple, 'stop', event => {\n    if (focusVisible) {\n      event.preventDefault();\n    }\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n  }, disableTouchRipple);\n  const handleTouchStart = useRippleHandler(ripple, 'start', onTouchStart, disableTouchRipple);\n  const handleTouchEnd = useRippleHandler(ripple, 'stop', onTouchEnd, disableTouchRipple);\n  const handleTouchMove = useRippleHandler(ripple, 'stop', onTouchMove, disableTouchRipple);\n  const handleBlur = useRippleHandler(ripple, 'stop', event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusVisible(false);\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  }, false);\n  const handleFocus = useEventCallback(event => {\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!buttonRef.current) {\n      buttonRef.current = event.currentTarget;\n    }\n    if (isFocusVisible(event.target)) {\n      setFocusVisible(true);\n      if (onFocusVisible) {\n        onFocusVisible(event);\n      }\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  });\n  const isNonNativeButton = () => {\n    const button = buttonRef.current;\n    return component && component !== 'button' && !(button.tagName === 'A' && button.href);\n  };\n  const handleKeyDown = useEventCallback(event => {\n    // Check if key is already down to avoid repeats being counted as multiple activations\n    if (focusRipple && !event.repeat && focusVisible && event.key === ' ') {\n      ripple.stop(event, () => {\n        ripple.start(event);\n      });\n    }\n    if (event.target === event.currentTarget && isNonNativeButton() && event.key === ' ') {\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n\n    // Keyboard accessibility for non interactive elements\n    if (event.target === event.currentTarget && isNonNativeButton() && event.key === 'Enter' && !disabled) {\n      event.preventDefault();\n      if (onClick) {\n        onClick(event);\n      }\n    }\n  });\n  const handleKeyUp = useEventCallback(event => {\n    // calling preventDefault in keyUp on a <button> will not dispatch a click event if Space is pressed\n    // https://codesandbox.io/p/sandbox/button-keyup-preventdefault-dn7f0\n    if (focusRipple && event.key === ' ' && focusVisible && !event.defaultPrevented) {\n      ripple.stop(event, () => {\n        ripple.pulsate(event);\n      });\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n\n    // Keyboard accessibility for non interactive elements\n    if (onClick && event.target === event.currentTarget && isNonNativeButton() && event.key === ' ' && !event.defaultPrevented) {\n      onClick(event);\n    }\n  });\n  let ComponentProp = component;\n  if (ComponentProp === 'button' && (other.href || other.to)) {\n    ComponentProp = LinkComponent;\n  }\n  const buttonProps = {};\n  if (ComponentProp === 'button') {\n    buttonProps.type = type === undefined ? 'button' : type;\n    buttonProps.disabled = disabled;\n  } else {\n    if (!other.href && !other.to) {\n      buttonProps.role = 'button';\n    }\n    if (disabled) {\n      buttonProps['aria-disabled'] = disabled;\n    }\n  }\n  const handleRef = useForkRef(ref, buttonRef);\n  const ownerState = {\n    ...props,\n    centerRipple,\n    component,\n    disabled,\n    disableRipple,\n    disableTouchRipple,\n    focusRipple,\n    tabIndex,\n    focusVisible\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(ButtonBaseRoot, {\n    as: ComponentProp,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    onBlur: handleBlur,\n    onClick: onClick,\n    onContextMenu: handleContextMenu,\n    onFocus: handleFocus,\n    onKeyDown: handleKeyDown,\n    onKeyUp: handleKeyUp,\n    onMouseDown: handleMouseDown,\n    onMouseLeave: handleMouseLeave,\n    onMouseUp: handleMouseUp,\n    onDragLeave: handleDragLeave,\n    onTouchEnd: handleTouchEnd,\n    onTouchMove: handleTouchMove,\n    onTouchStart: handleTouchStart,\n    ref: handleRef,\n    tabIndex: disabled ? -1 : tabIndex,\n    type: type,\n    ...buttonProps,\n    ...other,\n    children: [children, enableTouchRipple ? /*#__PURE__*/_jsx(TouchRipple, {\n      ref: handleRippleRef,\n      center: centerRipple,\n      ...TouchRippleProps\n    }) : null]\n  });\n});\nfunction useRippleHandler(ripple, rippleAction, eventCallback, skipRippleAction = false) {\n  return useEventCallback(event => {\n    if (eventCallback) {\n      eventCallback(event);\n    }\n    if (!skipRippleAction) {\n      ripple[rippleAction](event);\n    }\n    return true;\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ButtonBase.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports `focusVisible()` action.\n   */\n  action: refType,\n  /**\n   * If `true`, the ripples are centered.\n   * They won't start at the cursor interaction position.\n   * @default false\n   */\n  centerRipple: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: elementTypeAcceptingRef,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   *\n   * ⚠️ Without a ripple there is no styling for :focus-visible by default. Be sure\n   * to highlight the element by applying separate styles with the `.Mui-focusVisible` class.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If `true`, the touch ripple effect is disabled.\n   * @default false\n   */\n  disableTouchRipple: PropTypes.bool,\n  /**\n   * If `true`, the base button will have a keyboard focus ripple.\n   * @default false\n   */\n  focusRipple: PropTypes.bool,\n  /**\n   * This prop can help identify which element has keyboard focus.\n   * The class name will be applied when the element gains the focus through keyboard interaction.\n   * It's a polyfill for the [CSS :focus-visible selector](https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo).\n   * The rationale for using this feature [is explained here](https://github.com/WICG/focus-visible/blob/HEAD/explainer.md).\n   * A [polyfill can be used](https://github.com/WICG/focus-visible) to apply a `focus-visible` class to other components\n   * if needed.\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * @ignore\n   */\n  href: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The component used to render a link when the `href` prop is provided.\n   * @default 'a'\n   */\n  LinkComponent: PropTypes.elementType,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onContextMenu: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onDragLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the component is focused with a keyboard.\n   * We trigger a `onFocus` callback too.\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseUp: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onTouchEnd: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onTouchMove: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onTouchStart: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @default 0\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * Props applied to the `TouchRipple` element.\n   */\n  TouchRippleProps: PropTypes.object,\n  /**\n   * A ref that points to the `TouchRipple` element.\n   */\n  touchRippleRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      pulsate: PropTypes.func.isRequired,\n      start: PropTypes.func.isRequired,\n      stop: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * @ignore\n   */\n  type: PropTypes.oneOfType([PropTypes.oneOf(['button', 'reset', 'submit']), PropTypes.string])\n} : void 0;\nexport default ButtonBase;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;AAiBA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,qBAAqB,EACrB,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ,YAAY;YAAY,gBAAgB;SAAe;IACxE;IACA,MAAM,kBAAkB,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,2KAAA,CAAA,4BAAyB,EAAE;IACzE,IAAI,gBAAgB,uBAAuB;QACzC,gBAAgB,IAAI,IAAI,CAAC,CAAC,EAAE,uBAAuB;IACrD;IACA,OAAO;AACT;AACO,MAAM,iBAAiB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,UAAU;IAC7C,MAAM;IACN,MAAM;AACR,GAAG;IACD,SAAS;IACT,YAAY;IACZ,gBAAgB;IAChB,UAAU;IACV,WAAW;IACX,yBAAyB;IACzB,iBAAiB;IACjB,sBAAsB;IACtB,iEAAiE;IACjE,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,8BAA8B;IAC9B,cAAc;IACd,SAAS;IACT,gCAAgC;IAChC,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,eAAe;IACf,QAAQ;IACR,kBAAkB;IAClB,QAAQ;IACR,gBAAgB;IAChB,iEAAiE;IACjE,OAAO;IACP,uBAAuB;QACrB,aAAa,OAAO,iCAAiC;IACvD;IACA,CAAC,CAAC,EAAE,EAAE,2KAAA,CAAA,UAAiB,CAAC,QAAQ,EAAE,CAAC,EAAE;QACnC,eAAe;QACf,4BAA4B;QAC5B,QAAQ;IACV;IACA,gBAAgB;QACd,aAAa;IACf;AACF;AAEA;;;;CAIC,GACD,MAAM,aAAa,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,WAAW,OAAO,EAAE,GAAG;IAC/E,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,MAAM,EACN,eAAe,KAAK,EACpB,QAAQ,EACR,SAAS,EACT,YAAY,QAAQ,EACpB,WAAW,KAAK,EAChB,gBAAgB,KAAK,EACrB,qBAAqB,KAAK,EAC1B,cAAc,KAAK,EACnB,qBAAqB,EACrB,gBAAgB,GAAG,EACnB,MAAM,EACN,OAAO,EACP,aAAa,EACb,WAAW,EACX,OAAO,EACP,cAAc,EACd,SAAS,EACT,OAAO,EACP,WAAW,EACX,YAAY,EACZ,SAAS,EACT,UAAU,EACV,WAAW,EACX,YAAY,EACZ,WAAW,CAAC,EACZ,gBAAgB,EAChB,cAAc,EACd,IAAI,EACJ,GAAG,OACJ,GAAG;IACJ,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,0KAAA,CAAA,UAAa,AAAD;IAC3B,MAAM,kBAAkB,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO,GAAG,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACvD,IAAI,YAAY,cAAc;QAC5B,gBAAgB;IAClB;IACA,CAAA,GAAA,qMAAA,CAAA,sBAAyB,AAAD,EAAE,QAAQ,IAAM,CAAC;YACvC,cAAc;gBACZ,gBAAgB;gBAChB,UAAU,OAAO,CAAC,KAAK;YACzB;QACF,CAAC,GAAG,EAAE;IACN,MAAM,oBAAoB,OAAO,WAAW,IAAI,CAAC,iBAAiB,CAAC;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,gBAAgB,eAAe,CAAC,eAAe;YACjD,OAAO,OAAO;QAChB;IACF,GAAG;QAAC;QAAe;QAAa;QAAc;KAAO;IACrD,MAAM,kBAAkB,iBAAiB,QAAQ,SAAS,aAAa;IACvE,MAAM,oBAAoB,iBAAiB,QAAQ,QAAQ,eAAe;IAC1E,MAAM,kBAAkB,iBAAiB,QAAQ,QAAQ,aAAa;IACtE,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ,WAAW;IAClE,MAAM,mBAAmB,iBAAiB,QAAQ,QAAQ,CAAA;QACxD,IAAI,cAAc;YAChB,MAAM,cAAc;QACtB;QACA,IAAI,cAAc;YAChB,aAAa;QACf;IACF,GAAG;IACH,MAAM,mBAAmB,iBAAiB,QAAQ,SAAS,cAAc;IACzE,MAAM,iBAAiB,iBAAiB,QAAQ,QAAQ,YAAY;IACpE,MAAM,kBAAkB,iBAAiB,QAAQ,QAAQ,aAAa;IACtE,MAAM,aAAa,iBAAiB,QAAQ,QAAQ,CAAA;QAClD,IAAI,CAAC,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,MAAM,MAAM,GAAG;YACjC,gBAAgB;QAClB;QACA,IAAI,QAAQ;YACV,OAAO;QACT;IACF,GAAG;IACH,MAAM,cAAc,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,CAAA;QACnC,wDAAwD;QACxD,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB,UAAU,OAAO,GAAG,MAAM,aAAa;QACzC;QACA,IAAI,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,MAAM,MAAM,GAAG;YAChC,gBAAgB;YAChB,IAAI,gBAAgB;gBAClB,eAAe;YACjB;QACF;QACA,IAAI,SAAS;YACX,QAAQ;QACV;IACF;IACA,MAAM,oBAAoB;QACxB,MAAM,SAAS,UAAU,OAAO;QAChC,OAAO,aAAa,cAAc,YAAY,CAAC,CAAC,OAAO,OAAO,KAAK,OAAO,OAAO,IAAI;IACvF;IACA,MAAM,gBAAgB,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,CAAA;QACrC,sFAAsF;QACtF,IAAI,eAAe,CAAC,MAAM,MAAM,IAAI,gBAAgB,MAAM,GAAG,KAAK,KAAK;YACrE,OAAO,IAAI,CAAC,OAAO;gBACjB,OAAO,KAAK,CAAC;YACf;QACF;QACA,IAAI,MAAM,MAAM,KAAK,MAAM,aAAa,IAAI,uBAAuB,MAAM,GAAG,KAAK,KAAK;YACpF,MAAM,cAAc;QACtB;QACA,IAAI,WAAW;YACb,UAAU;QACZ;QAEA,sDAAsD;QACtD,IAAI,MAAM,MAAM,KAAK,MAAM,aAAa,IAAI,uBAAuB,MAAM,GAAG,KAAK,WAAW,CAAC,UAAU;YACrG,MAAM,cAAc;YACpB,IAAI,SAAS;gBACX,QAAQ;YACV;QACF;IACF;IACA,MAAM,cAAc,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,CAAA;QACnC,oGAAoG;QACpG,qEAAqE;QACrE,IAAI,eAAe,MAAM,GAAG,KAAK,OAAO,gBAAgB,CAAC,MAAM,gBAAgB,EAAE;YAC/E,OAAO,IAAI,CAAC,OAAO;gBACjB,OAAO,OAAO,CAAC;YACjB;QACF;QACA,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,sDAAsD;QACtD,IAAI,WAAW,MAAM,MAAM,KAAK,MAAM,aAAa,IAAI,uBAAuB,MAAM,GAAG,KAAK,OAAO,CAAC,MAAM,gBAAgB,EAAE;YAC1H,QAAQ;QACV;IACF;IACA,IAAI,gBAAgB;IACpB,IAAI,kBAAkB,YAAY,CAAC,MAAM,IAAI,IAAI,MAAM,EAAE,GAAG;QAC1D,gBAAgB;IAClB;IACA,MAAM,cAAc,CAAC;IACrB,IAAI,kBAAkB,UAAU;QAC9B,YAAY,IAAI,GAAG,SAAS,YAAY,WAAW;QACnD,YAAY,QAAQ,GAAG;IACzB,OAAO;QACL,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YAC5B,YAAY,IAAI,GAAG;QACrB;QACA,IAAI,UAAU;YACZ,WAAW,CAAC,gBAAgB,GAAG;QACjC;IACF;IACA,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,KAAK;IAClC,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,gBAAgB;QACxC,IAAI;QACJ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,YAAY;QACZ,QAAQ;QACR,SAAS;QACT,eAAe;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,aAAa;QACb,cAAc;QACd,WAAW;QACX,aAAa;QACb,YAAY;QACZ,aAAa;QACb,cAAc;QACd,KAAK;QACL,UAAU,WAAW,CAAC,IAAI;QAC1B,MAAM;QACN,GAAG,WAAW;QACd,GAAG,KAAK;QACR,UAAU;YAAC;YAAU,oBAAoB,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,qKAAA,CAAA,UAAW,EAAE;gBACtE,KAAK;gBACL,QAAQ;gBACR,GAAG,gBAAgB;YACrB,KAAK;SAAK;IACZ;AACF;AACA,SAAS,iBAAiB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,mBAAmB,KAAK;IACrF,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,CAAA;QACtB,IAAI,eAAe;YACjB,cAAc;QAChB;QACA,IAAI,CAAC,kBAAkB;YACrB,MAAM,CAAC,aAAa,CAAC;QACvB;QACA,OAAO;IACT;AACF;AACA,uCAAwC,WAAW,SAAS,GAA0B;IACpF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;;GAGC,GACD,QAAQ,2JAAA,CAAA,UAAO;IACf;;;;GAIC,GACD,cAAc,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;GAGC,GACD,WAAW,2LAAA,CAAA,UAAuB;IAClC;;;GAGC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;;;;;GAMC,GACD,eAAe,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B;;;GAGC,GACD,oBAAoB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAClC;;;GAGC,GACD,aAAa,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC3B;;;;;;;GAOC,GACD,uBAAuB,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvC;;GAEC,GACD,MAAM,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,GAAG;IACzD;;;GAGC,GACD,eAAe,sIAAA,CAAA,UAAS,CAAC,WAAW;IACpC;;GAEC,GACD,QAAQ,sIAAA,CAAA,UAAS,CAAC,IAAI;IACtB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI;IACvB;;GAEC,GACD,eAAe,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B;;GAEC,GACD,aAAa,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC3B;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI;IACvB;;;GAGC,GACD,gBAAgB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC9B;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;IACzB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI;IACvB;;GAEC,GACD,aAAa,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC3B;;GAEC,GACD,cAAc,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;IACzB;;GAEC,GACD,YAAY,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC1B;;GAEC,GACD,aAAa,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC3B;;GAEC,GACD,cAAc,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC1B;;GAEC,GACD,kBAAkB,sIAAA,CAAA,UAAS,CAAC,MAAM;IAClC;;GAEC,GACD,gBAAgB,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YACnE,SAAS,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;gBACvB,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;gBAClC,OAAO,sIAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;gBAChC,MAAM,sIAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;YACjC;QACF;KAAG;IACH;;GAEC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAU;YAAS;SAAS;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AAC9F;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/CircularProgress/circularProgressClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCircularProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiCircularProgress', slot);\n}\nconst circularProgressClasses = generateUtilityClasses('MuiCircularProgress', ['root', 'determinate', 'indeterminate', 'colorPrimary', 'colorSecondary', 'svg', 'circle', 'circleDeterminate', 'circleIndeterminate', 'circleDisableShrink']);\nexport default circularProgressClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,gCAAgC,IAAI;IAClD,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,uBAAuB;AACrD;AACA,MAAM,0BAA0B,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,uBAAuB;IAAC;IAAQ;IAAe;IAAiB;IAAgB;IAAkB;IAAO;IAAU;IAAqB;IAAuB;CAAsB;uCAC7N", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/CircularProgress/CircularProgress.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { keyframes, css, styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { getCircularProgressUtilityClass } from \"./circularProgressClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SIZE = 44;\nconst circularRotateKeyframe = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n`;\nconst circularDashKeyframe = keyframes`\n  0% {\n    stroke-dasharray: 1px, 200px;\n    stroke-dashoffset: 0;\n  }\n\n  50% {\n    stroke-dasharray: 100px, 200px;\n    stroke-dashoffset: -15px;\n  }\n\n  100% {\n    stroke-dasharray: 1px, 200px;\n    stroke-dashoffset: -126px;\n  }\n`;\n\n// This implementation is for supporting both Styled-components v4+ and Pigment CSS.\n// A global animation has to be created here for Styled-components v4+ (https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#12).\n// which can be done by checking typeof indeterminate1Keyframe !== 'string' (at runtime, Pigment CSS transform keyframes`` to a string).\nconst rotateAnimation = typeof circularRotateKeyframe !== 'string' ? css`\n        animation: ${circularRotateKeyframe} 1.4s linear infinite;\n      ` : null;\nconst dashAnimation = typeof circularDashKeyframe !== 'string' ? css`\n        animation: ${circularDashKeyframe} 1.4s ease-in-out infinite;\n      ` : null;\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    color,\n    disableShrink\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, `color${capitalize(color)}`],\n    svg: ['svg'],\n    circle: ['circle', `circle${capitalize(variant)}`, disableShrink && 'circleDisableShrink']\n  };\n  return composeClasses(slots, getCircularProgressUtilityClass, classes);\n};\nconst CircularProgressRoot = styled('span', {\n  name: 'MuiCircularProgress',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  variants: [{\n    props: {\n      variant: 'determinate'\n    },\n    style: {\n      transition: theme.transitions.create('transform')\n    }\n  }, {\n    props: {\n      variant: 'indeterminate'\n    },\n    style: rotateAnimation || {\n      animation: `${circularRotateKeyframe} 1.4s linear infinite`\n    }\n  }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      color: (theme.vars || theme).palette[color].main\n    }\n  }))]\n})));\nconst CircularProgressSVG = styled('svg', {\n  name: 'MuiCircularProgress',\n  slot: 'Svg'\n})({\n  display: 'block' // Keeps the progress centered\n});\nconst CircularProgressCircle = styled('circle', {\n  name: 'MuiCircularProgress',\n  slot: 'Circle',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.circle, styles[`circle${capitalize(ownerState.variant)}`], ownerState.disableShrink && styles.circleDisableShrink];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  stroke: 'currentColor',\n  variants: [{\n    props: {\n      variant: 'determinate'\n    },\n    style: {\n      transition: theme.transitions.create('stroke-dashoffset')\n    }\n  }, {\n    props: {\n      variant: 'indeterminate'\n    },\n    style: {\n      // Some default value that looks fine waiting for the animation to kicks in.\n      strokeDasharray: '80px, 200px',\n      strokeDashoffset: 0 // Add the unit to fix a Edge 16 and below bug.\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.variant === 'indeterminate' && !ownerState.disableShrink,\n    style: dashAnimation || {\n      // At runtime for Pigment CSS, `bufferAnimation` will be null and the generated keyframe will be used.\n      animation: `${circularDashKeyframe} 1.4s ease-in-out infinite`\n    }\n  }]\n})));\n\n/**\n * ## ARIA\n *\n * If the progress bar is describing the loading progress of a particular region of a page,\n * you should use `aria-describedby` to point to the progress bar, and set the `aria-busy`\n * attribute to `true` on that region until it has finished loading.\n */\nconst CircularProgress = /*#__PURE__*/React.forwardRef(function CircularProgress(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCircularProgress'\n  });\n  const {\n    className,\n    color = 'primary',\n    disableShrink = false,\n    size = 40,\n    style,\n    thickness = 3.6,\n    value = 0,\n    variant = 'indeterminate',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    disableShrink,\n    size,\n    thickness,\n    value,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const circleStyle = {};\n  const rootStyle = {};\n  const rootProps = {};\n  if (variant === 'determinate') {\n    const circumference = 2 * Math.PI * ((SIZE - thickness) / 2);\n    circleStyle.strokeDasharray = circumference.toFixed(3);\n    rootProps['aria-valuenow'] = Math.round(value);\n    circleStyle.strokeDashoffset = `${((100 - value) / 100 * circumference).toFixed(3)}px`;\n    rootStyle.transform = 'rotate(-90deg)';\n  }\n  return /*#__PURE__*/_jsx(CircularProgressRoot, {\n    className: clsx(classes.root, className),\n    style: {\n      width: size,\n      height: size,\n      ...rootStyle,\n      ...style\n    },\n    ownerState: ownerState,\n    ref: ref,\n    role: \"progressbar\",\n    ...rootProps,\n    ...other,\n    children: /*#__PURE__*/_jsx(CircularProgressSVG, {\n      className: classes.svg,\n      ownerState: ownerState,\n      viewBox: `${SIZE / 2} ${SIZE / 2} ${SIZE} ${SIZE}`,\n      children: /*#__PURE__*/_jsx(CircularProgressCircle, {\n        className: classes.circle,\n        style: circleStyle,\n        ownerState: ownerState,\n        cx: SIZE,\n        cy: SIZE,\n        r: (SIZE - thickness) / 2,\n        fill: \"none\",\n        strokeWidth: thickness\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CircularProgress.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * If `true`, the shrink animation is disabled.\n   * This only works if variant is `indeterminate`.\n   * @default false\n   */\n  disableShrink: chainPropTypes(PropTypes.bool, props => {\n    if (props.disableShrink && props.variant && props.variant !== 'indeterminate') {\n      return new Error('MUI: You have provided the `disableShrink` prop ' + 'with a variant other than `indeterminate`. This will have no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The size of the component.\n   * If using a number, the pixel unit is assumed.\n   * If using a string, you need to provide the CSS unit, for example '3rem'.\n   * @default 40\n   */\n  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The thickness of the circle.\n   * @default 3.6\n   */\n  thickness: PropTypes.number,\n  /**\n   * The value of the progress indicator for the determinate variant.\n   * Value between 0 and 100.\n   * @default 0\n   */\n  value: PropTypes.number,\n  /**\n   * The variant to use.\n   * Use indeterminate when there is no progress value.\n   * @default 'indeterminate'\n   */\n  variant: PropTypes.oneOf(['determinate', 'indeterminate'])\n} : void 0;\nexport default CircularProgress;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAcA,MAAM,OAAO;AACb,MAAM,yBAAyB,oMAAA,CAAA,YAAS,CAAC;;;;;;;;AAQzC,CAAC;AACD,MAAM,uBAAuB,oMAAA,CAAA,YAAS,CAAC;;;;;;;;;;;;;;;AAevC,CAAC;AAED,oFAAoF;AACpF,4LAA4L;AAC5L,wIAAwI;AACxI,MAAM,kBAAkB,OAAO,2BAA2B,WAAW,oMAAA,CAAA,MAAG,CAAC;mBACtD,EAAE,uBAAuB;MACtC,CAAC,GAAG;AACV,MAAM,gBAAgB,OAAO,yBAAyB,WAAW,oMAAA,CAAA,MAAG,CAAC;mBAClD,EAAE,qBAAqB;MACpC,CAAC,GAAG;AACV,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,OAAO,EACP,OAAO,EACP,KAAK,EACL,aAAa,EACd,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ;YAAS,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,QAAQ;SAAC;QACpD,KAAK;YAAC;SAAM;QACZ,QAAQ;YAAC;YAAU,CAAC,MAAM,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,UAAU;YAAE,iBAAiB;SAAsB;IAC5F;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,uLAAA,CAAA,kCAA+B,EAAE;AAChE;AACA,MAAM,uBAAuB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAC1C,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,MAAM,CAAC,WAAW,OAAO,CAAC;YAAE,MAAM,CAAC,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,KAAK,GAAG,CAAC;SAAC;IAClG;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN,GAAK,CAAC;QACL,SAAS;QACT,UAAU;YAAC;gBACT,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;gBACvC;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO,mBAAmB;oBACxB,WAAW,GAAG,uBAAuB,qBAAqB,CAAC;gBAC7D;YACF;eAAM,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,MAAM,CAAC,CAAA,GAAA,mLAAA,CAAA,UAA8B,AAAD,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK,CAAC;oBAC7F,OAAO;wBACL;oBACF;oBACA,OAAO;wBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;oBAClD;gBACF,CAAC;SAAG;IACN,CAAC;AACD,MAAM,sBAAsB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACxC,MAAM;IACN,MAAM;AACR,GAAG;IACD,SAAS,QAAQ,8BAA8B;AACjD;AACA,MAAM,yBAAyB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,UAAU;IAC9C,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,MAAM;YAAE,MAAM,CAAC,CAAC,MAAM,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,OAAO,GAAG,CAAC;YAAE,WAAW,aAAa,IAAI,OAAO,mBAAmB;SAAC;IACnI;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN,GAAK,CAAC;QACL,QAAQ;QACR,UAAU;YAAC;gBACT,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;gBACvC;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,4EAA4E;oBAC5E,iBAAiB;oBACjB,kBAAkB,EAAE,+CAA+C;gBACrE;YACF;YAAG;gBACD,OAAO,CAAC,EACN,UAAU,EACX,GAAK,WAAW,OAAO,KAAK,mBAAmB,CAAC,WAAW,aAAa;gBACzE,OAAO,iBAAiB;oBACtB,sGAAsG;oBACtG,WAAW,GAAG,qBAAqB,0BAA0B,CAAC;gBAChE;YACF;SAAE;IACJ,CAAC;AAED;;;;;;CAMC,GACD,MAAM,mBAAmB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,iBAAiB,OAAO,EAAE,GAAG;IAC3F,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,SAAS,EACT,QAAQ,SAAS,EACjB,gBAAgB,KAAK,EACrB,OAAO,EAAE,EACT,KAAK,EACL,YAAY,GAAG,EACf,QAAQ,CAAC,EACT,UAAU,eAAe,EACzB,GAAG,OACJ,GAAG;IACJ,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,MAAM,cAAc,CAAC;IACrB,MAAM,YAAY,CAAC;IACnB,MAAM,YAAY,CAAC;IACnB,IAAI,YAAY,eAAe;QAC7B,MAAM,gBAAgB,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,SAAS,IAAI,CAAC;QAC3D,YAAY,eAAe,GAAG,cAAc,OAAO,CAAC;QACpD,SAAS,CAAC,gBAAgB,GAAG,KAAK,KAAK,CAAC;QACxC,YAAY,gBAAgB,GAAG,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,MAAM,aAAa,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QACtF,UAAU,SAAS,GAAG;IACxB;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,sBAAsB;QAC7C,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,OAAO;YACL,OAAO;YACP,QAAQ;YACR,GAAG,SAAS;YACZ,GAAG,KAAK;QACV;QACA,YAAY;QACZ,KAAK;QACL,MAAM;QACN,GAAG,SAAS;QACZ,GAAG,KAAK;QACR,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,qBAAqB;YAC/C,WAAW,QAAQ,GAAG;YACtB,YAAY;YACZ,SAAS,GAAG,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM;YAClD,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wBAAwB;gBAClD,WAAW,QAAQ,MAAM;gBACzB,OAAO;gBACP,YAAY;gBACZ,IAAI;gBACJ,IAAI;gBACJ,GAAG,CAAC,OAAO,SAAS,IAAI;gBACxB,MAAM;gBACN,aAAa;YACf;QACF;IACF;AACF;AACA,uCAAwC,iBAAiB,SAAS,GAA0B;IAC1F,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;;GAKC,GACD,OAAO,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAW;YAAW;YAAa;YAAS;YAAQ;YAAW;SAAU;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAChL;;;;GAIC,GACD,eAAe,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,sIAAA,CAAA,UAAS,CAAC,IAAI,EAAE,CAAA;QAC5C,IAAI,MAAM,aAAa,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,iBAAiB;YAC7E,OAAO,IAAI,MAAM,qDAAqD;QACxE;QACA,OAAO;IACT;IACA;;;;;GAKC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC9D;;GAEC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvB;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;GAIC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvB;;;;GAIC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAAC;QAAe;KAAgB;AAC3D;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Button/buttonClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiButton', slot);\n}\nconst buttonClasses = generateUtilityClasses('MuiButton', ['root', 'text', 'textInherit', 'textPrimary', 'textSecondary', 'textSuccess', 'textError', 'textInfo', 'textWarning', 'outlined', 'outlinedInherit', 'outlinedPrimary', 'outlinedSecondary', 'outlinedSuccess', 'outlinedError', 'outlinedInfo', 'outlinedWarning', 'contained', 'containedInherit', 'containedPrimary', 'containedSecondary', 'containedSuccess', 'containedError', 'containedInfo', 'containedWarning', 'disableElevation', 'focusVisible', 'disabled', 'colorInherit', 'colorPrimary', 'colorSecondary', 'colorSuccess', 'colorError', 'colorInfo', 'colorWarning', 'textSizeSmall', 'textSizeMedium', 'textSizeLarge', 'outlinedSizeSmall', 'outlinedSizeMedium', 'outlinedSizeLarge', 'containedSizeSmall', 'containedSizeMedium', 'containedSizeLarge', 'sizeMedium', 'sizeSmall', 'sizeLarge', 'fullWidth', 'startIcon', 'endIcon', 'icon', 'iconSizeSmall', 'iconSizeMedium', 'iconSizeLarge', 'loading', 'loadingWrapper', 'loadingIconPlaceholder', 'loadingIndicator', 'loadingPositionCenter', 'loadingPositionStart', 'loadingPositionEnd']);\nexport default buttonClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,sBAAsB,IAAI;IACxC,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,aAAa;AAC3C;AACA,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,aAAa;IAAC;IAAQ;IAAQ;IAAe;IAAe;IAAiB;IAAe;IAAa;IAAY;IAAe;IAAY;IAAmB;IAAmB;IAAqB;IAAmB;IAAiB;IAAgB;IAAmB;IAAa;IAAoB;IAAoB;IAAsB;IAAoB;IAAkB;IAAiB;IAAoB;IAAoB;IAAgB;IAAY;IAAgB;IAAgB;IAAkB;IAAgB;IAAc;IAAa;IAAgB;IAAiB;IAAkB;IAAiB;IAAqB;IAAsB;IAAqB;IAAsB;IAAuB;IAAsB;IAAc;IAAa;IAAa;IAAa;IAAa;IAAW;IAAQ;IAAiB;IAAkB;IAAiB;IAAW;IAAkB;IAA0B;IAAoB;IAAyB;IAAwB;CAAqB;uCACpjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonGroup/ButtonGroupContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupContext.displayName = 'ButtonGroupContext';\n}\nexport default ButtonGroupContext;"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA;;CAEC,GACD,MAAM,qBAAqB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,CAAC;AAC7D,wCAA2C;IACzC,mBAAmB,WAAW,GAAG;AACnC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/ButtonGroup/ButtonGroupButtonContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupButtonContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupButtonContext.displayName = 'ButtonGroupButtonContext';\n}\nexport default ButtonGroupButtonContext;"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA;;CAEC,GACD,MAAM,2BAA2B,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE;AAClE,wCAA2C;IACzC,yBAAyB,WAAW,GAAG;AACzC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Button/Button.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport resolveProps from '@mui/utils/resolveProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { unstable_useId as useId } from \"../utils/index.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport CircularProgress from \"../CircularProgress/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport buttonClasses, { getButtonUtilityClass } from \"./buttonClasses.js\";\nimport ButtonGroupContext from \"../ButtonGroup/ButtonGroupContext.js\";\nimport ButtonGroupButtonContext from \"../ButtonGroup/ButtonGroupButtonContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    disableElevation,\n    fullWidth,\n    size,\n    variant,\n    loading,\n    loadingPosition,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', loading && 'loading', variant, `${variant}${capitalize(color)}`, `size${capitalize(size)}`, `${variant}Size${capitalize(size)}`, `color${capitalize(color)}`, disableElevation && 'disableElevation', fullWidth && 'fullWidth', loading && `loadingPosition${capitalize(loadingPosition)}`],\n    startIcon: ['icon', 'startIcon', `iconSize${capitalize(size)}`],\n    endIcon: ['icon', 'endIcon', `iconSize${capitalize(size)}`],\n    loadingIndicator: ['loadingIndicator'],\n    loadingWrapper: ['loadingWrapper']\n  };\n  const composedClasses = composeClasses(slots, getButtonUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the focused, disabled, etc. classes to the ButtonBase\n    ...composedClasses\n  };\n};\nconst commonIconStyles = [{\n  props: {\n    size: 'small'\n  },\n  style: {\n    '& > *:nth-of-type(1)': {\n      fontSize: 18\n    }\n  }\n}, {\n  props: {\n    size: 'medium'\n  },\n  style: {\n    '& > *:nth-of-type(1)': {\n      fontSize: 20\n    }\n  }\n}, {\n  props: {\n    size: 'large'\n  },\n  style: {\n    '& > *:nth-of-type(1)': {\n      fontSize: 22\n    }\n  }\n}];\nconst ButtonRoot = styled(ButtonBase, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`${ownerState.variant}${capitalize(ownerState.color)}`], styles[`size${capitalize(ownerState.size)}`], styles[`${ownerState.variant}Size${capitalize(ownerState.size)}`], ownerState.color === 'inherit' && styles.colorInherit, ownerState.disableElevation && styles.disableElevation, ownerState.fullWidth && styles.fullWidth, ownerState.loading && styles.loading];\n  }\n})(memoTheme(({\n  theme\n}) => {\n  const inheritContainedBackgroundColor = theme.palette.mode === 'light' ? theme.palette.grey[300] : theme.palette.grey[800];\n  const inheritContainedHoverBackgroundColor = theme.palette.mode === 'light' ? theme.palette.grey.A100 : theme.palette.grey[700];\n  return {\n    ...theme.typography.button,\n    minWidth: 64,\n    padding: '6px 16px',\n    border: 0,\n    borderRadius: (theme.vars || theme).shape.borderRadius,\n    transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color', 'color'], {\n      duration: theme.transitions.duration.short\n    }),\n    '&:hover': {\n      textDecoration: 'none'\n    },\n    [`&.${buttonClasses.disabled}`]: {\n      color: (theme.vars || theme).palette.action.disabled\n    },\n    variants: [{\n      props: {\n        variant: 'contained'\n      },\n      style: {\n        color: `var(--variant-containedColor)`,\n        backgroundColor: `var(--variant-containedBg)`,\n        boxShadow: (theme.vars || theme).shadows[2],\n        '&:hover': {\n          boxShadow: (theme.vars || theme).shadows[4],\n          // Reset on touch devices, it doesn't add specificity\n          '@media (hover: none)': {\n            boxShadow: (theme.vars || theme).shadows[2]\n          }\n        },\n        '&:active': {\n          boxShadow: (theme.vars || theme).shadows[8]\n        },\n        [`&.${buttonClasses.focusVisible}`]: {\n          boxShadow: (theme.vars || theme).shadows[6]\n        },\n        [`&.${buttonClasses.disabled}`]: {\n          color: (theme.vars || theme).palette.action.disabled,\n          boxShadow: (theme.vars || theme).shadows[0],\n          backgroundColor: (theme.vars || theme).palette.action.disabledBackground\n        }\n      }\n    }, {\n      props: {\n        variant: 'outlined'\n      },\n      style: {\n        padding: '5px 15px',\n        border: '1px solid currentColor',\n        borderColor: `var(--variant-outlinedBorder, currentColor)`,\n        backgroundColor: `var(--variant-outlinedBg)`,\n        color: `var(--variant-outlinedColor)`,\n        [`&.${buttonClasses.disabled}`]: {\n          border: `1px solid ${(theme.vars || theme).palette.action.disabledBackground}`\n        }\n      }\n    }, {\n      props: {\n        variant: 'text'\n      },\n      style: {\n        padding: '6px 8px',\n        color: `var(--variant-textColor)`,\n        backgroundColor: `var(--variant-textBg)`\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({\n      props: {\n        color\n      },\n      style: {\n        '--variant-textColor': (theme.vars || theme).palette[color].main,\n        '--variant-outlinedColor': (theme.vars || theme).palette[color].main,\n        '--variant-outlinedBorder': theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.5)` : alpha(theme.palette[color].main, 0.5),\n        '--variant-containedColor': (theme.vars || theme).palette[color].contrastText,\n        '--variant-containedBg': (theme.vars || theme).palette[color].main,\n        '@media (hover: hover)': {\n          '&:hover': {\n            '--variant-containedBg': (theme.vars || theme).palette[color].dark,\n            '--variant-textBg': theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n            '--variant-outlinedBorder': (theme.vars || theme).palette[color].main,\n            '--variant-outlinedBg': theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity)\n          }\n        }\n      }\n    })), {\n      props: {\n        color: 'inherit'\n      },\n      style: {\n        color: 'inherit',\n        borderColor: 'currentColor',\n        '--variant-containedBg': theme.vars ? theme.vars.palette.Button.inheritContainedBg : inheritContainedBackgroundColor,\n        '@media (hover: hover)': {\n          '&:hover': {\n            '--variant-containedBg': theme.vars ? theme.vars.palette.Button.inheritContainedHoverBg : inheritContainedHoverBackgroundColor,\n            '--variant-textBg': theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.text.primary, theme.palette.action.hoverOpacity),\n            '--variant-outlinedBg': theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.text.primary, theme.palette.action.hoverOpacity)\n          }\n        }\n      }\n    }, {\n      props: {\n        size: 'small',\n        variant: 'text'\n      },\n      style: {\n        padding: '4px 5px',\n        fontSize: theme.typography.pxToRem(13)\n      }\n    }, {\n      props: {\n        size: 'large',\n        variant: 'text'\n      },\n      style: {\n        padding: '8px 11px',\n        fontSize: theme.typography.pxToRem(15)\n      }\n    }, {\n      props: {\n        size: 'small',\n        variant: 'outlined'\n      },\n      style: {\n        padding: '3px 9px',\n        fontSize: theme.typography.pxToRem(13)\n      }\n    }, {\n      props: {\n        size: 'large',\n        variant: 'outlined'\n      },\n      style: {\n        padding: '7px 21px',\n        fontSize: theme.typography.pxToRem(15)\n      }\n    }, {\n      props: {\n        size: 'small',\n        variant: 'contained'\n      },\n      style: {\n        padding: '4px 10px',\n        fontSize: theme.typography.pxToRem(13)\n      }\n    }, {\n      props: {\n        size: 'large',\n        variant: 'contained'\n      },\n      style: {\n        padding: '8px 22px',\n        fontSize: theme.typography.pxToRem(15)\n      }\n    }, {\n      props: {\n        disableElevation: true\n      },\n      style: {\n        boxShadow: 'none',\n        '&:hover': {\n          boxShadow: 'none'\n        },\n        [`&.${buttonClasses.focusVisible}`]: {\n          boxShadow: 'none'\n        },\n        '&:active': {\n          boxShadow: 'none'\n        },\n        [`&.${buttonClasses.disabled}`]: {\n          boxShadow: 'none'\n        }\n      }\n    }, {\n      props: {\n        fullWidth: true\n      },\n      style: {\n        width: '100%'\n      }\n    }, {\n      props: {\n        loadingPosition: 'center'\n      },\n      style: {\n        transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n          duration: theme.transitions.duration.short\n        }),\n        [`&.${buttonClasses.loading}`]: {\n          color: 'transparent'\n        }\n      }\n    }]\n  };\n}));\nconst ButtonStartIcon = styled('span', {\n  name: 'MuiButton',\n  slot: 'StartIcon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.startIcon, ownerState.loading && styles.startIconLoadingStart, styles[`iconSize${capitalize(ownerState.size)}`]];\n  }\n})(({\n  theme\n}) => ({\n  display: 'inherit',\n  marginRight: 8,\n  marginLeft: -4,\n  variants: [{\n    props: {\n      size: 'small'\n    },\n    style: {\n      marginLeft: -2\n    }\n  }, {\n    props: {\n      loadingPosition: 'start',\n      loading: true\n    },\n    style: {\n      transition: theme.transitions.create(['opacity'], {\n        duration: theme.transitions.duration.short\n      }),\n      opacity: 0\n    }\n  }, {\n    props: {\n      loadingPosition: 'start',\n      loading: true,\n      fullWidth: true\n    },\n    style: {\n      marginRight: -8\n    }\n  }, ...commonIconStyles]\n}));\nconst ButtonEndIcon = styled('span', {\n  name: 'MuiButton',\n  slot: 'EndIcon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.endIcon, ownerState.loading && styles.endIconLoadingEnd, styles[`iconSize${capitalize(ownerState.size)}`]];\n  }\n})(({\n  theme\n}) => ({\n  display: 'inherit',\n  marginRight: -4,\n  marginLeft: 8,\n  variants: [{\n    props: {\n      size: 'small'\n    },\n    style: {\n      marginRight: -2\n    }\n  }, {\n    props: {\n      loadingPosition: 'end',\n      loading: true\n    },\n    style: {\n      transition: theme.transitions.create(['opacity'], {\n        duration: theme.transitions.duration.short\n      }),\n      opacity: 0\n    }\n  }, {\n    props: {\n      loadingPosition: 'end',\n      loading: true,\n      fullWidth: true\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, ...commonIconStyles]\n}));\nconst ButtonLoadingIndicator = styled('span', {\n  name: 'MuiButton',\n  slot: 'LoadingIndicator'\n})(({\n  theme\n}) => ({\n  display: 'none',\n  position: 'absolute',\n  visibility: 'visible',\n  variants: [{\n    props: {\n      loading: true\n    },\n    style: {\n      display: 'flex'\n    }\n  }, {\n    props: {\n      loadingPosition: 'start'\n    },\n    style: {\n      left: 14\n    }\n  }, {\n    props: {\n      loadingPosition: 'start',\n      size: 'small'\n    },\n    style: {\n      left: 10\n    }\n  }, {\n    props: {\n      variant: 'text',\n      loadingPosition: 'start'\n    },\n    style: {\n      left: 6\n    }\n  }, {\n    props: {\n      loadingPosition: 'center'\n    },\n    style: {\n      left: '50%',\n      transform: 'translate(-50%)',\n      color: (theme.vars || theme).palette.action.disabled\n    }\n  }, {\n    props: {\n      loadingPosition: 'end'\n    },\n    style: {\n      right: 14\n    }\n  }, {\n    props: {\n      loadingPosition: 'end',\n      size: 'small'\n    },\n    style: {\n      right: 10\n    }\n  }, {\n    props: {\n      variant: 'text',\n      loadingPosition: 'end'\n    },\n    style: {\n      right: 6\n    }\n  }, {\n    props: {\n      loadingPosition: 'start',\n      fullWidth: true\n    },\n    style: {\n      position: 'relative',\n      left: -10\n    }\n  }, {\n    props: {\n      loadingPosition: 'end',\n      fullWidth: true\n    },\n    style: {\n      position: 'relative',\n      right: -10\n    }\n  }]\n}));\nconst ButtonLoadingIconPlaceholder = styled('span', {\n  name: 'MuiButton',\n  slot: 'LoadingIconPlaceholder'\n})({\n  display: 'inline-block',\n  width: '1em',\n  height: '1em'\n});\nconst Button = /*#__PURE__*/React.forwardRef(function Button(inProps, ref) {\n  // props priority: `inProps` > `contextProps` > `themeDefaultProps`\n  const contextProps = React.useContext(ButtonGroupContext);\n  const buttonGroupButtonContextPositionClassName = React.useContext(ButtonGroupButtonContext);\n  const resolvedProps = resolveProps(contextProps, inProps);\n  const props = useDefaultProps({\n    props: resolvedProps,\n    name: 'MuiButton'\n  });\n  const {\n    children,\n    color = 'primary',\n    component = 'button',\n    className,\n    disabled = false,\n    disableElevation = false,\n    disableFocusRipple = false,\n    endIcon: endIconProp,\n    focusVisibleClassName,\n    fullWidth = false,\n    id: idProp,\n    loading = null,\n    loadingIndicator: loadingIndicatorProp,\n    loadingPosition = 'center',\n    size = 'medium',\n    startIcon: startIconProp,\n    type,\n    variant = 'text',\n    ...other\n  } = props;\n  const loadingId = useId(idProp);\n  const loadingIndicator = loadingIndicatorProp ?? /*#__PURE__*/_jsx(CircularProgress, {\n    \"aria-labelledby\": loadingId,\n    color: \"inherit\",\n    size: 16\n  });\n  const ownerState = {\n    ...props,\n    color,\n    component,\n    disabled,\n    disableElevation,\n    disableFocusRipple,\n    fullWidth,\n    loading,\n    loadingIndicator,\n    loadingPosition,\n    size,\n    type,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const startIcon = (startIconProp || loading && loadingPosition === 'start') && /*#__PURE__*/_jsx(ButtonStartIcon, {\n    className: classes.startIcon,\n    ownerState: ownerState,\n    children: startIconProp || /*#__PURE__*/_jsx(ButtonLoadingIconPlaceholder, {\n      className: classes.loadingIconPlaceholder,\n      ownerState: ownerState\n    })\n  });\n  const endIcon = (endIconProp || loading && loadingPosition === 'end') && /*#__PURE__*/_jsx(ButtonEndIcon, {\n    className: classes.endIcon,\n    ownerState: ownerState,\n    children: endIconProp || /*#__PURE__*/_jsx(ButtonLoadingIconPlaceholder, {\n      className: classes.loadingIconPlaceholder,\n      ownerState: ownerState\n    })\n  });\n  const positionClassName = buttonGroupButtonContextPositionClassName || '';\n  const loader = typeof loading === 'boolean' ?\n  /*#__PURE__*/\n  // use plain HTML span to minimize the runtime overhead\n  _jsx(\"span\", {\n    className: classes.loadingWrapper,\n    style: {\n      display: 'contents'\n    },\n    children: loading && /*#__PURE__*/_jsx(ButtonLoadingIndicator, {\n      className: classes.loadingIndicator,\n      ownerState: ownerState,\n      children: loadingIndicator\n    })\n  }) : null;\n  return /*#__PURE__*/_jsxs(ButtonRoot, {\n    ownerState: ownerState,\n    className: clsx(contextProps.className, classes.root, className, positionClassName),\n    component: component,\n    disabled: disabled || loading,\n    focusRipple: !disableFocusRipple,\n    focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n    ref: ref,\n    type: type,\n    id: loading ? loadingId : idProp,\n    ...other,\n    classes: classes,\n    children: [startIcon, loadingPosition !== 'end' && loader, children, loadingPosition === 'end' && loader, endIcon]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Button.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'primary', 'secondary', 'success', 'error', 'info', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, no elevation is used.\n   * @default false\n   */\n  disableElevation: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   *\n   * ⚠️ Without a ripple there is no styling for :focus-visible by default. Be sure\n   * to highlight the element by applying separate styles with the `.Mui-focusVisible` class.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * Element placed after the children.\n   */\n  endIcon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * If `true`, the button will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The URL to link to when the button is clicked.\n   * If defined, an `a` element will be used as the root node.\n   */\n  href: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the loading indicator is visible and the button is disabled.\n   * If `true | false`, the loading wrapper is always rendered before the children to prevent [Google Translation Crash](https://github.com/mui/material-ui/issues/27853).\n   * @default null\n   */\n  loading: PropTypes.bool,\n  /**\n   * Element placed before the children if the button is in loading state.\n   * The node should contain an element with `role=\"progressbar\"` with an accessible name.\n   * By default, it renders a `CircularProgress` that is labeled by the button itself.\n   * @default <CircularProgress color=\"inherit\" size={16} />\n   */\n  loadingIndicator: PropTypes.node,\n  /**\n   * The loading indicator can be positioned on the start, end, or the center of the button.\n   * @default 'center'\n   */\n  loadingPosition: PropTypes.oneOf(['center', 'end', 'start']),\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense button styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * Element placed before the children.\n   */\n  startIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  type: PropTypes.oneOfType([PropTypes.oneOf(['button', 'reset', 'submit']), PropTypes.string]),\n  /**\n   * The variant to use.\n   * @default 'text'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['contained', 'outlined', 'text']), PropTypes.string])\n} : void 0;\nexport default Button;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;;;;;AAqBA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,KAAK,EACL,gBAAgB,EAChB,SAAS,EACT,IAAI,EACJ,OAAO,EACP,OAAO,EACP,eAAe,EACf,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ,WAAW;YAAW;YAAS,GAAG,UAAU,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,QAAQ;YAAE,CAAC,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO;YAAE,GAAG,QAAQ,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO;YAAE,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,QAAQ;YAAE,oBAAoB;YAAoB,aAAa;YAAa,WAAW,CAAC,eAAe,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,kBAAkB;SAAC;QAC1S,WAAW;YAAC;YAAQ;YAAa,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO;SAAC;QAC/D,SAAS;YAAC;YAAQ;YAAW,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO;SAAC;QAC3D,kBAAkB;YAAC;SAAmB;QACtC,gBAAgB;YAAC;SAAiB;IACpC;IACA,MAAM,kBAAkB,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,mKAAA,CAAA,wBAAqB,EAAE;IACrE,OAAO;QACL,GAAG,OAAO;QACV,gEAAgE;QAChE,GAAG,eAAe;IACpB;AACF;AACA,MAAM,mBAAmB;IAAC;QACxB,OAAO;YACL,MAAM;QACR;QACA,OAAO;YACL,wBAAwB;gBACtB,UAAU;YACZ;QACF;IACF;IAAG;QACD,OAAO;YACL,MAAM;QACR;QACA,OAAO;YACL,wBAAwB;gBACtB,UAAU;YACZ;QACF;IACF;IAAG;QACD,OAAO;YACL,MAAM;QACR;QACA,OAAO;YACL,wBAAwB;gBACtB,UAAU;YACZ;QACF;IACF;CAAE;AACF,MAAM,aAAa,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,oKAAA,CAAA,UAAU,EAAE;IACpC,mBAAmB,CAAA,OAAQ,CAAA,GAAA,2KAAA,CAAA,UAAqB,AAAD,EAAE,SAAS,SAAS;IACnE,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,MAAM,CAAC,WAAW,OAAO,CAAC;YAAE,MAAM,CAAC,GAAG,WAAW,OAAO,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,KAAK,GAAG,CAAC;YAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,IAAI,GAAG,CAAC;YAAE,MAAM,CAAC,GAAG,WAAW,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,IAAI,GAAG,CAAC;YAAE,WAAW,KAAK,KAAK,aAAa,OAAO,YAAY;YAAE,WAAW,gBAAgB,IAAI,OAAO,gBAAgB;YAAE,WAAW,SAAS,IAAI,OAAO,SAAS;YAAE,WAAW,OAAO,IAAI,OAAO,OAAO;SAAC;IACla;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN;IACC,MAAM,kCAAkC,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI;IAC1H,MAAM,uCAAuC,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI;IAC/H,OAAO;QACL,GAAG,MAAM,UAAU,CAAC,MAAM;QAC1B,UAAU;QACV,SAAS;QACT,QAAQ;QACR,cAAc,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,KAAK,CAAC,YAAY;QACtD,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;YAAC;YAAoB;YAAc;YAAgB;SAAQ,EAAE;YAChG,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK;QAC5C;QACA,WAAW;YACT,gBAAgB;QAClB;QACA,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,QAAQ,EAAE,CAAC,EAAE;YAC/B,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;QACtD;QACA,UAAU;YAAC;gBACT,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO,CAAC,6BAA6B,CAAC;oBACtC,iBAAiB,CAAC,0BAA0B,CAAC;oBAC7C,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;oBAC3C,WAAW;wBACT,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;wBAC3C,qDAAqD;wBACrD,wBAAwB;4BACtB,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;wBAC7C;oBACF;oBACA,YAAY;wBACV,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;oBAC7C;oBACA,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,YAAY,EAAE,CAAC,EAAE;wBACnC,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;oBAC7C;oBACA,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,QAAQ,EAAE,CAAC,EAAE;wBAC/B,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;wBACpD,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;wBAC3C,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,kBAAkB;oBAC1E;gBACF;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR,aAAa,CAAC,2CAA2C,CAAC;oBAC1D,iBAAiB,CAAC,yBAAyB,CAAC;oBAC5C,OAAO,CAAC,4BAA4B,CAAC;oBACrC,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,QAAQ,EAAE,CAAC,EAAE;wBAC/B,QAAQ,CAAC,UAAU,EAAE,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE;oBAChF;gBACF;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,wBAAwB,CAAC;oBACjC,iBAAiB,CAAC,qBAAqB,CAAC;gBAC1C;YACF;eAAM,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,MAAM,CAAC,CAAA,GAAA,mLAAA,CAAA,UAA8B,AAAD,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK,CAAC;oBAC7F,OAAO;wBACL;oBACF;oBACA,OAAO;wBACL,uBAAuB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;wBAChE,2BAA2B,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;wBACpE,4BAA4B,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;wBACnI,4BAA4B,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY;wBAC7E,yBAAyB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;wBAClE,yBAAyB;4BACvB,WAAW;gCACT,yBAAyB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;gCAClE,oBAAoB,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,YAAY;gCAClM,4BAA4B,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;gCACrE,wBAAwB,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,YAAY;4BACxM;wBACF;oBACF;gBACF,CAAC;YAAI;gBACH,OAAO;oBACL,OAAO;gBACT;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,yBAAyB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,GAAG;oBACrF,yBAAyB;wBACvB,WAAW;4BACT,yBAAyB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,uBAAuB,GAAG;4BAC1F,oBAAoB,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,YAAY;4BACpM,wBAAwB,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,YAAY;wBAC1M;oBACF;gBACF;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,UAAU,MAAM,UAAU,CAAC,OAAO,CAAC;gBACrC;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,UAAU,MAAM,UAAU,CAAC,OAAO,CAAC;gBACrC;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,UAAU,MAAM,UAAU,CAAC,OAAO,CAAC;gBACrC;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,UAAU,MAAM,UAAU,CAAC,OAAO,CAAC;gBACrC;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,UAAU,MAAM,UAAU,CAAC,OAAO,CAAC;gBACrC;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;oBACT,UAAU,MAAM,UAAU,CAAC,OAAO,CAAC;gBACrC;YACF;YAAG;gBACD,OAAO;oBACL,kBAAkB;gBACpB;gBACA,OAAO;oBACL,WAAW;oBACX,WAAW;wBACT,WAAW;oBACb;oBACA,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,YAAY,EAAE,CAAC,EAAE;wBACnC,WAAW;oBACb;oBACA,YAAY;wBACV,WAAW;oBACb;oBACA,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,QAAQ,EAAE,CAAC,EAAE;wBAC/B,WAAW;oBACb;gBACF;YACF;YAAG;gBACD,OAAO;oBACL,WAAW;gBACb;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;gBACnB;gBACA,OAAO;oBACL,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;wBAAC;wBAAoB;wBAAc;qBAAe,EAAE;wBACvF,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK;oBAC5C;oBACA,CAAC,CAAC,EAAE,EAAE,mKAAA,CAAA,UAAa,CAAC,OAAO,EAAE,CAAC,EAAE;wBAC9B,OAAO;oBACT;gBACF;YACF;SAAE;IACJ;AACF;AACA,MAAM,kBAAkB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IACrC,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,SAAS;YAAE,WAAW,OAAO,IAAI,OAAO,qBAAqB;YAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,IAAI,GAAG,CAAC;SAAC;IACjI;AACF,GAAG,CAAC,EACF,KAAK,EACN,GAAK,CAAC;QACL,SAAS;QACT,aAAa;QACb,YAAY,CAAC;QACb,UAAU;YAAC;gBACT,OAAO;oBACL,MAAM;gBACR;gBACA,OAAO;oBACL,YAAY,CAAC;gBACf;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,SAAS;gBACX;gBACA,OAAO;oBACL,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;wBAAC;qBAAU,EAAE;wBAChD,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK;oBAC5C;oBACA,SAAS;gBACX;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,SAAS;oBACT,WAAW;gBACb;gBACA,OAAO;oBACL,aAAa,CAAC;gBAChB;YACF;eAAM;SAAiB;IACzB,CAAC;AACD,MAAM,gBAAgB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IACnC,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,OAAO;YAAE,WAAW,OAAO,IAAI,OAAO,iBAAiB;YAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,IAAI,GAAG,CAAC;SAAC;IAC3H;AACF,GAAG,CAAC,EACF,KAAK,EACN,GAAK,CAAC;QACL,SAAS;QACT,aAAa,CAAC;QACd,YAAY;QACZ,UAAU;YAAC;gBACT,OAAO;oBACL,MAAM;gBACR;gBACA,OAAO;oBACL,aAAa,CAAC;gBAChB;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,SAAS;gBACX;gBACA,OAAO;oBACL,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;wBAAC;qBAAU,EAAE;wBAChD,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK;oBAC5C;oBACA,SAAS;gBACX;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,SAAS;oBACT,WAAW;gBACb;gBACA,OAAO;oBACL,YAAY,CAAC;gBACf;YACF;eAAM;SAAiB;IACzB,CAAC;AACD,MAAM,yBAAyB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAC5C,MAAM;IACN,MAAM;AACR,GAAG,CAAC,EACF,KAAK,EACN,GAAK,CAAC;QACL,SAAS;QACT,UAAU;QACV,YAAY;QACZ,UAAU;YAAC;gBACT,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;gBACnB;gBACA,OAAO;oBACL,MAAM;gBACR;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,MAAM;gBACR;gBACA,OAAO;oBACL,MAAM;gBACR;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;oBACT,iBAAiB;gBACnB;gBACA,OAAO;oBACL,MAAM;gBACR;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;gBACnB;gBACA,OAAO;oBACL,MAAM;oBACN,WAAW;oBACX,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;gBACtD;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;gBACnB;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,MAAM;gBACR;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;oBACT,iBAAiB;gBACnB;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,WAAW;gBACb;gBACA,OAAO;oBACL,UAAU;oBACV,MAAM,CAAC;gBACT;YACF;YAAG;gBACD,OAAO;oBACL,iBAAiB;oBACjB,WAAW;gBACb;gBACA,OAAO;oBACL,UAAU;oBACV,OAAO,CAAC;gBACV;YACF;SAAE;IACJ,CAAC;AACD,MAAM,+BAA+B,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAClD,MAAM;IACN,MAAM;AACR,GAAG;IACD,SAAS;IACT,OAAO;IACP,QAAQ;AACV;AACA,MAAM,SAAS,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,OAAO,OAAO,EAAE,GAAG;IACvE,mEAAmE;IACnE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,6KAAA,CAAA,UAAkB;IACxD,MAAM,4CAA4C,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,mLAAA,CAAA,UAAwB;IAC3F,MAAM,gBAAgB,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE,cAAc;IACjD,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,QAAQ,EACR,QAAQ,SAAS,EACjB,YAAY,QAAQ,EACpB,SAAS,EACT,WAAW,KAAK,EAChB,mBAAmB,KAAK,EACxB,qBAAqB,KAAK,EAC1B,SAAS,WAAW,EACpB,qBAAqB,EACrB,YAAY,KAAK,EACjB,IAAI,MAAM,EACV,UAAU,IAAI,EACd,kBAAkB,oBAAoB,EACtC,kBAAkB,QAAQ,EAC1B,OAAO,QAAQ,EACf,WAAW,aAAa,EACxB,IAAI,EACJ,UAAU,MAAM,EAChB,GAAG,OACJ,GAAG;IACJ,MAAM,YAAY,CAAA,GAAA,uMAAA,CAAA,iBAAK,AAAD,EAAE;IACxB,MAAM,mBAAmB,wBAAwB,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,gLAAA,CAAA,UAAgB,EAAE;QACnF,mBAAmB;QACnB,OAAO;QACP,MAAM;IACR;IACA,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,MAAM,YAAY,CAAC,iBAAiB,WAAW,oBAAoB,OAAO,KAAK,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,iBAAiB;QAChH,WAAW,QAAQ,SAAS;QAC5B,YAAY;QACZ,UAAU,iBAAiB,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,8BAA8B;YACzE,WAAW,QAAQ,sBAAsB;YACzC,YAAY;QACd;IACF;IACA,MAAM,UAAU,CAAC,eAAe,WAAW,oBAAoB,KAAK,KAAK,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,eAAe;QACxG,WAAW,QAAQ,OAAO;QAC1B,YAAY;QACZ,UAAU,eAAe,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,8BAA8B;YACvE,WAAW,QAAQ,sBAAsB;YACzC,YAAY;QACd;IACF;IACA,MAAM,oBAAoB,6CAA6C;IACvE,MAAM,SAAS,OAAO,YAAY,YAClC,WAAW,GACX,uDAAuD;IACvD,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;QACX,WAAW,QAAQ,cAAc;QACjC,OAAO;YACL,SAAS;QACX;QACA,UAAU,WAAW,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wBAAwB;YAC7D,WAAW,QAAQ,gBAAgB;YACnC,YAAY;YACZ,UAAU;QACZ;IACF,KAAK;IACL,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,YAAY;QACpC,YAAY;QACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,aAAa,SAAS,EAAE,QAAQ,IAAI,EAAE,WAAW;QACjE,WAAW;QACX,UAAU,YAAY;QACtB,aAAa,CAAC;QACd,uBAAuB,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,YAAY,EAAE;QAClD,KAAK;QACL,MAAM;QACN,IAAI,UAAU,YAAY;QAC1B,GAAG,KAAK;QACR,SAAS;QACT,UAAU;YAAC;YAAW,oBAAoB,SAAS;YAAQ;YAAU,oBAAoB,SAAS;YAAQ;SAAQ;IACpH;AACF;AACA,uCAAwC,OAAO,SAAS,GAA0B;IAChF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;;GAKC,GACD,OAAO,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAW;YAAW;YAAa;YAAW;YAAS;YAAQ;SAAU;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAChL;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;;GAGC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;;GAGC,GACD,kBAAkB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAChC;;;GAGC,GACD,oBAAoB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAClC;;;;;;GAMC,GACD,eAAe,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI;IACvB;;GAEC,GACD,uBAAuB,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvC;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;IACzB;;;GAGC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,MAAM;IACtB;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;IACpB;;;;GAIC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,IAAI;IACvB;;;;;GAKC,GACD,kBAAkB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAChC;;;GAGC,GACD,iBAAiB,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAAC;QAAU;QAAO;KAAQ;IAC3D;;;;GAIC,GACD,MAAM,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAS;YAAU;SAAQ;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACjI;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;IACzB;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;GAEC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAU;YAAS;SAAS;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC5F;;;GAGC,GACD,SAAS,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAa;YAAY;SAAO;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AAC3I;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/styles/useTheme.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useTheme as useThemeSystem } from '@mui/system';\nimport defaultTheme from \"./defaultTheme.js\";\nimport THEME_ID from \"./identifier.js\";\nexport default function useTheme() {\n  const theme = useThemeSystem(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[THEME_ID] || theme;\n}"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAMe,SAAS;IACtB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,kKAAA,CAAA,UAAY;IACzC,wCAA2C;QACzC,wHAAwH;QACxH,sDAAsD;QACtD,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE;IACtB;IACA,OAAO,KAAK,CAAC,gKAAA,CAAA,UAAQ,CAAC,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Paper/paperClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaperUtilityClass(slot) {\n  return generateUtilityClass('MuiPaper', slot);\n}\nconst paperClasses = generateUtilityClasses('MuiPaper', ['root', 'rounded', 'outlined', 'elevation', 'elevation0', 'elevation1', 'elevation2', 'elevation3', 'elevation4', 'elevation5', 'elevation6', 'elevation7', 'elevation8', 'elevation9', 'elevation10', 'elevation11', 'elevation12', 'elevation13', 'elevation14', 'elevation15', 'elevation16', 'elevation17', 'elevation18', 'elevation19', 'elevation20', 'elevation21', 'elevation22', 'elevation23', 'elevation24']);\nexport default paperClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,qBAAqB,IAAI;IACvC,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,YAAY;AAC1C;AACA,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,YAAY;IAAC;IAAQ;IAAW;IAAY;IAAa;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;IAAe;CAAc;uCAClc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Paper/Paper.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport getOverlayAlpha from \"../styles/getOverlayAlpha.js\";\nimport { getPaperUtilityClass } from \"./paperClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  color: (theme.vars || theme).palette.text.primary,\n  transition: theme.transitions.create('box-shadow'),\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.square,\n    style: {\n      borderRadius: theme.shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      border: `1px solid ${(theme.vars || theme).palette.divider}`\n    }\n  }, {\n    props: {\n      variant: 'elevation'\n    },\n    style: {\n      boxShadow: 'var(--Paper-shadow)',\n      backgroundImage: 'var(--Paper-overlay)'\n    }\n  }]\n})));\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const theme = useTheme();\n  const {\n    className,\n    component = 'div',\n    elevation = 1,\n    square = false,\n    variant = 'elevation',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    elevation,\n    square,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      ...(variant === 'elevation' && {\n        '--Paper-shadow': (theme.vars || theme).shadows[elevation],\n        ...(theme.vars && {\n          '--Paper-overlay': theme.vars.overlays?.[elevation]\n        }),\n        ...(!theme.vars && theme.palette.mode === 'dark' && {\n          '--Paper-overlay': `linear-gradient(${alpha('#fff', getOverlayAlpha(elevation))}, ${alpha('#fff', getOverlayAlpha(elevation))})`\n        })\n      }),\n      ...other.style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAeA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ;YAAS,CAAC,UAAU;YAAW,YAAY,eAAe,CAAC,SAAS,EAAE,WAAW;SAAC;IACnG;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,iKAAA,CAAA,uBAAoB,EAAE;AACrD;AACA,MAAM,YAAY,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IAC9B,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,MAAM,CAAC,WAAW,OAAO,CAAC;YAAE,CAAC,WAAW,MAAM,IAAI,OAAO,OAAO;YAAE,WAAW,OAAO,KAAK,eAAe,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,SAAS,EAAE,CAAC;SAAC;IAC1K;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN,GAAK,CAAC;QACL,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/D,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO;QACjD,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;QACrC,UAAU;YAAC;gBACT,OAAO,CAAC,EACN,UAAU,EACX,GAAK,CAAC,WAAW,MAAM;gBACxB,OAAO;oBACL,cAAc,MAAM,KAAK,CAAC,YAAY;gBACxC;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,QAAQ,CAAC,UAAU,EAAE,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE;gBAC9D;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,WAAW;oBACX,iBAAiB;gBACnB;YACF;SAAE;IACJ,CAAC;AACD,MAAM,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,MAAM,OAAO,EAAE,GAAG;IACrE,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,EACJ,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,CAAC,EACb,SAAS,KAAK,EACd,UAAU,WAAW,EACrB,GAAG,OACJ,GAAG;IACJ,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,wCAA2C;QACzC,IAAI,MAAM,OAAO,CAAC,UAAU,KAAK,WAAW;YAC1C,QAAQ,KAAK,CAAC;gBAAC,CAAC,8CAA8C,EAAE,UAAU,iCAAiC,CAAC;gBAAE,CAAC,sCAAsC,EAAE,UAAU,eAAe,CAAC;aAAC,CAAC,IAAI,CAAC;QAC1L;IACF;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;QAClC,IAAI;QACJ,YAAY;QACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,KAAK;QACL,GAAG,KAAK;QACR,OAAO;YACL,GAAI,YAAY,eAAe;gBAC7B,kBAAkB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,UAAU;gBAC1D,GAAI,MAAM,IAAI,IAAI;oBAChB,mBAAmB,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,UAAU;gBACrD,CAAC;gBACD,GAAI,CAAC,MAAM,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU;oBAClD,mBAAmB,CAAC,gBAAgB,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,CAAA,GAAA,qKAAA,CAAA,UAAe,AAAD,EAAE,YAAY,EAAE,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,CAAA,GAAA,qKAAA,CAAA,UAAe,AAAD,EAAE,YAAY,CAAC,CAAC;gBAClI,CAAC;YACH,CAAC;YACD,GAAG,MAAM,KAAK;QAChB;IACF;AACF;AACA,uCAAwC,MAAM,SAAS,GAA0B;IAC/E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;;;GAIC,GACD,WAAW,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,2KAAA,CAAA,UAAe,EAAE,CAAA;QACzC,MAAM,EACJ,SAAS,EACT,OAAO,EACR,GAAG;QACJ,IAAI,YAAY,KAAK,YAAY,YAAY;YAC3C,OAAO,IAAI,MAAM,CAAC,4BAA4B,EAAE,UAAU,oBAAoB,EAAE,QAAQ,+EAA+E,CAAC;QAC1K;QACA,OAAO;IACT;IACA;;;GAGC,GACD,QAAQ,sIAAA,CAAA,UAAS,CAAC,IAAI;IACtB;;GAEC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvB;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;;GAGC,GACD,SAAS,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAa;SAAW;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AACnI;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Card/cardClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,oBAAoB,IAAI;IACtC,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,WAAW;AACzC;AACA,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,WAAW;IAAC;CAAO;uCAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Card/Card.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAYA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;SAAO;IAChB;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,+JAAA,CAAA,sBAAmB,EAAE;AACpD;AACA,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,0JAAA,CAAA,UAAK,EAAE;IAC7B,MAAM;IACN,MAAM;AACR,GAAG;IACD,UAAU;AACZ;AACA,MAAM,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,KAAK,OAAO,EAAE,GAAG;IACnE,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,SAAS,EACT,SAAS,KAAK,EACd,GAAG,OACJ,GAAG;IACJ,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,UAAU;QACjC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,WAAW,SAAS,IAAI;QACxB,KAAK;QACL,YAAY;QACZ,GAAG,KAAK;IACV;AACF;AACA,uCAAwC,KAAK,SAAS,GAA0B;IAC9E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;GAGC,GACD,QAAQ,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,sIAAA,CAAA,UAAS,CAAC,IAAI,EAAE,CAAA;QACrC,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,YAAY;YAChD,OAAO,IAAI,MAAM;QACnB;QACA,OAAO;IACT;IACA;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AACxJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/CardContent/cardContentClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,2BAA2B,IAAI;IAC7C,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,kBAAkB;AAChD;AACA,MAAM,qBAAqB,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,kBAAkB;IAAC;CAAO;uCAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/CardContent/CardContent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAUA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;SAAO;IAChB;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,6KAAA,CAAA,6BAA0B,EAAE;AAC3D;AACA,MAAM,kBAAkB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACpC,MAAM;IACN,MAAM;AACR,GAAG;IACD,SAAS;IACT,gBAAgB;QACd,eAAe;IACjB;AACF;AACA,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,YAAY,OAAO,EAAE,GAAG;IACjF,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,SAAS,EACT,YAAY,KAAK,EACjB,GAAG,OACJ,GAAG;IACJ,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,iBAAiB;QACxC,IAAI;QACJ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,YAAY;QACZ,KAAK;QACL,GAAG,KAAK;IACV;AACF;AACA,uCAAwC,YAAY,SAAS,GAA0B;IACrF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AACxJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/requirePropFactory.js"], "sourcesContent": ["import requirePropFactory from '@mui/utils/requirePropFactory';\nexport default requirePropFactory;"], "names": [], "mappings": ";;;AAAA;;uCACe,iLAAA,CAAA,UAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Grid/Grid.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid } from '@mui/system/Grid';\nimport requirePropFactory from \"../utils/requirePropFactory.js\";\nimport { styled } from \"../styles/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useTheme from \"../styles/useTheme.js\";\n/**\n *\n * Demos:\n *\n * - [Grid](https://mui.com/material-ui/react-grid/)\n *\n * API:\n *\n * - [Grid API](https://mui.com/material-ui/api/grid/)\n */\nconst Grid = createGrid({\n  createStyledComponent: styled('div', {\n    name: '<PERSON>i<PERSON><PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, ownerState.container && styles.container];\n    }\n  }),\n  componentName: 'MuiGrid',\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: '<PERSON><PERSON><PERSON><PERSON>'\n  }),\n  useTheme\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The number of columns.\n   * @default 12\n   */\n  columns: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number, PropTypes.object]),\n  /**\n   * Defines the horizontal space between the type `item` components.\n   * It overrides the value of the `spacing` prop.\n   */\n  columnSpacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * If `true`, the component will have the flex *container* behavior.\n   * You should be wrapping *items* with a *container*.\n   * @default false\n   */\n  container: PropTypes.bool,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'row'\n   */\n  direction: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Defines the offset value for the type `item` components.\n   */\n  offset: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])), PropTypes.object]),\n  /**\n   * Defines the vertical space between the type `item` components.\n   * It overrides the value of the `spacing` prop.\n   */\n  rowSpacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * Defines the size of the the type `item` components.\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number])), PropTypes.object]),\n  /**\n   * Defines the space between the type `item` components.\n   * It can only be used on a type `container` component.\n   * @default 0\n   */\n  spacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @internal\n   * The level of the grid starts from `0` and increases when the grid nests\n   * inside another grid. Nesting is defined as a container Grid being a direct\n   * child of a container Grid.\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <Grid container> // level 1\n   *     <Grid container> // level 2\n   * ```\n   *\n   * Only consecutive grid is considered nesting. A grid container will start at\n   * `0` if there are non-Grid container element above it.\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <div>\n   *     <Grid container> // level 0\n   * ```\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <Grid>\n   *     <Grid container> // level 0\n   * ```\n   */\n  unstable_level: PropTypes.number,\n  /**\n   * Defines the `flex-wrap` style property.\n   * It's applied for all screen sizes.\n   * @default 'wrap'\n   */\n  wrap: PropTypes.oneOf(['nowrap', 'wrap-reverse', 'wrap'])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  const Component = Grid;\n  const requireProp = requirePropFactory('Grid', Component);\n  // eslint-disable-next-line no-useless-concat\n  Component['propTypes' + ''] = {\n    // eslint-disable-next-line react/forbid-foreign-prop-types\n    ...Component.propTypes,\n    direction: requireProp('container'),\n    spacing: requireProp('container'),\n    wrap: requireProp('container')\n  };\n}\nexport default Grid;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAQA;;;;;;;;;CASC,GACD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACtB,uBAAuB,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QACnC,MAAM;QACN,MAAM;QACN,mBAAmB,CAAC,OAAO;YACzB,MAAM,EACJ,UAAU,EACX,GAAG;YACJ,OAAO;gBAAC,OAAO,IAAI;gBAAE,WAAW,SAAS,IAAI,OAAO,SAAS;aAAC;QAChE;IACF;IACA,eAAe;IACf,eAAe,CAAA,UAAW,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;YACxC,OAAO;YACP,MAAM;QACR;IACA,UAAA,8JAAA,CAAA,UAAQ;AACV;AACA,uCAAwC,KAAK,SAAS,GAA0B;IAC9E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;;GAGC,GACD,SAAS,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC5I;;;GAGC,GACD,eAAe,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC7M;;;;GAIC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;IACzB;;;;GAIC,GACD,WAAW,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAkB;YAAU;YAAe;SAAM;QAAG,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAkB;YAAU;YAAe;SAAM;QAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACpP;;GAEC,GACD,QAAQ,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtM;;;GAGC,GACD,YAAY,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC1M;;GAEC,GACD,MAAM,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACpO;;;;GAIC,GACD,SAAS,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACvM;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BC,GACD,gBAAgB,sIAAA,CAAA,UAAS,CAAC,MAAM;IAChC;;;;GAIC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAAC;QAAU;QAAgB;KAAO;AAC1D;AACA,wCAA2C;IACzC,MAAM,YAAY;IAClB,MAAM,cAAc,CAAA,GAAA,uKAAA,CAAA,UAAkB,AAAD,EAAE,QAAQ;IAC/C,6CAA6C;IAC7C,SAAS,CAAC,cAAc,GAAG,GAAG;QAC5B,2DAA2D;QAC3D,GAAG,UAAU,SAAS;QACtB,WAAW,YAAY;QACvB,SAAS,YAAY;QACrB,MAAM,YAAY;IACpB;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Fab/fabClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFabUtilityClass(slot) {\n  return generateUtilityClass('MuiFab', slot);\n}\nconst fabClasses = generateUtilityClasses('MuiFab', ['root', 'primary', 'secondary', 'extended', 'circular', 'focusVisible', 'disabled', 'colorInherit', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'info', 'error', 'warning', 'success']);\nexport default fabClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,mBAAmB,IAAI;IACrC,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,UAAU;AACxC;AACA,MAAM,aAAa,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,UAAU;IAAC;IAAQ;IAAW;IAAa;IAAY;IAAY;IAAgB;IAAY;IAAgB;IAAa;IAAc;IAAa;IAAQ;IAAS;IAAW;CAAU;uCACxN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/Fab/Fab.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport fabClasses, { getFabUtilityClass } from \"./fabClasses.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant,\n    classes,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, `size${capitalize(size)}`, color === 'inherit' ? 'colorInherit' : color]\n  };\n  const composedClasses = composeClasses(slots, getFabUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the focused, disabled, etc. classes to the ButtonBase\n    ...composedClasses\n  };\n};\nconst FabRoot = styled(ButtonBase, {\n  name: 'MuiFab',\n  slot: 'Root',\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`size${capitalize(ownerState.size)}`], ownerState.color === 'inherit' && styles.colorInherit, styles[capitalize(ownerState.size)], styles[ownerState.color]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  ...theme.typography.button,\n  minHeight: 36,\n  transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n    duration: theme.transitions.duration.short\n  }),\n  borderRadius: '50%',\n  padding: 0,\n  minWidth: 0,\n  width: 56,\n  height: 56,\n  zIndex: (theme.vars || theme).zIndex.fab,\n  boxShadow: (theme.vars || theme).shadows[6],\n  '&:active': {\n    boxShadow: (theme.vars || theme).shadows[12]\n  },\n  color: theme.vars ? theme.vars.palette.grey[900] : theme.palette.getContrastText?.(theme.palette.grey[300]),\n  backgroundColor: (theme.vars || theme).palette.grey[300],\n  '&:hover': {\n    backgroundColor: (theme.vars || theme).palette.grey.A100,\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: (theme.vars || theme).palette.grey[300]\n    },\n    textDecoration: 'none'\n  },\n  [`&.${fabClasses.focusVisible}`]: {\n    boxShadow: (theme.vars || theme).shadows[6]\n  },\n  variants: [{\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 40\n    }\n  }, {\n    props: {\n      size: 'medium'\n    },\n    style: {\n      width: 48,\n      height: 48\n    }\n  }, {\n    props: {\n      variant: 'extended'\n    },\n    style: {\n      borderRadius: 48 / 2,\n      padding: '0 16px',\n      width: 'auto',\n      minHeight: 'auto',\n      minWidth: 48,\n      height: 48\n    }\n  }, {\n    props: {\n      variant: 'extended',\n      size: 'small'\n    },\n    style: {\n      width: 'auto',\n      padding: '0 8px',\n      borderRadius: 34 / 2,\n      minWidth: 34,\n      height: 34\n    }\n  }, {\n    props: {\n      variant: 'extended',\n      size: 'medium'\n    },\n    style: {\n      width: 'auto',\n      padding: '0 16px',\n      borderRadius: 40 / 2,\n      minWidth: 40,\n      height: 40\n    }\n  }, {\n    props: {\n      color: 'inherit'\n    },\n    style: {\n      color: 'inherit'\n    }\n  }]\n})), memoTheme(({\n  theme\n}) => ({\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark', 'contrastText'])) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      color: (theme.vars || theme).palette[color].contrastText,\n      backgroundColor: (theme.vars || theme).palette[color].main,\n      '&:hover': {\n        backgroundColor: (theme.vars || theme).palette[color].dark,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: (theme.vars || theme).palette[color].main\n        }\n      }\n    }\n  }))]\n})), memoTheme(({\n  theme\n}) => ({\n  [`&.${fabClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.action.disabled,\n    boxShadow: (theme.vars || theme).shadows[0],\n    backgroundColor: (theme.vars || theme).palette.action.disabledBackground\n  }\n})));\nconst Fab = /*#__PURE__*/React.forwardRef(function Fab(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFab'\n  });\n  const {\n    children,\n    className,\n    color = 'default',\n    component = 'button',\n    disabled = false,\n    disableFocusRipple = false,\n    focusVisibleClassName,\n    size = 'large',\n    variant = 'circular',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    component,\n    disabled,\n    disableFocusRipple,\n    size,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FabRoot, {\n    className: clsx(classes.root, className),\n    component: component,\n    disabled: disabled,\n    focusRipple: !disableFocusRipple,\n    focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n    ownerState: ownerState,\n    ref: ref,\n    ...other,\n    classes: classes,\n    children: children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Fab.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'error', 'info', 'inherit', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The URL to link to when the button is clicked.\n   * If defined, an `a` element will be used as the root node.\n   */\n  href: PropTypes.string,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense button styling.\n   * @default 'large'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'extended']), PropTypes.string])\n} : void 0;\nexport default Fab;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAeA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACL,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ;YAAS,CAAC,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,OAAO;YAAE,UAAU,YAAY,iBAAiB;SAAM;IAClG;IACA,MAAM,kBAAkB,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,6JAAA,CAAA,qBAAkB,EAAE;IAClE,OAAO;QACL,GAAG,OAAO;QACV,gEAAgE;QAChE,GAAG,eAAe;IACpB;AACF;AACA,MAAM,UAAU,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,oKAAA,CAAA,UAAU,EAAE;IACjC,MAAM;IACN,MAAM;IACN,mBAAmB,CAAA,OAAQ,CAAA,GAAA,2KAAA,CAAA,UAAqB,AAAD,EAAE,SAAS,SAAS;IACnE,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,MAAM,CAAC,WAAW,OAAO,CAAC;YAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,IAAI,GAAG,CAAC;YAAE,WAAW,KAAK,KAAK,aAAa,OAAO,YAAY;YAAE,MAAM,CAAC,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,IAAI,EAAE;YAAE,MAAM,CAAC,WAAW,KAAK,CAAC;SAAC;IACtN;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN,GAAK,CAAC;QACL,GAAG,MAAM,UAAU,CAAC,MAAM;QAC1B,WAAW;QACX,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;YAAC;YAAoB;YAAc;SAAe,EAAE;YACvF,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK;QAC5C;QACA,cAAc;QACd,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG;QACxC,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;QAC3C,YAAY;YACV,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,GAAG;QAC9C;QACA,OAAO,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,OAAO,CAAC,eAAe,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI;QAC1G,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;QACxD,WAAW;YACT,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;YACxD,qDAAqD;YACrD,wBAAwB;gBACtB,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;YAC1D;YACA,gBAAgB;QAClB;QACA,CAAC,CAAC,EAAE,EAAE,6JAAA,CAAA,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE;YAChC,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;QAC7C;QACA,UAAU;YAAC;gBACT,OAAO;oBACL,MAAM;gBACR;gBACA,OAAO;oBACL,OAAO;oBACP,QAAQ;gBACV;YACF;YAAG;gBACD,OAAO;oBACL,MAAM;gBACR;gBACA,OAAO;oBACL,OAAO;oBACP,QAAQ;gBACV;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;gBACX;gBACA,OAAO;oBACL,cAAc,KAAK;oBACnB,SAAS;oBACT,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;gBACA,OAAO;oBACL,OAAO;oBACP,SAAS;oBACT,cAAc,KAAK;oBACnB,UAAU;oBACV,QAAQ;gBACV;YACF;YAAG;gBACD,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;gBACA,OAAO;oBACL,OAAO;oBACP,SAAS;oBACT,cAAc,KAAK;oBACnB,UAAU;oBACV,QAAQ;gBACV;YACF;YAAG;gBACD,OAAO;oBACL,OAAO;gBACT;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;SAAE;IACJ,CAAC,IAAI,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACd,KAAK,EACN,GAAK,CAAC;QACL,UAAU;eAAI,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,MAAM,CAAC,CAAA,GAAA,mLAAA,CAAA,UAA8B,AAAD,EAAE;gBAAC;gBAAQ;aAAe,GAAG,+CAA+C;aAC3J,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK,CAAC;oBACjB,OAAO;wBACL;oBACF;oBACA,OAAO;wBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY;wBACxD,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;wBAC1D,WAAW;4BACT,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;4BAC1D,qDAAqD;4BACrD,wBAAwB;gCACtB,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;4BAC5D;wBACF;oBACF;gBACF,CAAC;SAAG;IACN,CAAC,IAAI,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACd,KAAK,EACN,GAAK,CAAC;QACL,CAAC,CAAC,EAAE,EAAE,6JAAA,CAAA,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE;YAC5B,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;YACpD,WAAW,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE;YAC3C,iBAAiB,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,kBAAkB;QAC1E;IACF,CAAC;AACD,MAAM,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,IAAI,OAAO,EAAE,GAAG;IACjE,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,QAAQ,SAAS,EACjB,YAAY,QAAQ,EACpB,WAAW,KAAK,EAChB,qBAAqB,KAAK,EAC1B,qBAAqB,EACrB,OAAO,OAAO,EACd,UAAU,UAAU,EACpB,GAAG,OACJ,GAAG;IACJ,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,UAAU,kBAAkB;IAClC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,SAAS;QAChC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,WAAW;QACX,UAAU;QACV,aAAa,CAAC;QACd,uBAAuB,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,YAAY,EAAE;QAClD,YAAY;QACZ,KAAK;QACL,GAAG,KAAK;QACR,SAAS;QACT,UAAU;IACZ;AACF;AACA,uCAAwC,IAAI,SAAS,GAA0B;IAC7E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;;GAKC,GACD,OAAO,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAW;YAAS;YAAQ;YAAW;YAAW;YAAa;YAAW;SAAU;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAC3L;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;;GAGC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;;GAGC,GACD,oBAAoB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAClC;;GAEC,GACD,eAAe,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B;;GAEC,GACD,uBAAuB,sIAAA,CAAA,UAAS,CAAC,MAAM;IACvC;;;GAGC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,MAAM;IACtB;;;;GAIC,GACD,MAAM,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAS;YAAU;SAAQ;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACjI;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;;GAGC,GACD,SAAS,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAY;SAAW;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;AAClI;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/SvgIcon/svgIconClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSvgIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSvgIcon', slot);\n}\nconst svgIconClasses = generateUtilityClasses('MuiSvgIcon', ['root', 'colorPrimary', 'colorSecondary', 'colorAction', 'colorError', 'colorDisabled', 'fontSizeInherit', 'fontSizeSmall', 'fontSizeMedium', 'fontSizeLarge']);\nexport default svgIconClasses;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,uBAAuB,IAAI;IACzC,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,cAAc;AAC5C;AACA,MAAM,iBAAiB,CAAA,GAAA,yLAAA,CAAA,UAAsB,AAAD,EAAE,cAAc;IAAC;IAAQ;IAAgB;IAAkB;IAAe;IAAc;IAAiB;IAAmB;IAAiB;IAAkB;CAAgB;uCAC5M", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/SvgIcon/SvgIcon.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from \"../utils/capitalize.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getSvgIconUtilityClass } from \"./svgIconClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    fontSize,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', color !== 'inherit' && `color${capitalize(color)}`, `fontSize${capitalize(fontSize)}`]\n  };\n  return composeClasses(slots, getSvgIconUtilityClass, classes);\n};\nconst SvgIconRoot = styled('svg', {\n  name: 'MuiSvgIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color !== 'inherit' && styles[`color${capitalize(ownerState.color)}`], styles[`fontSize${capitalize(ownerState.fontSize)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  userSelect: 'none',\n  width: '1em',\n  height: '1em',\n  display: 'inline-block',\n  flexShrink: 0,\n  transition: theme.transitions?.create?.('fill', {\n    duration: (theme.vars ?? theme).transitions?.duration?.shorter\n  }),\n  variants: [{\n    props: props => !props.hasSvgAsChild,\n    style: {\n      // the <svg> will define the property that has `currentColor`\n      // for example heroicons uses fill=\"none\" and stroke=\"currentColor\"\n      fill: 'currentColor'\n    }\n  }, {\n    props: {\n      fontSize: 'inherit'\n    },\n    style: {\n      fontSize: 'inherit'\n    }\n  }, {\n    props: {\n      fontSize: 'small'\n    },\n    style: {\n      fontSize: theme.typography?.pxToRem?.(20) || '1.25rem'\n    }\n  }, {\n    props: {\n      fontSize: 'medium'\n    },\n    style: {\n      fontSize: theme.typography?.pxToRem?.(24) || '1.5rem'\n    }\n  }, {\n    props: {\n      fontSize: 'large'\n    },\n    style: {\n      fontSize: theme.typography?.pxToRem?.(35) || '2.1875rem'\n    }\n  },\n  // TODO v5 deprecate color prop, v6 remove for sx\n  ...Object.entries((theme.vars ?? theme).palette).filter(([, value]) => value && value.main).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      color: (theme.vars ?? theme).palette?.[color]?.main\n    }\n  })), {\n    props: {\n      color: 'action'\n    },\n    style: {\n      color: (theme.vars ?? theme).palette?.action?.active\n    }\n  }, {\n    props: {\n      color: 'disabled'\n    },\n    style: {\n      color: (theme.vars ?? theme).palette?.action?.disabled\n    }\n  }, {\n    props: {\n      color: 'inherit'\n    },\n    style: {\n      color: undefined\n    }\n  }]\n})));\nconst SvgIcon = /*#__PURE__*/React.forwardRef(function SvgIcon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSvgIcon'\n  });\n  const {\n    children,\n    className,\n    color = 'inherit',\n    component = 'svg',\n    fontSize = 'medium',\n    htmlColor,\n    inheritViewBox = false,\n    titleAccess,\n    viewBox = '0 0 24 24',\n    ...other\n  } = props;\n  const hasSvgAsChild = /*#__PURE__*/React.isValidElement(children) && children.type === 'svg';\n  const ownerState = {\n    ...props,\n    color,\n    component,\n    fontSize,\n    instanceFontSize: inProps.fontSize,\n    inheritViewBox,\n    viewBox,\n    hasSvgAsChild\n  };\n  const more = {};\n  if (!inheritViewBox) {\n    more.viewBox = viewBox;\n  }\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SvgIconRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    focusable: \"false\",\n    color: htmlColor,\n    \"aria-hidden\": titleAccess ? undefined : true,\n    role: titleAccess ? 'img' : undefined,\n    ref: ref,\n    ...more,\n    ...other,\n    ...(hasSvgAsChild && children.props),\n    ownerState: ownerState,\n    children: [hasSvgAsChild ? children.props.children : children, titleAccess ? /*#__PURE__*/_jsx(\"title\", {\n      children: titleAccess\n    }) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SvgIcon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Node passed into the SVG element.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * You can use the `htmlColor` prop to apply a color attribute to the SVG element.\n   * @default 'inherit'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'action', 'disabled', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The fontSize applied to the icon. Defaults to 24px, but can be configure to inherit font size.\n   * @default 'medium'\n   */\n  fontSize: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'large', 'medium', 'small']), PropTypes.string]),\n  /**\n   * Applies a color attribute to the SVG element.\n   */\n  htmlColor: PropTypes.string,\n  /**\n   * If `true`, the root node will inherit the custom `component`'s viewBox and the `viewBox`\n   * prop will be ignored.\n   * Useful when you want to reference a custom `component` and have `SvgIcon` pass that\n   * `component`'s viewBox to the root node.\n   * @default false\n   */\n  inheritViewBox: PropTypes.bool,\n  /**\n   * The shape-rendering attribute. The behavior of the different options is described on the\n   * [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Web/SVG/Reference/Attribute/shape-rendering).\n   * If you are having issues with blurry icons you should investigate this prop.\n   */\n  shapeRendering: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Provides a human-readable title for the element that contains it.\n   * https://www.w3.org/TR/SVG-access/#Equivalent\n   */\n  titleAccess: PropTypes.string,\n  /**\n   * Allows you to redefine what the coordinates without units mean inside an SVG element.\n   * For example, if the SVG element is 500 (width) by 200 (height),\n   * and you pass viewBox=\"0 0 50 20\",\n   * this means that the coordinates inside the SVG will go from the top left corner (0,0)\n   * to bottom right (50,20) and each unit will be worth 10px.\n   * @default '0 0 24 24'\n   */\n  viewBox: PropTypes.string\n} : void 0;\nSvgIcon.muiName = 'SvgIcon';\nexport default SvgIcon;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAYA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,OAAO,EACR,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ,UAAU,aAAa,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,QAAQ;YAAE,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW;SAAC;IACvG;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,qKAAA,CAAA,yBAAsB,EAAE;AACvD;AACA,MAAM,cAAc,CAAA,GAAA,iNAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IAChC,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,WAAW,KAAK,KAAK,aAAa,MAAM,CAAC,CAAC,KAAK,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,KAAK,GAAG,CAAC;YAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAA,GAAA,+JAAA,CAAA,UAAU,AAAD,EAAE,WAAW,QAAQ,GAAG,CAAC;SAAC;IAC9J;AACF,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAS,AAAD,EAAE,CAAC,EACZ,KAAK,EACN,GAAK,CAAC;QACL,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,YAAY,MAAM,WAAW,EAAE,SAAS,QAAQ;YAC9C,UAAU,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,WAAW,EAAE,UAAU;QACzD;QACA,UAAU;YAAC;gBACT,OAAO,CAAA,QAAS,CAAC,MAAM,aAAa;gBACpC,OAAO;oBACL,6DAA6D;oBAC7D,mEAAmE;oBACnE,MAAM;gBACR;YACF;YAAG;gBACD,OAAO;oBACL,UAAU;gBACZ;gBACA,OAAO;oBACL,UAAU;gBACZ;YACF;YAAG;gBACD,OAAO;oBACL,UAAU;gBACZ;gBACA,OAAO;oBACL,UAAU,MAAM,UAAU,EAAE,UAAU,OAAO;gBAC/C;YACF;YAAG;gBACD,OAAO;oBACL,UAAU;gBACZ;gBACA,OAAO;oBACL,UAAU,MAAM,UAAU,EAAE,UAAU,OAAO;gBAC/C;YACF;YAAG;gBACD,OAAO;oBACL,UAAU;gBACZ;gBACA,OAAO;oBACL,UAAU,MAAM,UAAU,EAAE,UAAU,OAAO;gBAC/C;YACF;YACA,iDAAiD;eAC9C,OAAO,OAAO,CAAC,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,SAAS,MAAM,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAK,CAAC;oBAC5G,OAAO;wBACL;oBACF;oBACA,OAAO;wBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC,MAAM,EAAE;oBACjD;gBACF,CAAC;YAAI;gBACH,OAAO;oBACL,OAAO;gBACT;gBACA,OAAO;oBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,EAAE,QAAQ;gBAChD;YACF;YAAG;gBACD,OAAO;oBACL,OAAO;gBACT;gBACA,OAAO;oBACL,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,OAAO,EAAE,QAAQ;gBAChD;YACF;YAAG;gBACD,OAAO;oBACL,OAAO;gBACT;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;SAAE;IACJ,CAAC;AACD,MAAM,UAAU,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,QAAQ,OAAO,EAAE,GAAG;IACzE,MAAM,QAAQ,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;QAC5B,OAAO;QACP,MAAM;IACR;IACA,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,QAAQ,SAAS,EACjB,YAAY,KAAK,EACjB,WAAW,QAAQ,EACnB,SAAS,EACT,iBAAiB,KAAK,EACtB,WAAW,EACX,UAAU,WAAW,EACrB,GAAG,OACJ,GAAG;IACJ,MAAM,gBAAgB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,aAAa,SAAS,IAAI,KAAK;IACvF,MAAM,aAAa;QACjB,GAAG,KAAK;QACR;QACA;QACA;QACA,kBAAkB,QAAQ,QAAQ;QAClC;QACA;QACA;IACF;IACA,MAAM,OAAO,CAAC;IACd,IAAI,CAAC,gBAAgB;QACnB,KAAK,OAAO,GAAG;IACjB;IACA,MAAM,UAAU,kBAAkB;IAClC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,aAAa;QACrC,IAAI;QACJ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;QAC9B,WAAW;QACX,OAAO;QACP,eAAe,cAAc,YAAY;QACzC,MAAM,cAAc,QAAQ;QAC5B,KAAK;QACL,GAAG,IAAI;QACP,GAAG,KAAK;QACR,GAAI,iBAAiB,SAAS,KAAK;QACnC,YAAY;QACZ,UAAU;YAAC,gBAAgB,SAAS,KAAK,CAAC,QAAQ,GAAG;YAAU,cAAc,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBACtG,UAAU;YACZ,KAAK;SAAK;IACZ;AACF;AACA,uCAAwC,QAAQ,SAAS,GAA0B;IACjF,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E,0EAA0E;IAC1E;;GAEC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IACxB;;GAEC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;IACzB;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;;;GAMC,GACD,OAAO,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAW;YAAU;YAAY;YAAW;YAAa;YAAS;YAAQ;YAAW;SAAU;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtM;;;GAGC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;IAChC;;;GAGC,GACD,UAAU,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAW;YAAS;YAAU;SAAQ;QAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IAChJ;;GAEC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B;;;;;;GAMC,GACD,gBAAgB,sIAAA,CAAA,UAAS,CAAC,IAAI;IAC9B;;;;GAIC,GACD,gBAAgB,sIAAA,CAAA,UAAS,CAAC,MAAM;IAChC;;GAEC,GACD,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;SAAC;QAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;QAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;KAAC;IACtJ;;;GAGC,GACD,aAAa,sIAAA,CAAA,UAAS,CAAC,MAAM;IAC7B;;;;;;;GAOC,GACD,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;AAC3B;AACA,QAAQ,OAAO,GAAG;uCACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/material/esm/utils/createSvgIcon.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport SvgIcon from \"../SvgIcon/index.js\";\n\n/**\n * Private module reserved for @mui packages.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createSvgIcon(path, displayName) {\n  function Component(props, ref) {\n    return /*#__PURE__*/_jsx(SvgIcon, {\n      \"data-testid\": process.env.NODE_ENV !== 'production' ? `${displayName}Icon` : undefined,\n      ref: ref,\n      ...props,\n      children: path\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to set `displayName` on the inner component for React.memo.\n    // React prior to 16.14 ignores `displayName` on the wrapper.\n    Component.displayName = `${displayName}Icon`;\n  }\n  Component.muiName = SvgIcon.muiName;\n  return /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(Component));\n}"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;;CAEC,GACD;AARA;;;;AASe,SAAS,cAAc,IAAI,EAAE,WAAW;IACrD,SAAS,UAAU,KAAK,EAAE,GAAG;QAC3B,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,8JAAA,CAAA,UAAO,EAAE;YAChC,eAAe,uCAAwC,GAAG,YAAY,IAAI,CAAC;YAC3E,KAAK;YACL,GAAG,KAAK;YACR,UAAU;QACZ;IACF;IACA,wCAA2C;QACzC,mEAAmE;QACnE,6DAA6D;QAC7D,UAAU,WAAW,GAAG,GAAG,YAAY,IAAI,CAAC;IAC9C;IACA,UAAU,OAAO,GAAG,8JAAA,CAAA,UAAO,CAAC,OAAO;IACnC,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,OAAU,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;AAC/D", "ignoreList": [0], "debugId": null}}]}