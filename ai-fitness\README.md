# AI Fitness - Your Personal Training Assistant

A modern, AI-powered fitness application built with Next.js 15, TypeScript, and Tailwind CSS. Transform your fitness journey with personalized workout plans, intelligent progress tracking, and real-time coaching.

## 🚀 Features

- **AI-Powered Workout Plans**: Personalized training programs based on your fitness level and goals
- **Smart Progress Tracking**: Comprehensive analytics and insights to monitor your fitness journey
- **Interactive Dashboard**: Real-time overview of your workouts, achievements, and progress
- **Exercise Library**: Extensive collection of exercises with detailed instructions and video guides
- **Responsive Design**: Optimized for all devices - desktop, tablet, and mobile
- **Modern UI/UX**: Clean, intuitive interface built with Tailwind CSS and Radix UI components

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI primitives
- **State Management**: Zustand
- **Data Fetching**: TanStack Query (React Query)
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Animations**: Framer Motion

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ai-fitness
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Dashboard page
│   ├── workouts/          # Workout plans page
│   └── layout.tsx         # Root layout
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components (Button, Card, etc.)
│   └── navigation.tsx    # Navigation component
├── features/             # Feature-specific components
│   ├── auth/            # Authentication features
│   ├── dashboard/       # Dashboard features
│   ├── workout/         # Workout features
│   └── analytics/       # Analytics features
├── hooks/               # Custom React hooks
├── lib/                 # Utility functions
├── stores/              # Zustand state stores
└── types/               # TypeScript type definitions
```

## 🎯 Key Features Overview

### Dashboard
- Weekly workout progress tracking
- Calorie burn monitoring
- Workout streak tracking
- Quick action buttons for common tasks

### Workout Plans
- AI-curated workout plans for different fitness levels
- Categorized by type (Strength, Cardio, HIIT, Yoga, etc.)
- Detailed exercise information with duration and difficulty
- Search and filter functionality

### Progress Tracking
- Comprehensive analytics dashboard
- Progress visualization with charts and graphs
- Achievement system and milestones
- Historical data tracking

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Add your environment variables here
NEXT_PUBLIC_API_URL=your_api_url
```

## 🚀 Deployment

The app is optimized for deployment on Vercel:

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with zero configuration

For other platforms, build the app:

```bash
npm run build
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [Radix UI](https://www.radix-ui.com/)
- Icons from [Lucide](https://lucide.dev/)
- Styling with [Tailwind CSS](https://tailwindcss.com/)
