(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__e12e3850._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/i18n/index.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Simple custom i18n implementation for Next.js 15 compatibility
__turbopack_context__.s({
    "addLocaleToPathname": (()=>addLocaleToPathname),
    "defaultLocale": (()=>defaultLocale),
    "getLocale": (()=>getLocale),
    "getLocaleFromPathname": (()=>getLocaleFromPathname),
    "locales": (()=>locales),
    "removeLocaleFromPathname": (()=>removeLocaleFromPathname),
    "setLocale": (()=>setLocale)
});
const locales = [
    'en',
    'zh'
];
const defaultLocale = 'en';
function getLocale() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return defaultLocale;
}
function setLocale(locale) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
function getLocaleFromPathname(pathname) {
    const segments = pathname.split('/');
    const localeSegment = segments[1];
    if (locales.includes(localeSegment)) {
        return localeSegment;
    }
    return defaultLocale;
}
function removeLocaleFromPathname(pathname) {
    const segments = pathname.split('/');
    const localeSegment = segments[1];
    if (locales.includes(localeSegment)) {
        return '/' + segments.slice(2).join('/');
    }
    return pathname;
}
function addLocaleToPathname(pathname, locale) {
    if (locale === defaultLocale) {
        return pathname;
    }
    const cleanPath = removeLocaleFromPathname(pathname);
    return `/${locale}${cleanPath}`;
}
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n/index.ts [middleware-edge] (ecmascript)");
;
;
function middleware(request) {
    const pathname = request.nextUrl.pathname;
    // Check if pathname already has a locale
    const pathnameHasLocale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"].some((locale)=>pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`);
    // If no locale in pathname, redirect to default locale
    if (!pathnameHasLocale) {
        // Get locale from cookie or use default
        const locale = request.cookies.get('locale')?.value || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"];
        // Only add locale prefix for non-default locale
        if (locale !== __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$index$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"]) {
            const newUrl = new URL(`/${locale}${pathname}`, request.url);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(newUrl);
        }
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        // Skip all internal paths (_next)
        '/((?!_next|api|favicon.ico|robots.txt|sitemap.xml).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__e12e3850._.js.map