import { ExerciseAttributeValueEnum } from "@prisma/client";

export const ShouldersGroup = ({
  onToggleMuscle,
  getMuscleClasses,
}: {
  onToggleMuscle: (muscle: ExerciseAttributeValueEnum) => void;
  getMuscleClasses: (muscle: ExerciseAttributeValueEnum) => string;
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(ExerciseAttributeValueEnum.SHOULDERS)}>
      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.SHOULDERS)}
        d="M 349.30,110.19
           C 350.53,110.32 351.75,110.47 352.37,110.54
             358.40,110.63 363.20,108.85 367.19,105.28
             370.34,102.46 373.12,99.22 376.21,96.32
             377.79,94.83 379.66,93.63 381.47,92.40
             382.21,91.90 382.52,91.35 382.46,90.50
             382.16,85.93 377.78,80.55 373.38,79.53
             372.52,79.33 371.48,79.36 370.65,79.64
             364.68,81.65 359.28,84.64 354.93,89.27
             351.53,92.89 349.96,97.39 349.10,102.16
             348.71,104.40 348.57,106.68 348.31,108.93
             348.22,109.69 348.54,110.10 349.30,110.19"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        id="path96"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.SHOULDERS)}
        d="M 439.90,90.50
           C 439.84,91.35 440.15,91.90 440.89,92.40
             442.70,93.63 444.57,94.83 446.15,96.32
             449.24,99.22 452.02,102.46 455.17,105.28
             459.17,108.85 463.96,110.63 469.99,110.54
             470.61,110.47 471.83,110.32 473.06,110.19
             473.82,110.10 474.14,109.69 474.05,108.93
             473.80,106.68 473.66,104.40 473.26,102.16
             472.41,97.39 470.83,92.89 467.44,89.27
             463.08,84.64 457.68,81.65 451.71,79.64
             450.88,79.36 449.84,79.33 448.98,79.53
             444.58,80.55 440.20,85.93 439.90,90.50"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        id="path88"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 82.75,82.25
           C 82.75,82.25 75.75,78.50 75.75,78.50
             75.75,78.50 64.00,80.50 64.00,80.50
             64.00,80.50 58.25,85.25 58.25,85.25
             58.25,85.25 53.25,90.75 53.25,90.75
             53.25,90.75 50.25,98.25 50.25,98.25
             50.25,98.25 48.50,109.50 48.50,109.50
             48.50,109.50 48.75,118.50 48.75,118.50
             48.75,118.50 55.50,114.25 55.50,114.25
             55.50,114.25 62.50,111.75 62.50,111.75
             62.50,111.75 71.50,110.50 71.50,110.25
             71.50,110.00 76.00,105.00 76.00,105.00
             76.00,105.00 80.75,96.50 80.75,96.50
             80.75,96.50 85.00,86.25 85.00,86.25
             85.00,86.25 82.75,82.50 82.75,82.50"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />
      <path
        className="fill-transparent"
        d="M 144.25,85.00
           C 144.25,85.00 150.50,79.25 150.75,79.25
             151.00,79.25 157.25,78.50 157.25,78.50
             157.25,78.50 167.00,81.75 167.00,81.75
             167.00,81.75 172.75,86.75 172.75,86.75
             172.75,86.75 177.00,94.00 177.00,94.00
             177.00,94.00 180.50,103.75 180.50,103.75
             180.50,103.75 180.50,113.25 180.50,113.25
             180.50,113.25 180.50,117.50 180.50,117.50
             180.50,117.50 176.25,115.75 176.25,115.75
             176.25,115.75 166.00,112.25 166.00,112.25
             166.00,112.25 159.00,111.00 159.00,111.00
             159.00,111.00 155.25,107.75 155.25,107.75
             155.25,107.75 149.25,97.00 149.25,97.00
             149.25,97.00 146.25,90.50 146.25,90.50
             146.25,90.50 145.00,87.00 145.00,87.00"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />
      <path
        className="fill-transparent"
        d="M 364.00,79.50
           C 364.00,79.50 357.50,82.75 357.50,82.75
             357.50,82.75 352.50,87.50 352.50,87.50
             352.50,87.50 349.25,93.50 349.25,93.50
             349.25,93.50 346.25,102.25 346.25,102.25
             346.25,102.25 345.75,109.50 345.75,109.50
             345.75,109.50 348.25,111.00 348.25,111.00
             348.25,111.00 354.75,111.25 354.75,111.25
             354.75,111.25 362.50,110.00 362.50,110.00
             362.50,110.00 367.75,106.25 367.75,106.25
             367.75,106.25 373.75,101.00 373.75,101.00
             373.75,101.00 379.00,95.50 379.00,95.50
             379.00,95.50 384.25,92.25 384.25,92.25
             384.25,92.25 382.25,85.50 382.25,85.50
             382.25,85.50 378.50,80.25 378.50,80.25
             378.50,80.25 374.25,79.00 374.25,79.00
             374.25,79.00 369.50,78.25 369.50,78.25
             369.50,78.25 370.75,78.25 367.25,78.75"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 453.25,76.50
           C 453.25,76.50 462.00,80.25 462.00,80.25
             462.00,80.25 467.75,85.50 467.75,85.50
             467.75,85.50 473.00,92.50 473.00,92.50
             473.00,92.50 476.75,103.00 476.75,103.00
             476.75,103.00 476.75,109.00 476.75,109.00
             476.75,109.00 475.50,111.00 475.50,111.00
             475.50,111.00 471.25,111.00 471.25,111.00
             471.25,111.00 464.50,111.00 464.50,111.00
             464.50,111.00 457.50,108.00 457.50,108.00
             457.50,108.00 452.00,102.75 452.00,102.75
             452.00,102.75 445.25,96.75 445.25,96.75
             445.25,96.75 440.00,93.25 440.00,93.25
             440.00,93.25 439.00,90.00 439.00,90.00
             439.00,90.00 441.00,86.00 441.00,86.00
             441.00,86.00 443.50,81.75 443.50,81.75
             443.50,81.75 448.50,78.00 448.50,78.00
             448.50,78.00 453.75,76.75 453.75,76.75"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.SHOULDERS)}
        d="M 145.59,86.76
           C 146.96,92.75 151.13,99.57 154.22,104.96
             156.56,109.06 156.74,107.90 160.65,109.64
             162.67,110.54 168.21,111.43 170.18,112.42
             171.86,113.27 174.37,114.35 175.95,115.39
             176.25,115.59 177.13,116.42 177.51,116.76
             177.60,116.40 177.69,116.23 177.69,116.06
             177.46,109.59 178.00,102.38 175.66,96.29
             174.38,92.97 170.21,88.28 167.38,86.15
             162.73,82.66 159.09,81.86 152.93,80.92
             152.27,80.82 151.35,80.76 150.79,81.09
             149.27,82.00 147.83,83.08 146.44,84.18
             145.66,84.81 145.36,85.71 145.59,86.76"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        id="path152"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(ExerciseAttributeValueEnum.SHOULDERS)}
        d="M 53.58,115.39
           C 55.17,114.35 57.67,113.27 59.35,112.42
             61.32,111.43 66.87,110.54 68.88,109.64
             72.80,107.90 72.98,109.06 75.32,104.96
             78.40,99.57 82.58,92.75 83.94,86.76
             84.18,85.71 83.88,84.81 83.10,84.18
             81.70,83.08 80.27,82.00 78.74,81.09
             78.18,80.76 77.27,80.82 76.60,80.92
             70.44,81.86 66.80,82.66 62.16,86.15
             59.33,88.28 55.15,92.97 53.87,96.29
             51.53,102.38 52.08,109.59 51.85,116.06
             51.84,116.23 51.93,116.40 52.02,116.76
             52.40,116.42 53.28,115.59 53.58,115.39"
        data-elem={ExerciseAttributeValueEnum.SHOULDERS}
        id="path142"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
};
