(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{3738:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(5155),a=r(2115),l=r(5695),n=r(6874),i=r.n(n),o=r(5251),d=r(8467);function c(){let e=(0,l.useRouter)(),s=(0,o.go)(),[r,n]=(0,a.useState)({email:"",password:""}),[c,m]=(0,a.useState)(!1),u=async t=>{t.preventDefault();try{await s.mutateAsync(r),e.push("/")}catch(e){console.error("Sign in failed:",e)}},x=e=>{n(s=>({...s,[e.target.name]:e.target.value}))};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to AI-fitness-singles"}),(0,t.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Welcome back! Please sign in to your account."})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:u,children:[(0,t.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email address",value:r.email,onChange:x,disabled:s.isPending})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:c?"text":"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Password",value:r.password,onChange:x,disabled:s.isPending}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>m(!c),children:c?(0,t.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):(0,t.jsxs)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),s.error&&(0,t.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Sign in failed"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,d.u1)(s.error)})]})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(i(),{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:s.isPending,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:s.isPending?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Signing in..."]}):"Sign in"})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(i(),{href:"/auth/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up"})]})})]})]})})}},9619:(e,s,r)=>{Promise.resolve().then(r.bind(r,3738))}},e=>{var s=s=>e(e.s=s);e.O(0,[76,96,358],()=>s(9619)),_N_E=e.O()}]);