'use client';

import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Fab,
  useTheme,
  alpha,
} from '@mui/material';
import {
  FitnessCenter,
  DirectionsRun,
  Assessment,
  PlayArrow,
  TrendingUp,
  Timer,
  EmojiEvents,
  Add,
} from '@mui/icons-material';
import Link from 'next/link';

const features = [
  {
    icon: <FitnessCenter />,
    title: '智能训练计划',
    description: 'AI 定制个性化训练方案',
    color: '#FF6B35',
    href: '/workouts',
  },
  {
    icon: <DirectionsRun />,
    title: '运动动作库',
    description: '千种运动动作详细指导',
    color: '#4CAF50',
    href: '/exercises',
  },
  {
    icon: <Assessment />,
    title: '进度分析',
    description: '数据驱动的健身追踪',
    color: '#2196F3',
    href: '/progress',
  },
];

const quickStats = [
  { label: '今日训练', value: '45分钟', icon: <Timer />, color: '#FF6B35' },
  { label: '本周目标', value: '80%', icon: <TrendingUp />, color: '#4CAF50' },
  { label: '连续天数', value: '12天', icon: <EmojiEvents />, color: '#2196F3' },
];

export function MuiHome() {
  const theme = useTheme();

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Box textAlign="center" sx={{ position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '2.5rem', md: '4rem' },
                fontWeight: 700,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
              }}
            >
              AI-fitness-singles
            </Typography>
            <Typography
              variant="h4"
              sx={{
                fontSize: { xs: '1.25rem', md: '1.75rem' },
                color: 'text.secondary',
                mb: 4,
                fontWeight: 400,
              }}
            >
              智能健身平台，专为单身人士打造
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<PlayArrow />}
                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}
              >
                开始训练
              </Button>
              <Button
                variant="outlined"
                size="large"
                startIcon={<DirectionsRun />}
                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}
                component={Link}
                href="/exercises"
              >
                浏览动作
              </Button>
            </Box>
          </Box>
        </Container>

        {/* Decorative elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: 100,
            height: 100,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.2)}, ${alpha(theme.palette.secondary.main, 0.2)})`,
            animation: 'float 6s ease-in-out infinite',
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '20%',
            left: '5%',
            width: 60,
            height: 60,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,
            animation: 'float 4s ease-in-out infinite reverse',
          }}
        />
      </Box>

      {/* Quick Stats */}
      <Container maxWidth="lg" sx={{ mt: -4, position: 'relative', zIndex: 2 }}>
        <Grid container spacing={3}>
          {quickStats.map((stat, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card
                sx={{
                  textAlign: 'center',
                  py: 3,
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: alpha(stat.color, 0.1),
                      color: stat.color,
                      mb: 2,
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.label}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography
          variant="h2"
          textAlign="center"
          sx={{ mb: 2, fontWeight: 600 }}
        >
          核心功能
        </Typography>
        <Typography
          variant="h6"
          textAlign="center"
          color="text.secondary"
          sx={{ mb: 6 }}
        >
          一站式健身解决方案
        </Typography>

        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card
                component={Link}
                href={feature.href}
                sx={{
                  height: '100%',
                  textDecoration: 'none',
                  cursor: 'pointer',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                  },
                }}
              >
                <CardContent sx={{ textAlign: 'center', p: 4 }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      p: 3,
                      borderRadius: '50%',
                      bgcolor: alpha(feature.color, 0.1),
                      color: feature.color,
                      mb: 3,
                      fontSize: '2rem',
                    }}
                  >
                    {feature.icon}
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Quick Actions */}
      <Box sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}>
        <Fab
          color="primary"
          aria-label="add workout"
          sx={{
            width: 64,
            height: 64,
            '&:hover': {
              transform: 'scale(1.1)',
            },
          }}
        >
          <Add sx={{ fontSize: '2rem' }} />
        </Fab>
      </Box>

      {/* CSS for animations */}
      <style jsx global>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
      `}</style>
    </Box>
  );
}
