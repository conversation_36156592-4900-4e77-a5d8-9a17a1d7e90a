/**
 * React Query hooks for workout data
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { WorkoutService } from '../api/services/workouts';
import type {
  CreateWorkoutSessionData
} from '../api/types';

// Query keys for consistent caching
export const workoutKeys = {
  all: ['workouts'] as const,
  sessions: () => [...workoutKeys.all, 'sessions'] as const,
  session: (id: string) => [...workoutKeys.sessions(), id] as const,
  sessionsList: (params: any) => [...workoutKeys.sessions(), 'list', params] as const,
  programs: () => [...workoutKeys.all, 'programs'] as const,
  program: (id: string) => [...workoutKeys.programs(), id] as const,
  programsList: (params: any) => [...workoutKeys.programs(), 'list', params] as const,
  userPrograms: () => [...workoutKeys.programs(), 'user'] as const,
  popular: (limit: number) => [...workoutKeys.programs(), 'popular', limit] as const,
  recommended: (limit: number) => [...workoutKeys.programs(), 'recommended', limit] as const,
  stats: (period: string) => [...workoutKeys.all, 'stats', period] as const,
  history: (params: any) => [...workoutKeys.all, 'history', params] as const,
};

/**
 * Hook to get workout sessions
 */
export function useWorkoutSessions(params: Parameters<typeof WorkoutService.getWorkoutSessions>[0] = {}) {
  return useQuery({
    queryKey: workoutKeys.sessionsList(params),
    queryFn: () => WorkoutService.getWorkoutSessions(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get a specific workout session
 */
export function useWorkoutSession(id: string, enabled = true) {
  return useQuery({
    queryKey: workoutKeys.session(id),
    queryFn: () => WorkoutService.getWorkoutSession(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create a workout session
 */
export function useCreateWorkoutSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateWorkoutSessionData) => WorkoutService.createWorkoutSession(data),
    onSuccess: () => {
      // Invalidate sessions list to refetch
      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });
    },
  });
}

/**
 * Hook to update a workout session
 */
export function useUpdateWorkoutSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateWorkoutSessionData> }) =>
      WorkoutService.updateWorkoutSession(id, data),
    onSuccess: (updatedSession) => {
      // Update the specific session cache
      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);
      
      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });
    },
  });
}

/**
 * Hook to delete a workout session
 */
export function useDeleteWorkoutSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => WorkoutService.deleteWorkoutSession(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: workoutKeys.session(deletedId) });
      
      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });
    },
  });
}

/**
 * Hook to start a workout session
 */
export function useStartWorkoutSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => WorkoutService.startWorkoutSession(id),
    onSuccess: (updatedSession) => {
      // Update the specific session cache
      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);
      
      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });
    },
  });
}

/**
 * Hook to complete a workout session
 */
export function useCompleteWorkoutSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: { duration: number; notes?: string } }) =>
      WorkoutService.completeWorkoutSession(id, data),
    onSuccess: (updatedSession) => {
      // Update the specific session cache
      queryClient.setQueryData(workoutKeys.session(updatedSession.id), updatedSession);
      
      // Invalidate sessions list and stats
      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });
      queryClient.invalidateQueries({ queryKey: workoutKeys.stats('month') });
    },
  });
}

/**
 * Hook to get workout programs
 */
export function useWorkoutPrograms(params: Parameters<typeof WorkoutService.getWorkoutPrograms>[0] = {}) {
  return useQuery({
    queryKey: workoutKeys.programsList(params),
    queryFn: () => WorkoutService.getWorkoutPrograms(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get a specific workout program
 */
export function useWorkoutProgram(id: string, enabled = true) {
  return useQuery({
    queryKey: workoutKeys.program(id),
    queryFn: () => WorkoutService.getWorkoutProgram(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get user's joined programs
 */
export function useUserPrograms() {
  return useQuery({
    queryKey: workoutKeys.userPrograms(),
    queryFn: () => WorkoutService.getUserPrograms(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get popular programs
 */
export function usePopularPrograms(limit = 10) {
  return useQuery({
    queryKey: workoutKeys.popular(limit),
    queryFn: () => WorkoutService.getPopularPrograms(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

/**
 * Hook to get recommended programs
 */
export function useRecommendedPrograms(limit = 6) {
  return useQuery({
    queryKey: workoutKeys.recommended(limit),
    queryFn: () => WorkoutService.getRecommendedPrograms(limit),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to create a workout program
 */
export function useCreateWorkoutProgram() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => WorkoutService.createWorkoutProgram(data),
    onSuccess: () => {
      // Invalidate programs list
      queryClient.invalidateQueries({ queryKey: workoutKeys.programs() });
    },
  });
}

/**
 * Hook to join a workout program
 */
export function useJoinWorkoutProgram() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => WorkoutService.joinWorkoutProgram(id),
    onSuccess: () => {
      // Invalidate user programs
      queryClient.invalidateQueries({ queryKey: workoutKeys.userPrograms() });
    },
  });
}

/**
 * Hook to leave a workout program
 */
export function useLeaveWorkoutProgram() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => WorkoutService.leaveWorkoutProgram(id),
    onSuccess: () => {
      // Invalidate user programs
      queryClient.invalidateQueries({ queryKey: workoutKeys.userPrograms() });
    },
  });
}

/**
 * Hook to generate AI workout
 */
export function useGenerateAIWorkout() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (preferences: Parameters<typeof WorkoutService.generateAIWorkout>[0]) =>
      WorkoutService.generateAIWorkout(preferences),
    onSuccess: () => {
      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: workoutKeys.sessions() });
    },
  });
}

/**
 * Hook to get workout statistics
 */
export function useWorkoutStats(period: 'week' | 'month' | 'year' = 'month') {
  return useQuery({
    queryKey: workoutKeys.stats(period),
    queryFn: () => WorkoutService.getWorkoutStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get workout history with infinite scrolling
 */
export function useWorkoutHistory(params: Omit<Parameters<typeof WorkoutService.getWorkoutHistory>[0], 'offset'> = {}) {
  return useInfiniteQuery({
    queryKey: workoutKeys.history(params),
    queryFn: ({ pageParam = 0 }) => 
      WorkoutService.getWorkoutHistory({ ...params, offset: pageParam }),
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      const { pagination } = lastPage;
      return pagination.hasNext ? pagination.page * pagination.limit : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
}
