"use client"

import { useState } from "react"
import { Navigation } from "@/components/Navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { LoadingPage, LoadingGrid } from "@/components/ui/loading"
import { ErrorMessage } from "@/components/ui/error"
import { EmptyWorkouts, EmptySearchResults } from "@/components/ui/empty-state"
import {
  Clock,
  Users,
  Zap,
  Target,
  Play,
  Plus,
  Filter,
  Search,
  Star,
  BookOpen,
  Calendar,
  X,
  Edit,
  Trash2,
  Heart
} from "lucide-react"
import { 
  useWorkoutPrograms,
  useWorkoutSessions,
  usePopularPrograms,
  useRecommendedPrograms,
  useUserPrograms,
  useJoinWorkoutProgram,
  useCreateWorkoutSession
} from "@/lib/hooks/use-workouts"
import { useAuth } from "@/lib/hooks/use-auth"
import Link from "next/link"

export default function WorkoutsPage() {
  const [activeTab, setActiveTab] = useState<'programs' | 'sessions' | 'my-programs'>('programs')
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [selectedFilters, setSelectedFilters] = useState({
    category: [] as string[],
    difficulty: [] as string[],
    duration: [] as string[]
  })

  const { isAuthenticated } = useAuth()

  // API hooks
  const { data: programsData, isLoading: isLoadingPrograms, error: programsError } = useWorkoutPrograms({
    limit: 20,
    category: selectedFilters.category.length > 0 ? selectedFilters.category[0] : undefined,
    difficulty: selectedFilters.difficulty.length > 0 ? selectedFilters.difficulty[0] : undefined,
    duration: selectedFilters.duration.length > 0 ? selectedFilters.duration[0] : undefined
  })

  const { data: sessionsData, isLoading: isLoadingSessions } = useWorkoutSessions({
    limit: 20
  })

  const { data: popularPrograms, isLoading: isLoadingPopular } = usePopularPrograms(6)
  const { data: recommendedPrograms, isLoading: isLoadingRecommended } = useRecommendedPrograms(6)
  const { data: userPrograms, isLoading: isLoadingUserPrograms } = useUserPrograms()

  const joinProgramMutation = useJoinWorkoutProgram()
  const createSessionMutation = useCreateWorkoutSession()

  const handleFilterChange = (type: keyof typeof selectedFilters, value: string) => {
    setSelectedFilters(prev => {
      const currentValues = prev[type]
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value]
      
      return {
        ...prev,
        [type]: newValues
      }
    })
  }

  const clearFilters = () => {
    setSelectedFilters({
      category: [],
      difficulty: [],
      duration: []
    })
  }

  const hasActiveFilters = Object.values(selectedFilters).some(arr => arr.length > 0)

  const handleJoinProgram = async (programId: string) => {
    try {
      await joinProgramMutation.mutateAsync(programId)
    } catch (error) {
      console.error('Failed to join program:', error)
    }
  }

  const handleStartWorkout = async (programId: string) => {
    try {
      await createSessionMutation.mutateAsync({
        exercises: [],
        notes: `Quick workout session - ${new Date().toLocaleDateString()}`
      })
    } catch (error) {
      console.error('Failed to start workout:', error)
    }
  }

  const filteredPrograms = programsData?.data?.filter(program =>
    searchQuery === "" ||
    program.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    program.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || []

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Workout Programs
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover personalized workout programs designed to help you reach your fitness goals
          </p>
        </div>

        {/* Quick Stats */}
        {isAuthenticated && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Active Programs</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {userPrograms?.length || 0}
                    </p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">This Week</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sessionsData?.data?.filter(s => s.status === 'COMPLETED').length || 0}
                    </p>
                  </div>
                  <Calendar className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Minutes</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sessionsData?.data?.reduce((acc, s) => acc + (s.duration || 0), 0) || 0}
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm">
          <button
            onClick={() => setActiveTab('programs')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'programs'
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            All Programs
          </button>
          {isAuthenticated && (
            <>
              <button
                onClick={() => setActiveTab('my-programs')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'my-programs'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                My Programs
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'sessions'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                My Sessions
              </button>
            </>
          )}
        </div>

        {/* Search and Filters */}
        {activeTab === 'programs' && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              {/* Search Bar */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  type="text"
                  placeholder="Search workout programs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              {/* Filter Toggle */}
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filters
                {hasActiveFilters && (
                  <Badge variant="secondary" className="ml-1">
                    {Object.values(selectedFilters).reduce((acc, arr) => acc + arr.length, 0)}
                  </Badge>
                )}
              </Button>
            </div>

            {/* Filter Panel */}
            {showFilters && (
              <div className="border-t pt-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Category Filter */}
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Category</h3>
                    <div className="space-y-2">
                      {['Strength', 'Cardio', 'Flexibility', 'HIIT', 'Yoga'].map((category) => (
                        <label key={category} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={selectedFilters.category.includes(category)}
                            onChange={() => handleFilterChange('category', category)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm text-gray-700">{category}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Difficulty Filter */}
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Difficulty</h3>
                    <div className="space-y-2">
                      {['Beginner', 'Intermediate', 'Advanced'].map((difficulty) => (
                        <label key={difficulty} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={selectedFilters.difficulty.includes(difficulty)}
                            onChange={() => handleFilterChange('difficulty', difficulty)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm text-gray-700">{difficulty}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Duration Filter */}
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Duration</h3>
                    <div className="space-y-2">
                      {['15-30 min', '30-45 min', '45-60 min', '60+ min'].map((duration) => (
                        <label key={duration} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={selectedFilters.duration.includes(duration)}
                            onChange={() => handleFilterChange('duration', duration)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm text-gray-700">{duration}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                {hasActiveFilters && (
                  <div className="mt-4 pt-4 border-t">
                    <Button variant="outline" onClick={clearFilters} className="flex items-center gap-2">
                      <X className="h-4 w-4" />
                      Clear Filters
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Content based on active tab */}
        {activeTab === 'programs' && (
          <>
            {/* Popular Programs Section */}
            {!searchQuery && !hasActiveFilters && (
              <>
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Popular Programs</h2>
                  {isLoadingPopular ? (
                    <LoadingGrid />
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {popularPrograms?.slice(0, 6).map((program) => (
                        <Card key={program.id} className="hover:shadow-lg transition-shadow">
                          <CardHeader>
                            <div className="flex justify-between items-start">
                              <div>
                                <CardTitle className="text-lg">{program.title}</CardTitle>
                                <CardDescription>{program.description}</CardDescription>
                              </div>
                              <Button variant="ghost" size="icon">
                                <Heart className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="flex items-center justify-between text-sm text-gray-600">
                                <div className="flex items-center gap-1">
                                  <Clock className="h-4 w-4" />
                                  <span>{program.duration || 'N/A'} min</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Target className="h-4 w-4" />
                                  <span>{program.difficulty || 'N/A'}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Users className="h-4 w-4" />
                                  <span>{program.participantCount || 0}</span>
                                </div>
                              </div>

                              <div className="flex gap-2">
                                <Button 
                                  size="sm" 
                                  className="flex-1"
                                  onClick={() => handleStartWorkout(program.id)}
                                  disabled={createSessionMutation.isPending}
                                >
                                  <Play className="h-4 w-4 mr-2" />
                                  Start Workout
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleJoinProgram(program.id)}
                                  disabled={joinProgramMutation.isPending}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>

                {/* Recommended Programs Section */}
                {isAuthenticated && (
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Recommended for You</h2>
                    {isLoadingRecommended ? (
                      <LoadingGrid />
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {recommendedPrograms?.map((program) => (
                          <Card key={program.id} className="hover:shadow-lg transition-shadow">
                            <CardHeader>
                              <div className="flex justify-between items-start">
                                <div>
                                  <CardTitle className="text-lg">{program.title}</CardTitle>
                                  <CardDescription>{program.description}</CardDescription>
                                </div>
                                <Button variant="ghost" size="icon">
                                  <Heart className="h-4 w-4" />
                                </Button>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                <div className="flex items-center justify-between text-sm text-gray-600">
                                  <div className="flex items-center gap-1">
                                    <Clock className="h-4 w-4" />
                                    <span>{program.duration || 'N/A'} min</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Target className="h-4 w-4" />
                                    <span>{program.difficulty || 'N/A'}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Users className="h-4 w-4" />
                                    <span>{program.participantCount || 0}</span>
                                  </div>
                                </div>

                                <div className="flex gap-2">
                                  <Button 
                                    size="sm" 
                                    className="flex-1"
                                    onClick={() => handleStartWorkout(program.id)}
                                    disabled={createSessionMutation.isPending}
                                  >
                                    <Play className="h-4 w-4 mr-2" />
                                    Start Workout
                                  </Button>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleJoinProgram(program.id)}
                                    disabled={joinProgramMutation.isPending}
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}

            {/* All Programs */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {searchQuery ? `Search Results for "${searchQuery}"` : 'All Programs'}
              </h2>
              
              {isLoadingPrograms ? (
                <LoadingGrid />
              ) : programsError ? (
                <ErrorMessage 
                  title="Failed to load programs"
                  message="Please try again later"
                  onRetry={() => window.location.reload()}
                />
              ) : filteredPrograms.length === 0 ? (
                searchQuery ? (
                  <EmptySearchResults
                    searchTerm={searchQuery}
                    onClearSearch={() => setSearchQuery("")}
                  />
                ) : (
                  <EmptyWorkouts />
                )
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredPrograms.map((program) => (
                    <Card key={program.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg">{program.title}</CardTitle>
                            <CardDescription>{program.description}</CardDescription>
                          </div>
                          <Button variant="ghost" size="icon">
                            <Heart className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              <span>{program.duration || 'N/A'} min</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Target className="h-4 w-4" />
                              <span>{program.difficulty || 'N/A'}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              <span>{program.participantCount || 0}</span>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <Link href={`/workouts/${program.id}`} className="flex-1">
                              <Button size="sm" className="w-full">
                                <Play className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                            </Link>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleJoinProgram(program.id)}
                              disabled={joinProgramMutation.isPending}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </>
        )}

        {/* My Programs Tab */}
        {activeTab === 'my-programs' && isAuthenticated && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">My Programs</h2>
            {isLoadingUserPrograms ? (
              <LoadingGrid />
            ) : !userPrograms || userPrograms.length === 0 ? (
              <EmptyWorkouts />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {userPrograms.map((program) => (
                  <Card key={program.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{program.title}</CardTitle>
                          <CardDescription>{program.description}</CardDescription>
                        </div>
                        <Badge variant="secondary">Joined</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{program.duration || 'N/A'} min</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Target className="h-4 w-4" />
                            <span>{program.difficulty || 'N/A'}</span>
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            className="flex-1"
                            onClick={() => handleStartWorkout(program.id)}
                            disabled={createSessionMutation.isPending}
                          >
                            <Play className="h-4 w-4 mr-2" />
                            Start Workout
                          </Button>
                          <Link href={`/workouts/${program.id}`}>
                            <Button variant="outline" size="sm">
                              <BookOpen className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* My Sessions Tab */}
        {activeTab === 'sessions' && isAuthenticated && (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">My Workout Sessions</h2>
            {isLoadingSessions ? (
              <LoadingGrid />
            ) : !sessionsData?.data || sessionsData.data.length === 0 ? (
              <EmptyWorkouts />
            ) : (
              <div className="space-y-4">
                {sessionsData.data.map((session) => (
                  <Card key={session.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{session.notes || 'Workout Session'}</h3>
                          <p className="text-gray-600">Workout Session</p>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date().toLocaleDateString()}
                            </span>
                            {session.duration && (
                              <span className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                {session.duration} min
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={
                              session.status === 'COMPLETED' ? 'default' :
                              session.status === 'IN_PROGRESS' ? 'secondary' : 'outline'
                            }
                          >
                            {session.status}
                          </Badge>
                          <Link href={`/workouts/sessions/${session.id}`}>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
