"use client"

import { useState } from "react"
import { Navigation } from "@/components/navigation"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Clock,
  Users,
  Zap,
  Target,
  Play,
  Plus,
  Filter,
  Search,
  Star,
  BookOpen,
  Calendar,
  X,
  Edit,
  Trash2
} from "lucide-react"

const workoutPlans = [
  {
    id: 1,
    title: "Morning Energy Boost",
    description: "Quick 15-minute workout to start your day",
    duration: 15,
    difficulty: "Beginner",
    category: "Cardio",
    exercises: 8,
    image: "🌅",
    rating: 4.8,
    completions: 1247,
    isFavorite: false,
    lastCompleted: "2024-12-20",
    tags: ["morning", "energy", "quick"]
  },
  {
    id: 2,
    title: "Upper Body Strength",
    description: "Build strength in your arms, chest, and shoulders",
    duration: 45,
    difficulty: "Intermediate",
    category: "Strength",
    exercises: 12,
    image: "💪",
    rating: 4.9,
    completions: 892,
    isFavorite: true,
    lastCompleted: "2024-12-18",
    tags: ["strength", "upper-body", "muscle-building"]
  },
  {
    id: 3,
    title: "Core Crusher",
    description: "Intense core workout for a stronger midsection",
    duration: 30,
    difficulty: "Advanced",
    category: "Core",
    exercises: 10,
    image: "🔥",
    rating: 4.7,
    completions: 634,
    isFavorite: false,
    lastCompleted: null,
    tags: ["core", "abs", "intense"]
  },
  {
    id: 4,
    title: "Full Body HIIT",
    description: "High-intensity interval training for maximum burn",
    duration: 25,
    difficulty: "Intermediate",
    category: "HIIT",
    exercises: 6,
    image: "⚡",
    rating: 4.6,
    completions: 1156,
    isFavorite: true,
    lastCompleted: "2024-12-22",
    tags: ["hiit", "full-body", "fat-burn"]
  },
  {
    id: 5,
    title: "Yoga Flow",
    description: "Gentle stretching and mindfulness practice",
    duration: 40,
    difficulty: "Beginner",
    category: "Flexibility",
    exercises: 15,
    image: "🧘",
    rating: 4.9,
    completions: 2103,
    isFavorite: false,
    lastCompleted: "2024-12-19",
    tags: ["yoga", "flexibility", "mindfulness"]
  },
  {
    id: 6,
    title: "Lower Body Power",
    description: "Strengthen your legs and glutes",
    duration: 35,
    difficulty: "Intermediate",
    category: "Strength",
    exercises: 9,
    image: "🦵",
    rating: 4.8,
    completions: 743,
    isFavorite: false,
    lastCompleted: null,
    tags: ["legs", "glutes", "power"]
  }
]

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case "Beginner": return "bg-green-100 text-green-800"
    case "Intermediate": return "bg-yellow-100 text-yellow-800"
    case "Advanced": return "bg-red-100 text-red-800"
    default: return "bg-gray-100 text-gray-800"
  }
}

export default function Workouts() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedDifficulty, setSelectedDifficulty] = useState("All")
  const [sortBy, setSortBy] = useState("name")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [workouts, setWorkouts] = useState(workoutPlans)

  // Filter and sort workouts
  const filteredWorkouts = workouts
    .filter(workout => {
      const matchesSearch = workout.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           workout.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           workout.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      const matchesCategory = selectedCategory === "All" || workout.category === selectedCategory
      const matchesDifficulty = selectedDifficulty === "All" || workout.difficulty === selectedDifficulty
      return matchesSearch && matchesCategory && matchesDifficulty
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name": return a.title.localeCompare(b.title)
        case "duration": return a.duration - b.duration
        case "difficulty":
          const difficultyOrder = { "Beginner": 1, "Intermediate": 2, "Advanced": 3 }
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
        case "rating": return b.rating - a.rating
        case "popular": return b.completions - a.completions
        default: return 0
      }
    })

  const toggleFavorite = (id: number) => {
    setWorkouts(workouts.map(workout =>
      workout.id === id ? { ...workout, isFavorite: !workout.isFavorite } : workout
    ))
  }

  const categories = ["All", ...Array.from(new Set(workoutPlans.map(w => w.category)))]
  const difficulties = ["All", "Beginner", "Intermediate", "Advanced"]

  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      {/* Header Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              Your Workouts
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Create, customize, and track your workout plans
            </p>
          </div>

          <div className="flex justify-center">
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
              onClick={() => setShowCreateModal(true)}
            >
              <Plus className="mr-2 h-5 w-5" />
              Create New Workout
            </Button>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search workouts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="name">Sort by Name</option>
              <option value="duration">Sort by Duration</option>
              <option value="difficulty">Sort by Difficulty</option>
              <option value="rating">Sort by Rating</option>
              <option value="popular">Sort by Popularity</option>
            </select>
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            <div className="flex gap-2">
              <span className="text-sm font-medium text-gray-700 py-2">Category:</span>
              {categories.map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="text-xs"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <div className="flex gap-2">
              <span className="text-sm font-medium text-gray-700 py-2">Difficulty:</span>
              {difficulties.map(difficulty => (
                <Button
                  key={difficulty}
                  variant={selectedDifficulty === difficulty ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedDifficulty(difficulty)}
                  className="text-xs"
                >
                  {difficulty}
                </Button>
              ))}
            </div>
          </div>

          {/* Results Count */}
          <div className="text-sm text-gray-600">
            Showing {filteredWorkouts.length} of {workouts.length} workouts
          </div>
        </div>

        {/* Workout Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkouts.map((workout) => (
            <Card key={workout.id} className="hover:shadow-lg transition-shadow group">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="text-4xl mb-2">{workout.image}</div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleFavorite(workout.id)}
                      className="p-1 h-8 w-8"
                    >
                      <Star
                        className={`h-4 w-4 ${workout.isFavorite ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'}`}
                      />
                    </Button>
                    <Badge variant="secondary" className={getDifficultyColor(workout.difficulty)}>
                      {workout.difficulty}
                    </Badge>
                  </div>
                </div>
                <CardTitle className="text-lg">{workout.title}</CardTitle>
                <CardDescription>{workout.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Rating and Stats */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{workout.rating}</span>
                      <span className="text-gray-500">({workout.completions})</span>
                    </div>
                    {workout.lastCompleted && (
                      <div className="flex items-center gap-1 text-gray-500">
                        <Calendar className="h-4 w-4" />
                        <span className="text-xs">Last: {workout.lastCompleted}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{workout.duration} min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="h-4 w-4" />
                      <span>{workout.exercises} exercises</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      {workout.category}
                    </Badge>
                    {workout.tags.slice(0, 2).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Play className="h-4 w-4 mr-2" />
                      Start Workout
                    </Button>
                    <Button variant="outline" size="sm" className="px-3">
                      <BookOpen className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredWorkouts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No workouts found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filters</p>
            <Button variant="outline" onClick={() => {
              setSearchTerm("")
              setSelectedCategory("All")
              setSelectedDifficulty("All")
            }}>
              Clear Filters
            </Button>
          </div>
        )}

        {/* Popular Categories */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Popular Categories
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { name: "Strength", icon: "💪", count: 24 },
              { name: "Cardio", icon: "❤️", count: 18 },
              { name: "HIIT", icon: "⚡", count: 12 },
              { name: "Yoga", icon: "🧘", count: 15 }
            ].map((category) => (
              <Card key={category.name} className="text-center hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="text-3xl mb-2">{category.icon}</div>
                  <h3 className="font-semibold text-gray-900">{category.name}</h3>
                  <p className="text-sm text-gray-600">{category.count} workouts</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Create Workout Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900">Create New Workout</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCreateModal(false)}
                  className="p-1 h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Workout Name
                  </label>
                  <input
                    type="text"
                    placeholder="Enter workout name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    placeholder="Describe your workout"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Duration (minutes)
                    </label>
                    <input
                      type="number"
                      placeholder="30"
                      min="5"
                      max="120"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Difficulty
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option value="Beginner">Beginner</option>
                      <option value="Intermediate">Intermediate</option>
                      <option value="Advanced">Advanced</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="Strength">Strength</option>
                    <option value="Cardio">Cardio</option>
                    <option value="HIIT">HIIT</option>
                    <option value="Flexibility">Flexibility</option>
                    <option value="Core">Core</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Emoji Icon
                  </label>
                  <input
                    type="text"
                    placeholder="🏋️"
                    maxLength={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => setShowCreateModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    Create Workout
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
