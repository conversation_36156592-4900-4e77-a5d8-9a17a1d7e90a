"use client"

import { Navigation } from "@/components/navigation"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Clock,
  Users,
  Zap,
  Target,
  Play,
  Plus,
  Filter,
  Search
} from "lucide-react"

const workoutPlans = [
  {
    id: 1,
    title: "Morning Energy Boost",
    description: "Quick 15-minute workout to start your day",
    duration: 15,
    difficulty: "Beginner",
    category: "Cardio",
    exercises: 8,
    image: "🌅"
  },
  {
    id: 2,
    title: "Upper Body Strength",
    description: "Build strength in your arms, chest, and shoulders",
    duration: 45,
    difficulty: "Intermediate",
    category: "Strength",
    exercises: 12,
    image: "💪"
  },
  {
    id: 3,
    title: "Core Crusher",
    description: "Intense core workout for a stronger midsection",
    duration: 30,
    difficulty: "Advanced",
    category: "Core",
    exercises: 10,
    image: "🔥"
  },
  {
    id: 4,
    title: "Full Body HIIT",
    description: "High-intensity interval training for maximum burn",
    duration: 25,
    difficulty: "Intermediate",
    category: "HIIT",
    exercises: 6,
    image: "⚡"
  },
  {
    id: 5,
    title: "Yoga Flow",
    description: "Gentle stretching and mindfulness practice",
    duration: 40,
    difficulty: "Beginner",
    category: "Flexibility",
    exercises: 15,
    image: "🧘"
  },
  {
    id: 6,
    title: "Lower Body Power",
    description: "Strengthen your legs and glutes",
    duration: 35,
    difficulty: "Intermediate",
    category: "Strength",
    exercises: 9,
    image: "🦵"
  }
]

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case "Beginner": return "bg-green-100 text-green-800"
    case "Intermediate": return "bg-yellow-100 text-yellow-800"
    case "Advanced": return "bg-red-100 text-red-800"
    default: return "bg-gray-100 text-gray-800"
  }
}

export default function Workouts() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      {/* Header Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              Your Workouts
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Create, customize, and track your workout plans
            </p>
          </div>

          <div className="flex justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
              <Plus className="mr-2 h-5 w-5" />
              Create New Workout
            </Button>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        {/* Search and Filters */}

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search workouts..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>

        {/* Workout Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {workoutPlans.map((workout) => (
            <Card key={workout.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="text-4xl mb-2">{workout.image}</div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(workout.difficulty)}`}>
                    {workout.difficulty}
                  </span>
                </div>
                <CardTitle className="text-lg">{workout.title}</CardTitle>
                <CardDescription>{workout.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{workout.duration} min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="h-4 w-4" />
                      <span>{workout.exercises} exercises</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                      {workout.category}
                    </span>
                  </div>
                  
                  <Button className="w-full">
                    <Play className="h-4 w-4 mr-2" />
                    Start Workout
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Popular Categories */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Popular Categories
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { name: "Strength", icon: "💪", count: 24 },
              { name: "Cardio", icon: "❤️", count: 18 },
              { name: "HIIT", icon: "⚡", count: 12 },
              { name: "Yoga", icon: "🧘", count: 15 }
            ].map((category) => (
              <Card key={category.name} className="text-center hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="text-3xl mb-2">{category.icon}</div>
                  <h3 className="font-semibold text-gray-900">{category.name}</h3>
                  <p className="text-sm text-gray-600">{category.count} workouts</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
