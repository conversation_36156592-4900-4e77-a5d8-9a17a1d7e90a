{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/utils.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  Enabled,\n  FetchStatus,\n  MutationKey,\n  MutationStatus,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n  StaleTime,\n  StaleTimeFunction,\n} from './types'\nimport type { Mutation } from './mutation'\nimport type { FetchOptions, Query } from './query'\n\n// TYPES\n\nexport interface QueryFilters<TQueryKey extends QueryKey = QueryKey> {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: TQueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (\n    mutation: Mutation<TData, TError, TVariables, TContext>,\n  ) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: MutationKey\n  /**\n   * Filter by mutation status\n   */\n  status?: MutationStatus\n}\n\nexport type Updater<TInput, TOutput> = TOutput | ((input: TInput) => TOutput)\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in globalThis\n\nexport function noop(): void\nexport function noop(): undefined\nexport function noop() {}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as (_: TInput) => TOutput)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function resolveStaleTime<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  staleTime:\n    | undefined\n    | StaleTimeFunction<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): StaleTime | undefined {\n  return typeof staleTime === 'function' ? staleTime(query) : staleTime\n}\n\nexport function resolveEnabled<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  enabled: undefined | Enabled<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): boolean | undefined {\n  return typeof enabled === 'function' ? enabled(query) : enabled\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, status, predicate, mutationKey } = filters\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (status && mutation.state.status !== status) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: Pick<QueryOptions<any, any, any, any>, 'queryKeyHashFn'>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query & mutation keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashKey(queryKey: QueryKey | MutationKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean\nexport function partialMatchKey(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aItems = array ? a : Object.keys(a)\n    const aSize = aItems.length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n    const aItemsSet = new Set(aItems)\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      if (\n        ((!array && aItemsSet.has(key)) || array) &&\n        a[key] === undefined &&\n        b[key] === undefined\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key])\n        if (copy[key] === a[key] && a[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects.\n */\nexport function shallowEqualObjects<T extends Record<string, any>>(\n  a: T,\n  b: T | undefined,\n): boolean {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\n// eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has no constructor\n  const ctor = o.constructor\n  if (ctor === undefined) {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Handles Objects created by Object.create(<arbitrary prototype>)\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data) as TData\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== 'production') {\n      try {\n        return replaceEqualDeep(prevData, data)\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`,\n        )\n\n        // Prevent the replaceEqualDeep from being called again down below.\n        throw error\n      }\n    }\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n\nexport function keepPreviousData<T>(\n  previousData: T | undefined,\n): T | undefined {\n  return previousData\n}\n\nexport function addToEnd<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [...items, item]\n  return max && newItems.length > max ? newItems.slice(1) : newItems\n}\n\nexport function addToStart<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [item, ...items]\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems\n}\n\nexport const skipToken = Symbol()\nexport type SkipToken = typeof skipToken\n\nexport function ensureQueryFn<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: {\n    queryFn?: QueryFunction<TQueryFnData, TQueryKey> | SkipToken\n    queryHash?: string\n  },\n  fetchOptions?: FetchOptions<TQueryFnData>,\n): QueryFunction<TQueryFnData, TQueryKey> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`,\n      )\n    }\n  }\n\n  // if we attempt to retry a fetch that was triggered from an initialPromise\n  // when we don't have a queryFn yet, we can't retry, so we just return the already rejected initialPromise\n  // if an observer has already mounted, we will be able to retry with that queryFn\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise!\n  }\n\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () =>\n      Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`))\n  }\n\n  return options.queryFn\n}\n\nexport function shouldThrowError<T extends (...args: Array<any>) => boolean>(\n  throwOnError: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow throwOnError function to override throwing behavior on a per-error basis\n  if (typeof throwOnError === 'function') {\n    return throwOnError(...params)\n  }\n\n  return !!throwOnError\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EO,IAAM,WAAW,OAAO,WAAW,eAAe,UAAU;AAI5D,SAAS,OAAO,CAAC;AAEjB,SAAS,iBACd,OAAA,EACA,KAAA,EACS;IACT,OAAO,OAAO,YAAY,aACrB,QAAmC,KAAK,IACzC;AACN;AAEO,SAAS,eAAe,KAAA,EAAiC;IAC9D,OAAO,OAAO,UAAU,YAAY,SAAS,KAAK,UAAU;AAC9D;AAEO,SAAS,eAAe,SAAA,EAAmB,SAAA,EAA4B;IAC5E,OAAO,KAAK,GAAA,CAAI,YAAA,CAAa,aAAa,CAAA,IAAK,KAAK,GAAA,CAAI,GAAG,CAAC;AAC9D;AAEO,SAAS,iBAMd,SAAA,EAGA,KAAA,EACuB;IACvB,OAAO,OAAO,cAAc,aAAa,UAAU,KAAK,IAAI;AAC9D;AAEO,SAAS,eAMd,OAAA,EACA,KAAA,EACqB;IACrB,OAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AAEO,SAAS,WACd,OAAA,EACA,KAAA,EACS;IACT,MAAM,EACJ,OAAO,KAAA,EACP,KAAA,EACA,WAAA,EACA,SAAA,EACA,QAAA,EACA,KAAA,EACF,GAAI;IAEJ,IAAI,UAAU;QACZ,IAAI,OAAO;YACT,IAAI,MAAM,SAAA,KAAc,sBAAsB,UAAU,MAAM,OAAO,GAAG;gBACtE,OAAO;YACT;QACF,OAAA,IAAW,CAAC,gBAAgB,MAAM,QAAA,EAAU,QAAQ,GAAG;YACrD,OAAO;QACT;IACF;IAEA,IAAI,SAAS,OAAO;QAClB,MAAM,WAAW,MAAM,QAAA,CAAS;QAChC,IAAI,SAAS,YAAY,CAAC,UAAU;YAClC,OAAO;QACT;QACA,IAAI,SAAS,cAAc,UAAU;YACnC,OAAO;QACT;IACF;IAEA,IAAI,OAAO,UAAU,aAAa,MAAM,OAAA,CAAQ,MAAM,OAAO;QAC3D,OAAO;IACT;IAEA,IAAI,eAAe,gBAAgB,MAAM,KAAA,CAAM,WAAA,EAAa;QAC1D,OAAO;IACT;IAEA,IAAI,aAAa,CAAC,UAAU,KAAK,GAAG;QAClC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,cACd,OAAA,EACA,QAAA,EACS;IACT,MAAM,EAAE,KAAA,EAAO,MAAA,EAAQ,SAAA,EAAW,WAAA,CAAY,CAAA,GAAI;IAClD,IAAI,aAAa;QACf,IAAI,CAAC,SAAS,OAAA,CAAQ,WAAA,EAAa;YACjC,OAAO;QACT;QACA,IAAI,OAAO;YACT,IAAI,QAAQ,SAAS,OAAA,CAAQ,WAAW,MAAM,QAAQ,WAAW,GAAG;gBAClE,OAAO;YACT;QACF,OAAA,IAAW,CAAC,gBAAgB,SAAS,OAAA,CAAQ,WAAA,EAAa,WAAW,GAAG;YACtE,OAAO;QACT;IACF;IAEA,IAAI,UAAU,SAAS,KAAA,CAAM,MAAA,KAAW,QAAQ;QAC9C,OAAO;IACT;IAEA,IAAI,aAAa,CAAC,UAAU,QAAQ,GAAG;QACrC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,sBACd,QAAA,EACA,OAAA,EACQ;IACR,MAAM,SAAS,SAAS,kBAAkB;IAC1C,OAAO,OAAO,QAAQ;AACxB;AAMO,SAAS,QAAQ,QAAA,EAA0C;IAChE,OAAO,KAAK,SAAA,CAAU,UAAU,CAAC,GAAG,MAClC,cAAc,GAAG,IACb,OAAO,IAAA,CAAK,GAAG,EACZ,IAAA,CAAK,EACL,MAAA,CAAO,CAAC,QAAQ,QAAQ;YACvB,MAAA,CAAO,GAAG,CAAA,GAAI,GAAA,CAAI,GAAG,CAAA;YACrB,OAAO;QACT,GAAG,CAAC,CAAQ,IACd;AAER;AAMO,SAAS,gBAAgB,CAAA,EAAQ,CAAA,EAAiB;IACvD,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IAEA,IAAI,OAAO,MAAM,OAAO,GAAG;QACzB,OAAO;IACT;IAEA,IAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;QAC5D,OAAO,OAAO,IAAA,CAAK,CAAC,EAAE,KAAA,CAAM,CAAC,MAAQ,gBAAgB,CAAA,CAAE,GAAG,CAAA,EAAG,CAAA,CAAE,GAAG,CAAC,CAAC;IACtE;IAEA,OAAO;AACT;AAQO,SAAS,iBAAiB,CAAA,EAAQ,CAAA,EAAa;IACpD,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IAEA,MAAM,QAAQ,aAAa,CAAC,KAAK,aAAa,CAAC;IAE/C,IAAI,SAAU,cAAc,CAAC,KAAK,cAAc,CAAC,GAAI;QACnD,MAAM,SAAS,QAAQ,IAAI,OAAO,IAAA,CAAK,CAAC;QACxC,MAAM,QAAQ,OAAO,MAAA;QACrB,MAAM,SAAS,QAAQ,IAAI,OAAO,IAAA,CAAK,CAAC;QACxC,MAAM,QAAQ,OAAO,MAAA;QACrB,MAAM,OAAY,QAAQ,CAAC,CAAA,GAAI,CAAC;QAChC,MAAM,YAAY,IAAI,IAAI,MAAM;QAEhC,IAAI,aAAa;QAEjB,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,MAAM,QAAQ,IAAI,MAAA,CAAO,CAAC,CAAA;YAChC,IAAA,CACI,CAAC,SAAS,UAAU,GAAA,CAAI,GAAG,KAAM,KAAA,KACnC,CAAA,CAAE,GAAG,CAAA,KAAM,KAAA,KACX,CAAA,CAAE,GAAG,CAAA,KAAM,KAAA,GACX;gBACA,IAAA,CAAK,GAAG,CAAA,GAAI,KAAA;gBACZ;YACF,OAAO;gBACL,IAAA,CAAK,GAAG,CAAA,GAAI,iBAAiB,CAAA,CAAE,GAAG,CAAA,EAAG,CAAA,CAAE,GAAG,CAAC;gBAC3C,IAAI,IAAA,CAAK,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,IAAK,CAAA,CAAE,GAAG,CAAA,KAAM,KAAA,GAAW;oBAChD;gBACF;YACF;QACF;QAEA,OAAO,UAAU,SAAS,eAAe,QAAQ,IAAI;IACvD;IAEA,OAAO;AACT;AAKO,SAAS,oBACd,CAAA,EACA,CAAA,EACS;IACT,IAAI,CAAC,KAAK,OAAO,IAAA,CAAK,CAAC,EAAE,MAAA,KAAW,OAAO,IAAA,CAAK,CAAC,EAAE,MAAA,EAAQ;QACzD,OAAO;IACT;IAEA,IAAA,MAAW,OAAO,EAAG;QACnB,IAAI,CAAA,CAAE,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,EAAG;YACrB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,SAAS,aAAa,KAAA,EAAgB;IAC3C,OAAO,MAAM,OAAA,CAAQ,KAAK,KAAK,MAAM,MAAA,KAAW,OAAO,IAAA,CAAK,KAAK,EAAE,MAAA;AACrE;AAIO,SAAS,cAAc,CAAA,EAAqB;IACjD,IAAI,CAAC,mBAAmB,CAAC,GAAG;QAC1B,OAAO;IACT;IAGA,MAAM,OAAO,EAAE,WAAA;IACf,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO;IACT;IAGA,MAAM,OAAO,KAAK,SAAA;IAClB,IAAI,CAAC,mBAAmB,IAAI,GAAG;QAC7B,OAAO;IACT;IAGA,IAAI,CAAC,KAAK,cAAA,CAAe,eAAe,GAAG;QACzC,OAAO;IACT;IAGA,IAAI,OAAO,cAAA,CAAe,CAAC,MAAM,OAAO,SAAA,EAAW;QACjD,OAAO;IACT;IAGA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAiB;IAC3C,OAAO,OAAO,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,MAAM,OAAA,EAAgC;IACpD,OAAO,IAAI,QAAQ,CAAC,YAAY;QAC9B,WAAW,SAAS,OAAO;IAC7B,CAAC;AACH;AAEO,SAAS,YAGd,QAAA,EAA6B,IAAA,EAAa,OAAA,EAA0B;IACpE,IAAI,OAAO,QAAQ,iBAAA,KAAsB,YAAY;QACnD,OAAO,QAAQ,iBAAA,CAAkB,UAAU,IAAI;IACjD,OAAA,IAAW,QAAQ,iBAAA,KAAsB,OAAO;QAC9C,IAAI,QAAQ,IAAI,aAAa,WAAc;YACzC,IAAI;gBACF,OAAO,iBAAiB,UAAU,IAAI;YACxC,EAAA,OAAS,OAAO;gBACd,QAAQ,KAAA,CACN,CAAA,uJAAA,EAA0J,QAAQ,SAAS,CAAA,GAAA,EAAM,KAAK,EAAA;gBAIxL,MAAM;YACR;QACF;QAEA,OAAO,iBAAiB,UAAU,IAAI;IACxC;IACA,OAAO;AACT;AAEO,SAAS,iBACd,YAAA,EACe;IACf,OAAO;AACT;AAEO,SAAS,SAAY,KAAA,EAAiB,IAAA,EAAS,MAAM,CAAA,EAAa;IACvE,MAAM,WAAW,CAAC;WAAG;QAAO,IAAI;KAAA;IAChC,OAAO,OAAO,SAAS,MAAA,GAAS,MAAM,SAAS,KAAA,CAAM,CAAC,IAAI;AAC5D;AAEO,SAAS,WAAc,KAAA,EAAiB,IAAA,EAAS,MAAM,CAAA,EAAa;IACzE,MAAM,WAAW;QAAC,MAAM;WAAG,KAAK;KAAA;IAChC,OAAO,OAAO,SAAS,MAAA,GAAS,MAAM,SAAS,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI;AAChE;AAEO,IAAM,YAAY,OAAO;AAGzB,SAAS,cAId,OAAA,EAIA,YAAA,EACwC;IACxC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,QAAQ,OAAA,KAAY,WAAW;YACjC,QAAQ,KAAA,CACN,CAAA,sGAAA,EAAyG,QAAQ,SAAS,CAAA,CAAA,CAAA;QAE9H;IACF;IAKA,IAAI,CAAC,QAAQ,OAAA,IAAW,cAAc,gBAAgB;QACpD,OAAO,IAAM,aAAa,cAAA;IAC5B;IAEA,IAAI,CAAC,QAAQ,OAAA,IAAW,QAAQ,OAAA,KAAY,WAAW;QACrD,OAAO,IACL,QAAQ,MAAA,CAAO,IAAI,MAAM,CAAA,kBAAA,EAAqB,QAAQ,SAAS,CAAA,CAAA,CAAG,CAAC;IACvE;IAEA,OAAO,QAAQ,OAAA;AACjB;AAEO,SAAS,iBACd,YAAA,EACA,MAAA,EACS;IAET,IAAI,OAAO,iBAAiB,YAAY;QACtC,OAAO,aAAa,GAAG,MAAM;IAC/B;IAEA,OAAO,CAAC,CAAC;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/notifyManager.ts"], "sourcesContent": ["// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends Array<unknown>> = (...args: T) => void\n\ntype ScheduleFunction = (callback: () => void) => void\n\nexport const defaultScheduler: ScheduleFunction = (cb) => setTimeout(cb, 0)\n\nexport function createNotifyManager() {\n  let queue: Array<NotifyCallback> = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n  let scheduleFn = defaultScheduler\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  return {\n    batch: <T>(callback: () => T): T => {\n      let result\n      transactions++\n      try {\n        result = callback()\n      } finally {\n        transactions--\n        if (!transactions) {\n          flush()\n        }\n      }\n      return result\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: <T extends Array<unknown>>(\n      callback: BatchCallsCallback<T>,\n    ): BatchCallsCallback<T> => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args)\n        })\n      }\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn: NotifyFunction) => {\n      notifyFn = fn\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn: BatchNotifyFunction) => {\n      batchNotifyFn = fn\n    },\n    setScheduler: (fn: ScheduleFunction) => {\n      scheduleFn = fn\n    },\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n"], "names": [], "mappings": ";;;;;;AAYO,IAAM,mBAAqC,CAAC,KAAO,WAAW,IAAI,CAAC;AAEnE,SAAS,sBAAsB;IACpC,IAAI,QAA+B,CAAC,CAAA;IACpC,IAAI,eAAe;IACnB,IAAI,WAA2B,CAAC,aAAa;QAC3C,SAAS;IACX;IACA,IAAI,gBAAqC,CAAC,aAAyB;QACjE,SAAS;IACX;IACA,IAAI,aAAa;IAEjB,MAAM,WAAW,CAAC,aAAmC;QACnD,IAAI,cAAc;YAChB,MAAM,IAAA,CAAK,QAAQ;QACrB,OAAO;YACL,WAAW,MAAM;gBACf,SAAS,QAAQ;YACnB,CAAC;QACH;IACF;IACA,MAAM,QAAQ,MAAY;QACxB,MAAM,gBAAgB;QACtB,QAAQ,CAAC,CAAA;QACT,IAAI,cAAc,MAAA,EAAQ;YACxB,WAAW,MAAM;gBACf,cAAc,MAAM;oBAClB,cAAc,OAAA,CAAQ,CAAC,aAAa;wBAClC,SAAS,QAAQ;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;QACH;IACF;IAEA,OAAO;QACL,OAAO,CAAI,aAAyB;YAClC,IAAI;YACJ;YACA,IAAI;gBACF,SAAS,SAAS;YACpB,SAAE;gBACA;gBACA,IAAI,CAAC,cAAc;oBACjB,MAAM;gBACR;YACF;YACA,OAAO;QACT;QAAA;;KAAA,GAIA,YAAY,CACV,aAC0B;YAC1B,OAAO,CAAA,GAAI,SAAS;gBAClB,SAAS,MAAM;oBACb,SAAS,GAAG,IAAI;gBAClB,CAAC;YACH;QACF;QACA;QAAA;;;KAAA,GAKA,mBAAmB,CAAC,OAAuB;YACzC,WAAW;QACb;QAAA;;;KAAA,GAKA,wBAAwB,CAAC,OAA4B;YACnD,gBAAgB;QAClB;QACA,cAAc,CAAC,OAAyB;YACtC,aAAa;QACf;IACF;AACF;AAGO,IAAM,gBAAgB,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/subscribable.ts"], "sourcesContent": ["export class Subscribable<TListener extends Function> {\n  protected listeners = new Set<TListener>()\n\n  constructor() {\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    this.listeners.add(listener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(listener)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAM,eAAN,MAA+C;IAGpD,aAAc;QAFd,IAAA,CAAU,SAAA,GAAY,aAAA,GAAA,IAAI,IAAe;QAGvC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAI;IAC3C;IAEA,UAAU,QAAA,EAAiC;QACzC,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,QAAQ;QAE3B,IAAA,CAAK,WAAA,CAAY;QAEjB,OAAO,MAAM;YACX,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,QAAQ;YAC9B,IAAA,CAAK,aAAA,CAAc;QACrB;IACF;IAEA,eAAwB;QACtB,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;IAC/B;IAEU,cAAoB,CAE9B;IAEU,gBAAsB,CAEhC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/focusManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (focused: boolean) => void\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable<Listener> {\n  #focused?: boolean\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibilitychange\n        window.addEventListener('visibilitychange', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.#focused !== focused\n    if (changed) {\n      this.#focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    const isFocused = this.isFocused()\n    this.listeners.forEach((listener) => {\n      listener(isFocused)\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.#focused === 'boolean') {\n      return this.#focused\n    }\n\n    // document global can be unavailable in react native\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    return globalThis.document?.visibilityState !== 'hidden'\n  }\n}\n\nexport const focusManager = new FocusManager()\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB;;;AAQlB,IAAM,eAAN,6LAA2B,eAAA,CAAuB;KACvD,OAAA,CAAA;KACA,OAAA,CAAA;KAEA,KAAA,CAAA;IAEA,aAAc;QACZ,KAAA,CAAM;QACN,IAAA,EAAK,KAAA,GAAS,CAAC,YAAY;YAGzB,IAAI,yKAAC,WAAA,IAAY,OAAO,gBAAA,EAAkB;gBACxC,MAAM,WAAW,IAAM,QAAQ;gBAE/B,OAAO,gBAAA,CAAiB,oBAAoB,UAAU,KAAK;gBAE3D,OAAO,MAAM;oBAEX,OAAO,mBAAA,CAAoB,oBAAoB,QAAQ;gBACzD;YACF;YACA;QACF;IACF;IAEU,cAAoB;QAC5B,IAAI,CAAC,IAAA,EAAK,OAAA,EAAU;YAClB,IAAA,CAAK,gBAAA,CAAiB,IAAA,EAAK,KAAM;QACnC;IACF;IAEU,gBAAgB;QACxB,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,EAAK,OAAA,GAAW;YAChB,IAAA,EAAK,OAAA,GAAW,KAAA;QAClB;IACF;IAEA,iBAAiB,KAAA,EAAsB;QACrC,IAAA,EAAK,KAAA,GAAS;QACd,IAAA,EAAK,OAAA,GAAW;QAChB,IAAA,EAAK,OAAA,GAAW,MAAM,CAAC,YAAY;YACjC,IAAI,OAAO,YAAY,WAAW;gBAChC,IAAA,CAAK,UAAA,CAAW,OAAO;YACzB,OAAO;gBACL,IAAA,CAAK,OAAA,CAAQ;YACf;QACF,CAAC;IACH;IAEA,WAAW,OAAA,EAAyB;QAClC,MAAM,UAAU,IAAA,EAAK,OAAA,KAAa;QAClC,IAAI,SAAS;YACX,IAAA,EAAK,OAAA,GAAW;YAChB,IAAA,CAAK,OAAA,CAAQ;QACf;IACF;IAEA,UAAgB;QACd,MAAM,YAAY,IAAA,CAAK,SAAA,CAAU;QACjC,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;YACnC,SAAS,SAAS;QACpB,CAAC;IACH;IAEA,YAAqB;QACnB,IAAI,OAAO,IAAA,EAAK,OAAA,KAAa,WAAW;YACtC,OAAO,IAAA,EAAK,OAAA;QACd;QAIA,OAAO,WAAW,QAAA,EAAU,oBAAoB;IAClD;AACF;AAEO,IAAM,eAAe,IAAI,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/onlineManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (online: boolean) => void\ntype SetupFn = (setOnline: Listener) => (() => void) | undefined\n\nexport class OnlineManager extends Subscribable<Listener> {\n  #online = true\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true)\n        const offlineListener = () => onOnline(false)\n        // Listen to online\n        window.addEventListener('online', onlineListener, false)\n        window.addEventListener('offline', offlineListener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', onlineListener)\n          window.removeEventListener('offline', offlineListener)\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup(this.setOnline.bind(this))\n  }\n\n  setOnline(online: boolean): void {\n    const changed = this.#online !== online\n\n    if (changed) {\n      this.#online = online\n      this.listeners.forEach((listener) => {\n        listener(online)\n      })\n    }\n  }\n\n  isOnline(): boolean {\n    return this.#online\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB;;;AAKlB,IAAM,gBAAN,6LAA4B,eAAA,CAAuB;KACxD,MAAA,GAAU,KAAA;KACV,OAAA,CAAA;KAEA,KAAA,CAAA;IAEA,aAAc;QACZ,KAAA,CAAM;QACN,IAAA,EAAK,KAAA,GAAS,CAAC,aAAa;YAG1B,IAAI,yKAAC,WAAA,IAAY,OAAO,gBAAA,EAAkB;gBACxC,MAAM,iBAAiB,IAAM,SAAS,IAAI;gBAC1C,MAAM,kBAAkB,IAAM,SAAS,KAAK;gBAE5C,OAAO,gBAAA,CAAiB,UAAU,gBAAgB,KAAK;gBACvD,OAAO,gBAAA,CAAiB,WAAW,iBAAiB,KAAK;gBAEzD,OAAO,MAAM;oBAEX,OAAO,mBAAA,CAAoB,UAAU,cAAc;oBACnD,OAAO,mBAAA,CAAoB,WAAW,eAAe;gBACvD;YACF;YAEA;QACF;IACF;IAEU,cAAoB;QAC5B,IAAI,CAAC,IAAA,EAAK,OAAA,EAAU;YAClB,IAAA,CAAK,gBAAA,CAAiB,IAAA,EAAK,KAAM;QACnC;IACF;IAEU,gBAAgB;QACxB,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,EAAK,OAAA,GAAW;YAChB,IAAA,EAAK,OAAA,GAAW,KAAA;QAClB;IACF;IAEA,iBAAiB,KAAA,EAAsB;QACrC,IAAA,EAAK,KAAA,GAAS;QACd,IAAA,EAAK,OAAA,GAAW;QAChB,IAAA,EAAK,OAAA,GAAW,MAAM,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAI,CAAC;IACjD;IAEA,UAAU,MAAA,EAAuB;QAC/B,MAAM,UAAU,IAAA,EAAK,MAAA,KAAY;QAEjC,IAAI,SAAS;YACX,IAAA,EAAK,MAAA,GAAU;YACf,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,MAAM;YACjB,CAAC;QACH;IACF;IAEA,WAAoB;QAClB,OAAO,IAAA,EAAK,MAAA;IACd;AACF;AAEO,IAAM,gBAAgB,IAAI,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/thenable.ts"], "sourcesContent": ["/**\n * Thenable types which matches <PERSON><PERSON>'s types for promises\n *\n * <PERSON><PERSON> seemingly uses `.status`, `.value` and `.reason` properties on a promises to optimistically unwrap data from promises\n *\n * @see https://github.com/facebook/react/blob/main/packages/shared/ReactTypes.js#L112-L138\n * @see https://github.com/facebook/react/blob/4f604941569d2e8947ce1460a0b2997e835f37b9/packages/react-debug-tools/src/ReactDebugHooks.js#L224-L227\n */\n\nimport { noop } from './utils'\n\ninterface Fulfilled<T> {\n  status: 'fulfilled'\n  value: T\n}\ninterface Rejected {\n  status: 'rejected'\n  reason: unknown\n}\ninterface Pending<T> {\n  status: 'pending'\n\n  /**\n   * Resolve the promise with a value.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  resolve: (value: T) => void\n  /**\n   * Reject the promise with a reason.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  reject: (reason: unknown) => void\n}\n\nexport type FulfilledThenable<T> = Promise<T> & Fulfilled<T>\nexport type RejectedThenable<T> = Promise<T> & Rejected\nexport type PendingThenable<T> = Promise<T> & Pending<T>\n\nexport type Thenable<T> =\n  | FulfilledThenable<T>\n  | RejectedThenable<T>\n  | PendingThenable<T>\n\nexport function pendingThenable<T>(): PendingThenable<T> {\n  let resolve: Pending<T>['resolve']\n  let reject: Pending<T>['reject']\n  // this could use `Promise.withResolvers()` in the future\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve\n    reject = _reject\n  }) as PendingThenable<T>\n\n  thenable.status = 'pending'\n  thenable.catch(() => {\n    // prevent unhandled rejection errors\n  })\n\n  function finalize(data: Fulfilled<T> | Rejected) {\n    Object.assign(thenable, data)\n\n    // clear pending props props to avoid calling them twice\n    delete (thenable as Partial<PendingThenable<T>>).resolve\n    delete (thenable as Partial<PendingThenable<T>>).reject\n  }\n\n  thenable.resolve = (value) => {\n    finalize({\n      status: 'fulfilled',\n      value,\n    })\n\n    resolve(value)\n  }\n  thenable.reject = (reason) => {\n    finalize({\n      status: 'rejected',\n      reason,\n    })\n\n    reject(reason)\n  }\n\n  return thenable\n}\n\n/**\n * This function takes a Promise-like input and detects whether the data\n * is synchronously available or not.\n *\n * It does not inspect .status, .value or .reason properties of the promise,\n * as those are not always available, and the .status of React's promises\n * should not be considered part of the public API.\n */\nexport function tryResolveSync(promise: Promise<unknown> | Thenable<unknown>) {\n  let data: unknown\n\n  promise\n    .then((result) => {\n      data = result\n      return result\n    }, noop)\n    // .catch can be unavailable on certain kinds of thenable's\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    ?.catch(noop)\n\n  if (data !== undefined) {\n    return { data }\n  }\n\n  return undefined\n}\n"], "names": [], "mappings": ";;;;;AASA,SAAS,YAAY;;AAkCd,SAAS,kBAAyC;IACvD,IAAI;IACJ,IAAI;IAEJ,MAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,YAAY;QAClD,UAAU;QACV,SAAS;IACX,CAAC;IAED,SAAS,MAAA,GAAS;IAClB,SAAS,KAAA,CAAM,KAEf,CAFqB,AAEpB;IAED,SAAS,SAAS,IAAA,EAA+B;QAC/C,OAAO,MAAA,CAAO,UAAU,IAAI;QAG5B,OAAQ,SAAyC,OAAA;QACjD,OAAQ,SAAyC,MAAA;IACnD;IAEA,SAAS,OAAA,GAAU,CAAC,UAAU;QAC5B,SAAS;YACP,QAAQ;YACR;QACF,CAAC;QAED,QAAQ,KAAK;IACf;IACA,SAAS,MAAA,GAAS,CAAC,WAAW;QAC5B,SAAS;YACP,QAAQ;YACR;QACF,CAAC;QAED,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAUO,SAAS,eAAe,OAAA,EAA+C;IAC5E,IAAI;IAEJ,QACG,IAAA,CAAK,CAAC,WAAW;QAChB,OAAO;QACP,OAAO;IACT,2KAAG,OAAI,GAGL,8KAAM,OAAI;IAEd,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO;YAAE;QAAK;IAChB;IAEA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/retryer.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { pendingThenable } from './thenable'\nimport { isServer, sleep } from './utils'\nimport type { CancelOptions, DefaultError, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = DefaultError> {\n  fn: () => TData | Promise<TData>\n  initialPromise?: Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n  canRun: () => boolean\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n  canStart: () => boolean\n  start: () => Promise<TData>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError extends Error {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    super('CancelledError')\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = DefaultError>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => void) | undefined\n\n  const thenable = pendingThenable<TData>()\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const canContinue = () =>\n    focusManager.isFocused() &&\n    (config.networkMode === 'always' || onlineManager.isOnline()) &&\n    config.canRun()\n\n  const canStart = () => canFetch(config.networkMode) && config.canRun()\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      thenable.resolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      thenable.reject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value)\n        }\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // we can re-use config.initialPromise on the first call of run()\n    const initialPromise =\n      failureCount === 0 ? config.initialPromise : undefined\n\n    // Execute query\n    try {\n      promiseOrValue = initialPromise ?? config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? (isServer ? 0 : 3)\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            return canContinue() ? undefined : pause()\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.()\n      return thenable\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      // Start loop\n      if (canStart()) {\n        run()\n      } else {\n        pause().then(run)\n      }\n      return thenable\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAChC,SAAS,UAAU,aAAa;;;;;AA4ChC,SAAS,kBAAkB,YAAA,EAAsB;IAC/C,OAAO,KAAK,GAAA,CAAI,MAAO,KAAK,cAAc,GAAK;AACjD;AAEO,SAAS,SAAS,WAAA,EAA+C;IACtE,OAAA,CAAQ,eAAe,QAAA,MAAc,2LACjC,gBAAA,CAAc,QAAA,CAAS,IACvB;AACN;AAEO,IAAM,iBAAN,cAA6B,MAAM;IAGxC,YAAY,OAAA,CAAyB;QACnC,KAAA,CAAM,gBAAgB;QACtB,IAAA,CAAK,MAAA,GAAS,SAAS;QACvB,IAAA,CAAK,MAAA,GAAS,SAAS;IACzB;AACF;AAEO,SAAS,iBAAiB,KAAA,EAAqC;IACpE,OAAO,iBAAiB;AAC1B;AAEO,SAAS,cACd,MAAA,EACgB;IAChB,IAAI,mBAAmB;IACvB,IAAI,eAAe;IACnB,IAAI,aAAa;IACjB,IAAI;IAEJ,MAAM,0LAAW,kBAAA,CAAuB;IAExC,MAAM,SAAS,CAAC,kBAAwC;QACtD,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,eAAe,aAAa,CAAC;YAExC,OAAO,KAAA,GAAQ;QACjB;IACF;IACA,MAAM,cAAc,MAAM;QACxB,mBAAmB;IACrB;IAEA,MAAM,gBAAgB,MAAM;QAC1B,mBAAmB;IACrB;IAEA,MAAM,cAAc,IAClB,8LAAA,CAAa,SAAA,CAAU,KAAA,CACtB,OAAO,WAAA,KAAgB,4LAAY,gBAAA,CAAc,QAAA,CAAS,CAAA,KAC3D,OAAO,MAAA,CAAO;IAEhB,MAAM,WAAW,IAAM,SAAS,OAAO,WAAW,KAAK,OAAO,MAAA,CAAO;IAErE,MAAM,UAAU,CAAC,UAAe;QAC9B,IAAI,CAAC,YAAY;YACf,aAAa;YACb,OAAO,SAAA,GAAY,KAAK;YACxB,aAAa;YACb,SAAS,OAAA,CAAQ,KAAK;QACxB;IACF;IAEA,MAAM,SAAS,CAAC,UAAe;QAC7B,IAAI,CAAC,YAAY;YACf,aAAa;YACb,OAAO,OAAA,GAAU,KAAK;YACtB,aAAa;YACb,SAAS,MAAA,CAAO,KAAK;QACvB;IACF;IAEA,MAAM,QAAQ,MAAM;QAClB,OAAO,IAAI,QAAQ,CAAC,oBAAoB;YACtC,aAAa,CAAC,UAAU;gBACtB,IAAI,cAAc,YAAY,GAAG;oBAC/B,gBAAgB,KAAK;gBACvB;YACF;YACA,OAAO,OAAA,GAAU;QACnB,CAAC,EAAE,IAAA,CAAK,MAAM;YACZ,aAAa,KAAA;YACb,IAAI,CAAC,YAAY;gBACf,OAAO,UAAA,GAAa;YACtB;QACF,CAAC;IACH;IAGA,MAAM,MAAM,MAAM;QAEhB,IAAI,YAAY;YACd;QACF;QAEA,IAAI;QAGJ,MAAM,iBACJ,iBAAiB,IAAI,OAAO,cAAA,GAAiB,KAAA;QAG/C,IAAI;YACF,iBAAiB,kBAAkB,OAAO,EAAA,CAAG;QAC/C,EAAA,OAAS,OAAO;YACd,iBAAiB,QAAQ,MAAA,CAAO,KAAK;QACvC;QAEA,QAAQ,OAAA,CAAQ,cAAc,EAC3B,IAAA,CAAK,OAAO,EACZ,KAAA,CAAM,CAAC,UAAU;YAEhB,IAAI,YAAY;gBACd;YACF;YAGA,MAAM,QAAQ,OAAO,KAAA,IAAA,yKAAU,WAAA,GAAW,IAAI,CAAA;YAC9C,MAAM,aAAa,OAAO,UAAA,IAAc;YACxC,MAAM,QACJ,OAAO,eAAe,aAClB,WAAW,cAAc,KAAK,IAC9B;YACN,MAAM,cACJ,UAAU,QACT,OAAO,UAAU,YAAY,eAAe,SAC5C,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK;YAE3D,IAAI,oBAAoB,CAAC,aAAa;gBAEpC,OAAO,KAAK;gBACZ;YACF;YAEA;YAGA,OAAO,MAAA,GAAS,cAAc,KAAK;YAGnC,CAAA,GAAA,uKAAA,CAAA,QAAA,EAAM,KAAK,EAER,IAAA,CAAK,MAAM;gBACV,OAAO,YAAY,IAAI,KAAA,IAAY,MAAM;YAC3C,CAAC,EACA,IAAA,CAAK,MAAM;gBACV,IAAI,kBAAkB;oBACpB,OAAO,KAAK;gBACd,OAAO;oBACL,IAAI;gBACN;YACF,CAAC;QACL,CAAC;IACL;IAEA,OAAO;QACL,SAAS;QACT;QACA,UAAU,MAAM;YACd,aAAa;YACb,OAAO;QACT;QACA;QACA;QACA;QACA,OAAO,MAAM;YAEX,IAAI,SAAS,GAAG;gBACd,IAAI;YACN,OAAO;gBACL,MAAM,EAAE,IAAA,CAAK,GAAG;YAClB;YACA,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/removable.ts"], "sourcesContent": ["import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  gcTime!: number\n  #gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.gcTime)\n    }\n  }\n\n  protected updateGcTime(newGcTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no gcTime is set\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout)\n      this.#gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,UAAU,sBAAsB;;AAElC,IAAe,YAAf,MAAyB;KAE9B,SAAA,CAAA;IAEA,UAAgB;QACd,IAAA,CAAK,cAAA,CAAe;IACtB;IAEU,aAAmB;QAC3B,IAAA,CAAK,cAAA,CAAe;QAEpB,gLAAI,iBAAA,EAAe,IAAA,CAAK,MAAM,GAAG;YAC/B,IAAA,EAAK,SAAA,GAAa,WAAW,MAAM;gBACjC,IAAA,CAAK,cAAA,CAAe;YACtB,GAAG,IAAA,CAAK,MAAM;QAChB;IACF;IAEU,aAAa,SAAA,EAAqC;QAE1D,IAAA,CAAK,MAAA,GAAS,KAAK,GAAA,CACjB,IAAA,CAAK,MAAA,IAAU,GACf,aAAA,yKAAc,WAAA,GAAW,WAAW,IAAI,KAAK,GAAA;IAEjD;IAEU,iBAAiB;QACzB,IAAI,IAAA,EAAK,SAAA,EAAY;YACnB,aAAa,IAAA,EAAK,SAAU;YAC5B,IAAA,EAAK,SAAA,GAAa,KAAA;QACpB;IACF;AAGF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/query.ts"], "sourcesContent": ["import {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type { QueryCache } from './queryCache'\nimport type { QueryClient } from './queryClient'\nimport type {\n  CancelOptions,\n  DefaultError,\n  FetchStatus,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n  StaleTime,\n} from './types'\nimport type { QueryObserver } from './queryObserver'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  client: QueryClient\n  queryKey: TQ<PERSON>y<PERSON>ey\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = DefaultError> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: FetchMeta | null\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  client: QueryClient\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n    query: Query,\n  ) => void\n}\n\nexport type FetchDirection = 'forward' | 'backward'\n\nexport interface FetchMeta {\n  fetchMore?: { direction: FetchDirection }\n}\n\nexport interface FetchOptions<TData = unknown> {\n  cancelRefetch?: boolean\n  meta?: FetchMeta\n  initialPromise?: Promise<TData>\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: FetchMeta\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state: QueryState<TData, TError>\n\n  #initialState: QueryState<TData, TError>\n  #revertState?: QueryState<TData, TError>\n  #cache: QueryCache\n  #client: QueryClient\n  #retryer?: Retryer<TData>\n  observers: Array<QueryObserver<any, any, any, any, any>>\n  #defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  #abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.#abortSignalConsumed = false\n    this.#defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.#client = config.client\n    this.#cache = this.#client.getQueryCache()\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.#initialState = getDefaultState(this.options)\n    this.state = config.state ?? this.#initialState\n    this.scheduleGc()\n  }\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  get promise(): Promise<TData> | undefined {\n    return this.#retryer?.promise\n  }\n\n  setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.#cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.#dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.#dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.#retryer?.promise\n    this.#retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.#initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false,\n    )\n  }\n\n  isDisabled(): boolean {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive()\n    }\n    // if a query has no observers, it should still be considered disabled if it never attempted a fetch\n    return (\n      this.options.queryFn === skipToken ||\n      this.state.dataUpdateCount + this.state.errorUpdateCount === 0\n    )\n  }\n\n  isStatic(): boolean {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) =>\n          resolveStaleTime(observer.options.staleTime, this) === 'static',\n      )\n    }\n\n    return false\n  }\n\n  isStale(): boolean {\n    // check observers first, their `isStale` has the source of truth\n    // calculated with `isStaleByTime` and it takes `enabled` into account\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale,\n      )\n    }\n\n    return this.state.data === undefined || this.state.isInvalidated\n  }\n\n  isStaleByTime(staleTime: StaleTime = 0): boolean {\n    // no data is always stale\n    if (this.state.data === undefined) {\n      return true\n    }\n    // static is never stale\n    if (staleTime === 'static') {\n      return false\n    }\n    // if the query is invalidated, it is stale\n    if (this.state.isInvalidated) {\n      return true\n    }\n\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.#cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true })\n          } else {\n            this.#retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.#cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions<TQueryFnData>,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.data !== undefined && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetch\n        this.cancel({ silent: true })\n      } else if (this.#retryer) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.#retryer.continueRetry()\n        // Return current promise if we are already fetching\n        return this.#retryer.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = new AbortController()\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true\n          return abortController.signal\n        },\n      })\n    }\n\n    // Create fetch function\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions)\n\n      // Create query function context\n      const createQueryFnContext = (): QueryFunctionContext<TQueryKey> => {\n        const queryFnContext: OmitKeyof<\n          QueryFunctionContext<TQueryKey>,\n          'signal'\n        > = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta,\n        }\n        addSignalProperty(queryFnContext)\n        return queryFnContext as QueryFunctionContext<TQueryKey>\n      }\n\n      const queryFnContext = createQueryFnContext()\n\n      this.#abortSignalConsumed = false\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this as unknown as Query,\n        )\n      }\n\n      return queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const createFetchContext = (): FetchContext<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey\n    > => {\n      const context: OmitKeyof<\n        FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n        'signal'\n      > = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn,\n      }\n\n      addSignalProperty(context)\n      return context as FetchContext<TQueryFnData, TError, TData, TQueryKey>\n    }\n\n    const context = createFetchContext()\n\n    this.options.behavior?.onFetch(context, this as unknown as Query)\n\n    // Store state in case the current fetch needs to be reverted\n    this.#revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.#dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.#cache.config.onError?.(\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n      }\n\n      // Schedule query gc after fetching\n      this.scheduleGc()\n    }\n\n    // Try to fetch the data\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise as\n        | Promise<TData>\n        | undefined,\n      fn: context.fetchFn as () => Promise<TData>,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === undefined) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        try {\n          this.setData(data)\n        } catch (error) {\n          onError(error as TError)\n          return\n        }\n\n        // Notify cache callback\n        this.#cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error as any,\n          this as Query<any, any, any, any>,\n        )\n\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true,\n    })\n\n    return this.#retryer.start()\n  }\n\n  #dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null,\n          }\n        case 'success':\n          // If fetching ends successfully, we don't need revertState as a fallback anymore.\n          this.#revertState = undefined\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error\n\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate()\n      })\n\n      this.#cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nexport function fetchState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  data: TData | undefined,\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? 'fetching' : 'paused',\n    ...(data === undefined &&\n      ({\n        error: null,\n        status: 'pending',\n      } as const)),\n  } as const\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = data !== undefined\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? (initialDataUpdatedAt ?? Date.now()) : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'pending',\n    fetchStatus: 'idle',\n  }\n}\n"], "names": ["queryFnContext", "context"], "mappings": ";;;;;AAAA;AASA,SAAS,qBAAqB;AAC9B,SAAS,UAAU,eAAe,wBAAwB;AAC1D,SAAS,iBAAiB;;;;;AAmJnB,IAAM,QAAN,0LAKG,YAAA,CAAU;KAMlB,YAAA,CAAA;KACA,WAAA,CAAA;KACA,KAAA,CAAA;KACA,MAAA,CAAA;KACA,OAAA,CAAA;IAEA,eAAA,CAAA;KACA,mBAAA,CAAA;IAEA,YAAY,MAAA,CAA6D;QACvE,KAAA,CAAM;QAEN,IAAA,EAAK,mBAAA,GAAuB;QAC5B,IAAA,EAAK,cAAA,GAAkB,OAAO,cAAA;QAC9B,IAAA,CAAK,UAAA,CAAW,OAAO,OAAO;QAC9B,IAAA,CAAK,SAAA,GAAY,CAAC,CAAA;QAClB,IAAA,EAAK,MAAA,GAAU,OAAO,MAAA;QACtB,IAAA,CAAK,MAAA,GAAS,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc;QACzC,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACvB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,EAAK,YAAA,GAAgB,gBAAgB,IAAA,CAAK,OAAO;QACjD,IAAA,CAAK,KAAA,GAAQ,OAAO,KAAA,IAAS,IAAA,CAAK,aAAA;QAClC,IAAA,CAAK,UAAA,CAAW;IAClB;IACA,IAAI,OAA8B;QAChC,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;IACtB;IAEA,IAAI,UAAsC;QACxC,OAAO,IAAA,EAAK,OAAA,EAAU;IACxB;IAEA,WACE,OAAA,EACM;QACN,IAAA,CAAK,OAAA,GAAU;YAAE,GAAG,IAAA,EAAK,cAAA;YAAiB,GAAG,OAAA;QAAQ;QAErD,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,MAAM;IACvC;IAEU,iBAAiB;QACzB,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,MAAA,IAAU,IAAA,CAAK,KAAA,CAAM,WAAA,KAAgB,QAAQ;YAC/D,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,IAAI;QACzB;IACF;IAEA,QACE,OAAA,EACA,OAAA,EACO;QACP,MAAM,OAAO,0LAAA,EAAY,IAAA,CAAK,KAAA,CAAM,IAAA,EAAM,SAAS,IAAA,CAAK,OAAO;QAG/D,IAAA,EAAK,QAAA,CAAU;YACb;YACA,MAAM;YACN,eAAe,SAAS;YACxB,QAAQ,SAAS;QACnB,CAAC;QAED,OAAO;IACT;IAEA,SACE,KAAA,EACA,eAAA,EACM;QACN,IAAA,EAAK,QAAA,CAAU;YAAE,MAAM;YAAY;YAAO;QAAgB,CAAC;IAC7D;IAEA,OAAO,OAAA,EAAwC;QAC7C,MAAM,UAAU,IAAA,EAAK,OAAA,EAAU;QAC/B,IAAA,EAAK,OAAA,EAAU,OAAO,OAAO;QAC7B,OAAO,UAAU,QAAQ,IAAA,yKAAK,OAAI,EAAE,KAAA,yKAAM,OAAI,IAAI,QAAQ,OAAA,CAAQ;IACpE;IAEA,UAAgB;QACd,KAAA,CAAM,QAAQ;QAEd,IAAA,CAAK,MAAA,CAAO;YAAE,QAAQ;QAAK,CAAC;IAC9B;IAEA,QAAc;QACZ,IAAA,CAAK,OAAA,CAAQ;QACb,IAAA,CAAK,QAAA,CAAS,IAAA,EAAK,YAAa;IAClC;IAEA,WAAoB;QAClB,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,CACpB,CAAC,WAAa,6LAAA,EAAe,SAAS,OAAA,CAAQ,OAAA,EAAS,IAAI,MAAM;IAErE;IAEA,aAAsB;QACpB,IAAI,IAAA,CAAK,iBAAA,CAAkB,IAAI,GAAG;YAChC,OAAO,CAAC,IAAA,CAAK,QAAA,CAAS;QACxB;QAEA,OACE,IAAA,CAAK,OAAA,CAAQ,OAAA,6KAAY,YAAA,IACzB,IAAA,CAAK,KAAA,CAAM,eAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,gBAAA,KAAqB;IAEjE;IAEA,WAAoB;QAClB,IAAI,IAAA,CAAK,iBAAA,CAAkB,IAAI,GAAG;YAChC,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,CACpB,CAAC,uLACC,mBAAA,EAAiB,SAAS,OAAA,CAAQ,SAAA,EAAW,IAAI,MAAM;QAE7D;QAEA,OAAO;IACT;IAEA,UAAmB;QAGjB,IAAI,IAAA,CAAK,iBAAA,CAAkB,IAAI,GAAG;YAChC,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,CACpB,CAAC,WAAa,SAAS,gBAAA,CAAiB,EAAE,OAAA;QAE9C;QAEA,OAAO,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,KAAA,KAAa,IAAA,CAAK,KAAA,CAAM,aAAA;IACrD;IAEA,cAAc,YAAuB,CAAA,EAAY;QAE/C,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,KAAA,GAAW;YACjC,OAAO;QACT;QAEA,IAAI,cAAc,UAAU;YAC1B,OAAO;QACT;QAEA,IAAI,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;YAC5B,OAAO;QACT;QAEA,OAAO,KAAC,yLAAA,EAAe,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe,SAAS;IAC5D;IAEA,UAAgB;QACd,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,CAAC,IAAM,EAAE,wBAAA,CAAyB,CAAC;QAExE,UAAU,QAAQ;YAAE,eAAe;QAAM,CAAC;QAG1C,IAAA,EAAK,OAAA,EAAU,SAAS;IAC1B;IAEA,WAAiB;QACf,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,CAAC,IAAM,EAAE,sBAAA,CAAuB,CAAC;QAEtE,UAAU,QAAQ;YAAE,eAAe;QAAM,CAAC;QAG1C,IAAA,EAAK,OAAA,EAAU,SAAS;IAC1B;IAEA,YAAY,QAAA,EAAwD;QAClE,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,QAAQ,GAAG;YACtC,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,QAAQ;YAG5B,IAAA,CAAK,cAAA,CAAe;YAEpB,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO;gBAAE,MAAM;gBAAiB,OAAO,IAAA;gBAAM;YAAS,CAAC;QACrE;IACF;IAEA,eAAe,QAAA,EAAwD;QACrE,IAAI,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,QAAQ,GAAG;YACrC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,CAAC,IAAM,MAAM,QAAQ;YAE5D,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,MAAA,EAAQ;gBAG1B,IAAI,IAAA,EAAK,OAAA,EAAU;oBACjB,IAAI,IAAA,EAAK,mBAAA,EAAsB;wBAC7B,IAAA,EAAK,OAAA,CAAS,MAAA,CAAO;4BAAE,QAAQ;wBAAK,CAAC;oBACvC,OAAO;wBACL,IAAA,EAAK,OAAA,CAAS,WAAA,CAAY;oBAC5B;gBACF;gBAEA,IAAA,CAAK,UAAA,CAAW;YAClB;YAEA,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO;gBAAE,MAAM;gBAAmB,OAAO,IAAA;gBAAM;YAAS,CAAC;QACvE;IACF;IAEA,oBAA4B;QAC1B,OAAO,IAAA,CAAK,SAAA,CAAU,MAAA;IACxB;IAEA,aAAmB;QACjB,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;YAC7B,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;YAAa,CAAC;QACvC;IACF;IAEA,MACE,OAAA,EACA,YAAA,EACgB;QAChB,IAAI,IAAA,CAAK,KAAA,CAAM,WAAA,KAAgB,QAAQ;YACrC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,KAAA,KAAa,cAAc,eAAe;gBAEhE,IAAA,CAAK,MAAA,CAAO;oBAAE,QAAQ;gBAAK,CAAC;YAC9B,OAAA,IAAW,IAAA,EAAK,OAAA,EAAU;gBAExB,IAAA,CAAK,QAAA,CAAS,aAAA,CAAc;gBAE5B,OAAO,IAAA,EAAK,OAAA,CAAS,OAAA;YACvB;QACF;QAGA,IAAI,SAAS;YACX,IAAA,CAAK,UAAA,CAAW,OAAO;QACzB;QAIA,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;YACzB,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,CAAC,IAAM,EAAE,OAAA,CAAQ,OAAO;YAC7D,IAAI,UAAU;gBACZ,IAAA,CAAK,UAAA,CAAW,SAAS,OAAO;YAClC;QACF;QAEA,IAAI,QAAQ,IAAI,aAAa,WAAc;YACzC,IAAI,CAAC,MAAM,OAAA,CAAQ,IAAA,CAAK,OAAA,CAAQ,QAAQ,GAAG;gBACzC,QAAQ,KAAA,CACN,CAAA,mIAAA,CAAA;YAEJ;QACF;QAEA,MAAM,kBAAkB,IAAI,gBAAgB;QAK5C,MAAM,oBAAoB,CAAC,WAAoB;YAC7C,OAAO,cAAA,CAAe,QAAQ,UAAU;gBACtC,YAAY;gBACZ,KAAK,MAAM;oBACT,IAAA,EAAK,mBAAA,GAAuB;oBAC5B,OAAO,gBAAgB,MAAA;gBACzB;YACF,CAAC;QACH;QAGA,MAAM,UAAU,MAAM;YACpB,MAAM,WAAU,2LAAA,EAAc,IAAA,CAAK,OAAA,EAAS,YAAY;YAGxD,MAAM,uBAAuB,MAAuC;gBAClE,MAAMA,kBAGF;oBACF,QAAQ,IAAA,CAAK,OAAA;oBACb,UAAU,IAAA,CAAK,QAAA;oBACf,MAAM,IAAA,CAAK,IAAA;gBACb;gBACA,kBAAkBA,eAAc;gBAChC,OAAOA;YACT;YAEA,MAAM,iBAAiB,qBAAqB;YAE5C,IAAA,CAAK,oBAAA,GAAuB;YAC5B,IAAI,IAAA,CAAK,OAAA,CAAQ,SAAA,EAAW;gBAC1B,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAA,CAClB,SACA,gBACA,IAAA;YAEJ;YAEA,OAAO,QAAQ,cAAc;QAC/B;QAGA,MAAM,qBAAqB,MAKtB;YACH,MAAMC,WAGF;gBACF;gBACA,SAAS,IAAA,CAAK,OAAA;gBACd,UAAU,IAAA,CAAK,QAAA;gBACf,QAAQ,IAAA,EAAK,MAAA;gBACb,OAAO,IAAA,CAAK,KAAA;gBACZ;YACF;YAEA,kBAAkBA,QAAO;YACzB,OAAOA;QACT;QAEA,MAAM,UAAU,mBAAmB;QAEnC,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,QAAQ,SAAS,IAAwB;QAGhE,IAAA,EAAK,WAAA,GAAe,IAAA,CAAK,KAAA;QAGzB,IACE,IAAA,CAAK,KAAA,CAAM,WAAA,KAAgB,UAC3B,IAAA,CAAK,KAAA,CAAM,SAAA,KAAc,QAAQ,YAAA,EAAc,MAC/C;YACA,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;gBAAS,MAAM,QAAQ,YAAA,EAAc;YAAK,CAAC;QACpE;QAEA,MAAM,UAAU,CAAC,UAAyC;YAExD,IAAI,CAAA,KAAE,6LAAA,EAAiB,KAAK,KAAK,MAAM,MAAA,GAAS;gBAC9C,IAAA,EAAK,QAAA,CAAU;oBACb,MAAM;oBACN;gBACF,CAAC;YACH;YAEA,IAAI,+KAAC,mBAAA,EAAiB,KAAK,GAAG;gBAE5B,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,OAAA,GACjB,OACA,IAAA;gBAEF,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,SAAA,GACjB,IAAA,CAAK,KAAA,CAAM,IAAA,EACX,OACA,IAAA;YAEJ;YAGA,IAAA,CAAK,UAAA,CAAW;QAClB;QAGA,IAAA,EAAK,OAAA,iLAAW,gBAAA,EAAc;YAC5B,gBAAgB,cAAc;YAG9B,IAAI,QAAQ,OAAA;YACZ,OAAO,gBAAgB,KAAA,CAAM,IAAA,CAAK,eAAe;YACjD,WAAW,CAAC,SAAS;gBACnB,IAAI,SAAS,KAAA,GAAW;oBACtB,IAAI,QAAQ,IAAI,aAAa,WAAc;wBACzC,QAAQ,KAAA,CACN,CAAA,sIAAA,EAAyI,IAAA,CAAK,SAAS,EAAA;oBAE3J;oBACA,QAAQ,IAAI,MAAM,GAAG,IAAA,CAAK,SAAS,CAAA,kBAAA,CAAoB,CAAQ;oBAC/D;gBACF;gBAEA,IAAI;oBACF,IAAA,CAAK,OAAA,CAAQ,IAAI;gBACnB,EAAA,OAAS,OAAO;oBACd,QAAQ,KAAe;oBACvB;gBACF;gBAGA,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,SAAA,GAAY,MAAM,IAAiC;gBACtE,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,SAAA,GACjB,MACA,IAAA,CAAK,KAAA,CAAM,KAAA,EACX,IAAA;gBAIF,IAAA,CAAK,UAAA,CAAW;YAClB;YACA;YACA,QAAQ,CAAC,cAAc,UAAU;gBAC/B,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAU;oBAAc;gBAAM,CAAC;YACxD;YACA,SAAS,MAAM;gBACb,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;gBAAQ,CAAC;YAClC;YACA,YAAY,MAAM;gBAChB,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;gBAAW,CAAC;YACrC;YACA,OAAO,QAAQ,OAAA,CAAQ,KAAA;YACvB,YAAY,QAAQ,OAAA,CAAQ,UAAA;YAC5B,aAAa,QAAQ,OAAA,CAAQ,WAAA;YAC7B,QAAQ,IAAM;QAChB,CAAC;QAED,OAAO,IAAA,EAAK,OAAA,CAAS,KAAA,CAAM;IAC7B;KAEA,QAAA,CAAU,MAAA,EAAqC;QAC7C,MAAM,UAAU,CACd,UAC8B;YAC9B,OAAQ,OAAO,IAAA,EAAM;gBACnB,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,mBAAmB,OAAO,YAAA;wBAC1B,oBAAoB,OAAO,KAAA;oBAC7B;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,aAAa;oBACf;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,aAAa;oBACf;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,GAAG,WAAW,MAAM,IAAA,EAAM,IAAA,CAAK,OAAO,CAAA;wBACtC,WAAW,OAAO,IAAA,IAAQ;oBAC5B;gBACF,KAAK;oBAEH,IAAA,EAAK,WAAA,GAAe,KAAA;oBACpB,OAAO;wBACL,GAAG,KAAA;wBACH,MAAM,OAAO,IAAA;wBACb,iBAAiB,MAAM,eAAA,GAAkB;wBACzC,eAAe,OAAO,aAAA,IAAiB,KAAK,GAAA,CAAI;wBAChD,OAAO;wBACP,eAAe;wBACf,QAAQ;wBACR,GAAI,CAAC,OAAO,MAAA,IAAU;4BACpB,aAAa;4BACb,mBAAmB;4BACnB,oBAAoB;wBACtB,CAAA;oBACF;gBACF,KAAK;oBACH,MAAM,QAAQ,OAAO,KAAA;oBAErB,kLAAI,mBAAA,EAAiB,KAAK,KAAK,MAAM,MAAA,IAAU,IAAA,CAAK,YAAA,EAAc;wBAChE,OAAO;4BAAE,GAAG,IAAA,EAAK,WAAA;4BAAc,aAAa;wBAAO;oBACrD;oBAEA,OAAO;wBACL,GAAG,KAAA;wBACH;wBACA,kBAAkB,MAAM,gBAAA,GAAmB;wBAC3C,gBAAgB,KAAK,GAAA,CAAI;wBACzB,mBAAmB,MAAM,iBAAA,GAAoB;wBAC7C,oBAAoB;wBACpB,aAAa;wBACb,QAAQ;oBACV;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,eAAe;oBACjB;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,GAAG,OAAO,KAAA;oBACZ;YACJ;QACF;QAEA,IAAA,CAAK,KAAA,GAAQ,QAAQ,IAAA,CAAK,KAAK;QAE/B,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,aAAA,CAAc;YACzB,CAAC;YAED,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO;gBAAE,OAAO,IAAA;gBAAM,MAAM;gBAAW;YAAO,CAAC;QAC7D,CAAC;IACH;AACF;AAEO,SAAS,WAMd,IAAA,EACA,OAAA,EACA;IACA,OAAO;QACL,mBAAmB;QACnB,oBAAoB;QACpB,2LAAa,WAAA,EAAS,QAAQ,WAAW,IAAI,aAAa;QAC1D,GAAI,SAAS,KAAA,KACV;YACC,OAAO;YACP,QAAQ;QACV,CAAA;IACJ;AACF;AAEA,SAAS,gBAMP,OAAA,EAC2B;IAC3B,MAAM,OACJ,OAAO,QAAQ,WAAA,KAAgB,aAC1B,QAAQ,WAAA,CAA2C,IACpD,QAAQ,WAAA;IAEd,MAAM,UAAU,SAAS,KAAA;IAEzB,MAAM,uBAAuB,UACzB,OAAO,QAAQ,oBAAA,KAAyB,aACrC,QAAQ,oBAAA,CAAkD,IAC3D,QAAQ,oBAAA,GACV;IAEJ,OAAO;QACL;QACA,iBAAiB;QACjB,eAAe,UAAW,wBAAwB,KAAK,GAAA,CAAI,IAAK;QAChE,OAAO;QACP,kBAAkB;QAClB,gBAAgB;QAChB,mBAAmB;QACnB,oBAAoB;QACpB,WAAW;QACX,eAAe;QACf,QAAQ,UAAU,YAAY;QAC9B,aAAa;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/queryCache.ts"], "sourcesContent": ["import { hashQueryKeyByOptions, matchQuery } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type {\n  DefaultError,\n  NotifyEvent,\n  QueryKey,\n  QueryOptions,\n  WithRequired,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (\n    error: DefaultError,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\nexport interface QueryStore {\n  has: (queryHash: string) => boolean\n  set: (queryHash: string, query: Query) => void\n  get: (queryHash: string) => Query | undefined\n  delete: (queryHash: string) => void\n  values: () => IterableIterator<Query>\n}\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  #queries: QueryStore\n\n  constructor(public config: QueryCacheConfig = {}) {\n    super()\n    this.#queries = new Map<string, Query>()\n  }\n\n  build<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    client: QueryClient,\n    options: WithRequired<\n      QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query)\n\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.#queries.get(query.queryHash)\n\n    if (queryInMap) {\n      query.destroy()\n\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash)\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.#queries.get(queryHash) as\n      | Query<TQueryFnData, TError, TData, TQueryKey>\n      | undefined\n  }\n\n  getAll(): Array<Query> {\n    return [...this.#queries.values()]\n  }\n\n  find<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData>(\n    filters: WithRequired<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((query) =>\n      matchQuery(defaultedFilters, query),\n    ) as Query<TQueryFnData, TError, TData> | undefined\n  }\n\n  findAll(filters: QueryFilters<any> = {}): Array<Query> {\n    const queries = this.getAll()\n    return Object.keys(filters).length > 0\n      ? queries.filter((query) => matchQuery(filters, query))\n      : queries\n  }\n\n  notify(event: QueryCacheNotifyEvent): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAuB,kBAAkB;AAClD,SAAS,aAAa;AACtB,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;;;;;AAwFtB,IAAM,aAAN,6LAAyB,eAAA,CAAiC;IAG/D,YAAmB,SAA2B,CAAC,CAAA,CAAG;QAChD,KAAA,CAAM;QADW,IAAA,CAAA,MAAA,GAAA;QAEjB,IAAA,EAAK,OAAA,GAAW,aAAA,GAAA,IAAI,IAAmB;IACzC;KALA,OAAA,CAAA;IAOA,MAME,MAAA,EACA,OAAA,EAIA,KAAA,EAC+C;QAC/C,MAAM,WAAW,QAAQ,QAAA;QACzB,MAAM,YACJ,QAAQ,SAAA,gLAAa,wBAAA,EAAsB,UAAU,OAAO;QAC9D,IAAI,QAAQ,IAAA,CAAK,GAAA,CAA4C,SAAS;QAEtE,IAAI,CAAC,OAAO;YACV,QAAQ,4KAAI,QAAA,CAAM;gBAChB;gBACA;gBACA;gBACA,SAAS,OAAO,mBAAA,CAAoB,OAAO;gBAC3C;gBACA,gBAAgB,OAAO,gBAAA,CAAiB,QAAQ;YAClD,CAAC;YACD,IAAA,CAAK,GAAA,CAAI,KAAK;QAChB;QAEA,OAAO;IACT;IAEA,IAAI,KAAA,EAAwC;QAC1C,IAAI,CAAC,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,MAAM,SAAS,GAAG;YACvC,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,MAAM,SAAA,EAAW,KAAK;YAExC,IAAA,CAAK,MAAA,CAAO;gBACV,MAAM;gBACN;YACF,CAAC;QACH;IACF;IAEA,OAAO,KAAA,EAAwC;QAC7C,MAAM,aAAa,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,MAAM,SAAS;QAEpD,IAAI,YAAY;YACd,MAAM,OAAA,CAAQ;YAEd,IAAI,eAAe,OAAO;gBACxB,IAAA,EAAK,OAAA,CAAS,MAAA,CAAO,MAAM,SAAS;YACtC;YAEA,IAAA,CAAK,MAAA,CAAO;gBAAE,MAAM;gBAAW;YAAM,CAAC;QACxC;IACF;IAEA,QAAc;QACZ,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,MAAA,CAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC/B,IAAA,CAAK,MAAA,CAAO,KAAK;YACnB,CAAC;QACH,CAAC;IACH;IAEA,IAME,SAAA,EAC2D;QAC3D,OAAO,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,SAAS;IAGpC;IAEA,SAAuB;QACrB,OAAO,CAAC;eAAG,IAAA,EAAK,OAAA,CAAS,MAAA,CAAO,CAAC;SAAA;IACnC;IAEA,KACE,OAAA,EACgD;QAChD,MAAM,mBAAmB;YAAE,OAAO;YAAM,GAAG,OAAA;QAAQ;QAEnD,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,IAAA,CAAK,CAAC,oLACzB,aAAA,EAAW,kBAAkB,KAAK;IAEtC;IAEA,QAAQ,UAA6B,CAAC,CAAA,EAAiB;QACrD,MAAM,UAAU,IAAA,CAAK,MAAA,CAAO;QAC5B,OAAO,OAAO,IAAA,CAAK,OAAO,EAAE,MAAA,GAAS,IACjC,QAAQ,MAAA,CAAO,CAAC,SAAU,wLAAA,EAAW,SAAS,KAAK,CAAC,IACpD;IACN;IAEA,OAAO,KAAA,EAAoC;QACzC,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,KAAK;YAChB,CAAC;QACH,CAAC;IACH;IAEA,UAAgB;QACd,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,MAAA,CAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC/B,MAAM,OAAA,CAAQ;YAChB,CAAC;QACH,CAAC;IACH;IAEA,WAAiB;QACf,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,MAAA,CAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC/B,MAAM,QAAA,CAAS;YACjB,CAAC;QACH,CAAC;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/mutation.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { createR<PERSON>ry<PERSON> } from './retryer'\nimport type {\n  DefaultError,\n  MutationMeta,\n  MutationOptions,\n  MutationStatus,\n} from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n  submittedAt: number\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface PendingAction<TVariables, TContext> {\n  type: 'pending'\n  isPaused: boolean\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | PendingAction<TVariables, TContext>\n  | PauseAction\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  readonly mutationId: number\n\n  #observers: Array<MutationObserver<TData, TError, TVariables, TContext>>\n  #mutationCache: MutationCache\n  #retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.mutationId = config.mutationId\n    this.#mutationCache = config.mutationCache\n    this.#observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = options\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.#mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.#observers = this.#observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.#mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === 'pending') {\n        this.scheduleGc()\n      } else {\n        this.#mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return (\n      this.#retryer?.continue() ??\n      // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n      this.execute(this.state.variables!)\n    )\n  }\n\n  async execute(variables: TVariables): Promise<TData> {\n    const onContinue = () => {\n      this.#dispatch({ type: 'continue' })\n    }\n\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error('No mutationFn found'))\n        }\n        return this.options.mutationFn(variables)\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this),\n    })\n\n    const restored = this.state.status === 'pending'\n    const isPaused = !this.#retryer.canStart()\n\n    try {\n      if (restored) {\n        // Dispatch continue action to unpause restored mutation\n        onContinue()\n      } else {\n        this.#dispatch({ type: 'pending', variables, isPaused })\n        // Notify cache callback\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(variables)\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: 'pending',\n            context,\n            variables,\n            isPaused,\n          })\n        }\n      }\n      const data = await this.#retryer.start()\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(data, variables, this.state.context!)\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(data, null, variables, this.state.context)\n\n      this.#dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.#mutationCache.config.onError?.(\n          error as any,\n          variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onError?.(\n          error as TError,\n          variables,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.#mutationCache.config.onSettled?.(\n          undefined,\n          error as any,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          variables,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.#dispatch({ type: 'error', error: error as TError })\n      }\n    } finally {\n      this.#mutationCache.runNext(this)\n    }\n  }\n\n  #dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'pending':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: 'pending',\n            variables: action.variables,\n            submittedAt: Date.now(),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.#mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n    submittedAt: 0,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,qBAAqB;AAC9B,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;;;;AA8EvB,IAAM,WAAN,0LAKG,YAAA,CAAU;KAKlB,SAAA,CAAA;KACA,aAAA,CAAA;KACA,OAAA,CAAA;IAEA,YAAY,MAAA,CAA6D;QACvE,KAAA,CAAM;QAEN,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,EAAK,aAAA,GAAiB,OAAO,aAAA;QAC7B,IAAA,CAAK,UAAA,GAAa,CAAC,CAAA;QACnB,IAAA,CAAK,KAAA,GAAQ,OAAO,KAAA,IAAS,gBAAgB;QAE7C,IAAA,CAAK,UAAA,CAAW,OAAO,OAAO;QAC9B,IAAA,CAAK,UAAA,CAAW;IAClB;IAEA,WACE,OAAA,EACM;QACN,IAAA,CAAK,OAAA,GAAU;QAEf,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,MAAM;IACvC;IAEA,IAAI,OAAiC;QACnC,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;IACtB;IAEA,YAAY,QAAA,EAAsD;QAChE,IAAI,CAAC,IAAA,EAAK,SAAA,CAAW,QAAA,CAAS,QAAQ,GAAG;YACvC,IAAA,EAAK,SAAA,CAAW,IAAA,CAAK,QAAQ;YAG7B,IAAA,CAAK,cAAA,CAAe;YAEpB,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO;gBACzB,MAAM;gBACN,UAAU,IAAA;gBACV;YACF,CAAC;QACH;IACF;IAEA,eAAe,QAAA,EAAsD;QACnE,IAAA,CAAK,UAAA,GAAa,IAAA,EAAK,SAAA,CAAW,MAAA,CAAO,CAAC,IAAM,MAAM,QAAQ;QAE9D,IAAA,CAAK,UAAA,CAAW;QAEhB,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO;YACzB,MAAM;YACN,UAAU,IAAA;YACV;QACF,CAAC;IACH;IAEU,iBAAiB;QACzB,IAAI,CAAC,IAAA,CAAK,UAAA,CAAW,MAAA,EAAQ;YAC3B,IAAI,IAAA,CAAK,KAAA,CAAM,MAAA,KAAW,WAAW;gBACnC,IAAA,CAAK,UAAA,CAAW;YAClB,OAAO;gBACL,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,IAAI;YACjC;QACF;IACF;IAEA,WAA6B;QAC3B,OACE,IAAA,EAAK,OAAA,EAAU,SAAS,KAAA,kGAAA;QAExB,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,SAAU;IAEtC;IAEA,MAAM,QAAQ,SAAA,EAAuC;QACnD,MAAM,aAAa,MAAM;YACvB,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;YAAW,CAAC;QACrC;QAEA,IAAA,EAAK,OAAA,IAAW,6LAAA,EAAc;YAC5B,IAAI,MAAM;gBACR,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY;oBAC5B,OAAO,QAAQ,MAAA,CAAO,IAAI,MAAM,qBAAqB,CAAC;gBACxD;gBACA,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,SAAS;YAC1C;YACA,QAAQ,CAAC,cAAc,UAAU;gBAC/B,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAU;oBAAc;gBAAM,CAAC;YACxD;YACA,SAAS,MAAM;gBACb,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;gBAAQ,CAAC;YAClC;YACA;YACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA,IAAS;YAC7B,YAAY,IAAA,CAAK,OAAA,CAAQ,UAAA;YACzB,aAAa,IAAA,CAAK,OAAA,CAAQ,WAAA;YAC1B,QAAQ,IAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,IAAI;QAC/C,CAAC;QAED,MAAM,WAAW,IAAA,CAAK,KAAA,CAAM,MAAA,KAAW;QACvC,MAAM,WAAW,CAAC,IAAA,EAAK,OAAA,CAAS,QAAA,CAAS;QAEzC,IAAI;YACF,IAAI,UAAU;gBAEZ,WAAW;YACb,OAAO;gBACL,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAW;oBAAW;gBAAS,CAAC;gBAEvD,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,QAAA,GAC/B,WACA,IAAA;gBAEF,MAAM,UAAU,MAAM,IAAA,CAAK,OAAA,CAAQ,QAAA,GAAW,SAAS;gBACvD,IAAI,YAAY,IAAA,CAAK,KAAA,CAAM,OAAA,EAAS;oBAClC,IAAA,EAAK,QAAA,CAAU;wBACb,MAAM;wBACN;wBACA;wBACA;oBACF,CAAC;gBACH;YACF;YACA,MAAM,OAAO,MAAM,IAAA,EAAK,OAAA,CAAS,KAAA,CAAM;YAGvC,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,SAAA,GAC/B,MACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;YAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY,MAAM,WAAW,IAAA,CAAK,KAAA,CAAM,OAAQ;YAGnE,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,SAAA,GAC/B,MACA,MACA,IAAA,CAAK,KAAA,CAAM,SAAA,EACX,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;YAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY,MAAM,MAAM,WAAW,IAAA,CAAK,KAAA,CAAM,OAAO;YAExE,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;gBAAW;YAAK,CAAC;YACxC,OAAO;QACT,EAAA,OAAS,OAAO;YACd,IAAI;gBAEF,MAAM,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,OAAA,GAC/B,OACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;gBAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,OAAA,GACjB,OACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA;gBAIb,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,SAAA,GAC/B,KAAA,GACA,OACA,IAAA,CAAK,KAAA,CAAM,SAAA,EACX,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;gBAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,SAAA,GACjB,KAAA,GACA,OACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA;gBAEb,MAAM;YACR,SAAE;gBACA,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAS;gBAAuB,CAAC;YAC1D;QACF,SAAE;YACA,IAAA,EAAK,aAAA,CAAe,OAAA,CAAQ,IAAI;QAClC;IACF;IAEA,SAAA,CAAU,MAAA,EAA2D;QACnE,MAAM,UAAU,CACd,UACuD;YACvD,OAAQ,OAAO,IAAA,EAAM;gBACnB,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,cAAc,OAAO,YAAA;wBACrB,eAAe,OAAO,KAAA;oBACxB;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,UAAU;oBACZ;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,UAAU;oBACZ;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,SAAS,OAAO,OAAA;wBAChB,MAAM,KAAA;wBACN,cAAc;wBACd,eAAe;wBACf,OAAO;wBACP,UAAU,OAAO,QAAA;wBACjB,QAAQ;wBACR,WAAW,OAAO,SAAA;wBAClB,aAAa,KAAK,GAAA,CAAI;oBACxB;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,MAAM,OAAO,IAAA;wBACb,cAAc;wBACd,eAAe;wBACf,OAAO;wBACP,QAAQ;wBACR,UAAU;oBACZ;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,MAAM,KAAA;wBACN,OAAO,OAAO,KAAA;wBACd,cAAc,MAAM,YAAA,GAAe;wBACnC,eAAe,OAAO,KAAA;wBACtB,UAAU;wBACV,QAAQ;oBACV;YACJ;QACF;QACA,IAAA,CAAK,KAAA,GAAQ,QAAQ,IAAA,CAAK,KAAK;QAE/B,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,EAAK,SAAA,CAAW,OAAA,CAAQ,CAAC,aAAa;gBACpC,SAAS,gBAAA,CAAiB,MAAM;YAClC,CAAC;YACD,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO;gBACzB,UAAU,IAAA;gBACV,MAAM;gBACN;YACF,CAAC;QACH,CAAC;IACH;AACF;AAEO,SAAS,kBAKwC;IACtD,OAAO;QACL,SAAS,KAAA;QACT,MAAM,KAAA;QACN,OAAO;QACP,cAAc;QACd,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW,KAAA;QACX,aAAa;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { DefaultError, MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: DefaultError,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\nexport type MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  #mutations: Set<Mutation<any, any, any, any>>\n  #scopes: Map<string, Array<Mutation<any, any, any, any>>>\n  #mutationId: number\n\n  constructor(public config: MutationCacheConfig = {}) {\n    super()\n    this.#mutations = new Set()\n    this.#scopes = new Map()\n    this.#mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.#mutations.add(mutation)\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const scopedMutations = this.#scopes.get(scope)\n      if (scopedMutations) {\n        scopedMutations.push(mutation)\n      } else {\n        this.#scopes.set(scope, [mutation])\n      }\n    }\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation)\n      if (typeof scope === 'string') {\n        const scopedMutations = this.#scopes.get(scope)\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation)\n            if (index !== -1) {\n              scopedMutations.splice(index, 1)\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope)\n          }\n        }\n      }\n    }\n\n    // Currently we notify the removal even if the mutation was already removed.\n    // Consider making this an error or not notifying of the removal depending on the desired semantics.\n    this.notify({ type: 'removed', mutation })\n  }\n\n  canRun(mutation: Mutation<any, any, any, any>): boolean {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const mutationsWithSameScope = this.#scopes.get(scope)\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === 'pending',\n      )\n      // we can run if there is no current pending mutation (start use-case)\n      // or if WE are the first pending mutation (continue use-case)\n      return !firstPendingMutation || firstPendingMutation === mutation\n    } else {\n      // For unscoped mutations there are never any pending mutations in front of the\n      // current mutation\n      return true\n    }\n  }\n\n  runNext(mutation: Mutation<any, any, any, any>): Promise<unknown> {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const foundMutation = this.#scopes\n        .get(scope)\n        ?.find((m) => m !== mutation && m.state.isPaused)\n\n      return foundMutation?.continue() ?? Promise.resolve()\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: 'removed', mutation })\n      })\n      this.#mutations.clear()\n      this.#scopes.clear()\n    })\n  }\n\n  getAll(): Array<Mutation> {\n    return Array.from(this.#mutations)\n  }\n\n  find<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = any,\n    TContext = unknown,\n  >(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((mutation) =>\n      matchMutation(defaultedFilters, mutation),\n    ) as Mutation<TData, TError, TVariables, TContext> | undefined\n  }\n\n  findAll(filters: MutationFilters = {}): Array<Mutation> {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused)\n\n    return notifyManager.batch(() =>\n      Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop)),\n      ),\n    )\n  }\n}\n\nfunction scopeFor(mutation: Mutation<any, any, any, any>) {\n  return mutation.options.scope?.id\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB;AACzB,SAAS,eAAe,YAAY;AACpC,SAAS,oBAAoB;;;;;AAgFtB,IAAM,gBAAN,6LAA4B,eAAA,CAAoC;IAKrE,YAAmB,SAA8B,CAAC,CAAA,CAAG;QACnD,KAAA,CAAM;QADW,IAAA,CAAA,MAAA,GAAA;QAEjB,IAAA,EAAK,SAAA,GAAa,aAAA,GAAA,IAAI,IAAI;QAC1B,IAAA,EAAK,MAAA,GAAU,aAAA,GAAA,IAAI,IAAI;QACvB,IAAA,EAAK,UAAA,GAAc;IACrB;KATA,SAAA,CAAA;IACA,OAAA,CAAA;KACA,UAAA,CAAA;IASA,MACE,MAAA,EACA,OAAA,EACA,KAAA,EAC+C;QAC/C,MAAM,WAAW,+KAAI,WAAA,CAAS;YAC5B,eAAe,IAAA;YACf,YAAY,EAAE,IAAA,EAAK,UAAA;YACnB,SAAS,OAAO,sBAAA,CAAuB,OAAO;YAC9C;QACF,CAAC;QAED,IAAA,CAAK,GAAA,CAAI,QAAQ;QAEjB,OAAO;IACT;IAEA,IAAI,QAAA,EAA8C;QAChD,IAAA,EAAK,SAAA,CAAW,GAAA,CAAI,QAAQ;QAC5B,MAAM,QAAQ,SAAS,QAAQ;QAC/B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,kBAAkB,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,KAAK;YAC9C,IAAI,iBAAiB;gBACnB,gBAAgB,IAAA,CAAK,QAAQ;YAC/B,OAAO;gBACL,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,OAAO;oBAAC,QAAQ;iBAAC;YACpC;QACF;QACA,IAAA,CAAK,MAAA,CAAO;YAAE,MAAM;YAAS;QAAS,CAAC;IACzC;IAEA,OAAO,QAAA,EAA8C;QACnD,IAAI,IAAA,EAAK,SAAA,CAAW,MAAA,CAAO,QAAQ,GAAG;YACpC,MAAM,QAAQ,SAAS,QAAQ;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC7B,MAAM,kBAAkB,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,KAAK;gBAC9C,IAAI,iBAAiB;oBACnB,IAAI,gBAAgB,MAAA,GAAS,GAAG;wBAC9B,MAAM,QAAQ,gBAAgB,OAAA,CAAQ,QAAQ;wBAC9C,IAAI,UAAU,CAAA,GAAI;4BAChB,gBAAgB,MAAA,CAAO,OAAO,CAAC;wBACjC;oBACF,OAAA,IAAW,eAAA,CAAgB,CAAC,CAAA,KAAM,UAAU;wBAC1C,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAO,KAAK;oBAC3B;gBACF;YACF;QACF;QAIA,IAAA,CAAK,MAAA,CAAO;YAAE,MAAM;YAAW;QAAS,CAAC;IAC3C;IAEA,OAAO,QAAA,EAAiD;QACtD,MAAM,QAAQ,SAAS,QAAQ;QAC/B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,yBAAyB,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,KAAK;YACrD,MAAM,uBAAuB,wBAAwB,KACnD,CAAC,IAAM,EAAE,KAAA,CAAM,MAAA,KAAW;YAI5B,OAAO,CAAC,wBAAwB,yBAAyB;QAC3D,OAAO;YAGL,OAAO;QACT;IACF;IAEA,QAAQ,QAAA,EAA0D;QAChE,MAAM,QAAQ,SAAS,QAAQ;QAC/B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,gBAAgB,IAAA,EAAK,MAAA,CACxB,GAAA,CAAI,KAAK,GACR,KAAK,CAAC,IAAM,MAAM,YAAY,EAAE,KAAA,CAAM,QAAQ;YAElD,OAAO,eAAe,SAAS,KAAK,QAAQ,OAAA,CAAQ;QACtD,OAAO;YACL,OAAO,QAAQ,OAAA,CAAQ;QACzB;IACF;IAEA,QAAc;QACZ,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,EAAK,SAAA,CAAW,OAAA,CAAQ,CAAC,aAAa;gBACpC,IAAA,CAAK,MAAA,CAAO;oBAAE,MAAM;oBAAW;gBAAS,CAAC;YAC3C,CAAC;YACD,IAAA,EAAK,SAAA,CAAW,KAAA,CAAM;YACtB,IAAA,EAAK,MAAA,CAAQ,KAAA,CAAM;QACrB,CAAC;IACH;IAEA,SAA0B;QACxB,OAAO,MAAM,IAAA,CAAK,IAAA,EAAK,SAAU;IACnC;IAEA,KAME,OAAA,EAC2D;QAC3D,MAAM,mBAAmB;YAAE,OAAO;YAAM,GAAG,OAAA;QAAQ;QAEnD,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,IAAA,CAAK,CAAC,uLACzB,gBAAA,EAAc,kBAAkB,QAAQ;IAE5C;IAEA,QAAQ,UAA2B,CAAC,CAAA,EAAoB;QACtD,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,MAAA,CAAO,CAAC,uLAAa,gBAAA,EAAc,SAAS,QAAQ,CAAC;IAC5E;IAEA,OAAO,KAAA,EAAiC;QACtC,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,KAAK;YAChB,CAAC;QACH,CAAC;IACH;IAEA,wBAA0C;QACxC,MAAM,kBAAkB,IAAA,CAAK,MAAA,CAAO,EAAE,MAAA,CAAO,CAAC,IAAM,EAAE,KAAA,CAAM,QAAQ;QAEpE,uLAAO,gBAAA,CAAc,KAAA,CAAM,IACzB,QAAQ,GAAA,CACN,gBAAgB,GAAA,CAAI,CAAC,WAAa,SAAS,QAAA,CAAS,EAAE,KAAA,yKAAM,OAAI,CAAC;IAGvE;AACF;AAEA,SAAS,SAAS,QAAA,EAAwC;IACxD,OAAO,SAAS,OAAA,CAAQ,KAAA,EAAO;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/infiniteQueryBehavior.ts"], "sourcesContent": ["import { addToEnd, addToStart, ensureQueryFn } from './utils'\nimport type { QueryBehavior } from './query'\nimport type {\n  InfiniteData,\n  InfiniteQueryPageParamsOptions,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n} from './types'\n\nexport function infiniteQueryBehavior<TQueryFnData, TError, TData, TPageParam>(\n  pages?: number,\n): QueryBehavior<TQueryFnData, TError, InfiniteData<TData, TPageParam>> {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options as InfiniteQueryPageParamsOptions<TData>\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction\n      const oldPages = context.state.data?.pages || []\n      const oldPageParams = context.state.data?.pageParams || []\n      let result: InfiniteData<unknown> = { pages: [], pageParams: [] }\n      let currentPage = 0\n\n      const fetchFn = async () => {\n        let cancelled = false\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true\n              } else {\n                context.signal.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions)\n\n        // Create function to fetch a page\n        const fetchPage = async (\n          data: InfiniteData<unknown>,\n          param: unknown,\n          previous?: boolean,\n        ): Promise<InfiniteData<unknown>> => {\n          if (cancelled) {\n            return Promise.reject()\n          }\n\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data)\n          }\n\n          const createQueryFnContext = () => {\n            const queryFnContext: OmitKeyof<\n              QueryFunctionContext<QueryKey, unknown>,\n              'signal'\n            > = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? 'backward' : 'forward',\n              meta: context.options.meta,\n            }\n            addSignalProperty(queryFnContext)\n            return queryFnContext as QueryFunctionContext<QueryKey, unknown>\n          }\n\n          const queryFnContext = createQueryFnContext()\n\n          const page = await queryFn(queryFnContext)\n\n          const { maxPages } = context.options\n          const addTo = previous ? addToStart : addToEnd\n\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages),\n          }\n        }\n\n        // fetch next / previous page?\n        if (direction && oldPages.length) {\n          const previous = direction === 'backward'\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams,\n          }\n          const param = pageParamFn(options, oldData)\n\n          result = await fetchPage(oldData, param, previous)\n        } else {\n          const remainingPages = pages ?? oldPages.length\n\n          // Fetch all pages\n          do {\n            const param =\n              currentPage === 0\n                ? (oldPageParams[0] ?? options.initialPageParam)\n                : getNextPageParam(options, result)\n            if (currentPage > 0 && param == null) {\n              break\n            }\n            result = await fetchPage(result, param)\n            currentPage++\n          } while (currentPage < remainingPages)\n        }\n\n        return result\n      }\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn as any,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal,\n            },\n            query,\n          )\n        }\n      } else {\n        context.fetchFn = fetchFn\n      }\n    },\n  }\n}\n\nfunction getNextPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  const lastIndex = pages.length - 1\n  return pages.length > 0\n    ? options.getNextPageParam(\n        pages[lastIndex],\n        pages,\n        pageParams[lastIndex],\n        pageParams,\n      )\n    : undefined\n}\n\nfunction getPreviousPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  return pages.length > 0\n    ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams)\n    : undefined\n}\n\n/**\n * Checks if there is a next page.\n */\nexport function hasNextPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data) return false\n  return getNextPageParam(options, data) != null\n}\n\n/**\n * Checks if there is a previous page.\n */\nexport function hasPreviousPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data || !options.getPreviousPageParam) return false\n  return getPreviousPageParam(options, data) != null\n}\n"], "names": ["queryFnContext"], "mappings": ";;;;;;AAAA,SAAS,UAAU,YAAY,qBAAqB;;AAU7C,SAAS,sBACd,KAAA,EACsE;IACtE,OAAO;QACL,SAAS,CAAC,SAAS,UAAU;YAC3B,MAAM,UAAU,QAAQ,OAAA;YACxB,MAAM,YAAY,QAAQ,YAAA,EAAc,MAAM,WAAW;YACzD,MAAM,WAAW,QAAQ,KAAA,CAAM,IAAA,EAAM,SAAS,CAAC,CAAA;YAC/C,MAAM,gBAAgB,QAAQ,KAAA,CAAM,IAAA,EAAM,cAAc,CAAC,CAAA;YACzD,IAAI,SAAgC;gBAAE,OAAO,CAAC,CAAA;gBAAG,YAAY,CAAC,CAAA;YAAE;YAChE,IAAI,cAAc;YAElB,MAAM,UAAU,YAAY;gBAC1B,IAAI,YAAY;gBAChB,MAAM,oBAAoB,CAAC,WAAoB;oBAC7C,OAAO,cAAA,CAAe,QAAQ,UAAU;wBACtC,YAAY;wBACZ,KAAK,MAAM;4BACT,IAAI,QAAQ,MAAA,CAAO,OAAA,EAAS;gCAC1B,YAAY;4BACd,OAAO;gCACL,QAAQ,MAAA,CAAO,gBAAA,CAAiB,SAAS,MAAM;oCAC7C,YAAY;gCACd,CAAC;4BACH;4BACA,OAAO,QAAQ,MAAA;wBACjB;oBACF,CAAC;gBACH;gBAEA,MAAM,cAAU,wLAAA,EAAc,QAAQ,OAAA,EAAS,QAAQ,YAAY;gBAGnE,MAAM,YAAY,OAChB,MACA,OACA,aACmC;oBACnC,IAAI,WAAW;wBACb,OAAO,QAAQ,MAAA,CAAO;oBACxB;oBAEA,IAAI,SAAS,QAAQ,KAAK,KAAA,CAAM,MAAA,EAAQ;wBACtC,OAAO,QAAQ,OAAA,CAAQ,IAAI;oBAC7B;oBAEA,MAAM,uBAAuB,MAAM;wBACjC,MAAMA,kBAGF;4BACF,QAAQ,QAAQ,MAAA;4BAChB,UAAU,QAAQ,QAAA;4BAClB,WAAW;4BACX,WAAW,WAAW,aAAa;4BACnC,MAAM,QAAQ,OAAA,CAAQ,IAAA;wBACxB;wBACA,kBAAkBA,eAAc;wBAChC,OAAOA;oBACT;oBAEA,MAAM,iBAAiB,qBAAqB;oBAE5C,MAAM,OAAO,MAAM,QAAQ,cAAc;oBAEzC,MAAM,EAAE,QAAA,CAAS,CAAA,GAAI,QAAQ,OAAA;oBAC7B,MAAM,QAAQ,mLAAW,aAAA,0KAAa,YAAA;oBAEtC,OAAO;wBACL,OAAO,MAAM,KAAK,KAAA,EAAO,MAAM,QAAQ;wBACvC,YAAY,MAAM,KAAK,UAAA,EAAY,OAAO,QAAQ;oBACpD;gBACF;gBAGA,IAAI,aAAa,SAAS,MAAA,EAAQ;oBAChC,MAAM,WAAW,cAAc;oBAC/B,MAAM,cAAc,WAAW,uBAAuB;oBACtD,MAAM,UAAU;wBACd,OAAO;wBACP,YAAY;oBACd;oBACA,MAAM,QAAQ,YAAY,SAAS,OAAO;oBAE1C,SAAS,MAAM,UAAU,SAAS,OAAO,QAAQ;gBACnD,OAAO;oBACL,MAAM,iBAAiB,SAAS,SAAS,MAAA;oBAGzC,GAAG;wBACD,MAAM,QACJ,gBAAgB,IACX,aAAA,CAAc,CAAC,CAAA,IAAK,QAAQ,gBAAA,GAC7B,iBAAiB,SAAS,MAAM;wBACtC,IAAI,cAAc,KAAK,SAAS,MAAM;4BACpC;wBACF;wBACA,SAAS,MAAM,UAAU,QAAQ,KAAK;wBACtC;oBACF,QAAS,cAAc,eAAA;gBACzB;gBAEA,OAAO;YACT;YACA,IAAI,QAAQ,OAAA,CAAQ,SAAA,EAAW;gBAC7B,QAAQ,OAAA,GAAU,MAAM;oBACtB,OAAO,QAAQ,OAAA,CAAQ,SAAA,GACrB,SACA;wBACE,QAAQ,QAAQ,MAAA;wBAChB,UAAU,QAAQ,QAAA;wBAClB,MAAM,QAAQ,OAAA,CAAQ,IAAA;wBACtB,QAAQ,QAAQ,MAAA;oBAClB,GACA;gBAEJ;YACF,OAAO;gBACL,QAAQ,OAAA,GAAU;YACpB;QACF;IACF;AACF;AAEA,SAAS,iBACP,OAAA,EACA,EAAE,KAAA,EAAO,UAAA,CAAW,CAAA,EACC;IACrB,MAAM,YAAY,MAAM,MAAA,GAAS;IACjC,OAAO,MAAM,MAAA,GAAS,IAClB,QAAQ,gBAAA,CACN,KAAA,CAAM,SAAS,CAAA,EACf,OACA,UAAA,CAAW,SAAS,CAAA,EACpB,cAEF,KAAA;AACN;AAEA,SAAS,qBACP,OAAA,EACA,EAAE,KAAA,EAAO,UAAA,CAAW,CAAA,EACC;IACrB,OAAO,MAAM,MAAA,GAAS,IAClB,QAAQ,oBAAA,GAAuB,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,UAAA,CAAW,CAAC,CAAA,EAAG,UAAU,IACzE,KAAA;AACN;AAKO,SAAS,YACd,OAAA,EACA,IAAA,EACS;IACT,IAAI,CAAC,KAAM,CAAA,OAAO;IAClB,OAAO,iBAAiB,SAAS,IAAI,KAAK;AAC5C;AAKO,SAAS,gBACd,OAAA,EACA,IAAA,EACS;IACT,IAAI,CAAC,QAAQ,CAAC,QAAQ,oBAAA,CAAsB,CAAA,OAAO;IACnD,OAAO,qBAAqB,SAAS,IAAI,KAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/queryClient.ts"], "sourcesContent": ["import {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport type {\n  CancelOptions,\n  DefaultError,\n  DefaultOptions,\n  DefaultedQueryObserverOptions,\n  EnsureInfiniteQueryDataOptions,\n  EnsureQueryDataOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InferDataFromTag,\n  InferErrorFromTag,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  NoInfer,\n  OmitKeyof,\n  QueryClientConfig,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n} from './types'\nimport type { QueryState } from './query'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: OmitKeyof<QueryOptions<any, any, any>, 'queryKey'>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  #queryCache: QueryCache\n  #mutationCache: MutationCache\n  #defaultOptions: DefaultOptions\n  #queryDefaults: Map<string, QueryDefaults>\n  #mutationDefaults: Map<string, MutationDefaults>\n  #mountCount: number\n  #unsubscribeFocus?: () => void\n  #unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.#queryCache = config.queryCache || new QueryCache()\n    this.#mutationCache = config.mutationCache || new MutationCache()\n    this.#defaultOptions = config.defaultOptions || {}\n    this.#queryDefaults = new Map()\n    this.#mutationDefaults = new Map()\n    this.#mountCount = 0\n  }\n\n  mount(): void {\n    this.#mountCount++\n    if (this.#mountCount !== 1) return\n\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations()\n        this.#queryCache.onFocus()\n      }\n    })\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations()\n        this.#queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.#mountCount--\n    if (this.#mountCount !== 0) return\n\n    this.#unsubscribeFocus?.()\n    this.#unsubscribeFocus = undefined\n\n    this.#unsubscribeOnline?.()\n    this.#unsubscribeOnline = undefined\n  }\n\n  isFetching<TQueryFilters extends QueryFilters<any> = QueryFilters>(\n    filters?: TQueryFilters,\n  ): number {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: 'fetching' })\n      .length\n  }\n\n  isMutating<\n    TMutationFilters extends MutationFilters<any, any> = MutationFilters,\n  >(filters?: TMutationFilters): number {\n    return this.#mutationCache.findAll({ ...filters, status: 'pending' }).length\n  }\n\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(queryKey: TTaggedQueryKey): TInferredQueryFnData | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n\n    return this.#queryCache.get<TInferredQueryFnData>(options.queryHash)?.state\n      .data\n  }\n\n  ensureQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: EnsureQueryDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n    const query = this.#queryCache.build(this, defaultedOptions)\n    const cachedData = query.state.data\n\n    if (cachedData === undefined) {\n      return this.fetchQuery(options)\n    }\n\n    if (\n      options.revalidateIfStale &&\n      query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))\n    ) {\n      void this.prefetchQuery(defaultedOptions)\n    }\n\n    return Promise.resolve(cachedData)\n  }\n\n  getQueriesData<\n    TQueryFnData = unknown,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(filters: TQueryFilters): Array<[QueryKey, TQueryFnData | undefined]> {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data as TQueryFnData | undefined\n      return [queryKey, data]\n    })\n  }\n\n  setQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n    updater: Updater<\n      NoInfer<TInferredQueryFnData> | undefined,\n      NoInfer<TInferredQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): NoInfer<TInferredQueryFnData> | undefined {\n    const defaultedOptions = this.defaultQueryOptions<\n      any,\n      any,\n      unknown,\n      any,\n      QueryKey\n    >({ queryKey })\n\n    const query = this.#queryCache.get<TInferredQueryFnData>(\n      defaultedOptions.queryHash,\n    )\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (data === undefined) {\n      return undefined\n    }\n\n    return this.#queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<\n    TQueryFnData,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(\n    filters: TQueryFilters,\n    updater: Updater<\n      NoInfer<TQueryFnData> | undefined,\n      NoInfer<TQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): Array<[QueryKey, TQueryFnData | undefined]> {\n    return notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n    TInferredError = InferErrorFromTag<TError, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n  ): QueryState<TInferredQueryFnData, TInferredError> | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n    return this.#queryCache.get<TInferredQueryFnData, TInferredError>(\n      options.queryHash,\n    )?.state\n  }\n\n  removeQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n  ): void {\n    const queryCache = this.#queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    options?: ResetOptions,\n  ): Promise<void> {\n    const queryCache = this.#queryCache\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(\n        {\n          type: 'active',\n          ...filters,\n        },\n        options,\n      )\n    })\n  }\n\n  cancelQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    cancelOptions: CancelOptions = {},\n  ): Promise<void> {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions }\n\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(defaultedCancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: InvalidateQueryFilters<TTaggedQueryKey>,\n    options: InvalidateOptions = {},\n  ): Promise<void> {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters?.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? 'active',\n        },\n        options,\n      )\n    })\n  }\n\n  refetchQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: RefetchQueryFilters<TTaggedQueryKey>,\n    options: RefetchOptions = {},\n  ): Promise<void> {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true,\n    }\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled() && !query.isStatic())\n        .map((query) => {\n          let promise = query.fetch(undefined, fetchOptions)\n          if (!fetchOptions.throwOnError) {\n            promise = promise.catch(noop)\n          }\n          return query.state.fetchStatus === 'paused'\n            ? Promise.resolve()\n            : promise\n        }),\n    )\n\n    return Promise.all(promises).then(noop)\n  }\n\n  fetchQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options: FetchQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (defaultedOptions.retry === undefined) {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.#queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query),\n    )\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchQuery(options).then(noop).catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n    return this.fetchQuery(options as any)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop)\n  }\n\n  ensureInfiniteQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: EnsureInfiniteQueryDataOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n\n    return this.ensureQueryData(options as any)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations()\n    }\n    return Promise.resolve()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.#queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.#mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.#defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.#defaultOptions = options\n  }\n\n  setQueryDefaults<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n  >(\n    queryKey: QueryKey,\n    options: Partial<\n      OmitKeyof<\n        QueryObserverOptions<TQueryFnData, TError, TData, TQueryData>,\n        'queryKey'\n      >\n    >,\n  ): void {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options,\n    })\n  }\n\n  getQueryDefaults(\n    queryKey: QueryKey,\n  ): OmitKeyof<QueryObserverOptions<any, any, any, any, any>, 'queryKey'> {\n    const defaults = [...this.#queryDefaults.values()]\n\n    const result: OmitKeyof<\n      QueryObserverOptions<any, any, any, any, any>,\n      'queryKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n    return result\n  }\n\n  setMutationDefaults<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = void,\n    TContext = unknown,\n  >(\n    mutationKey: MutationKey,\n    options: OmitKeyof<\n      MutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey'\n    >,\n  ): void {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options,\n    })\n  }\n\n  getMutationDefaults(\n    mutationKey: MutationKey,\n  ): OmitKeyof<MutationObserverOptions<any, any, any, any>, 'mutationKey'> {\n    const defaults = [...this.#mutationDefaults.values()]\n\n    const result: OmitKeyof<\n      MutationObserverOptions<any, any, any, any>,\n      'mutationKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n\n    return result\n  }\n\n  defaultQueryOptions<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options:\n      | QueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey,\n          TPageParam\n        >\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (defaultedOptions.refetchOnReconnect === undefined) {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (defaultedOptions.throwOnError === undefined) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense\n    }\n\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = 'offlineFirst'\n    }\n\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey &&\n        this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.#queryCache.clear()\n    this.#mutationCache.clear()\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AASA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,6BAA6B;;;;;;;;AA8C/B,IAAM,cAAN,MAAkB;KACvB,UAAA,CAAA;KACA,aAAA,CAAA;KACA,cAAA,CAAA;KACA,aAAA,CAAA;KACA,gBAAA,CAAA;KACA,UAAA,CAAA;IACA,iBAAA,CAAA;KACA,iBAAA,CAAA;IAEA,YAAY,SAA4B,CAAC,CAAA,CAAG;QAC1C,IAAA,EAAK,UAAA,GAAc,OAAO,UAAA,IAAc,iLAAI,aAAA,CAAW;QACvD,IAAA,EAAK,aAAA,GAAiB,OAAO,aAAA,IAAiB,oLAAI,gBAAA,CAAc;QAChE,IAAA,EAAK,cAAA,GAAkB,OAAO,cAAA,IAAkB,CAAC;QACjD,IAAA,EAAK,aAAA,GAAiB,aAAA,GAAA,IAAI,IAAI;QAC9B,IAAA,EAAK,gBAAA,GAAoB,aAAA,GAAA,IAAI,IAAI;QACjC,IAAA,EAAK,UAAA,GAAc;IACrB;IAEA,QAAc;QACZ,IAAA,EAAK,UAAA;QACL,IAAI,IAAA,EAAK,UAAA,KAAgB,EAAG,CAAA;QAE5B,IAAA,EAAK,gBAAA,kLAAoB,eAAA,CAAa,SAAA,CAAU,OAAO,YAAY;YACjE,IAAI,SAAS;gBACX,MAAM,IAAA,CAAK,qBAAA,CAAsB;gBACjC,IAAA,EAAK,UAAA,CAAY,OAAA,CAAQ;YAC3B;QACF,CAAC;QACD,IAAA,EAAK,iBAAA,mLAAqB,gBAAA,CAAc,SAAA,CAAU,OAAO,WAAW;YAClE,IAAI,QAAQ;gBACV,MAAM,IAAA,CAAK,qBAAA,CAAsB;gBACjC,IAAA,EAAK,UAAA,CAAY,QAAA,CAAS;YAC5B;QACF,CAAC;IACH;IAEA,UAAgB;QACd,IAAA,EAAK,UAAA;QACL,IAAI,IAAA,EAAK,UAAA,KAAgB,EAAG,CAAA;QAE5B,IAAA,EAAK,gBAAA,GAAoB;QACzB,IAAA,EAAK,gBAAA,GAAoB,KAAA;QAEzB,IAAA,EAAK,iBAAA,GAAqB;QAC1B,IAAA,EAAK,iBAAA,GAAqB,KAAA;IAC5B;IAEA,WACE,OAAA,EACQ;QACR,OAAO,IAAA,CAAK,WAAA,CAAY,OAAA,CAAQ;YAAE,GAAG,OAAA;YAAS,aAAa;QAAW,CAAC,EACpE,MAAA;IACL;IAEA,WAEE,OAAA,EAAoC;QACpC,OAAO,IAAA,CAAK,cAAA,CAAe,OAAA,CAAQ;YAAE,GAAG,OAAA;YAAS,QAAQ;QAAU,CAAC,EAAE,MAAA;IACxE;IAAA;;;;;;GAAA,GASA,aAIE,QAAA,EAA6D;QAC7D,MAAM,UAAU,IAAA,CAAK,mBAAA,CAAoB;YAAE;QAAS,CAAC;QAErD,OAAO,IAAA,EAAK,UAAA,CAAY,GAAA,CAA0B,QAAQ,SAAS,GAAG,MACnE;IACL;IAEA,gBAME,OAAA,EACgB;QAChB,MAAM,mBAAmB,IAAA,CAAK,mBAAA,CAAoB,OAAO;QACzD,MAAM,QAAQ,IAAA,EAAK,UAAA,CAAY,KAAA,CAAM,IAAA,EAAM,gBAAgB;QAC3D,MAAM,aAAa,MAAM,KAAA,CAAM,IAAA;QAE/B,IAAI,eAAe,KAAA,GAAW;YAC5B,OAAO,IAAA,CAAK,UAAA,CAAW,OAAO;QAChC;QAEA,IACE,QAAQ,iBAAA,IACR,MAAM,aAAA,6KAAc,mBAAA,EAAiB,iBAAiB,SAAA,EAAW,KAAK,CAAC,GACvE;YACA,KAAK,IAAA,CAAK,aAAA,CAAc,gBAAgB;QAC1C;QAEA,OAAO,QAAQ,OAAA,CAAQ,UAAU;IACnC;IAEA,eAGE,OAAA,EAAqE;QACrE,OAAO,IAAA,EAAK,UAAA,CAAY,OAAA,CAAQ,OAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,EAAU,KAAA,CAAM,CAAA,KAAM;YACpE,MAAM,OAAO,MAAM,IAAA;YACnB,OAAO;gBAAC;gBAAU,IAAI;aAAA;QACxB,CAAC;IACH;IAEA,aAKE,QAAA,EACA,OAAA,EAIA,OAAA,EAC2C;QAC3C,MAAM,mBAAmB,IAAA,CAAK,mBAAA,CAM5B;YAAE;QAAS,CAAC;QAEd,MAAM,QAAQ,IAAA,EAAK,UAAA,CAAY,GAAA,CAC7B,iBAAiB,SAAA;QAEnB,MAAM,WAAW,OAAO,MAAM;QAC9B,MAAM,mLAAO,mBAAA,EAAiB,SAAS,QAAQ;QAE/C,IAAI,SAAS,KAAA,GAAW;YACtB,OAAO,KAAA;QACT;QAEA,OAAO,IAAA,EAAK,UAAA,CACT,KAAA,CAAM,IAAA,EAAM,gBAAgB,EAC5B,OAAA,CAAQ,MAAM;YAAE,GAAG,OAAA;YAAS,QAAQ;QAAK,CAAC;IAC/C;IAEA,eAIE,OAAA,EACA,OAAA,EAIA,OAAA,EAC6C;QAC7C,uLAAO,gBAAA,CAAc,KAAA,CAAM,IACzB,IAAA,EAAK,UAAA,CACF,OAAA,CAAQ,OAAO,EACf,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,CAAA,GAAM;oBACrB;oBACA,IAAA,CAAK,YAAA,CAA2B,UAAU,SAAS,OAAO;iBAC3D;IAEP;IAEA,cAOE,QAAA,EAC8D;QAC9D,MAAM,UAAU,IAAA,CAAK,mBAAA,CAAoB;YAAE;QAAS,CAAC;QACrD,OAAO,IAAA,EAAK,UAAA,CAAY,GAAA,CACtB,QAAQ,SAAA,GACP;IACL;IAEA,cACE,OAAA,EACM;QACN,MAAM,aAAa,IAAA,EAAK,UAAA;QACxB,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,WAAW,OAAA,CAAQ,OAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC7C,WAAW,MAAA,CAAO,KAAK;YACzB,CAAC;QACH,CAAC;IACH;IAEA,aACE,OAAA,EACA,OAAA,EACe;QACf,MAAM,aAAa,IAAA,EAAK,UAAA;QAExB,uLAAO,gBAAA,CAAc,KAAA,CAAM,MAAM;YAC/B,WAAW,OAAA,CAAQ,OAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC7C,MAAM,KAAA,CAAM;YACd,CAAC;YACD,OAAO,IAAA,CAAK,cAAA,CACV;gBACE,MAAM;gBACN,GAAG,OAAA;YACL,GACA;QAEJ,CAAC;IACH;IAEA,cACE,OAAA,EACA,gBAA+B,CAAC,CAAA,EACjB;QACf,MAAM,yBAAyB;YAAE,QAAQ;YAAM,GAAG,aAAA;QAAc;QAEhE,MAAM,2LAAW,gBAAA,CAAc,KAAA,CAAM,IACnC,IAAA,EAAK,UAAA,CACF,OAAA,CAAQ,OAAO,EACf,GAAA,CAAI,CAAC,QAAU,MAAM,MAAA,CAAO,sBAAsB,CAAC;QAGxD,OAAO,QAAQ,GAAA,CAAI,QAAQ,EAAE,IAAA,yKAAK,OAAI,EAAE,KAAA,wKAAM,QAAI;IACpD;IAEA,kBACE,OAAA,EACA,UAA6B,CAAC,CAAA,EACf;QACf,uLAAO,gBAAA,CAAc,KAAA,CAAM,MAAM;YAC/B,IAAA,EAAK,UAAA,CAAY,OAAA,CAAQ,OAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBACnD,MAAM,UAAA,CAAW;YACnB,CAAC;YAED,IAAI,SAAS,gBAAgB,QAAQ;gBACnC,OAAO,QAAQ,OAAA,CAAQ;YACzB;YACA,OAAO,IAAA,CAAK,cAAA,CACV;gBACE,GAAG,OAAA;gBACH,MAAM,SAAS,eAAe,SAAS,QAAQ;YACjD,GACA;QAEJ,CAAC;IACH;IAEA,eACE,OAAA,EACA,UAA0B,CAAC,CAAA,EACZ;QACf,MAAM,eAAe;YACnB,GAAG,OAAA;YACH,eAAe,QAAQ,aAAA,IAAiB;QAC1C;QACA,MAAM,2LAAW,gBAAA,CAAc,KAAA,CAAM,IACnC,IAAA,EAAK,UAAA,CACF,OAAA,CAAQ,OAAO,EACf,MAAA,CAAO,CAAC,QAAU,CAAC,MAAM,UAAA,CAAW,KAAK,CAAC,MAAM,QAAA,CAAS,CAAC,EAC1D,GAAA,CAAI,CAAC,UAAU;gBACd,IAAI,UAAU,MAAM,KAAA,CAAM,KAAA,GAAW,YAAY;gBACjD,IAAI,CAAC,aAAa,YAAA,EAAc;oBAC9B,UAAU,QAAQ,KAAA,yKAAM,OAAI;gBAC9B;gBACA,OAAO,MAAM,KAAA,CAAM,WAAA,KAAgB,WAC/B,QAAQ,OAAA,CAAQ,IAChB;YACN,CAAC;QAGL,OAAO,QAAQ,GAAA,CAAI,QAAQ,EAAE,IAAA,yKAAK,OAAI;IACxC;IAEA,WAOE,OAAA,EAOgB;QAChB,MAAM,mBAAmB,IAAA,CAAK,mBAAA,CAAoB,OAAO;QAGzD,IAAI,iBAAiB,KAAA,KAAU,KAAA,GAAW;YACxC,iBAAiB,KAAA,GAAQ;QAC3B;QAEA,MAAM,QAAQ,IAAA,EAAK,UAAA,CAAY,KAAA,CAAM,IAAA,EAAM,gBAAgB;QAE3D,OAAO,MAAM,aAAA,6KACX,mBAAA,EAAiB,iBAAiB,SAAA,EAAW,KAAK,KAEhD,MAAM,KAAA,CAAM,gBAAgB,IAC5B,QAAQ,OAAA,CAAQ,MAAM,KAAA,CAAM,IAAa;IAC/C;IAEA,cAME,OAAA,EACe;QACf,OAAO,IAAA,CAAK,UAAA,CAAW,OAAO,EAAE,IAAA,wKAAK,QAAI,EAAE,KAAA,yKAAM,OAAI;IACvD;IAEA,mBAOE,OAAA,EAO0C;QAC1C,QAAQ,QAAA,OAAW,gNAAA,EAKjB,QAAQ,KAAK;QACf,OAAO,IAAA,CAAK,UAAA,CAAW,OAAc;IACvC;IAEA,sBAOE,OAAA,EAOe;QACf,OAAO,IAAA,CAAK,kBAAA,CAAmB,OAAO,EAAE,IAAA,yKAAK,OAAI,EAAE,KAAA,yKAAM,OAAI;IAC/D;IAEA,wBAOE,OAAA,EAO0C;QAC1C,QAAQ,QAAA,+LAAW,wBAAA,EAKjB,QAAQ,KAAK;QAEf,OAAO,IAAA,CAAK,eAAA,CAAgB,OAAc;IAC5C;IAEA,wBAA0C;QACxC,oLAAI,gBAAA,CAAc,QAAA,CAAS,GAAG;YAC5B,OAAO,IAAA,EAAK,aAAA,CAAe,qBAAA,CAAsB;QACnD;QACA,OAAO,QAAQ,OAAA,CAAQ;IACzB;IAEA,gBAA4B;QAC1B,OAAO,IAAA,EAAK,UAAA;IACd;IAEA,mBAAkC;QAChC,OAAO,IAAA,EAAK,aAAA;IACd;IAEA,oBAAoC;QAClC,OAAO,IAAA,EAAK,cAAA;IACd;IAEA,kBAAkB,OAAA,EAA+B;QAC/C,IAAA,EAAK,cAAA,GAAkB;IACzB;IAEA,iBAME,QAAA,EACA,OAAA,EAMM;QACN,IAAA,EAAK,aAAA,CAAe,GAAA,KAAI,kLAAA,EAAQ,QAAQ,GAAG;YACzC;YACA,gBAAgB;QAClB,CAAC;IACH;IAEA,iBACE,QAAA,EACsE;QACtE,MAAM,WAAW,CAAC;eAAG,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,CAAC;SAAA;QAEjD,MAAM,SAGF,CAAC;QAEL,SAAS,OAAA,CAAQ,CAAC,iBAAiB;YACjC,gLAAI,kBAAA,EAAgB,UAAU,aAAa,QAAQ,GAAG;gBACpD,OAAO,MAAA,CAAO,QAAQ,aAAa,cAAc;YACnD;QACF,CAAC;QACD,OAAO;IACT;IAEA,oBAME,WAAA,EACA,OAAA,EAIM;QACN,IAAA,EAAK,gBAAA,CAAkB,GAAA,KAAI,kLAAA,EAAQ,WAAW,GAAG;YAC/C;YACA,gBAAgB;QAClB,CAAC;IACH;IAEA,oBACE,WAAA,EACuE;QACvE,MAAM,WAAW,CAAC;eAAG,IAAA,EAAK,gBAAA,CAAkB,MAAA,CAAO,CAAC;SAAA;QAEpD,MAAM,SAGF,CAAC;QAEL,SAAS,OAAA,CAAQ,CAAC,iBAAiB;YACjC,IAAI,8LAAA,EAAgB,aAAa,aAAa,WAAW,GAAG;gBAC1D,OAAO,MAAA,CAAO,QAAQ,aAAa,cAAc;YACnD;QACF,CAAC;QAED,OAAO;IACT;IAEA,oBAQE,OAAA,EAsBA;QACA,IAAI,QAAQ,UAAA,EAAY;YACtB,OAAO;QAOT;QAEA,MAAM,mBAAmB;YACvB,GAAG,IAAA,EAAK,cAAA,CAAgB,OAAA;YACxB,GAAG,IAAA,CAAK,gBAAA,CAAiB,QAAQ,QAAQ,CAAA;YACzC,GAAG,OAAA;YACH,YAAY;QACd;QAEA,IAAI,CAAC,iBAAiB,SAAA,EAAW;YAC/B,iBAAiB,SAAA,+KAAY,wBAAA,EAC3B,iBAAiB,QAAA,EACjB;QAEJ;QAGA,IAAI,iBAAiB,kBAAA,KAAuB,KAAA,GAAW;YACrD,iBAAiB,kBAAA,GACf,iBAAiB,WAAA,KAAgB;QACrC;QACA,IAAI,iBAAiB,YAAA,KAAiB,KAAA,GAAW;YAC/C,iBAAiB,YAAA,GAAe,CAAC,CAAC,iBAAiB,QAAA;QACrD;QAEA,IAAI,CAAC,iBAAiB,WAAA,IAAe,iBAAiB,SAAA,EAAW;YAC/D,iBAAiB,WAAA,GAAc;QACjC;QAEA,IAAI,iBAAiB,OAAA,6KAAY,YAAA,EAAW;YAC1C,iBAAiB,OAAA,GAAU;QAC7B;QAEA,OAAO;IAOT;IAEA,uBACE,OAAA,EACG;QACH,IAAI,SAAS,YAAY;YACvB,OAAO;QACT;QACA,OAAO;YACL,GAAG,IAAA,EAAK,cAAA,CAAgB,SAAA;YACxB,GAAI,SAAS,eACX,IAAA,CAAK,mBAAA,CAAoB,QAAQ,WAAW,CAAA;YAC9C,GAAG,OAAA;YACH,YAAY;QACd;IACF;IAEA,QAAc;QACZ,IAAA,EAAK,UAAA,CAAY,KAAA,CAAM;QACvB,IAAA,EAAK,aAAA,CAAe,KAAA,CAAM;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/queryObserver.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { notifyManager } from './notifyManager'\nimport { fetchState } from './query'\nimport { Subscribable } from './subscribable'\nimport { pendingThenable } from './thenable'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport type { FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { PendingThenable, Thenable } from './thenable'\nimport type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\ninterface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  #client: QueryClient\n  #currentQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> = undefined!\n  #currentQueryInitialState: QueryState<TQueryData, TError> = undefined!\n  #currentResult: QueryObserverResult<TData, TError> = undefined!\n  #currentResultState?: QueryState<TQueryData, TError>\n  #currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  #currentThenable: Thenable<TData>\n  #selectError: TError | null\n  #selectFn?: (data: TQueryData) => TData\n  #selectResult?: TData\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData?: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  #staleTimeoutId?: ReturnType<typeof setTimeout>\n  #refetchIntervalId?: ReturnType<typeof setInterval>\n  #currentRefetchInterval?: number | false\n  #trackedProps = new Set<keyof QueryObserverResult>()\n\n  constructor(\n    client: QueryClient,\n    public options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.#client = client\n    this.#selectError = null\n    this.#currentThenable = pendingThenable()\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error('experimental_prefetchInRender feature flag is not enabled'),\n      )\n    }\n\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch()\n      } else {\n        this.updateResult()\n      }\n\n      this.#updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#clearStaleTimeout()\n    this.#clearRefetchInterval()\n    this.#currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.#currentQuery\n\n    this.options = this.#client.defaultQueryOptions(options)\n\n    if (\n      this.options.enabled !== undefined &&\n      typeof this.options.enabled !== 'boolean' &&\n      typeof this.options.enabled !== 'function' &&\n      typeof resolveEnabled(this.options.enabled, this.#currentQuery) !==\n        'boolean'\n    ) {\n      throw new Error(\n        'Expected enabled to be a boolean or a callback that returns a boolean',\n      )\n    }\n\n    this.#updateQuery()\n    this.#currentQuery.setOptions(this.options)\n\n    if (\n      prevOptions._defaulted &&\n      !shallowEqualObjects(this.options, prevOptions)\n    ) {\n      this.#client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.#currentQuery,\n        observer: this,\n      })\n    }\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.#currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.#executeFetch()\n    }\n\n    // Update result\n    this.updateResult()\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        resolveStaleTime(this.options.staleTime, this.#currentQuery) !==\n          resolveStaleTime(prevOptions.staleTime, this.#currentQuery))\n    ) {\n      this.#updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.#computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        nextRefetchInterval !== this.#currentRefetchInterval)\n    ) {\n      this.#updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.#client.getQueryCache().build(this.#client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult every time\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.#currentResult = result\n      this.#currentResultOptions = this.options\n      this.#currentResultState = this.#currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.#currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    onPropTracked?: (key: keyof QueryObserverResult) => void,\n  ): QueryObserverResult<TData, TError> {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key as keyof QueryObserverResult)\n        onPropTracked?.(key as keyof QueryObserverResult)\n        return Reflect.get(target, key)\n      },\n    })\n  }\n\n  trackProp(key: keyof QueryObserverResult) {\n    this.#trackedProps.add(key)\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.#currentQuery\n  }\n\n  refetch({ ...options }: RefetchOptions = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.#client.defaultQueryOptions(options)\n\n    const query = this.#client\n      .getQueryCache()\n      .build(this.#client, defaultedOptions)\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.#currentResult\n    })\n  }\n\n  #executeFetch(\n    fetchOptions?: Omit<ObserverFetchOptions, 'initialPromise'>,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.#updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.#currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  #updateStaleTimeout(): void {\n    this.#clearStaleTimeout()\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery,\n    )\n\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return\n    }\n\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime)\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  #computeRefetchInterval() {\n    return (\n      (typeof this.options.refetchInterval === 'function'\n        ? this.options.refetchInterval(this.#currentQuery)\n        : this.options.refetchInterval) ?? false\n    )\n  }\n\n  #updateRefetchInterval(nextInterval: number | false): void {\n    this.#clearRefetchInterval()\n\n    this.#currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      resolveEnabled(this.options.enabled, this.#currentQuery) === false ||\n      !isValidTimeout(this.#currentRefetchInterval) ||\n      this.#currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.#refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.#executeFetch()\n      }\n    }, this.#currentRefetchInterval)\n  }\n\n  #updateTimers(): void {\n    this.#updateStaleTimeout()\n    this.#updateRefetchInterval(this.#computeRefetchInterval())\n  }\n\n  #clearStaleTimeout(): void {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId)\n      this.#staleTimeoutId = undefined\n    }\n  }\n\n  #clearRefetchInterval(): void {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId)\n      this.#refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.#currentQuery\n    const prevOptions = this.options\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.#currentResultState\n    const prevResultOptions = this.#currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.#currentQueryInitialState\n\n    const { state } = query\n    let newState = { ...state }\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options),\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        newState.fetchStatus = 'idle'\n      }\n    }\n\n    let { error, errorUpdatedAt, status } = newState\n\n    // Per default, use query data\n    data = newState.data as unknown as TData\n    let skipSelect = false\n\n    // use placeholderData if needed\n    if (\n      options.placeholderData !== undefined &&\n      data === undefined &&\n      status === 'pending'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n        // we have to skip select when reading this memoization\n        // because prevResult.data is already \"selected\"\n        skipSelect = true\n      } else {\n        // compute placeholderData\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (\n                options.placeholderData as unknown as PlaceholderDataFunction<TQueryData>\n              )(\n                this.#lastQueryWithDefinedData?.state.data,\n                this.#lastQueryWithDefinedData as any,\n              )\n            : options.placeholderData\n      }\n\n      if (placeholderData !== undefined) {\n        status = 'success'\n        data = replaceData(\n          prevResult?.data,\n          placeholderData as unknown,\n          options,\n        ) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    // Select data if needed\n    // this also runs placeholderData through the select function\n    if (options.select && data !== undefined && !skipSelect) {\n      // Memoize select result\n      if (\n        prevResult &&\n        data === prevResultState?.data &&\n        options.select === this.#selectFn\n      ) {\n        data = this.#selectResult\n      } else {\n        try {\n          this.#selectFn = options.select\n          data = options.select(data as any)\n          data = replaceData(prevResult?.data, data, options)\n          this.#selectResult = data\n          this.#selectError = null\n        } catch (selectError) {\n          this.#selectError = selectError as TError\n        }\n      }\n    }\n\n    if (this.#selectError) {\n      error = this.#selectError as any\n      data = this.#selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = newState.fetchStatus === 'fetching'\n    const isPending = status === 'pending'\n    const isError = status === 'error'\n\n    const isLoading = isPending && isFetching\n    const hasData = data !== undefined\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        newState.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === 'paused',\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n    }\n\n    const nextResult = result as QueryObserverResult<TData, TError>\n\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable: PendingThenable<TData>) => {\n        if (nextResult.status === 'error') {\n          thenable.reject(nextResult.error)\n        } else if (nextResult.data !== undefined) {\n          thenable.resolve(nextResult.data)\n        }\n      }\n\n      /**\n       * Create a new thenable and result promise when the results have changed\n       */\n      const recreateThenable = () => {\n        const pending =\n          (this.#currentThenable =\n          nextResult.promise =\n            pendingThenable())\n\n        finalizeThenableIfPossible(pending)\n      }\n\n      const prevThenable = this.#currentThenable\n      switch (prevThenable.status) {\n        case 'pending':\n          // Finalize the previous thenable if it was pending\n          // and we are still observing the same query\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable)\n          }\n          break\n        case 'fulfilled':\n          if (\n            nextResult.status === 'error' ||\n            nextResult.data !== prevThenable.value\n          ) {\n            recreateThenable()\n          }\n          break\n        case 'rejected':\n          if (\n            nextResult.status !== 'error' ||\n            nextResult.error !== prevThenable.reason\n          ) {\n            recreateThenable()\n          }\n          break\n      }\n    }\n\n    return nextResult\n  }\n\n  updateResult(): void {\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.#currentQuery, this.options)\n\n    this.#currentResultState = this.#currentQuery.state\n    this.#currentResultOptions = this.options\n\n    if (this.#currentResultState.data !== undefined) {\n      this.#lastQueryWithDefinedData = this.#currentQuery\n    }\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.#currentResult = nextResult\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.#trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps,\n      )\n\n      if (this.options.throwOnError) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey]\n\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    this.#notify({ listeners: shouldNotifyListeners() })\n  }\n\n  #updateQuery(): void {\n    const query = this.#client.getQueryCache().build(this.#client, this.options)\n\n    if (query === this.#currentQuery) {\n      return\n    }\n\n    const prevQuery = this.#currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.#currentQuery = query\n    this.#currentQueryInitialState = query.state\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(): void {\n    this.updateResult()\n\n    if (this.hasListeners()) {\n      this.#updateTimers()\n    }\n  }\n\n  #notify(notifyOptions: { listeners: boolean }): void {\n    notifyManager.batch(() => {\n      // First, trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: 'observerResultsUpdated',\n      })\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.state.data === undefined &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.data !== undefined &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: (typeof options)['refetchOnMount'] &\n    (typeof options)['refetchOnWindowFocus'] &\n    (typeof options)['refetchOnReconnect'],\n) {\n  if (\n    resolveEnabled(options.enabled, query) !== false &&\n    resolveStaleTime(options.staleTime, query) !== 'static'\n  ) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    (query !== prevQuery ||\n      resolveEnabled(prevOptions.enabled, query) === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.isStaleByTime(resolveStaleTime(options.staleTime, query))\n  )\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n) {\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B,SAAS,uBAAuB;AAChC;;;;;;;AAiCO,IAAM,gBAAN,6LAMG,eAAA,CAAmD;IAyB3D,YACE,MAAA,EACO,OAAA,CAOP;QACA,KAAA,CAAM;QARC,IAAA,CAAA,OAAA,GAAA;QAUP,IAAA,EAAK,MAAA,GAAU;QACf,IAAA,EAAK,WAAA,GAAe;QACpB,IAAA,EAAK,eAAA,GAAmB,iMAAA,CAAgB;QACxC,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,6BAAA,EAA+B;YAC/C,IAAA,EAAK,eAAA,CAAiB,MAAA,CACpB,IAAI,MAAM,2DAA2D;QAEzE;QAEA,IAAA,CAAK,WAAA,CAAY;QACjB,IAAA,CAAK,UAAA,CAAW,OAAO;IACzB;IA/CA,OAAA,CAAA;KACA,YAAA,GAAoE,KAAA,EAAA;KACpE,wBAAA,GAA4D,KAAA,EAAA;KAC5D,aAAA,GAAqD,KAAA,EAAA;KACrD,kBAAA,CAAA;KACA,oBAAA,CAAA;IAOA,gBAAA,CAAA;KACA,WAAA,CAAA;KACA,QAAA,CAAA;KACA,YAAA,CAAA;IAAA,iEAAA;IAAA,mGAAA;KAGA,wBAAA,CAAA;KACA,cAAA,CAAA;KACA,iBAAA,CAAA;KACA,sBAAA,CAAA;KACA,YAAA,GAAgB,aAAA,GAAA,IAAI,IAA+B,EAAA;IA2BzC,cAAoB;QAC5B,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAI;IACvC;IAEU,cAAoB;QAC5B,IAAI,IAAA,CAAK,SAAA,CAAU,IAAA,KAAS,GAAG;YAC7B,IAAA,EAAK,YAAA,CAAc,WAAA,CAAY,IAAI;YAEnC,IAAI,mBAAmB,IAAA,EAAK,YAAA,EAAe,IAAA,CAAK,OAAO,GAAG;gBACxD,IAAA,EAAK,YAAA,CAAc;YACrB,OAAO;gBACL,IAAA,CAAK,YAAA,CAAa;YACpB;YAEA,IAAA,CAAK,aAAA,CAAc;QACrB;IACF;IAEU,gBAAsB;QAC9B,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,CAAK,OAAA,CAAQ;QACf;IACF;IAEA,yBAAkC;QAChC,OAAO,cACL,IAAA,CAAK,aAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,CAAQ,kBAAA;IAEjB;IAEA,2BAAoC;QAClC,OAAO,cACL,IAAA,EAAK,YAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,CAAQ,oBAAA;IAEjB;IAEA,UAAgB;QACd,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;QACzB,IAAA,EAAK,iBAAA,CAAmB;QACxB,IAAA,EAAK,oBAAA,CAAsB;QAC3B,IAAA,EAAK,YAAA,CAAc,cAAA,CAAe,IAAI;IACxC;IAEA,WACE,OAAA,EAOM;QACN,MAAM,cAAc,IAAA,CAAK,OAAA;QACzB,MAAM,YAAY,IAAA,EAAK,YAAA;QAEvB,IAAA,CAAK,OAAA,GAAU,IAAA,EAAK,MAAA,CAAQ,mBAAA,CAAoB,OAAO;QAEvD,IACE,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,KAAA,KACzB,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,aAChC,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,cAChC,mLAAO,iBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,MAC5D,WACF;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,IAAA,EAAK,WAAA,CAAa;QAClB,IAAA,CAAK,aAAA,CAAc,UAAA,CAAW,IAAA,CAAK,OAAO;QAE1C,IACE,YAAY,UAAA,IACZ,CAAC,kMAAA,EAAoB,IAAA,CAAK,OAAA,EAAS,WAAW,GAC9C;YACA,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,MAAA,CAAO;gBAClC,MAAM;gBACN,OAAO,IAAA,EAAK,YAAA;gBACZ,UAAU,IAAA;YACZ,CAAC;QACH;QAEA,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa;QAGlC,IACE,WACA,sBACE,IAAA,EAAK,YAAA,EACL,WACA,IAAA,CAAK,OAAA,EACL,cAEF;YACA,IAAA,EAAK,YAAA,CAAc;QACrB;QAGA,IAAA,CAAK,YAAA,CAAa;QAGlB,IACE,WAAA,CACC,IAAA,EAAK,YAAA,KAAkB,wLACtB,kBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,kLACrD,iBAAA,EAAe,YAAY,OAAA,EAAS,IAAA,EAAK,YAAa,iLACxD,mBAAA,EAAiB,IAAA,CAAK,OAAA,CAAQ,SAAA,EAAW,IAAA,EAAK,YAAa,kLACzD,mBAAA,EAAiB,YAAY,SAAA,EAAW,IAAA,EAAK,YAAa,CAAA,GAC9D;YACA,IAAA,EAAK,kBAAA,CAAoB;QAC3B;QAEA,MAAM,sBAAsB,IAAA,EAAK,sBAAA,CAAwB;QAGzD,IACE,WAAA,CACC,IAAA,EAAK,YAAA,KAAkB,aACtB,6LAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,iLACrD,kBAAA,EAAe,YAAY,OAAA,EAAS,IAAA,EAAK,YAAa,KACxD,wBAAwB,IAAA,EAAK,sBAAA,GAC/B;YACA,IAAA,EAAK,qBAAA,CAAuB,mBAAmB;QACjD;IACF;IAEA,oBACE,OAAA,EAOoC;QACpC,MAAM,QAAQ,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,OAAO;QAEtE,MAAM,SAAS,IAAA,CAAK,YAAA,CAAa,OAAO,OAAO;QAE/C,IAAI,sCAAsC,IAAA,EAAM,MAAM,GAAG;YAiBvD,IAAA,EAAK,aAAA,GAAiB;YACtB,IAAA,EAAK,oBAAA,GAAwB,IAAA,CAAK,OAAA;YAClC,IAAA,EAAK,kBAAA,GAAsB,IAAA,EAAK,YAAA,CAAc,KAAA;QAChD;QACA,OAAO;IACT;IAEA,mBAAuD;QACrD,OAAO,IAAA,EAAK,aAAA;IACd;IAEA,YACE,MAAA,EACA,aAAA,EACoC;QACpC,OAAO,IAAI,MAAM,QAAQ;YACvB,KAAK,CAAC,QAAQ,QAAQ;gBACpB,IAAA,CAAK,SAAA,CAAU,GAAgC;gBAC/C,gBAAgB,GAAgC;gBAChD,OAAO,QAAQ,GAAA,CAAI,QAAQ,GAAG;YAChC;QACF,CAAC;IACH;IAEA,UAAU,GAAA,EAAgC;QACxC,IAAA,EAAK,YAAA,CAAc,GAAA,CAAI,GAAG;IAC5B;IAEA,kBAAsE;QACpE,OAAO,IAAA,EAAK,YAAA;IACd;IAEA,QAAQ,EAAE,GAAG,QAAQ,CAAA,GAAoB,CAAC,CAAA,EAExC;QACA,OAAO,IAAA,CAAK,KAAA,CAAM;YAChB,GAAG,OAAA;QACL,CAAC;IACH;IAEA,gBACE,OAAA,EAO6C;QAC7C,MAAM,mBAAmB,IAAA,EAAK,MAAA,CAAQ,mBAAA,CAAoB,OAAO;QAEjE,MAAM,QAAQ,IAAA,EAAK,MAAA,CAChB,aAAA,CAAc,EACd,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,gBAAgB;QAEvC,OAAO,MAAM,KAAA,CAAM,EAAE,IAAA,CAAK,IAAM,IAAA,CAAK,YAAA,CAAa,OAAO,gBAAgB,CAAC;IAC5E;IAEU,MACR,YAAA,EAC6C;QAC7C,OAAO,IAAA,EAAK,YAAA,CAAc;YACxB,GAAG,YAAA;YACH,eAAe,aAAa,aAAA,IAAiB;QAC/C,CAAC,EAAE,IAAA,CAAK,MAAM;YACZ,IAAA,CAAK,YAAA,CAAa;YAClB,OAAO,IAAA,EAAK,aAAA;QACd,CAAC;IACH;KAEA,YAAA,CACE,YAAA,EACiC;QAEjC,IAAA,CAAK,YAAA,CAAa;QAGlB,IAAI,UAA2C,IAAA,EAAK,YAAA,CAAc,KAAA,CAChE,IAAA,CAAK,OAAA,EACL;QAGF,IAAI,CAAC,cAAc,cAAc;YAC/B,UAAU,QAAQ,KAAA,yKAAM,OAAI;QAC9B;QAEA,OAAO;IACT;KAEA,kBAAA,GAA4B;QAC1B,IAAA,CAAK,kBAAA,CAAmB;QACxB,MAAM,wLAAY,mBAAA,EAChB,IAAA,CAAK,OAAA,CAAQ,SAAA,EACb,IAAA,EAAK,YAAA;QAGP,4KAAI,WAAA,IAAY,IAAA,EAAK,aAAA,CAAe,OAAA,IAAW,KAAC,yLAAA,EAAe,SAAS,GAAG;YACzE;QACF;QAEA,MAAM,mLAAO,iBAAA,EAAe,IAAA,EAAK,aAAA,CAAe,aAAA,EAAe,SAAS;QAIxE,MAAM,UAAU,OAAO;QAEvB,IAAA,EAAK,cAAA,GAAkB,WAAW,MAAM;YACtC,IAAI,CAAC,IAAA,CAAK,cAAA,CAAe,OAAA,EAAS;gBAChC,IAAA,CAAK,YAAA,CAAa;YACpB;QACF,GAAG,OAAO;IACZ;KAEA,sBAAA,GAA0B;QACxB,OAAA,CACG,OAAO,IAAA,CAAK,OAAA,CAAQ,eAAA,KAAoB,aACrC,IAAA,CAAK,OAAA,CAAQ,eAAA,CAAgB,IAAA,CAAK,aAAa,IAC/C,IAAA,CAAK,OAAA,CAAQ,eAAA,KAAoB;IAEzC;KAEA,qBAAA,CAAuB,YAAA,EAAoC;QACzD,IAAA,EAAK,oBAAA,CAAsB;QAE3B,IAAA,CAAK,uBAAA,GAA0B;QAE/B,4KACE,WAAA,gLACA,iBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,MAAM,SAC7D,6KAAC,iBAAA,EAAe,IAAA,EAAK,sBAAuB,KAC5C,IAAA,CAAK,uBAAA,KAA4B,GACjC;YACA;QACF;QAEA,IAAA,EAAK,iBAAA,GAAqB,YAAY,MAAM;YAC1C,IACE,IAAA,CAAK,OAAA,CAAQ,2BAAA,mLACb,eAAA,CAAa,SAAA,CAAU,GACvB;gBACA,IAAA,EAAK,YAAA,CAAc;YACrB;QACF,GAAG,IAAA,EAAK,sBAAuB;IACjC;KAEA,YAAA,GAAsB;QACpB,IAAA,CAAK,mBAAA,CAAoB;QACzB,IAAA,EAAK,qBAAA,CAAuB,IAAA,EAAK,sBAAA,CAAwB,CAAC;IAC5D;KAEA,iBAAA,GAA2B;QACzB,IAAI,IAAA,CAAK,eAAA,EAAiB;YACxB,aAAa,IAAA,EAAK,cAAe;YACjC,IAAA,EAAK,cAAA,GAAkB,KAAA;QACzB;IACF;KAEA,oBAAA,GAA8B;QAC5B,IAAI,IAAA,EAAK,iBAAA,EAAoB;YAC3B,cAAc,IAAA,EAAK,iBAAkB;YACrC,IAAA,EAAK,iBAAA,GAAqB,KAAA;QAC5B;IACF;IAEU,aACR,KAAA,EACA,OAAA,EAOoC;QACpC,MAAM,YAAY,IAAA,EAAK,YAAA;QACvB,MAAM,cAAc,IAAA,CAAK,OAAA;QACzB,MAAM,aAAa,IAAA,EAAK,aAAA;QAGxB,MAAM,kBAAkB,IAAA,CAAK,mBAAA;QAC7B,MAAM,oBAAoB,IAAA,EAAK,oBAAA;QAC/B,MAAM,cAAc,UAAU;QAC9B,MAAM,oBAAoB,cACtB,MAAM,KAAA,GACN,IAAA,EAAK,wBAAA;QAET,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI;QAClB,IAAI,WAAW;YAAE,GAAG,KAAA;QAAM;QAC1B,IAAI,oBAAoB;QACxB,IAAI;QAGJ,IAAI,QAAQ,kBAAA,EAAoB;YAC9B,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa;YAElC,MAAM,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;YAElE,MAAM,kBACJ,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;YAEzE,IAAI,gBAAgB,iBAAiB;gBACnC,WAAW;oBACT,GAAG,QAAA;oBACH,+KAAG,aAAA,EAAW,MAAM,IAAA,EAAM,MAAM,OAAO,CAAA;gBACzC;YACF;YACA,IAAI,QAAQ,kBAAA,KAAuB,eAAe;gBAChD,SAAS,WAAA,GAAc;YACzB;QACF;QAEA,IAAI,EAAE,KAAA,EAAO,cAAA,EAAgB,MAAA,CAAO,CAAA,GAAI;QAGxC,OAAO,SAAS,IAAA;QAChB,IAAI,aAAa;QAGjB,IACE,QAAQ,eAAA,KAAoB,KAAA,KAC5B,SAAS,KAAA,KACT,WAAW,WACX;YACA,IAAI;YAGJ,IACE,YAAY,qBACZ,QAAQ,eAAA,KAAoB,mBAAmB,iBAC/C;gBACA,kBAAkB,WAAW,IAAA;gBAG7B,aAAa;YACf,OAAO;gBAEL,kBACE,OAAO,QAAQ,eAAA,KAAoB,aAE7B,QAAQ,eAAA,CAER,IAAA,EAAK,wBAAA,EAA2B,MAAM,MACtC,IAAA,CAAK,yBAAA,IAEP,QAAQ,eAAA;YAChB;YAEA,IAAI,oBAAoB,KAAA,GAAW;gBACjC,SAAS;gBACT,mLAAO,cAAA,EACL,YAAY,MACZ,iBACA;gBAEF,oBAAoB;YACtB;QACF;QAIA,IAAI,QAAQ,MAAA,IAAU,SAAS,KAAA,KAAa,CAAC,YAAY;YAEvD,IACE,cACA,SAAS,iBAAiB,QAC1B,QAAQ,MAAA,KAAW,IAAA,EAAK,QAAA,EACxB;gBACA,OAAO,IAAA,EAAK,YAAA;YACd,OAAO;gBACL,IAAI;oBACF,IAAA,EAAK,QAAA,GAAY,QAAQ,MAAA;oBACzB,OAAO,QAAQ,MAAA,CAAO,IAAW;oBACjC,mLAAO,cAAA,EAAY,YAAY,MAAM,MAAM,OAAO;oBAClD,IAAA,CAAK,aAAA,GAAgB;oBACrB,IAAA,EAAK,WAAA,GAAe;gBACtB,EAAA,OAAS,aAAa;oBACpB,IAAA,EAAK,WAAA,GAAe;gBACtB;YACF;QACF;QAEA,IAAI,IAAA,EAAK,WAAA,EAAc;YACrB,QAAQ,IAAA,CAAK,YAAA;YACb,OAAO,IAAA,EAAK,YAAA;YACZ,iBAAiB,KAAK,GAAA,CAAI;YAC1B,SAAS;QACX;QAEA,MAAM,aAAa,SAAS,WAAA,KAAgB;QAC5C,MAAM,YAAY,WAAW;QAC7B,MAAM,UAAU,WAAW;QAE3B,MAAM,YAAY,aAAa;QAC/B,MAAM,UAAU,SAAS,KAAA;QAEzB,MAAM,SAAiD;YACrD;YACA,aAAa,SAAS,WAAA;YACtB;YACA,WAAW,WAAW;YACtB;YACA,kBAAkB;YAClB;YACA;YACA,eAAe,SAAS,aAAA;YACxB;YACA;YACA,cAAc,SAAS,iBAAA;YACvB,eAAe,SAAS,kBAAA;YACxB,kBAAkB,SAAS,gBAAA;YAC3B,WAAW,SAAS,eAAA,GAAkB,KAAK,SAAS,gBAAA,GAAmB;YACvE,qBACE,SAAS,eAAA,GAAkB,kBAAkB,eAAA,IAC7C,SAAS,gBAAA,GAAmB,kBAAkB,gBAAA;YAChD;YACA,cAAc,cAAc,CAAC;YAC7B,gBAAgB,WAAW,CAAC;YAC5B,UAAU,SAAS,WAAA,KAAgB;YACnC;YACA,gBAAgB,WAAW;YAC3B,SAAS,QAAQ,OAAO,OAAO;YAC/B,SAAS,IAAA,CAAK,OAAA;YACd,SAAS,IAAA,EAAK,eAAA;QAChB;QAEA,MAAM,aAAa;QAEnB,IAAI,IAAA,CAAK,OAAA,CAAQ,6BAAA,EAA+B;YAC9C,MAAM,6BAA6B,CAAC,aAAqC;gBACvE,IAAI,WAAW,MAAA,KAAW,SAAS;oBACjC,SAAS,MAAA,CAAO,WAAW,KAAK;gBAClC,OAAA,IAAW,WAAW,IAAA,KAAS,KAAA,GAAW;oBACxC,SAAS,OAAA,CAAQ,WAAW,IAAI;gBAClC;YACF;YAKA,MAAM,mBAAmB,MAAM;gBAC7B,MAAM,UACH,IAAA,CAAK,gBAAA,GACN,WAAW,OAAA,kLACT,kBAAA,CAAgB;gBAEpB,2BAA2B,OAAO;YACpC;YAEA,MAAM,eAAe,IAAA,EAAK,eAAA;YAC1B,OAAQ,aAAa,MAAA,EAAQ;gBAC3B,KAAK;oBAGH,IAAI,MAAM,SAAA,KAAc,UAAU,SAAA,EAAW;wBAC3C,2BAA2B,YAAY;oBACzC;oBACA;gBACF,KAAK;oBACH,IACE,WAAW,MAAA,KAAW,WACtB,WAAW,IAAA,KAAS,aAAa,KAAA,EACjC;wBACA,iBAAiB;oBACnB;oBACA;gBACF,KAAK;oBACH,IACE,WAAW,MAAA,KAAW,WACtB,WAAW,KAAA,KAAU,aAAa,MAAA,EAClC;wBACA,iBAAiB;oBACnB;oBACA;YACJ;QACF;QAEA,OAAO;IACT;IAEA,eAAqB;QACnB,MAAM,aAAa,IAAA,EAAK,aAAA;QAIxB,MAAM,aAAa,IAAA,CAAK,YAAA,CAAa,IAAA,EAAK,YAAA,EAAe,IAAA,CAAK,OAAO;QAErE,IAAA,EAAK,kBAAA,GAAsB,IAAA,EAAK,YAAA,CAAc,KAAA;QAC9C,IAAA,EAAK,oBAAA,GAAwB,IAAA,CAAK,OAAA;QAElC,IAAI,IAAA,EAAK,kBAAA,CAAoB,IAAA,KAAS,KAAA,GAAW;YAC/C,IAAA,EAAK,wBAAA,GAA4B,IAAA,EAAK,YAAA;QACxC;QAGA,gLAAI,sBAAA,EAAoB,YAAY,UAAU,GAAG;YAC/C;QACF;QAEA,IAAA,EAAK,aAAA,GAAiB;QAEtB,MAAM,wBAAwB,MAAe;YAC3C,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YAEA,MAAM,EAAE,mBAAA,CAAoB,CAAA,GAAI,IAAA,CAAK,OAAA;YACrC,MAAM,2BACJ,OAAO,wBAAwB,aAC3B,oBAAoB,IACpB;YAEN,IACE,6BAA6B,SAC5B,CAAC,4BAA4B,CAAC,IAAA,EAAK,YAAA,CAAc,IAAA,EAClD;gBACA,OAAO;YACT;YAEA,MAAM,gBAAgB,IAAI,IACxB,4BAA4B,IAAA,EAAK,YAAA;YAGnC,IAAI,IAAA,CAAK,OAAA,CAAQ,YAAA,EAAc;gBAC7B,cAAc,GAAA,CAAI,OAAO;YAC3B;YAEA,OAAO,OAAO,IAAA,CAAK,IAAA,EAAK,aAAc,EAAE,IAAA,CAAK,CAAC,QAAQ;gBACpD,MAAM,WAAW;gBACjB,MAAM,UAAU,IAAA,EAAK,aAAA,CAAe,QAAQ,CAAA,KAAM,UAAA,CAAW,QAAQ,CAAA;gBAErE,OAAO,WAAW,cAAc,GAAA,CAAI,QAAQ;YAC9C,CAAC;QACH;QAEA,IAAA,EAAK,MAAA,CAAQ;YAAE,WAAW,sBAAsB;QAAE,CAAC;IACrD;KAEA,WAAA,GAAqB;QACnB,MAAM,QAAQ,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,OAAO;QAE3E,IAAI,UAAU,IAAA,EAAK,YAAA,EAAe;YAChC;QACF;QAEA,MAAM,YAAY,IAAA,EAAK,YAAA;QAGvB,IAAA,EAAK,YAAA,GAAgB;QACrB,IAAA,EAAK,wBAAA,GAA4B,MAAM,KAAA;QAEvC,IAAI,IAAA,CAAK,YAAA,CAAa,GAAG;YACvB,WAAW,eAAe,IAAI;YAC9B,MAAM,WAAA,CAAY,IAAI;QACxB;IACF;IAEA,gBAAsB;QACpB,IAAA,CAAK,YAAA,CAAa;QAElB,IAAI,IAAA,CAAK,YAAA,CAAa,GAAG;YACvB,IAAA,EAAK,YAAA,CAAc;QACrB;IACF;KAEA,MAAA,CAAQ,aAAA,EAA6C;QACnD,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YAExB,IAAI,cAAc,SAAA,EAAW;gBAC3B,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;oBACnC,SAAS,IAAA,CAAK,cAAc;gBAC9B,CAAC;YACH;YAGA,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,MAAA,CAAO;gBAClC,OAAO,IAAA,EAAK,YAAA;gBACZ,MAAM;YACR,CAAC;QACH,CAAC;IACH;AACF;AAEA,SAAS,kBACP,KAAA,EACA,OAAA,EACS;IACT,mLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA,KACrB,CAAA,CAAE,MAAM,KAAA,CAAM,MAAA,KAAW,WAAW,QAAQ,YAAA,KAAiB,KAAA;AAEjE;AAEA,SAAS,mBACP,KAAA,EACA,OAAA,EACS;IACT,OACE,kBAAkB,OAAO,OAAO,KAC/B,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA,KACpB,cAAc,OAAO,SAAS,QAAQ,cAAc;AAE1D;AAEA,SAAS,cACP,KAAA,EACA,OAAA,EACA,KAAA,EAGA;IACA,gLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,+LAAA,EAAiB,QAAQ,SAAA,EAAW,KAAK,MAAM,UAC/C;QACA,MAAM,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;QAE3D,OAAO,UAAU,YAAa,UAAU,SAAS,QAAQ,OAAO,OAAO;IACzE;IACA,OAAO;AACT;AAEA,SAAS,sBACP,KAAA,EACA,SAAA,EACA,OAAA,EACA,WAAA,EACS;IACT,OAAA,CACG,UAAU,cACT,4LAAA,EAAe,YAAY,OAAA,EAAS,KAAK,MAAM,KAAA,KAAA,CAChD,CAAC,QAAQ,QAAA,IAAY,MAAM,KAAA,CAAM,MAAA,KAAW,OAAA,KAC7C,QAAQ,OAAO,OAAO;AAE1B;AAEA,SAAS,QACP,KAAA,EACA,OAAA,EACS;IACT,mLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,MAAM,aAAA,6KAAc,mBAAA,EAAiB,QAAQ,SAAA,EAAW,KAAK,CAAC;AAElE;AAIA,SAAS,sCAOP,QAAA,EACA,gBAAA,EACA;IAGA,IAAI,6KAAC,sBAAA,EAAoB,SAAS,gBAAA,CAAiB,GAAG,gBAAgB,GAAG;QACvE,OAAO;IACT;IAGA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40tanstack/query-core/src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { hashKey, shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options as\n      | MutationObserverOptions<TData, TError, TVariables, TContext>\n      | undefined\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n\n    if (\n      prevOptions?.mutationKey &&\n      this.options.mutationKey &&\n      hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)\n    ) {\n      this.reset()\n    } else if (this.#currentMutation?.state.status === 'pending') {\n      this.#currentMutation.setOptions(this.options)\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    // reset needs to remove the observer from the mutation because there is no way to \"get it back\"\n    // another mutate call will yield a new mutation!\n    this.#currentMutation?.removeObserver(this)\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables!\n        const context = this.#currentResult.context\n\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context!)\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context)\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(action.error, variables, context)\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            variables,\n            context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAuB;AAChC,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B,SAAS,SAAS,2BAA2B;;;;;AAkBtC,IAAM,mBAAN,6LAKG,eAAA,CAER;KAGA,MAAA,CAAA;KACA,aAAA,GACE,KAAA,EAAA;KACF,eAAA,CAAA;IACA,cAAA,CAAA;IAEA,YACE,MAAA,EACA,OAAA,CACA;QACA,KAAA,CAAM;QAEN,IAAA,EAAK,MAAA,GAAU;QACf,IAAA,CAAK,UAAA,CAAW,OAAO;QACvB,IAAA,CAAK,WAAA,CAAY;QACjB,IAAA,EAAK,YAAA,CAAc;IACrB;IAEU,cAAoB;QAC5B,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI;QACnC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI;IACnC;IAEA,WACE,OAAA,EACA;QACA,MAAM,cAAc,IAAA,CAAK,OAAA;QAGzB,IAAA,CAAK,OAAA,GAAU,IAAA,EAAK,MAAA,CAAQ,sBAAA,CAAuB,OAAO;QAC1D,IAAI,CAAC,kMAAA,EAAoB,IAAA,CAAK,OAAA,EAAS,WAAW,GAAG;YACnD,IAAA,EAAK,MAAA,CAAQ,gBAAA,CAAiB,EAAE,MAAA,CAAO;gBACrC,MAAM;gBACN,UAAU,IAAA,EAAK,eAAA;gBACf,UAAU,IAAA;YACZ,CAAC;QACH;QAEA,IACE,aAAa,eACb,IAAA,CAAK,OAAA,CAAQ,WAAA,gLACb,UAAA,EAAQ,YAAY,WAAW,MAAM,sLAAA,EAAQ,IAAA,CAAK,OAAA,CAAQ,WAAW,GACrE;YACA,IAAA,CAAK,KAAA,CAAM;QACb,OAAA,IAAW,IAAA,CAAK,gBAAA,EAAkB,MAAM,WAAW,WAAW;YAC5D,IAAA,EAAK,eAAA,CAAiB,UAAA,CAAW,IAAA,CAAK,OAAO;QAC/C;IACF;IAEU,gBAAsB;QAC9B,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,EAAK,eAAA,EAAkB,eAAe,IAAI;QAC5C;IACF;IAEA,iBAAiB,MAAA,EAA2D;QAC1E,IAAA,EAAK,YAAA,CAAc;QAEnB,IAAA,CAAK,OAAA,CAAQ,MAAM;IACrB;IAEA,mBAKE;QACA,OAAO,IAAA,EAAK,aAAA;IACd;IAEA,QAAc;QAGZ,IAAA,EAAK,eAAA,EAAkB,eAAe,IAAI;QAC1C,IAAA,EAAK,eAAA,GAAmB,KAAA;QACxB,IAAA,EAAK,YAAA,CAAc;QACnB,IAAA,EAAK,MAAA,CAAQ;IACf;IAEA,OACE,SAAA,EACA,OAAA,EACgB;QAChB,IAAA,EAAK,aAAA,GAAiB;QAEtB,IAAA,EAAK,eAAA,EAAkB,eAAe,IAAI;QAE1C,IAAA,EAAK,eAAA,GAAmB,IAAA,EAAK,MAAA,CAC1B,gBAAA,CAAiB,EACjB,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,OAAO;QAEnC,IAAA,EAAK,eAAA,CAAiB,WAAA,CAAY,IAAI;QAEtC,OAAO,IAAA,EAAK,eAAA,CAAiB,OAAA,CAAQ,SAAS;IAChD;KAEA,YAAA,GAAsB;QACpB,MAAM,QACJ,IAAA,EAAK,eAAA,EAAkB,wLACvB,kBAAA,CAAqD;QAEvD,IAAA,EAAK,aAAA,GAAiB;YACpB,GAAG,KAAA;YACH,WAAW,MAAM,MAAA,KAAW;YAC5B,WAAW,MAAM,MAAA,KAAW;YAC5B,SAAS,MAAM,MAAA,KAAW;YAC1B,QAAQ,MAAM,MAAA,KAAW;YACzB,QAAQ,IAAA,CAAK,MAAA;YACb,OAAO,IAAA,CAAK,KAAA;QACd;IACF;KAEA,MAAA,CAAQ,MAAA,EAA4D;QAClE,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YAExB,IAAI,IAAA,EAAK,aAAA,IAAkB,IAAA,CAAK,YAAA,CAAa,GAAG;gBAC9C,MAAM,YAAY,IAAA,EAAK,aAAA,CAAe,SAAA;gBACtC,MAAM,UAAU,IAAA,EAAK,aAAA,CAAe,OAAA;gBAEpC,IAAI,QAAQ,SAAS,WAAW;oBAC9B,IAAA,EAAK,aAAA,CAAe,SAAA,GAAY,OAAO,IAAA,EAAM,WAAW,OAAQ;oBAChE,IAAA,EAAK,aAAA,CAAe,SAAA,GAAY,OAAO,IAAA,EAAM,MAAM,WAAW,OAAO;gBACvE,OAAA,IAAW,QAAQ,SAAS,SAAS;oBACnC,IAAA,EAAK,aAAA,CAAe,OAAA,GAAU,OAAO,KAAA,EAAO,WAAW,OAAO;oBAC9D,IAAA,EAAK,aAAA,CAAe,SAAA,GAClB,KAAA,GACA,OAAO,KAAA,EACP,WACA;gBAEJ;YACF;YAGA,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,IAAA,EAAK,aAAc;YAC9B,CAAC;QACH,CAAC;IACH;AACF", "ignoreList": [0], "debugId": null}}]}