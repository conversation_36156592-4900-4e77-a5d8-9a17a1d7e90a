import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AppProviders } from "@/components/providers/app-providers";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI-fitness-singles - Smart Fitness Platform",
  description: "AI-powered fitness platform for singles. Create custom workout plans, access comprehensive exercise database, and track your fitness progress with detailed analytics.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className} antialiased`}
        suppressHydrationWarning={true}
      >
        <AppProviders>
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
