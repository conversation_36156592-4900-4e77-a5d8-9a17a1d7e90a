import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo/utils';
import { Breadcrumb } from '@/components/ui/breadcrumb';

export const metadata: Metadata = generatePageMetadata({
  title: "Dashboard - Your Fitness Command Center",
  description: "Access your personalized fitness dashboard with workout summaries, progress overview, upcoming sessions, and quick access to all your fitness tools.",
  path: "/dashboard",
  noIndex: true, // Dashboard is typically private content
});

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Dashboard', href: '/dashboard' }
  ];

  return (
    <div>
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      {children}
    </div>
  );
}
