"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => setIsOpen(!isOpen)

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Dumbbell className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900">Workout.cool</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/workouts"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Workouts
            </Link>
            <Link
              href="/exercises"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Exercises
            </Link>
            <Link
              href="/progress"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Progress
            </Link>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="outline" size="sm">
              Sign In
            </Button>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              Get Started
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleMenu}>
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t">
            <div className="flex flex-col space-y-4">
              <Link
                href="/workouts"
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <Dumbbell className="h-4 w-4" />
                <span>Workouts</span>
              </Link>
              <Link
                href="/exercises"
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <BookOpen className="h-4 w-4" />
                <span>Exercises</span>
              </Link>
              <Link
                href="/progress"
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <BarChart3 className="h-4 w-4" />
                <span>Progress</span>
              </Link>
              <div className="pt-4 border-t">
                <div className="flex flex-col space-y-2">
                  <Button variant="outline" className="justify-start">
                    Sign In
                  </Button>
                  <Button className="justify-start bg-blue-600 hover:bg-blue-700">
                    Get Started
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
