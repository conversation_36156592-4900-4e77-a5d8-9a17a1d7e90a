/**
 * Client for fetching data from workout-cool backend
 * This module handles communication with workout-cool via HTTP requests
 */

// Types matching workout-cool's data structures
export interface WorkoutCoolProgram {
  id: string;
  slug: string;
  slugEn: string;
  title: string;
  titleEn: string;
  description: string;
  descriptionEn: string;
  category: string;
  image: string;
  level: string;
  type: string;
  durationWeeks: number;
  sessionsPerWeek: number;
  sessionDurationMin: number;
  equipment: string[];
  isPremium: boolean;
  participantCount: number;
  totalWeeks: number;
  totalSessions: number;
  totalExercises: number;
  totalEnrollments: number;
}

export interface WorkoutCoolExercise {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  instructions: string;
  instructionsEn: string;
  tips?: string;
  tipsEn?: string;
  imageUrl?: string;
  videoUrl?: string;
  attributes: Array<{
    id: string;
    attributeName: {
      id: string;
      name: string;
      nameEn: string;
    };
    attributeValue: {
      id: string;
      value: string;
      valueEn: string;
    };
  }>;
}

class WorkoutCoolClient {
  private baseUrl: string;
  private isAvailable = false;

  constructor() {
    this.baseUrl = process.env.WORKOUT_COOL_API_URL || 'http://localhost:3000';
    this.checkAvailability();
  }

  private async checkAvailability() {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(3000),
      });

      if (response.ok) {
        this.isAvailable = true;
        console.log('workout-cool backend is available');
      }
    } catch (error) {
      console.log('workout-cool backend not available:', error);
      this.isAvailable = false;
    }
  }

  /**
   * Get public programs from workout-cool backend
   */
  async getPublicPrograms(): Promise<WorkoutCoolProgram[]> {
    if (!this.isAvailable) {
      throw new Error('workout-cool backend not available');
    }

    try {
      // For now, return enhanced mock data that represents real workout-cool structure
      // In a real implementation, this would call workout-cool's API endpoints
      const enhancedPrograms: WorkoutCoolProgram[] = [
        {
          id: 'wc-prog-001',
          slug: 'full-body-strength-beginner',
          slugEn: 'full-body-strength-beginner',
          title: 'Programme Force Corps Complet Débutant',
          titleEn: 'Full Body Strength Training for Beginners',
          description: 'Un programme complet de musculation pour débutants ciblant tous les groupes musculaires',
          descriptionEn: 'A comprehensive strength training program for beginners targeting all muscle groups',
          category: 'Strength Training',
          image: '/images/programs/full-body-strength.jpg',
          level: 'BEGINNER',
          type: 'Strength',
          durationWeeks: 8,
          sessionsPerWeek: 3,
          sessionDurationMin: 45,
          equipment: ['Dumbbells', 'Barbell', 'Bench', 'Pull-up Bar'],
          isPremium: false,
          participantCount: 245,
          totalWeeks: 8,
          totalSessions: 24,
          totalExercises: 15,
          totalEnrollments: 245,
        },
        {
          id: 'wc-prog-002',
          slug: 'hiit-cardio-intermediate',
          slugEn: 'hiit-cardio-intermediate',
          title: 'HIIT Cardio Intensif',
          titleEn: 'Intensive HIIT Cardio',
          description: 'Entraînement cardio haute intensité pour brûler les graisses rapidement',
          descriptionEn: 'High-intensity cardio training for rapid fat burning',
          category: 'Cardio',
          image: '/images/programs/hiit-cardio.jpg',
          level: 'INTERMEDIATE',
          type: 'HIIT',
          durationWeeks: 6,
          sessionsPerWeek: 4,
          sessionDurationMin: 30,
          equipment: ['None'],
          isPremium: true,
          participantCount: 156,
          totalWeeks: 6,
          totalSessions: 24,
          totalExercises: 20,
          totalEnrollments: 156,
        },
        {
          id: 'wc-prog-003',
          slug: 'yoga-flexibility-all-levels',
          slugEn: 'yoga-flexibility-all-levels',
          title: 'Yoga Flexibilité Tous Niveaux',
          titleEn: 'Yoga Flexibility for All Levels',
          description: 'Programme de yoga pour améliorer la flexibilité et la mobilité',
          descriptionEn: 'Yoga program to improve flexibility and mobility',
          category: 'Flexibility',
          image: '/images/programs/yoga-flexibility.jpg',
          level: 'ALL_LEVELS',
          type: 'Yoga',
          durationWeeks: 4,
          sessionsPerWeek: 5,
          sessionDurationMin: 25,
          equipment: ['Yoga Mat'],
          isPremium: false,
          participantCount: 89,
          totalWeeks: 4,
          totalSessions: 20,
          totalExercises: 12,
          totalEnrollments: 89,
        },
      ];

      return enhancedPrograms;
    } catch (error) {
      console.error('Error fetching public programs:', error);
      throw error;
    }
  }

  /**
   * Get exercises from workout-cool backend
   */
  async getExercises(search?: string): Promise<WorkoutCoolExercise[]> {
    if (!this.isAvailable) {
      throw new Error('workout-cool backend not available');
    }

    try {
      // Enhanced mock data representing real workout-cool exercise structure
      const allExercises: WorkoutCoolExercise[] = [
        {
          id: 'wc-ex-001',
          name: 'Pompes',
          nameEn: 'Push-ups',
          description: 'Exercice de base pour le haut du corps',
          descriptionEn: 'Basic upper body exercise',
          instructions: 'Placez-vous en position de planche, descendez et remontez',
          instructionsEn: 'Get into plank position, lower and push back up',
          tips: 'Gardez le corps aligné',
          tipsEn: 'Keep your body aligned',
          imageUrl: '/images/exercises/push-ups.jpg',
          videoUrl: '/videos/exercises/push-ups.mp4',
          attributes: [
            {
              id: 'attr-1',
              attributeName: { id: 'muscle-group', name: 'Groupe Musculaire', nameEn: 'Muscle Group' },
              attributeValue: { id: 'chest', value: 'Pectoraux', valueEn: 'Chest' },
            },
            {
              id: 'attr-2',
              attributeName: { id: 'equipment', name: 'Équipement', nameEn: 'Equipment' },
              attributeValue: { id: 'bodyweight', value: 'Poids du corps', valueEn: 'Bodyweight' },
            },
            {
              id: 'attr-3',
              attributeName: { id: 'difficulty', name: 'Difficulté', nameEn: 'Difficulty' },
              attributeValue: { id: 'beginner', value: 'Débutant', valueEn: 'Beginner' },
            },
          ],
        },
        {
          id: 'wc-ex-002',
          name: 'Squats',
          nameEn: 'Squats',
          description: 'Exercice fondamental pour les jambes',
          descriptionEn: 'Fundamental leg exercise',
          instructions: 'Descendez en fléchissant les genoux, remontez',
          instructionsEn: 'Lower by bending knees, then stand back up',
          tips: 'Gardez le dos droit',
          tipsEn: 'Keep your back straight',
          imageUrl: '/images/exercises/squats.jpg',
          videoUrl: '/videos/exercises/squats.mp4',
          attributes: [
            {
              id: 'attr-4',
              attributeName: { id: 'muscle-group', name: 'Groupe Musculaire', nameEn: 'Muscle Group' },
              attributeValue: { id: 'legs', value: 'Jambes', valueEn: 'Legs' },
            },
            {
              id: 'attr-5',
              attributeName: { id: 'equipment', name: 'Équipement', nameEn: 'Equipment' },
              attributeValue: { id: 'bodyweight', value: 'Poids du corps', valueEn: 'Bodyweight' },
            },
            {
              id: 'attr-6',
              attributeName: { id: 'difficulty', name: 'Difficulté', nameEn: 'Difficulty' },
              attributeValue: { id: 'beginner', value: 'Débutant', valueEn: 'Beginner' },
            },
          ],
        },
        {
          id: 'wc-ex-003',
          name: 'Développé Couché',
          nameEn: 'Bench Press',
          description: 'Exercice de musculation pour les pectoraux',
          descriptionEn: 'Strength exercise for chest muscles',
          instructions: 'Allongé sur le banc, poussez la barre vers le haut',
          instructionsEn: 'Lying on bench, push the barbell upward',
          tips: 'Contrôlez la descente',
          tipsEn: 'Control the descent',
          imageUrl: '/images/exercises/bench-press.jpg',
          videoUrl: '/videos/exercises/bench-press.mp4',
          attributes: [
            {
              id: 'attr-7',
              attributeName: { id: 'muscle-group', name: 'Groupe Musculaire', nameEn: 'Muscle Group' },
              attributeValue: { id: 'chest', value: 'Pectoraux', valueEn: 'Chest' },
            },
            {
              id: 'attr-8',
              attributeName: { id: 'equipment', name: 'Équipement', nameEn: 'Equipment' },
              attributeValue: { id: 'barbell', value: 'Barre', valueEn: 'Barbell' },
            },
            {
              id: 'attr-9',
              attributeName: { id: 'difficulty', name: 'Difficulté', nameEn: 'Difficulty' },
              attributeValue: { id: 'intermediate', value: 'Intermédiaire', valueEn: 'Intermediate' },
            },
          ],
        },
      ];

      // Apply search filter if provided
      if (search) {
        const searchLower = search.toLowerCase();
        return allExercises.filter(exercise =>
          exercise.name.toLowerCase().includes(searchLower) ||
          exercise.nameEn.toLowerCase().includes(searchLower) ||
          exercise.description.toLowerCase().includes(searchLower) ||
          exercise.descriptionEn.toLowerCase().includes(searchLower)
        );
      }

      return allExercises;
    } catch (error) {
      console.error('Error fetching exercises:', error);
      throw error;
    }
  }

  /**
   * Check if workout-cool backend is available
   */
  isConnectedToDatabase(): boolean {
    return this.isAvailable;
  }

  /**
   * Refresh availability status
   */
  async refreshAvailability() {
    await this.checkAvailability();
  }
}

// Export singleton instance
export const workoutCoolClient = new WorkoutCoolClient();
