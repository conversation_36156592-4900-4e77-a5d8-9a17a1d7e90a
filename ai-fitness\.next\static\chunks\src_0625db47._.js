(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/providers/query-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "QueryProvider": (()=>QueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
/**
 * React Query Provider for AI-fitness application
 * Provides global state management and caching for API requests
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function QueryProvider({ children }) {
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "QueryProvider.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
                defaultOptions: {
                    queries: {
                        // Stale time: how long data is considered fresh
                        staleTime: 5 * 60 * 1000,
                        // Cache time: how long data stays in cache after component unmounts
                        gcTime: 10 * 60 * 1000,
                        // Retry configuration
                        retry: {
                            "QueryProvider.useState": (failureCount, error)=>{
                                // Don't retry on authentication errors
                                if (error?.status === 401 || error?.status === 403) {
                                    return false;
                                }
                                // Don't retry on validation errors
                                if (error?.status === 422) {
                                    return false;
                                }
                                // Retry up to 3 times for other errors
                                return failureCount < 3;
                            }
                        }["QueryProvider.useState"],
                        // Retry delay with exponential backoff
                        retryDelay: {
                            "QueryProvider.useState": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
                        }["QueryProvider.useState"],
                        // Refetch on window focus (useful for real-time data)
                        refetchOnWindowFocus: false,
                        // Refetch on reconnect
                        refetchOnReconnect: true,
                        // Background refetch interval (disabled by default)
                        refetchInterval: false
                    },
                    mutations: {
                        // Retry mutations on network errors
                        retry: {
                            "QueryProvider.useState": (failureCount, error)=>{
                                // Don't retry on client errors (4xx)
                                if (error?.status >= 400 && error?.status < 500) {
                                    return false;
                                }
                                // Retry up to 2 times for server errors
                                return failureCount < 2;
                            }
                        }["QueryProvider.useState"],
                        // Retry delay for mutations
                        retryDelay: {
                            "QueryProvider.useState": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 10000)
                        }["QueryProvider.useState"]
                    }
                }
            })
    }["QueryProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {
                initialIsOpen: false,
                buttonPosition: "bottom-right"
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/query-provider.tsx",
                lineNumber: 80,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/providers/query-provider.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
_s(QueryProvider, "DuffM51qS3GS6gWca18rho9jwnk=");
_c = QueryProvider;
var _c;
__turbopack_context__.k.register(_c, "QueryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/config.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API Configuration for AI-fitness application
 * Connects to workout-cool backend API
 */ __turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_CONFIG = {
    // Base URL for the workout-cool backend API
    BASE_URL: ("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000',
    // API endpoints
    ENDPOINTS: {
        // Authentication
        AUTH: {
            SIGNIN: '/api/auth/signin',
            SIGNUP: '/api/auth/signup',
            SIGNOUT: '/api/auth/signout',
            SESSION: '/api/auth/session',
            RESET_PASSWORD: '/api/auth/reset-password'
        },
        // Exercises
        EXERCISES: {
            LIST: '/api/exercises',
            SEARCH: '/api/exercises/search',
            DETAILS: (id)=>`/api/exercises/${id}`,
            ATTRIBUTES: '/api/exercises/attributes'
        },
        // Workout Sessions
        WORKOUTS: {
            LIST: '/api/workout-sessions',
            CREATE: '/api/workout-sessions',
            DETAILS: (id)=>`/api/workout-sessions/${id}`,
            UPDATE: (id)=>`/api/workout-sessions/${id}`,
            DELETE: (id)=>`/api/workout-sessions/${id}`,
            COMPLETE: (id)=>`/api/workout-sessions/${id}/complete`,
            SYNC: '/api/workout-sessions/sync'
        },
        // Programs
        PROGRAMS: {
            LIST: '/api/programs',
            CREATE: '/api/programs',
            DETAILS: (slug)=>`/api/programs/${slug}`,
            UPDATE: (id)=>`/api/programs/${id}`,
            DELETE: (id)=>`/api/programs/${id}`,
            ENROLL: (id)=>`/api/programs/${id}/enroll`,
            SESSIONS: (programId)=>`/api/programs/${programId}/sessions`,
            START_SESSION: '/api/programs/sessions/start',
            COMPLETE_SESSION: '/api/programs/sessions/complete'
        },
        // User Progress
        PROGRESS: {
            LIST: '/api/progress',
            DETAILS: (id)=>`/api/progress/${id}`,
            CREATE: '/api/progress',
            UPDATE: (id)=>`/api/progress/${id}`,
            DELETE: (id)=>`/api/progress/${id}`,
            OVERVIEW: '/api/progress/overview',
            STATS: '/api/progress/stats',
            HISTORY: '/api/progress/history',
            GOALS: '/api/progress/goals',
            EXPORT: '/api/progress/export'
        },
        // Premium
        PREMIUM: {
            PLANS: '/api/premium/plans',
            SUBSCRIPTION: '/api/premium/subscription',
            CHECKOUT: '/api/premium/checkout',
            BILLING_PORTAL: '/api/premium/billing-portal'
        },
        // User Management
        USERS: {
            PROFILE: '/api/users/profile',
            UPDATE_PROFILE: '/api/users/profile',
            PREFERENCES: '/api/users/preferences'
        }
    },
    // Request timeouts
    TIMEOUT: {
        DEFAULT: 10000,
        UPLOAD: 30000,
        DOWNLOAD: 60000
    },
    // Retry configuration
    RETRY: {
        ATTEMPTS: 3,
        DELAY: 1000,
        BACKOFF_FACTOR: 2
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/errors.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API Error Classes for AI-fitness application
 * Provides structured error handling for different types of API errors
 */ __turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "AuthenticationError": (()=>AuthenticationError),
    "AuthorizationError": (()=>AuthorizationError),
    "ConflictError": (()=>ConflictError),
    "NetworkError": (()=>NetworkError),
    "NotFoundError": (()=>NotFoundError),
    "RateLimitError": (()=>RateLimitError),
    "ValidationError": (()=>ValidationError),
    "getErrorCode": (()=>getErrorCode),
    "getErrorMessage": (()=>getErrorMessage),
    "isApiError": (()=>isApiError),
    "isAuthenticationError": (()=>isAuthenticationError),
    "isNetworkError": (()=>isNetworkError),
    "isValidationError": (()=>isValidationError),
    "shouldRetry": (()=>shouldRetry)
});
class ApiError extends Error {
    status;
    code;
    details;
    constructor(message, status = 500, code, details){
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.code = code;
        this.details = details;
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ApiError);
        }
    }
    /**
   * Check if error is a client error (4xx)
   */ get isClientError() {
        return this.status >= 400 && this.status < 500;
    }
    /**
   * Check if error is a server error (5xx)
   */ get isServerError() {
        return this.status >= 500;
    }
    /**
   * Convert error to JSON for logging
   */ toJSON() {
        return {
            name: this.name,
            message: this.message,
            status: this.status,
            code: this.code,
            details: this.details,
            stack: this.stack
        };
    }
}
class NetworkError extends ApiError {
    constructor(message = 'Network error occurred'){
        super(message, 0, 'NETWORK_ERROR');
        this.name = 'NetworkError';
    }
}
class AuthenticationError extends ApiError {
    constructor(message = 'Authentication required'){
        super(message, 401, 'AUTHENTICATION_ERROR');
        this.name = 'AuthenticationError';
    }
}
class AuthorizationError extends ApiError {
    constructor(message = 'Access denied'){
        super(message, 403, 'AUTHORIZATION_ERROR');
        this.name = 'AuthorizationError';
    }
}
class ValidationError extends ApiError {
    fieldErrors;
    constructor(message = 'Validation failed', fieldErrors = {}){
        super(message, 422, 'VALIDATION_ERROR', fieldErrors);
        this.name = 'ValidationError';
        this.fieldErrors = fieldErrors;
    }
    /**
   * Get error message for a specific field
   */ getFieldError(field) {
        const errors = this.fieldErrors[field];
        return errors && errors.length > 0 ? errors[0] : null;
    }
    /**
   * Check if a specific field has errors
   */ hasFieldError(field) {
        return Boolean(this.fieldErrors[field]?.length);
    }
    /**
   * Get all field error messages as a flat array
   */ getAllFieldErrors() {
        return Object.values(this.fieldErrors).flat();
    }
}
class NotFoundError extends ApiError {
    constructor(message = 'Resource not found'){
        super(message, 404, 'NOT_FOUND_ERROR');
        this.name = 'NotFoundError';
    }
}
class ConflictError extends ApiError {
    constructor(message = 'Resource conflict'){
        super(message, 409, 'CONFLICT_ERROR');
        this.name = 'ConflictError';
    }
}
class RateLimitError extends ApiError {
    retryAfter;
    constructor(message = 'Rate limit exceeded', retryAfter){
        super(message, 429, 'RATE_LIMIT_ERROR', {
            retryAfter
        });
        this.name = 'RateLimitError';
        this.retryAfter = retryAfter;
    }
}
function isApiError(error) {
    return error instanceof ApiError;
}
function isNetworkError(error) {
    return error instanceof NetworkError;
}
function isAuthenticationError(error) {
    return error instanceof AuthenticationError;
}
function isValidationError(error) {
    return error instanceof ValidationError;
}
function getErrorMessage(error) {
    if (isApiError(error)) {
        return error.message;
    }
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'An unexpected error occurred';
}
function getErrorCode(error) {
    if (isApiError(error)) {
        return error.code || null;
    }
    return null;
}
function shouldRetry(error) {
    if (isNetworkError(error)) {
        return true;
    }
    if (isApiError(error)) {
        // Retry on server errors but not client errors
        return error.isServerError;
    }
    return false;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/client.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API Client for AI-fitness application
 * Handles HTTP requests, authentication, and error handling
 */ __turbopack_context__.s({
    "apiClient": (()=>apiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/errors.ts [app-client] (ecmascript)");
;
;
class ApiClient {
    baseUrl;
    defaultHeaders;
    constructor(){
        this.baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }
    /**
   * Get authentication token from session storage or cookies
   */ getAuthToken() {
        if ("TURBOPACK compile-time truthy", 1) {
            // Try to get token from localStorage first
            const token = localStorage.getItem('auth-token');
            if (token) {
                return token;
            }
            // Try to get user data and extract token
            const userData = localStorage.getItem('auth-user');
            if (userData) {
                try {
                    const user = JSON.parse(userData);
                    return user.token || null;
                } catch  {
                    return null;
                }
            }
        }
        return null;
    }
    /**
   * Build request headers with authentication if available
   */ buildHeaders(options = {}) {
        const headers = {
            ...this.defaultHeaders
        };
        // Add authentication header if required and available
        if (options.requireAuth !== false) {
            const token = this.getAuthToken();
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }
        // Merge with custom headers
        if (options.headers) {
            Object.assign(headers, options.headers);
        }
        return headers;
    }
    /**
   * Handle API response and errors
   */ async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        const isJson = contentType?.includes('application/json');
        let data;
        try {
            data = isJson ? await response.json() : await response.text();
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiError"]('Failed to parse response', response.status);
        }
        if (!response.ok) {
            switch(response.status){
                case 401:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthenticationError"](data.message || 'Authentication required');
                case 422:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationError"](data.message || 'Validation failed', data.errors);
                case 404:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Resource not found', 404);
                case 500:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Internal server error', 500);
                default:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Request failed', response.status);
            }
        }
        return data;
    }
    /**
   * Make HTTP request with retry logic
   */ async makeRequest(url, options = {}) {
        const { timeout = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT.DEFAULT, retries = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.ATTEMPTS, ...requestOptions } = options;
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), timeout);
        const requestConfig = {
            ...requestOptions,
            headers: this.buildHeaders(options),
            signal: controller.signal
        };
        let lastError = null;
        for(let attempt = 0; attempt <= retries; attempt++){
            try {
                const response = await fetch(`${this.baseUrl}${url}`, requestConfig);
                clearTimeout(timeoutId);
                return await this.handleResponse(response);
            } catch (error) {
                lastError = error;
                // Don't retry on authentication or validation errors
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthenticationError"] || error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationError"]) {
                    throw error;
                }
                // Don't retry on the last attempt
                if (attempt === retries) {
                    break;
                }
                // Wait before retrying
                const delay = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.DELAY * Math.pow(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.BACKOFF_FACTOR, attempt);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            }
        }
        clearTimeout(timeoutId);
        // Handle network errors
        if (lastError) {
            if (lastError.name === 'AbortError') {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkError"]('Request timeout');
            }
            if (lastError.name === 'TypeError') {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkError"]('Network error - please check your connection');
            }
            throw lastError;
        }
        throw new Error('Request failed after all retries');
    }
    /**
   * GET request
   */ async get(url, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'GET'
        });
    }
    /**
   * POST request
   */ async post(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * PUT request
   */ async put(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * PATCH request
   */ async patch(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * DELETE request
   */ async delete(url, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'DELETE'
        });
    }
}
const apiClient = new ApiClient();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Authentication API Service
 * Handles authentication-related API calls to workout-cool backend
 */ __turbopack_context__.s({
    "AuthService": (()=>AuthService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-client] (ecmascript)");
;
;
class AuthService {
    /**
   * Sign in with email and password
   */ static async signIn(credentials) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNIN, credentials, {
            requireAuth: false
        });
    }
    /**
   * Sign up with user data
   */ static async signUp(userData) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNUP, userData, {
            requireAuth: false
        });
    }
    /**
   * Sign out current user
   */ static async signOut() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNOUT);
    }
    /**
   * Get current session
   */ static async getSession() {
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SESSION);
        } catch (error) {
            // Return null if not authenticated
            if (error.status === 401) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Request password reset
   */ static async requestPasswordReset(email) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.RESET_PASSWORD, {
            email
        }, {
            requireAuth: false
        });
    }
    /**
   * Reset password with token
   */ static async resetPassword(token, newPassword) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.RESET_PASSWORD, {
            token,
            password: newPassword
        }, {
            requireAuth: false
        });
    }
    /**
   * Verify email with token
   */ static async verifyEmail(token) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/verify-email', {
            token
        }, {
            requireAuth: false
        });
    }
    /**
   * Refresh authentication token
   */ static async refreshToken() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/refresh');
    }
    /**
   * Update user profile
   */ static async updateProfile(updates) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch('/api/auth/profile', updates);
    }
    /**
   * Change password
   */ static async changePassword(currentPassword, newPassword) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/change-password', {
            currentPassword,
            newPassword
        });
    }
    /**
   * Delete account
   */ static async deleteAccount(password) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete('/api/auth/account', {
            body: JSON.stringify({
                password
            })
        });
    }
    /**
   * Get user sessions
   */ static async getUserSessions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/api/auth/sessions');
    }
    /**
   * Revoke a specific session
   */ static async revokeSession(sessionId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/api/auth/sessions/${sessionId}`);
    }
    /**
   * Revoke all other sessions
   */ static async revokeAllOtherSessions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/sessions/revoke-all');
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/hooks/use-auth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Authentication React Query hooks
 */ __turbopack_context__.s({
    "authKeys": (()=>authKeys),
    "useAuth": (()=>useAuth),
    "useChangePassword": (()=>useChangePassword),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsPremium": (()=>useIsPremium),
    "useRequestPasswordReset": (()=>useRequestPasswordReset),
    "useResetPassword": (()=>useResetPassword),
    "useRevokeSession": (()=>useRevokeSession),
    "useSession": (()=>useSession),
    "useSignIn": (()=>useSignIn),
    "useSignOut": (()=>useSignOut),
    "useSignUp": (()=>useSignUp),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUserSessions": (()=>useUserSessions),
    "useVerifyEmail": (()=>useVerifyEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/auth.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature(), _s10 = __turbopack_context__.k.signature(), _s11 = __turbopack_context__.k.signature(), _s12 = __turbopack_context__.k.signature(), _s13 = __turbopack_context__.k.signature();
;
;
const authKeys = {
    all: [
        'auth'
    ],
    session: ()=>[
            ...authKeys.all,
            'session'
        ],
    sessions: ()=>[
            ...authKeys.all,
            'sessions'
        ]
};
function useSession() {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: authKeys.session(),
        queryFn: {
            "useSession.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].getSession()
        }["useSession.useQuery"],
        staleTime: 5 * 60 * 1000,
        retry: false
    });
}
_s(useSession, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useAuth() {
    _s1();
    const { data: session, isLoading, error } = useSession();
    return {
        user: session?.user || null,
        isAuthenticated: !!session?.user,
        isLoading,
        error,
        session
    };
}
_s1(useAuth, "dC7FjMVN9b2K9ZTPVT1oOIoiuEg=", false, function() {
    return [
        useSession
    ];
});
function useIsAdmin() {
    _s2();
    const { user, isAuthenticated } = useAuth();
    return isAuthenticated && user?.role === 'admin';
}
_s2(useIsAdmin, "ZciNvz1w38ujgxJkc4IsNEDLTNc=", false, function() {
    return [
        useAuth
    ];
});
function useIsPremium() {
    _s3();
    const { user, isAuthenticated } = useAuth();
    return isAuthenticated && user?.isPremium === true;
}
_s3(useIsPremium, "ZciNvz1w38ujgxJkc4IsNEDLTNc=", false, function() {
    return [
        useAuth
    ];
});
function useSignIn() {
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSignIn.useMutation": (credentials)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].signIn(credentials)
        }["useSignIn.useMutation"],
        onSuccess: {
            "useSignIn.useMutation": (session)=>{
                // Update session cache
                queryClient.setQueryData(authKeys.session(), session);
                // Invalidate all queries to refetch with new auth state
                queryClient.invalidateQueries();
            }
        }["useSignIn.useMutation"],
        onError: {
            "useSignIn.useMutation": (error)=>{
                console.error('Sign in failed:', error);
            }
        }["useSignIn.useMutation"]
    });
}
_s4(useSignIn, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useSignUp() {
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSignUp.useMutation": (userData)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].signUp(userData)
        }["useSignUp.useMutation"],
        onSuccess: {
            "useSignUp.useMutation": (session)=>{
                // Update session cache
                queryClient.setQueryData(authKeys.session(), session);
                // Invalidate all queries to refetch with new auth state
                queryClient.invalidateQueries();
            }
        }["useSignUp.useMutation"],
        onError: {
            "useSignUp.useMutation": (error)=>{
                console.error('Sign up failed:', error);
            }
        }["useSignUp.useMutation"]
    });
}
_s5(useSignUp, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useSignOut() {
    _s6();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useSignOut.useMutation": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].signOut()
        }["useSignOut.useMutation"],
        onSuccess: {
            "useSignOut.useMutation": ()=>{
                // Clear session cache
                queryClient.setQueryData(authKeys.session(), null);
                // Clear all cached data
                queryClient.clear();
            }
        }["useSignOut.useMutation"],
        onError: {
            "useSignOut.useMutation": (error)=>{
                console.error('Sign out failed:', error);
                // Even if sign out fails on server, clear local cache
                queryClient.setQueryData(authKeys.session(), null);
                queryClient.clear();
            }
        }["useSignOut.useMutation"]
    });
}
_s6(useSignOut, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useRequestPasswordReset() {
    _s7();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useRequestPasswordReset.useMutation": (email)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].requestPasswordReset(email)
        }["useRequestPasswordReset.useMutation"],
        onError: {
            "useRequestPasswordReset.useMutation": (error)=>{
                console.error('Password reset request failed:', error);
            }
        }["useRequestPasswordReset.useMutation"]
    });
}
_s7(useRequestPasswordReset, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useResetPassword() {
    _s8();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useResetPassword.useMutation": ({ token, newPassword })=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].resetPassword(token, newPassword)
        }["useResetPassword.useMutation"],
        onError: {
            "useResetPassword.useMutation": (error)=>{
                console.error('Password reset failed:', error);
            }
        }["useResetPassword.useMutation"]
    });
}
_s8(useResetPassword, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useVerifyEmail() {
    _s9();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useVerifyEmail.useMutation": (token)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].verifyEmail(token)
        }["useVerifyEmail.useMutation"],
        onError: {
            "useVerifyEmail.useMutation": (error)=>{
                console.error('Email verification failed:', error);
            }
        }["useVerifyEmail.useMutation"]
    });
}
_s9(useVerifyEmail, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUpdateProfile() {
    _s10();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateProfile.useMutation": (updates)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].updateProfile(updates)
        }["useUpdateProfile.useMutation"],
        onSuccess: {
            "useUpdateProfile.useMutation": (updatedUser)=>{
                // Update session cache with new user data
                const currentSession = queryClient.getQueryData(authKeys.session());
                if (currentSession) {
                    queryClient.setQueryData(authKeys.session(), {
                        ...currentSession,
                        user: updatedUser
                    });
                }
            }
        }["useUpdateProfile.useMutation"],
        onError: {
            "useUpdateProfile.useMutation": (error)=>{
                console.error('Profile update failed:', error);
            }
        }["useUpdateProfile.useMutation"]
    });
}
_s10(useUpdateProfile, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useChangePassword() {
    _s11();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useChangePassword.useMutation": ({ currentPassword, newPassword })=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].changePassword(currentPassword, newPassword)
        }["useChangePassword.useMutation"],
        onError: {
            "useChangePassword.useMutation": (error)=>{
                console.error('Password change failed:', error);
            }
        }["useChangePassword.useMutation"]
    });
}
_s11(useChangePassword, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
function useUserSessions() {
    _s12();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: authKeys.sessions(),
        queryFn: {
            "useUserSessions.useQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].getUserSessions()
        }["useUserSessions.useQuery"],
        staleTime: 2 * 60 * 1000
    });
}
_s12(useUserSessions, "4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useRevokeSession() {
    _s13();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useRevokeSession.useMutation": (sessionId)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthService"].revokeSession(sessionId)
        }["useRevokeSession.useMutation"],
        onSuccess: {
            "useRevokeSession.useMutation": ()=>{
                // Refetch sessions list
                queryClient.invalidateQueries({
                    queryKey: authKeys.sessions()
                });
            }
        }["useRevokeSession.useMutation"],
        onError: {
            "useRevokeSession.useMutation": (error)=>{
                console.error('Session revocation failed:', error);
            }
        }["useRevokeSession.useMutation"]
    });
}
_s13(useRevokeSession, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/providers/auth-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuthContext": (()=>useAuthContext),
    "withAdminAuth": (()=>withAdminAuth),
    "withAuth": (()=>withAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
/**
 * Authentication Provider for AI-fitness application
 * Provides authentication context and session management
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/use-auth.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const { user, isAuthenticated, isLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [refreshKey, setRefreshKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const refreshAuth = ()=>{
        setRefreshKey((prev)=>prev + 1);
    };
    // Update API client with auth token when user changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            if (user && "object" !== 'undefined') {
                // Store auth state in localStorage for API client
                localStorage.setItem('auth-user', JSON.stringify(user));
            } else if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('auth-user');
            }
        }
    }["AuthProvider.useEffect"], [
        user
    ]);
    const contextValue = {
        user,
        isAuthenticated,
        isLoading,
        error,
        refreshAuth
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/providers/auth-provider.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "/T0Wmc1AzIvnHvq6pWqgfiGd/UE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = AuthProvider;
function useAuthContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuthContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function withAuth(Component) {
    var _s = __turbopack_context__.k.signature();
    return _s(function AuthenticatedComponent(props) {
        _s();
        const { isAuthenticated, isLoading } = useAuthContext();
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 82,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 81,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-md p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-center text-gray-900 mb-4",
                            children: "Authentication Required"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 91,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-center mb-6",
                            children: "Please sign in to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 94,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.href = '/auth/signin',
                                    className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
                                    children: "Sign In"
                                }, void 0, false, {
                                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                                    lineNumber: 98,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.href = '/auth/signup',
                                    className: "w-full bg-gray-200 text-gray-900 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors",
                                    children: "Sign Up"
                                }, void 0, false, {
                                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                                    lineNumber: 104,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 90,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 89,
                columnNumber: 9
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/lib/providers/auth-provider.tsx",
            lineNumber: 116,
            columnNumber: 12
        }, this);
    }, "jVZxeGYQ6nzM4j1uYJkL2uehxwQ=", false, function() {
        return [
            useAuthContext
        ];
    });
}
function withAdminAuth(Component) {
    var _s = __turbopack_context__.k.signature();
    return _s(function AdminAuthenticatedComponent(props) {
        _s();
        const { user, isAuthenticated, isLoading } = useAuthContext();
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 132,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 131,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated || user?.role !== 'admin') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-md p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-center text-gray-900 mb-4",
                            children: "Admin Access Required"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-center mb-6",
                            children: "You need administrator privileges to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>window.location.href = '/',
                            className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
                            children: "Go Home"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 140,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/lib/providers/auth-provider.tsx",
            lineNumber: 158,
            columnNumber: 12
        }, this);
    }, "Sm7vgtQkRIhbYHT4ELk3PPETFDw=", false, function() {
        return [
            useAuthContext
        ];
    });
}
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/store/app-store.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Global Application State Management with Zustand
 * Handles app-wide state that needs to persist across components
 */ __turbopack_context__.s({
    "appActions": (()=>appActions),
    "useAppStore": (()=>useAppStore),
    "useHasPendingSync": (()=>useHasPendingSync),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsOffline": (()=>useIsOffline),
    "useNotifications": (()=>useNotifications),
    "useOfflineState": (()=>useOfflineState),
    "useSettings": (()=>useSettings),
    "useUIState": (()=>useUIState),
    "useUnreadNotificationCount": (()=>useUnreadNotificationCount),
    "useUser": (()=>useUser),
    "useWorkoutPreferences": (()=>useWorkoutPreferences)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2f$immer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware/immer.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature();
;
;
;
// ============================================================================
// DEFAULT VALUES
// ============================================================================
const defaultSettings = {
    theme: 'system',
    language: 'en',
    units: 'metric',
    notifications: {
        workoutReminders: true,
        progressUpdates: true,
        achievements: true,
        marketing: false
    },
    privacy: {
        shareProgress: false,
        showInLeaderboards: true,
        allowDataCollection: true
    }
};
const defaultWorkoutPreferences = {
    defaultDuration: 45,
    preferredDifficulty: 'INTERMEDIATE',
    favoriteCategories: [],
    excludedEquipment: [],
    restTimeBetweenSets: 60,
    autoStartNextExercise: false,
    playWorkoutMusic: true,
    voiceInstructions: false
};
const defaultUIState = {
    sidebarCollapsed: false,
    activeWorkoutSession: null,
    currentPage: '/',
    breadcrumbs: [],
    notifications: [],
    modals: {
        workoutComplete: false,
        goalAchieved: false,
        subscriptionPrompt: false
    }
};
const defaultOfflineState = {
    isOnline: true,
    pendingSync: [],
    lastSyncTime: null
};
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2f$immer$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["immer"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        settings: defaultSettings,
        workoutPreferences: defaultWorkoutPreferences,
        ui: defaultUIState,
        offline: defaultOfflineState,
        // Actions
        setUser: (user)=>set((state)=>{
                state.user = user;
            }),
        setAuthenticated: (authenticated)=>set((state)=>{
                state.isAuthenticated = authenticated;
                if (!authenticated) {
                    state.user = null;
                }
            }),
        updateSettings: (newSettings)=>set((state)=>{
                Object.assign(state.settings, newSettings);
            }),
        updateWorkoutPreferences: (newPreferences)=>set((state)=>{
                Object.assign(state.workoutPreferences, newPreferences);
            }),
        updateUIState: (newUIState)=>set((state)=>{
                Object.assign(state.ui, newUIState);
            }),
        addNotification: (notification)=>set((state)=>{
                const newNotification = {
                    ...notification,
                    id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now(),
                    read: false
                };
                state.ui.notifications.unshift(newNotification);
                // Keep only last 50 notifications
                if (state.ui.notifications.length > 50) {
                    state.ui.notifications = state.ui.notifications.slice(0, 50);
                }
            }),
        markNotificationRead: (id)=>set((state)=>{
                const notification = state.ui.notifications.find((n)=>n.id === id);
                if (notification) {
                    notification.read = true;
                }
            }),
        clearNotifications: ()=>set((state)=>{
                state.ui.notifications = [];
            }),
        setOnlineStatus: (isOnline)=>set((state)=>{
                state.offline.isOnline = isOnline;
            }),
        addPendingSync: (item)=>set((state)=>{
                const newItem = {
                    ...item,
                    id: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now()
                };
                state.offline.pendingSync.push(newItem);
            }),
        removePendingSync: (id)=>set((state)=>{
                state.offline.pendingSync = state.offline.pendingSync.filter((item)=>item.id !== id);
            }),
        clearPendingSync: ()=>set((state)=>{
                state.offline.pendingSync = [];
            }),
        updateLastSyncTime: ()=>set((state)=>{
                state.offline.lastSyncTime = Date.now();
            }),
        reset: ()=>set(()=>({
                    user: null,
                    isAuthenticated: false,
                    settings: defaultSettings,
                    workoutPreferences: defaultWorkoutPreferences,
                    ui: defaultUIState,
                    offline: defaultOfflineState
                }))
    })), {
    name: 'ai-fitness-app-store',
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
    partialize: (state)=>({
            // Only persist certain parts of the state
            settings: state.settings,
            workoutPreferences: state.workoutPreferences,
            ui: {
                sidebarCollapsed: state.ui.sidebarCollapsed
            },
            offline: {
                pendingSync: state.offline.pendingSync,
                lastSyncTime: state.offline.lastSyncTime
            }
        })
}));
const useUser = ()=>{
    _s();
    return useAppStore({
        "useUser.useAppStore": (state)=>state.user
    }["useUser.useAppStore"]);
};
_s(useUser, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useIsAuthenticated = ()=>{
    _s1();
    return useAppStore({
        "useIsAuthenticated.useAppStore": (state)=>state.isAuthenticated
    }["useIsAuthenticated.useAppStore"]);
};
_s1(useIsAuthenticated, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useSettings = ()=>{
    _s2();
    return useAppStore({
        "useSettings.useAppStore": (state)=>state.settings
    }["useSettings.useAppStore"]);
};
_s2(useSettings, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useWorkoutPreferences = ()=>{
    _s3();
    return useAppStore({
        "useWorkoutPreferences.useAppStore": (state)=>state.workoutPreferences
    }["useWorkoutPreferences.useAppStore"]);
};
_s3(useWorkoutPreferences, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useUIState = ()=>{
    _s4();
    return useAppStore({
        "useUIState.useAppStore": (state)=>state.ui
    }["useUIState.useAppStore"]);
};
_s4(useUIState, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useNotifications = ()=>{
    _s5();
    return useAppStore({
        "useNotifications.useAppStore": (state)=>state.ui.notifications
    }["useNotifications.useAppStore"]);
};
_s5(useNotifications, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useOfflineState = ()=>{
    _s6();
    return useAppStore({
        "useOfflineState.useAppStore": (state)=>state.offline
    }["useOfflineState.useAppStore"]);
};
_s6(useOfflineState, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useUnreadNotificationCount = ()=>{
    _s7();
    return useAppStore({
        "useUnreadNotificationCount.useAppStore": (state)=>state.ui.notifications.filter({
                "useUnreadNotificationCount.useAppStore": (n)=>!n.read
            }["useUnreadNotificationCount.useAppStore"]).length
    }["useUnreadNotificationCount.useAppStore"]);
};
_s7(useUnreadNotificationCount, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useHasPendingSync = ()=>{
    _s8();
    return useAppStore({
        "useHasPendingSync.useAppStore": (state)=>state.offline.pendingSync.length > 0
    }["useHasPendingSync.useAppStore"]);
};
_s8(useHasPendingSync, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const useIsOffline = ()=>{
    _s9();
    return useAppStore({
        "useIsOffline.useAppStore": (state)=>!state.offline.isOnline
    }["useIsOffline.useAppStore"]);
};
_s9(useIsOffline, "WaWoZFGsV3qiiOjJHq8b1vvtvBw=", false, function() {
    return [
        useAppStore
    ];
});
const appActions = {
    setUser: (user)=>useAppStore.getState().setUser(user),
    setAuthenticated: (authenticated)=>useAppStore.getState().setAuthenticated(authenticated),
    updateSettings: (settings)=>useAppStore.getState().updateSettings(settings),
    updateWorkoutPreferences: (preferences)=>useAppStore.getState().updateWorkoutPreferences(preferences),
    addNotification: (notification)=>useAppStore.getState().addNotification(notification),
    setOnlineStatus: (isOnline)=>useAppStore.getState().setOnlineStatus(isOnline),
    addPendingSync: (item)=>useAppStore.getState().addPendingSync(item),
    removePendingSync: (id)=>useAppStore.getState().removePendingSync(id),
    clearPendingSync: ()=>useAppStore.getState().clearPendingSync(),
    updateLastSyncTime: ()=>useAppStore.getState().updateLastSyncTime(),
    reset: ()=>useAppStore.getState().reset()
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/workouts.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Workout API Service
 * Handles all workout-related API calls
 */ __turbopack_context__.s({
    "WorkoutService": (()=>WorkoutService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-client] (ecmascript)");
;
;
class WorkoutService {
    /**
   * Get user's workout sessions with optional filtering
   */ static async getWorkoutSessions(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.status) searchParams.append('status', params.status);
        if (params.programId) searchParams.append('programId', params.programId);
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.LIST;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
    /**
   * Get workout session by ID
   */ static async getWorkoutSession(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id));
    }
    /**
   * Create a new workout session
   */ static async createWorkoutSession(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.CREATE, data);
    }
    /**
   * Update workout session
   */ static async updateWorkoutSession(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.UPDATE(id), data);
    }
    /**
   * Delete workout session
   */ static async deleteWorkoutSession(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DELETE(id));
    }
    /**
   * Start a workout session
   */ static async startWorkoutSession(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id)}/start`);
    }
    /**
   * Complete a workout session
   */ static async completeWorkoutSession(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id)}/complete`, data);
    }
    /**
   * Get workout programs
   */ static async getWorkoutPrograms(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.category) searchParams.append('category', params.category);
        if (params.difficulty) searchParams.append('difficulty', params.difficulty);
        if (params.duration) searchParams.append('duration', params.duration);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
    /**
   * Get workout program by ID
   */ static async getWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id));
    }
    /**
   * Create a new workout program
   */ static async createWorkoutProgram(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.CREATE, data);
    }
    /**
   * Update workout program
   */ static async updateWorkoutProgram(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.UPDATE(id), data);
    }
    /**
   * Delete workout program
   */ static async deleteWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DELETE(id));
    }
    /**
   * Join a workout program
   */ static async joinWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id)}/join`);
    }
    /**
   * Leave a workout program
   */ static async leaveWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id)}/leave`);
    }
    /**
   * Get user's joined programs
   */ static async getUserPrograms() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/api/user/programs');
        return response.programs || [];
    }
    /**
   * Get popular workout programs
   */ static async getPopularPrograms(limit = 10) {
        const searchParams = new URLSearchParams({
            sort: 'popular',
            limit: limit.toString()
        });
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data || [];
    }
    /**
   * Get recommended workout programs for user
   */ static async getRecommendedPrograms(limit = 6) {
        const searchParams = new URLSearchParams({
            recommended: 'true',
            limit: limit.toString()
        });
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data || [];
    }
    /**
   * Generate AI workout plan
   */ static async generateAIWorkout(preferences) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/workouts/generate', preferences);
    }
    /**
   * Get workout statistics
   */ static async getWorkoutStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/workouts/stats?period=${period}`);
    }
    /**
   * Get workout history
   */ static async getWorkoutHistory(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        const queryString = searchParams.toString();
        const url = queryString ? `/api/workouts/history?${queryString}` : '/api/workouts/history';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/progress.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Progress Tracking API Service
 * Handles all progress and analytics related API calls
 */ __turbopack_context__.s({
    "ProgressService": (()=>ProgressService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-client] (ecmascript)");
;
;
class ProgressService {
    /**
   * Get user's progress records with optional filtering
   */ static async getProgressRecords(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.type) searchParams.append('type', params.type);
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);
        if (params.workoutId) searchParams.append('workoutId', params.workoutId);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.LIST;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
    /**
   * Get progress record by ID
   */ static async getProgressRecord(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.DETAILS(id));
    }
    /**
   * Create a new progress record
   */ static async createProgressRecord(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.CREATE, data);
    }
    /**
   * Update progress record
   */ static async updateProgressRecord(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.UPDATE(id), data);
    }
    /**
   * Delete progress record
   */ static async deleteProgressRecord(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.DELETE(id));
    }
    /**
   * Get comprehensive progress statistics
   */ static async getProgressStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.STATS}?period=${period}`);
    }
    /**
   * Get workout completion statistics
   */ static async getWorkoutStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/workout-stats?period=${period}`);
    }
    /**
   * Get exercise performance data
   */ static async getExerciseProgress(exerciseId, period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/exercise/${exerciseId}?period=${period}`);
    }
    /**
   * Get body measurements progress
   */ static async getBodyMeasurements(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/body-measurements?period=${period}`);
    }
    /**
   * Add body measurement record
   */ static async addBodyMeasurement(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/progress/body-measurements', data);
    }
    /**
   * Get fitness goals and progress
   */ static async getFitnessGoals() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/goals');
    }
    /**
   * Create a new fitness goal
   */ static async createFitnessGoal(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/api/progress/goals', data);
    }
    /**
   * Update fitness goal progress
   */ static async updateGoalProgress(goalId, currentValue) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].patch(`/api/progress/goals/${goalId}`, {
            currentValue
        });
    }
    /**
   * Get achievement badges and milestones
   */ static async getAchievements() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/achievements');
    }
    /**
   * Get workout calendar data
   */ static async getWorkoutCalendar(year, month) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/calendar?year=${year}&month=${month}`);
    }
    /**
   * Get personal records (PRs)
   */ static async getPersonalRecords() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/personal-records');
    }
    /**
   * Get strength progression data
   */ static async getStrengthProgression(exerciseIds) {
        const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/strength-progression${params}`);
    }
    /**
   * Export progress data
   */ static async exportProgressData(format = 'csv', period = 'all') {
        const response = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL}/api/progress/export?format=${format}&period=${period}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
            }
        });
        if (!response.ok) {
            throw new Error('Failed to export data');
        }
        return response.blob();
    }
    /**
   * Get workout intensity analysis
   */ static async getWorkoutIntensity(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/workout-intensity?period=${period}`);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/query/config.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * React Query Configuration and Cache Management
 * Centralized configuration for data fetching, caching, and synchronization
 */ __turbopack_context__.s({
    "cacheUtils": (()=>cacheUtils),
    "default": (()=>queryClient),
    "offlineUtils": (()=>offlineUtils),
    "queryClient": (()=>queryClient),
    "queryKeys": (()=>queryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$persist$2d$client$2d$core$2f$build$2f$modern$2f$persist$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-persist-client-core/build/modern/persist.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$sync$2d$storage$2d$persister$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-sync-storage-persister/build/modern/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-client] (ecmascript)");
;
;
;
;
// ============================================================================
// QUERY CLIENT CONFIGURATION
// ============================================================================
const queryConfig = {
    queries: {
        // Global defaults for all queries
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: (failureCount, error)=>{
            // Don't retry on 4xx errors (client errors)
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry up to 3 times for other errors
            return failureCount < 3;
        },
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: true
    },
    mutations: {
        // Global defaults for all mutations
        retry: (failureCount, error)=>{
            // Don't retry mutations on client errors
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry once for server errors
            return failureCount < 1;
        },
        onError: (error)=>{
            // Global error handling for mutations
            console.error('Mutation error:', error);
            // Add error notification
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'error',
                title: 'Operation Failed',
                message: error?.message || 'An unexpected error occurred'
            });
        }
    }
};
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
    defaultOptions: queryConfig
});
// ============================================================================
// PERSISTENCE CONFIGURATION
// ============================================================================
const persister = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$sync$2d$storage$2d$persister$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSyncStoragePersister"])({
    storage: ("TURBOPACK compile-time truthy", 1) ? window.localStorage : ("TURBOPACK unreachable", undefined),
    key: 'ai-fitness-query-cache',
    serialize: JSON.stringify,
    deserialize: JSON.parse
});
// Persist query client (only in browser)
if ("TURBOPACK compile-time truthy", 1) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$persist$2d$client$2d$core$2f$build$2f$modern$2f$persist$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persistQueryClient"])({
        queryClient,
        persister,
        maxAge: 24 * 60 * 60 * 1000,
        buster: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_APP_VERSION || '1.0.0'
    });
}
const cacheUtils = {
    /**
   * Invalidate all queries for a specific entity type
   */ invalidateEntity: (entityType)=>{
        queryClient.invalidateQueries({
            queryKey: [
                entityType
            ]
        });
    },
    /**
   * Remove all cached data for a specific entity
   */ removeEntity: (entityType, id)=>{
        if (id) {
            queryClient.removeQueries({
                queryKey: [
                    entityType,
                    id
                ]
            });
        } else {
            queryClient.removeQueries({
                queryKey: [
                    entityType
                ]
            });
        }
    },
    /**
   * Prefetch data for better UX
   */ prefetch: async (queryKey, queryFn)=>{
        await queryClient.prefetchQuery({
            queryKey,
            queryFn,
            staleTime: 10 * 60 * 1000
        });
    },
    /**
   * Set query data manually (for optimistic updates)
   */ setQueryData: (queryKey, data)=>{
        queryClient.setQueryData(queryKey, data);
    },
    /**
   * Get cached query data
   */ getQueryData: (queryKey)=>{
        return queryClient.getQueryData(queryKey);
    },
    /**
   * Clear all cached data
   */ clearAll: ()=>{
        queryClient.clear();
    },
    /**
   * Reset queries to refetch fresh data
   */ resetQueries: (queryKey)=>{
        if (queryKey) {
            queryClient.resetQueries({
                queryKey
            });
        } else {
            queryClient.resetQueries();
        }
    },
    /**
   * Cancel ongoing queries
   */ cancelQueries: (queryKey)=>{
        if (queryKey) {
            queryClient.cancelQueries({
                queryKey
            });
        } else {
            queryClient.cancelQueries();
        }
    }
};
const queryKeys = {
    // Auth
    auth: {
        all: [
            'auth'
        ],
        session: ()=>[
                ...queryKeys.auth.all,
                'session'
            ],
        user: ()=>[
                ...queryKeys.auth.all,
                'user'
            ]
    },
    // Exercises
    exercises: {
        all: [
            'exercises'
        ],
        lists: ()=>[
                ...queryKeys.exercises.all,
                'list'
            ],
        list: (filters)=>[
                ...queryKeys.exercises.lists(),
                filters
            ],
        details: ()=>[
                ...queryKeys.exercises.all,
                'detail'
            ],
        detail: (id)=>[
                ...queryKeys.exercises.details(),
                id
            ],
        search: (query)=>[
                ...queryKeys.exercises.all,
                'search',
                query
            ],
        attributes: ()=>[
                ...queryKeys.exercises.all,
                'attributes'
            ]
    },
    // Workouts
    workouts: {
        all: [
            'workouts'
        ],
        sessions: ()=>[
                ...queryKeys.workouts.all,
                'sessions'
            ],
        session: (id)=>[
                ...queryKeys.workouts.sessions(),
                id
            ],
        programs: ()=>[
                ...queryKeys.workouts.all,
                'programs'
            ],
        program: (id)=>[
                ...queryKeys.workouts.programs(),
                id
            ],
        history: (filters)=>[
                ...queryKeys.workouts.all,
                'history',
                filters
            ],
        stats: (period)=>[
                ...queryKeys.workouts.all,
                'stats',
                period
            ]
    },
    // Progress
    progress: {
        all: [
            'progress'
        ],
        records: ()=>[
                ...queryKeys.progress.all,
                'records'
            ],
        record: (id)=>[
                ...queryKeys.progress.records(),
                id
            ],
        stats: (period)=>[
                ...queryKeys.progress.all,
                'stats',
                period
            ],
        goals: ()=>[
                ...queryKeys.progress.all,
                'goals'
            ],
        achievements: ()=>[
                ...queryKeys.progress.all,
                'achievements'
            ],
        calendar: (year, month)=>[
                ...queryKeys.progress.all,
                'calendar',
                year,
                month
            ]
    },
    // User
    user: {
        all: [
            'user'
        ],
        profile: ()=>[
                ...queryKeys.user.all,
                'profile'
            ],
        preferences: ()=>[
                ...queryKeys.user.all,
                'preferences'
            ],
        subscription: ()=>[
                ...queryKeys.user.all,
                'subscription'
            ]
    }
};
const offlineUtils = {
    /**
   * Check if we're online
   */ isOnline: ()=>{
        return typeof navigator !== 'undefined' ? navigator.onLine : true;
    },
    /**
   * Setup online/offline event listeners
   */ setupNetworkListeners: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const handleOnline = ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].setOnlineStatus(true);
            // Refetch all queries when coming back online
            queryClient.refetchQueries();
        };
        const handleOffline = ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].setOnlineStatus(false);
        };
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        // Set initial status
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].setOnlineStatus(navigator.onLine);
        // Return cleanup function
        return ()=>{
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    },
    /**
   * Queue mutation for offline sync
   */ queueOfflineMutation: (type, action, data)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addPendingSync({
            type: type,
            action: action,
            data
        });
    }
};
// ============================================================================
// QUERY CLIENT EVENTS
// ============================================================================
// Setup global query client event listeners
queryClient.getQueryCache().subscribe((event)=>{
    // Log query events in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Query event:', event);
    }
    // Handle specific events
    switch(event.type){
        case 'added':
            break;
        case 'removed':
            break;
        case 'updated':
            break;
    }
});
queryClient.getMutationCache().subscribe((event)=>{
    // Log mutation events in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Mutation event:', event);
    }
    // Handle mutation success/error globally
    if (event.type === 'updated') {
        const mutation = event.mutation;
        if (mutation.state.status === 'success') {
            // Global success handling
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'success',
                title: 'Success',
                message: 'Operation completed successfully'
            });
        }
    }
});
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/sync/sync-manager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Sync Manager for Offline Data Synchronization
 * Handles syncing offline data when connection is restored
 */ __turbopack_context__.s({
    "SyncManager": (()=>SyncManager),
    "default": (()=>__TURBOPACK__default__export__),
    "optimisticUpdates": (()=>optimisticUpdates),
    "useSyncManager": (()=>useSyncManager),
    "useSyncStatus": (()=>useSyncStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/workouts.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/progress.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/config.ts [app-client] (ecmascript)");
;
;
;
;
class SyncManager {
    static instance;
    syncInProgress = false;
    syncQueue = [];
    constructor(){
        this.setupNetworkListeners();
    }
    static getInstance() {
        if (!SyncManager.instance) {
            SyncManager.instance = new SyncManager();
        }
        return SyncManager.instance;
    }
    /**
   * Setup network event listeners
   */ setupNetworkListeners() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const handleOnline = ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].setOnlineStatus(true);
            this.syncPendingData();
        };
        const handleOffline = ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].setOnlineStatus(false);
        };
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        // Set initial status
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].setOnlineStatus(navigator.onLine);
    }
    /**
   * Add data to sync queue for offline processing
   */ addToSyncQueue(item) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addPendingSync(item);
        // Try to sync immediately if online
        if (navigator.onLine) {
            this.syncPendingData();
        }
    }
    /**
   * Sync all pending data
   */ async syncPendingData() {
        if (this.syncInProgress || !navigator.onLine) {
            return;
        }
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState();
        const pendingItems = state.offline.pendingSync;
        if (pendingItems.length === 0) {
            return;
        }
        this.syncInProgress = true;
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'info',
                title: 'Syncing Data',
                message: `Syncing ${pendingItems.length} pending items...`
            });
            const syncResults = await Promise.allSettled(pendingItems.map((item)=>this.syncSingleItem(item)));
            // Process results
            let successCount = 0;
            let failureCount = 0;
            syncResults.forEach((result, index)=>{
                const item = pendingItems[index];
                if (result.status === 'fulfilled') {
                    successCount++;
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].removePendingSync(item.id);
                } else {
                    failureCount++;
                    console.error(`Failed to sync item ${item.id}:`, result.reason);
                }
            });
            // Update last sync time
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].updateLastSyncTime();
            // Show result notification
            if (failureCount === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                    type: 'success',
                    title: 'Sync Complete',
                    message: `Successfully synced ${successCount} items`
                });
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                    type: 'warning',
                    title: 'Sync Partially Complete',
                    message: `Synced ${successCount} items, ${failureCount} failed`
                });
            }
            // Invalidate relevant queries to refetch fresh data
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].invalidateEntity('workouts');
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].invalidateEntity('progress');
        } catch (error) {
            console.error('Sync failed:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'error',
                title: 'Sync Failed',
                message: 'Failed to sync offline data. Will retry later.'
            });
        } finally{
            this.syncInProgress = false;
        }
    }
    /**
   * Sync a single item
   */ async syncSingleItem(item) {
        switch(item.type){
            case 'workout':
                return this.syncWorkoutItem(item);
            case 'progress':
                return this.syncProgressItem(item);
            case 'goal':
                return this.syncGoalItem(item);
            default:
                throw new Error(`Unknown sync item type: ${item.type}`);
        }
    }
    /**
   * Sync workout-related items
   */ async syncWorkoutItem(item) {
        switch(item.action){
            case 'create':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WorkoutService"].createWorkoutSession(item.data);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WorkoutService"].createWorkoutProgram(item.data);
                }
                break;
            case 'update':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WorkoutService"].updateWorkoutSession(item.data.id, item.data);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WorkoutService"].updateWorkoutProgram(item.data.id, item.data);
                }
                break;
            case 'delete':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WorkoutService"].deleteWorkoutSession(item.data.id);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WorkoutService"].deleteWorkoutProgram(item.data.id);
                }
                break;
        }
        throw new Error(`Unknown workout sync action: ${item.action}`);
    }
    /**
   * Sync progress-related items
   */ async syncProgressItem(item) {
        switch(item.action){
            case 'create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProgressService"].createProgressRecord(item.data);
            case 'update':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProgressService"].updateProgressRecord(item.data.id, item.data);
            case 'delete':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProgressService"].deleteProgressRecord(item.data.id);
        }
        throw new Error(`Unknown progress sync action: ${item.action}`);
    }
    /**
   * Sync goal-related items
   */ async syncGoalItem(item) {
        switch(item.action){
            case 'create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProgressService"].createFitnessGoal(item.data);
            case 'update':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProgressService"].updateGoalProgress(item.data.id, item.data.currentValue);
            default:
                throw new Error(`Unknown goal sync action: ${item.action}`);
        }
    }
    /**
   * Force sync all data
   */ async forceSyncAll() {
        await this.syncPendingData();
    }
    /**
   * Clear all pending sync data
   */ clearPendingSync() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["appActions"].clearPendingSync();
    }
    /**
   * Get sync status
   */ getSyncStatus() {
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState();
        return {
            isOnline: state.offline.isOnline,
            pendingCount: state.offline.pendingSync.length,
            lastSyncTime: state.offline.lastSyncTime,
            syncInProgress: this.syncInProgress
        };
    }
}
function useSyncManager() {
    const syncManager = SyncManager.getInstance();
    return {
        addToSyncQueue: (item)=>syncManager.addToSyncQueue(item),
        syncPendingData: ()=>syncManager.syncPendingData(),
        forceSyncAll: ()=>syncManager.forceSyncAll(),
        clearPendingSync: ()=>syncManager.clearPendingSync(),
        getSyncStatus: ()=>syncManager.getSyncStatus()
    };
}
function useSyncStatus() {
    const syncManager = SyncManager.getInstance();
    const status = syncManager.getSyncStatus();
    return status;
}
const optimisticUpdates = {
    /**
   * Optimistically update workout session
   */ updateWorkoutSession: (sessionId, updates)=>{
        const queryKey = [
            'workouts',
            'sessions',
            sessionId
        ];
        const previousData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].getQueryData(queryKey);
        // Apply optimistic update
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, {
            ...previousData || {},
            ...updates,
            updatedAt: new Date().toISOString()
        });
        return previousData;
    },
    /**
   * Optimistically add progress record
   */ addProgressRecord: (record)=>{
        const queryKey = [
            'progress',
            'records'
        ];
        const previousData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].getQueryData(queryKey);
        if (previousData?.data) {
            const newRecord = {
                ...record,
                id: `temp-${Date.now()}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, {
                ...previousData,
                data: [
                    newRecord,
                    ...previousData.data
                ]
            });
        }
        return previousData;
    },
    /**
   * Revert optimistic update
   */ revertUpdate: (queryKey, previousData)=>{
        if (previousData !== undefined) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, previousData);
        }
    }
};
const __TURBOPACK__default__export__ = SyncManager;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/app-providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppProviders": (()=>AppProviders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/query-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/auth-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sync$2f$sync$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/sync/sync-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/config.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
function AppProviders({ children }) {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProviders.useEffect": ()=>{
            // Initialize sync manager
            const syncManager = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sync$2f$sync$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SyncManager"].getInstance();
            // Setup network listeners
            const cleanupNetworkListeners = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["offlineUtils"].setupNetworkListeners();
            // Cleanup on unmount
            return ({
                "AppProviders.useEffect": ()=>{
                    if (cleanupNetworkListeners) {
                        cleanupNetworkListeners();
                    }
                }
            })["AppProviders.useEffect"];
        }
    }["AppProviders.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/providers/app-providers.tsx",
            lineNumber: 31,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/providers/app-providers.tsx",
        lineNumber: 30,
        columnNumber: 5
    }, this);
}
_s(AppProviders, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = AppProviders;
var _c;
__turbopack_context__.k.register(_c, "AppProviders");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/performance/web-vitals.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "WebVitals": (()=>WebVitals),
    "checkPerformanceBudget": (()=>checkPerformanceBudget),
    "useCoreWebVitals": (()=>useCoreWebVitals),
    "usePerformanceObserver": (()=>usePerformanceObserver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/web-vitals/dist/web-vitals.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
// Send metrics to analytics service
function sendToAnalytics(metric) {
    // In a real application, you would send this to your analytics service
    // For example: Google Analytics, Vercel Analytics, or custom endpoint
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Web Vitals Metric:', {
            name: metric.name,
            value: metric.value,
            rating: metric.rating,
            id: metric.id
        });
    }
    // Example: Send to Google Analytics
    if ("object" !== 'undefined' && window.gtag) {
        window.gtag('event', metric.name, {
            event_category: 'Web Vitals',
            event_label: metric.id,
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            non_interaction: true
        });
    }
    // Example: Send to custom analytics endpoint
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_ANALYTICS_URL) {
        fetch(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_ANALYTICS_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                metric: metric.name,
                value: metric.value,
                rating: metric.rating,
                id: metric.id,
                url: window.location.href,
                timestamp: Date.now()
            })
        }).catch(console.error);
    }
}
function WebVitals() {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "WebVitals.useEffect": ()=>{
            // Largest Contentful Paint (LCP)
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onLCP"])(sendToAnalytics);
            // First Input Delay (FID)
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFID"])(sendToAnalytics);
            // Cumulative Layout Shift (CLS)
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onCLS"])(sendToAnalytics);
            // First Contentful Paint (FCP)
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFCP"])(sendToAnalytics);
            // Time to First Byte (TTFB)
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onTTFB"])(sendToAnalytics);
        }
    }["WebVitals.useEffect"], []);
    return null; // This component doesn't render anything
}
_s(WebVitals, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = WebVitals;
function usePerformanceObserver() {
    _s1();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePerformanceObserver.useEffect": ()=>{
            if ("object" === 'undefined' || !('PerformanceObserver' in window)) {
                return;
            }
            // Observe navigation timing
            const navObserver = new PerformanceObserver({
                "usePerformanceObserver.useEffect": (list)=>{
                    for (const entry of list.getEntries()){
                        if (entry.entryType === 'navigation') {
                            const navEntry = entry;
                            // Calculate custom metrics
                            const metrics = {
                                dns: navEntry.domainLookupEnd - navEntry.domainLookupStart,
                                tcp: navEntry.connectEnd - navEntry.connectStart,
                                ssl: navEntry.connectEnd - navEntry.secureConnectionStart,
                                ttfb: navEntry.responseStart - navEntry.requestStart,
                                download: navEntry.responseEnd - navEntry.responseStart,
                                domParse: navEntry.domContentLoadedEventStart - navEntry.responseEnd,
                                domReady: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
                                loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart
                            };
                            if ("TURBOPACK compile-time truthy", 1) {
                                console.log('Navigation Timing:', metrics);
                            }
                        }
                    }
                }
            }["usePerformanceObserver.useEffect"]);
            navObserver.observe({
                entryTypes: [
                    'navigation'
                ]
            });
            // Observe resource timing
            const resourceObserver = new PerformanceObserver({
                "usePerformanceObserver.useEffect": (list)=>{
                    for (const entry of list.getEntries()){
                        if (entry.entryType === 'resource') {
                            const resourceEntry = entry;
                            // Track slow resources
                            if (resourceEntry.duration > 1000) {
                                if ("TURBOPACK compile-time truthy", 1) {
                                    console.warn('Slow resource detected:', {
                                        name: resourceEntry.name,
                                        duration: resourceEntry.duration,
                                        size: resourceEntry.transferSize
                                    });
                                }
                            }
                        }
                    }
                }
            }["usePerformanceObserver.useEffect"]);
            resourceObserver.observe({
                entryTypes: [
                    'resource'
                ]
            });
            return ({
                "usePerformanceObserver.useEffect": ()=>{
                    navObserver.disconnect();
                    resourceObserver.disconnect();
                }
            })["usePerformanceObserver.useEffect"];
        }
    }["usePerformanceObserver.useEffect"], []);
}
_s1(usePerformanceObserver, "OD7bBpZva5O2jO+Puf00hKivP7c=");
function useCoreWebVitals() {
    _s2();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCoreWebVitals.useEffect": ()=>{
            const metrics = {};
            const updateMetric = {
                "useCoreWebVitals.useEffect.updateMetric": (metric)=>{
                    metrics[metric.name] = metric.value;
                }
            }["useCoreWebVitals.useEffect.updateMetric"];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onLCP"])(updateMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFID"])(updateMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onCLS"])(updateMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFCP"])(updateMetric);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onTTFB"])(updateMetric);
            // Return cleanup function
            return ({
                "useCoreWebVitals.useEffect": ()=>{
                // Cleanup if needed
                }
            })["useCoreWebVitals.useEffect"];
        }
    }["useCoreWebVitals.useEffect"], []);
}
_s2(useCoreWebVitals, "OD7bBpZva5O2jO+Puf00hKivP7c=");
function checkPerformanceBudget() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const budget = {
        LCP: 2500,
        FID: 100,
        CLS: 0.1,
        FCP: 1800,
        TTFB: 800
    };
    const checkMetric = (metric)=>{
        const threshold = budget[metric.name];
        if (threshold && metric.value > threshold) {
            console.warn(`Performance budget exceeded for ${metric.name}:`, {
                value: metric.value,
                budget: threshold,
                excess: metric.value - threshold
            });
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onLCP"])(checkMetric);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFID"])(checkMetric);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onCLS"])(checkMetric);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onFCP"])(checkMetric);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$vitals$2f$dist$2f$web$2d$vitals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["onTTFB"])(checkMetric);
}
var _c;
__turbopack_context__.k.register(_c, "WebVitals");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_0625db47._.js.map