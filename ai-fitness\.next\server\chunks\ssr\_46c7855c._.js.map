{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/seo/utils.ts"], "sourcesContent": ["import { Metadata } from 'next';\nimport { siteConfig } from './config';\n\n// Generate canonical URL\nexport function generateCanonicalUrl(path: string): string {\n  const baseUrl = siteConfig.url;\n  const cleanPath = path.startsWith('/') ? path : `/${path}`;\n  return `${baseUrl}${cleanPath}`;\n}\n\n// Generate Open Graph image URL\nexport function generateOGImageUrl(title: string, description?: string): string {\n  const baseUrl = siteConfig.url;\n  const params = new URLSearchParams();\n  params.set('title', title);\n  if (description) {\n    params.set('description', description);\n  }\n  return `${baseUrl}/api/og?${params.toString()}`;\n}\n\n// Truncate text for meta descriptions\nexport function truncateText(text: string, maxLength: number = 160): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength - 3).trim() + '...';\n}\n\n// Generate keywords from content\nexport function generateKeywords(content: string, additionalKeywords: string[] = []): string[] {\n  const baseKeywords = siteConfig.keywords;\n  const contentWords = content\n    .toLowerCase()\n    .replace(/[^\\w\\s]/g, ' ')\n    .split(/\\s+/)\n    .filter(word => word.length > 3)\n    .slice(0, 10);\n\n  return [...baseKeywords, ...additionalKeywords, ...contentWords];\n}\n\n// Generate page metadata\nexport function generatePageMetadata({\n  title,\n  description,\n  path,\n  keywords = [],\n  image,\n  type = 'website'\n}: {\n  title: string;\n  description: string;\n  path: string;\n  keywords?: string[];\n  image?: string;\n  type?: 'website' | 'article';\n}): Metadata {\n  const fullTitle = title.includes(siteConfig.name) ? title : `${title} | ${siteConfig.name}`;\n  const canonicalUrl = generateCanonicalUrl(path);\n  const ogImage = image || generateOGImageUrl(title, description);\n\n  const allKeywords = generateKeywords(description, keywords);\n\n  return {\n    title: fullTitle,\n    description: truncateText(description),\n    keywords: allKeywords,\n    canonical: canonicalUrl,\n    openGraph: {\n      title: fullTitle,\n      description: truncateText(description),\n      url: canonicalUrl,\n      type,\n      images: [\n        {\n          url: ogImage,\n          width: 1200,\n          height: 630,\n          alt: title\n        }\n      ],\n      siteName: siteConfig.name\n    },\n    twitter: {\n      title: fullTitle,\n      description: truncateText(description),\n      card: 'summary_large_image',\n      images: [ogImage]\n    },\n    alternates: {\n      canonical: canonicalUrl\n    }\n  };\n}\n\n// Generate exercise-specific metadata\nexport function generateExerciseMetadata(exercise: {\n  name: string;\n  description: string;\n  targetMuscles?: string[];\n  equipment?: string;\n  difficulty?: string;\n  category?: string;\n}): Metadata {\n  const title = `${exercise.name} - Exercise Guide | ${siteConfig.name}`;\n  const description = truncateText(\n    `Learn how to perform ${exercise.name} correctly. ${exercise.description} Target muscles: ${exercise.targetMuscles?.join(', ') || 'Multiple'}. Equipment: ${exercise.equipment || 'Bodyweight'}.`\n  );\n  \n  const keywords = generateKeywords(\n    `${exercise.name} ${exercise.description}`,\n    [\n      exercise.category || 'exercise',\n      exercise.equipment || 'bodyweight',\n      exercise.difficulty || 'fitness',\n      ...(exercise.targetMuscles || [])\n    ]\n  );\n\n  return {\n    title,\n    description,\n    keywords,\n    openGraph: {\n      title,\n      description,\n      type: 'article',\n      images: [\n        {\n          url: generateOGImageUrl(exercise.name, `${exercise.category} exercise for ${exercise.targetMuscles?.join(', ')}`),\n          width: 1200,\n          height: 630,\n          alt: `${exercise.name} exercise demonstration`\n        }\n      ]\n    },\n    twitter: {\n      title,\n      description,\n      card: 'summary_large_image'\n    }\n  };\n}\n\n// Generate workout-specific metadata\nexport function generateWorkoutMetadata(workout: {\n  title: string;\n  description: string;\n  duration?: number;\n  difficulty?: string;\n  type?: string;\n  targetMuscles?: string[];\n}): Metadata {\n  const title = `${workout.title} - Workout Program | ${siteConfig.name}`;\n  const description = truncateText(\n    `${workout.description} Duration: ${workout.duration || 'Flexible'} minutes. Difficulty: ${workout.difficulty || 'All levels'}. Type: ${workout.type || 'General fitness'}.`\n  );\n  \n  const keywords = generateKeywords(\n    `${workout.title} ${workout.description}`,\n    [\n      'workout program',\n      'fitness plan',\n      workout.type || 'training',\n      workout.difficulty || 'exercise',\n      ...(workout.targetMuscles || [])\n    ]\n  );\n\n  return {\n    title,\n    description,\n    keywords,\n    openGraph: {\n      title,\n      description,\n      type: 'article',\n      images: [\n        {\n          url: generateOGImageUrl(workout.title, `${workout.type} workout program - ${workout.difficulty} level`),\n          width: 1200,\n          height: 630,\n          alt: `${workout.title} workout program`\n        }\n      ]\n    },\n    twitter: {\n      title,\n      description,\n      card: 'summary_large_image'\n    }\n  };\n}\n\n// Generate FAQ schema from common questions\nexport function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {\n  return {\n    '@context': 'https://schema.org',\n    '@type': 'FAQPage',\n    mainEntity: faqs.map(faq => ({\n      '@type': 'Question',\n      name: faq.question,\n      acceptedAnswer: {\n        '@type': 'Answer',\n        text: faq.answer\n      }\n    }))\n  };\n}\n\n// Common fitness-related FAQs\nexport const commonFitnessQAs = [\n  {\n    question: \"How often should I work out?\",\n    answer: \"For general fitness, aim for at least 150 minutes of moderate-intensity exercise or 75 minutes of vigorous-intensity exercise per week, plus muscle-strengthening activities on 2 or more days per week.\"\n  },\n  {\n    question: \"What's the best time to exercise?\",\n    answer: \"The best time to exercise is when you can be consistent. Some people prefer morning workouts for energy, while others prefer evening sessions. Choose a time that fits your schedule and stick to it.\"\n  },\n  {\n    question: \"Do I need equipment to get fit?\",\n    answer: \"No, you can achieve great fitness results with bodyweight exercises. However, equipment like dumbbells, resistance bands, or kettlebells can add variety and progression to your workouts.\"\n  },\n  {\n    question: \"How long before I see results?\",\n    answer: \"You may notice improvements in energy and mood within a few days. Physical changes typically become noticeable after 2-4 weeks of consistent exercise, with significant changes after 8-12 weeks.\"\n  },\n  {\n    question: \"Should I do cardio or strength training?\",\n    answer: \"Both are important for overall fitness. Cardio improves heart health and endurance, while strength training builds muscle and bone density. A balanced program includes both types of exercise.\"\n  }\n];\n\n// Generate rich snippets for search results\nexport function generateRichSnippets(type: 'exercise' | 'workout' | 'article', data: any) {\n  switch (type) {\n    case 'exercise':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'HowTo',\n        name: `How to do ${data.name}`,\n        description: data.description,\n        totalTime: data.duration ? `PT${data.duration}M` : undefined,\n        supply: data.equipment ? [data.equipment] : undefined,\n        tool: data.equipment ? [data.equipment] : undefined,\n        step: data.instructions?.map((instruction: string, index: number) => ({\n          '@type': 'HowToStep',\n          position: index + 1,\n          text: instruction\n        }))\n      };\n    \n    case 'workout':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'ExercisePlan',\n        name: data.title,\n        description: data.description,\n        category: 'Fitness',\n        exerciseType: data.type,\n        intensity: data.difficulty,\n        workload: data.duration ? `${data.duration} minutes` : undefined\n      };\n    \n    default:\n      return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;;AAGO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAU,2HAAA,CAAA,aAAU,CAAC,GAAG;IAC9B,MAAM,YAAY,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAC1D,OAAO,GAAG,UAAU,WAAW;AACjC;AAGO,SAAS,mBAAmB,KAAa,EAAE,WAAoB;IACpE,MAAM,UAAU,2HAAA,CAAA,aAAU,CAAC,GAAG;IAC9B,MAAM,SAAS,IAAI;IACnB,OAAO,GAAG,CAAC,SAAS;IACpB,IAAI,aAAa;QACf,OAAO,GAAG,CAAC,eAAe;IAC5B;IACA,OAAO,GAAG,QAAQ,QAAQ,EAAE,OAAO,QAAQ,IAAI;AACjD;AAGO,SAAS,aAAa,IAAY,EAAE,YAAoB,GAAG;IAChE,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,GAAG,IAAI,KAAK;AACnD;AAGO,SAAS,iBAAiB,OAAe,EAAE,qBAA+B,EAAE;IACjF,MAAM,eAAe,2HAAA,CAAA,aAAU,CAAC,QAAQ;IACxC,MAAM,eAAe,QAClB,WAAW,GACX,OAAO,CAAC,YAAY,KACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG;IAEZ,OAAO;WAAI;WAAiB;WAAuB;KAAa;AAClE;AAGO,SAAS,qBAAqB,EACnC,KAAK,EACL,WAAW,EACX,IAAI,EACJ,WAAW,EAAE,EACb,KAAK,EACL,OAAO,SAAS,EAQjB;IACC,MAAM,YAAY,MAAM,QAAQ,CAAC,2HAAA,CAAA,aAAU,CAAC,IAAI,IAAI,QAAQ,GAAG,MAAM,GAAG,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IAC3F,MAAM,eAAe,qBAAqB;IAC1C,MAAM,UAAU,SAAS,mBAAmB,OAAO;IAEnD,MAAM,cAAc,iBAAiB,aAAa;IAElD,OAAO;QACL,OAAO;QACP,aAAa,aAAa;QAC1B,UAAU;QACV,WAAW;QACX,WAAW;YACT,OAAO;YACP,aAAa,aAAa;YAC1B,KAAK;YACL;YACA,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,UAAU,2HAAA,CAAA,aAAU,CAAC,IAAI;QAC3B;QACA,SAAS;YACP,OAAO;YACP,aAAa,aAAa;YAC1B,MAAM;YACN,QAAQ;gBAAC;aAAQ;QACnB;QACA,YAAY;YACV,WAAW;QACb;IACF;AACF;AAGO,SAAS,yBAAyB,QAOxC;IACC,MAAM,QAAQ,GAAG,SAAS,IAAI,CAAC,oBAAoB,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACtE,MAAM,cAAc,aAClB,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,SAAS,WAAW,CAAC,iBAAiB,EAAE,SAAS,aAAa,EAAE,KAAK,SAAS,WAAW,aAAa,EAAE,SAAS,SAAS,IAAI,aAAa,CAAC,CAAC;IAGnM,MAAM,WAAW,iBACf,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,WAAW,EAAE,EAC1C;QACE,SAAS,QAAQ,IAAI;QACrB,SAAS,SAAS,IAAI;QACtB,SAAS,UAAU,IAAI;WACnB,SAAS,aAAa,IAAI,EAAE;KACjC;IAGH,OAAO;QACL;QACA;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,mBAAmB,SAAS,IAAI,EAAE,GAAG,SAAS,QAAQ,CAAC,cAAc,EAAE,SAAS,aAAa,EAAE,KAAK,OAAO;oBAChH,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,SAAS,IAAI,CAAC,uBAAuB,CAAC;gBAChD;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,MAAM;QACR;IACF;AACF;AAGO,SAAS,wBAAwB,OAOvC;IACC,MAAM,QAAQ,GAAG,QAAQ,KAAK,CAAC,qBAAqB,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACvE,MAAM,cAAc,aAClB,GAAG,QAAQ,WAAW,CAAC,WAAW,EAAE,QAAQ,QAAQ,IAAI,WAAW,sBAAsB,EAAE,QAAQ,UAAU,IAAI,aAAa,QAAQ,EAAE,QAAQ,IAAI,IAAI,kBAAkB,CAAC,CAAC;IAG9K,MAAM,WAAW,iBACf,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,EACzC;QACE;QACA;QACA,QAAQ,IAAI,IAAI;QAChB,QAAQ,UAAU,IAAI;WAClB,QAAQ,aAAa,IAAI,EAAE;KAChC;IAGH,OAAO;QACL;QACA;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,mBAAmB,QAAQ,KAAK,EAAE,GAAG,QAAQ,IAAI,CAAC,mBAAmB,EAAE,QAAQ,UAAU,CAAC,MAAM,CAAC;oBACtG,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,QAAQ,KAAK,CAAC,gBAAgB,CAAC;gBACzC;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,MAAM;QACR;IACF;AACF;AAGO,SAAS,kBAAkB,IAAiD;IACjF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,SAAS;gBACT,MAAM,IAAI,QAAQ;gBAClB,gBAAgB;oBACd,SAAS;oBACT,MAAM,IAAI,MAAM;gBAClB;YACF,CAAC;IACH;AACF;AAGO,MAAM,mBAAmB;IAC9B;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAGM,SAAS,qBAAqB,IAAwC,EAAE,IAAS;IACtF,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;gBAC9B,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,QAAQ,GAAG,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG;gBACnD,QAAQ,KAAK,SAAS,GAAG;oBAAC,KAAK,SAAS;iBAAC,GAAG;gBAC5C,MAAM,KAAK,SAAS,GAAG;oBAAC,KAAK,SAAS;iBAAC,GAAG;gBAC1C,MAAM,KAAK,YAAY,EAAE,IAAI,CAAC,aAAqB,QAAkB,CAAC;wBACpE,SAAS;wBACT,UAAU,QAAQ;wBAClB,MAAM;oBACR,CAAC;YACH;QAEF,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,MAAM,KAAK,KAAK;gBAChB,aAAa,KAAK,WAAW;gBAC7B,UAAU;gBACV,cAAc,KAAK,IAAI;gBACvB,WAAW,KAAK,UAAU;gBAC1B,UAAU,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,CAAC,QAAQ,CAAC,GAAG;YACzD;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { BreadcrumbStructuredData } from '@/components/seo/structured-data';\n\nexport interface BreadcrumbItem {\n  name: string;\n  href: string;\n  current?: boolean;\n}\n\ninterface BreadcrumbProps {\n  items: BreadcrumbItem[];\n  className?: string;\n}\n\nexport function Breadcrumb({ items, className = '' }: BreadcrumbProps) {\n  // Prepare structured data\n  const structuredDataItems = items.map(item => ({\n    name: item.name,\n    url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app'}${item.href}`\n  }));\n\n  return (\n    <>\n      <BreadcrumbStructuredData items={structuredDataItems} />\n      <nav \n        className={`flex ${className}`} \n        aria-label=\"Breadcrumb\"\n        role=\"navigation\"\n      >\n        <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n          {/* Home link */}\n          <li className=\"inline-flex items-center\">\n            <Link\n              href=\"/\"\n              className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white\"\n              aria-label=\"Home\"\n            >\n              <Home className=\"w-4 h-4 mr-2\" />\n              Home\n            </Link>\n          </li>\n\n          {/* Breadcrumb items */}\n          {items.map((item, index) => (\n            <li key={item.href} className=\"inline-flex items-center\">\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-1\" />\n              {item.current || index === items.length - 1 ? (\n                <span \n                  className=\"ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400\"\n                  aria-current=\"page\"\n                >\n                  {item.name}\n                </span>\n              ) : (\n                <Link\n                  href={item.href}\n                  className=\"ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white\"\n                >\n                  {item.name}\n                </Link>\n              )}\n            </li>\n          ))}\n        </ol>\n      </nav>\n    </>\n  );\n}\n\n// Utility function to generate breadcrumbs from pathname\nexport function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {\n  const segments = pathname.split('/').filter(Boolean);\n  const breadcrumbs: BreadcrumbItem[] = [];\n\n  segments.forEach((segment, index) => {\n    const href = '/' + segments.slice(0, index + 1).join('/');\n    const name = formatSegmentName(segment);\n    \n    breadcrumbs.push({\n      name,\n      href,\n      current: index === segments.length - 1\n    });\n  });\n\n  return breadcrumbs;\n}\n\n// Format segment names for display\nfunction formatSegmentName(segment: string): string {\n  // Handle dynamic routes\n  if (segment.startsWith('[') && segment.endsWith(']')) {\n    return 'Details';\n  }\n\n  // Convert kebab-case to Title Case\n  return segment\n    .split('-')\n    .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n    .join(' ');\n}\n\n// Predefined breadcrumb configurations for specific pages\nexport const breadcrumbConfigs: Record<string, BreadcrumbItem[]> = {\n  '/exercises': [\n    { name: 'Exercises', href: '/exercises' }\n  ],\n  '/workouts': [\n    { name: 'Workouts', href: '/workouts' }\n  ],\n  '/progress': [\n    { name: 'Progress', href: '/progress' }\n  ],\n  '/dashboard': [\n    { name: 'Dashboard', href: '/dashboard' }\n  ],\n  '/auth/signin': [\n    { name: 'Sign In', href: '/auth/signin' }\n  ],\n  '/auth/signup': [\n    { name: 'Sign Up', href: '/auth/signup' }\n  ]\n};\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AAAA;AACA;;;;;AAaO,SAAS,WAAW,EAAE,KAAK,EAAE,YAAY,EAAE,EAAmB;IACnE,0BAA0B;IAC1B,MAAM,sBAAsB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC7C,MAAM,KAAK,IAAI;YACf,KAAK,GAAG,QAAQ,GAAG,CAAC,mBAAmB,IAAI,0CAA0C,KAAK,IAAI,EAAE;QAClG,CAAC;IAED,qBACE;;0BACE,8OAAC,+IAAA,CAAA,2BAAwB;gBAAC,OAAO;;;;;;0BACjC,8OAAC;gBACC,WAAW,CAAC,KAAK,EAAE,WAAW;gBAC9B,cAAW;gBACX,MAAK;0BAEL,cAAA,8OAAC;oBAAG,WAAU;;sCAEZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,cAAW;;kDAEX,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;wBAMpC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCACvB,KAAK,OAAO,IAAI,UAAU,MAAM,MAAM,GAAG,kBACxC,8OAAC;wCACC,WAAU;wCACV,gBAAa;kDAEZ,KAAK,IAAI;;;;;6DAGZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;;;;;;;+BAdP,KAAK,IAAI;;;;;;;;;;;;;;;;;;AAuB9B;AAGO,SAAS,oBAAoB,QAAgB;IAClD,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,SAAS,OAAO,CAAC,CAAC,SAAS;QACzB,MAAM,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;QACrD,MAAM,OAAO,kBAAkB;QAE/B,YAAY,IAAI,CAAC;YACf;YACA;YACA,SAAS,UAAU,SAAS,MAAM,GAAG;QACvC;IACF;IAEA,OAAO;AACT;AAEA,mCAAmC;AACnC,SAAS,kBAAkB,OAAe;IACxC,wBAAwB;IACxB,IAAI,QAAQ,UAAU,CAAC,QAAQ,QAAQ,QAAQ,CAAC,MAAM;QACpD,OAAO;IACT;IAEA,mCAAmC;IACnC,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;AACV;AAGO,MAAM,oBAAsD;IACjE,cAAc;QACZ;YAAE,MAAM;YAAa,MAAM;QAAa;KACzC;IACD,aAAa;QACX;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;IACD,aAAa;QACX;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;IACD,cAAc;QACZ;YAAE,MAAM;YAAa,MAAM;QAAa;KACzC;IACD,gBAAgB;QACd;YAAE,MAAM;YAAW,MAAM;QAAe;KACzC;IACD,gBAAgB;QACd;YAAE,MAAM;YAAW,MAAM;QAAe;KACzC;AACH", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/exercises/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { generatePageMetadata } from '@/lib/seo/utils';\nimport { Breadcrumb } from '@/components/ui/breadcrumb';\n\nexport const metadata: Metadata = generatePageMetadata({\n  title: \"Exercise Database - Comprehensive Fitness Exercise Library\",\n  description: \"Explore our comprehensive database of fitness exercises with detailed instructions, target muscles, equipment requirements, and difficulty levels. Perfect for building custom workout plans.\",\n  path: \"/exercises\",\n});\n\nexport default function ExercisesLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const breadcrumbItems = [\n    { name: 'Exercises', href: '/exercises' }\n  ];\n\n  return (\n    <div>\n      <div className=\"container mx-auto px-4 py-4\">\n        <Breadcrumb items={breadcrumbItems} />\n      </div>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,MAAM,WAAqB,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;IACrD,OAAO;IACP,aAAa;IACb,MAAM;AACR;AAEe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAa,MAAM;QAAa;KACzC;IAED,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oBAAC,OAAO;;;;;;;;;;;YAEpB;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "file": "chevron-right.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,eAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}