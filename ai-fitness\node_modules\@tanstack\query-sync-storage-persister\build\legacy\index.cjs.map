{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["import { noop } from './utils'\nimport type {\n  PersistRetryer,\n  PersistedClient,\n  Persister,\n} from '@tanstack/query-persist-client-core'\n\ninterface Storage {\n  getItem: (key: string) => string | null\n  setItem: (key: string, value: string) => void\n  removeItem: (key: string) => void\n}\n\ninterface CreateSyncStoragePersisterOptions {\n  /** The storage client used for setting and retrieving items from cache.\n   * For SSR pass in `undefined`. Note that window.localStorage can be\n   * `null` in Android WebViews depending on how they are configured.\n   */\n  storage: Storage | undefined | null\n  /** The key to use when storing the cache */\n  key?: string\n  /** To avoid spamming,\n   * pass a time in ms to throttle saving the cache to disk */\n  throttleTime?: number\n  /**\n   * How to serialize the data to storage.\n   * @default `JSON.stringify`\n   */\n  serialize?: (client: PersistedClient) => string\n  /**\n   * How to deserialize the data from storage.\n   * @default `JSON.parse`\n   */\n  deserialize?: (cachedString: string) => PersistedClient\n\n  retry?: PersistRetryer\n}\n\n/**\n * @deprecated use `createAsyncStoragePersister` from `@tanstack/query-async-storage-persister` instead.\n */\nexport function createSyncStoragePersister({\n  storage,\n  key = `REACT_QUERY_OFFLINE_CACHE`,\n  throttleTime = 1000,\n  serialize = JSON.stringify,\n  deserialize = JSON.parse,\n  retry,\n}: CreateSyncStoragePersisterOptions): Persister {\n  if (storage) {\n    const trySave = (persistedClient: PersistedClient): Error | undefined => {\n      try {\n        storage.setItem(key, serialize(persistedClient))\n        return\n      } catch (error) {\n        return error as Error\n      }\n    }\n    return {\n      persistClient: throttle((persistedClient) => {\n        let client: PersistedClient | undefined = persistedClient\n        let error = trySave(client)\n        let errorCount = 0\n        while (error && client) {\n          errorCount++\n          client = retry?.({\n            persistedClient: client,\n            error,\n            errorCount,\n          })\n\n          if (client) {\n            error = trySave(client)\n          }\n        }\n      }, throttleTime),\n      restoreClient: () => {\n        const cacheString = storage.getItem(key)\n\n        if (!cacheString) {\n          return\n        }\n\n        return deserialize(cacheString)\n      },\n      removeClient: () => {\n        storage.removeItem(key)\n      },\n    }\n  }\n\n  return {\n    persistClient: noop,\n    restoreClient: noop,\n    removeClient: noop,\n  }\n}\n\nfunction throttle<TArgs extends Array<any>>(\n  func: (...args: TArgs) => any,\n  wait = 100,\n) {\n  let timer: ReturnType<typeof setTimeout> | null = null\n  let params: TArgs\n  return function (...args: TArgs) {\n    params = args\n    if (timer === null) {\n      timer = setTimeout(() => {\n        func(...params)\n        timer = null\n      }, wait)\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAqB;AAyCd,SAAS,2BAA2B;AAAA,EACzC;AAAA,EACA,MAAM;AAAA,EACN,eAAe;AAAA,EACf,YAAY,KAAK;AAAA,EACjB,cAAc,KAAK;AAAA,EACnB;AACF,GAAiD;AAC/C,MAAI,SAAS;AACX,UAAM,UAAU,CAAC,oBAAwD;AACvE,UAAI;AACF,gBAAQ,QAAQ,KAAK,UAAU,eAAe,CAAC;AAC/C;AAAA,MACF,SAAS,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,MACL,eAAe,SAAS,CAAC,oBAAoB;AAC3C,YAAI,SAAsC;AAC1C,YAAI,QAAQ,QAAQ,MAAM;AAC1B,YAAI,aAAa;AACjB,eAAO,SAAS,QAAQ;AACtB;AACA,mBAAS,+BAAQ;AAAA,YACf,iBAAiB;AAAA,YACjB;AAAA,YACA;AAAA,UACF;AAEA,cAAI,QAAQ;AACV,oBAAQ,QAAQ,MAAM;AAAA,UACxB;AAAA,QACF;AAAA,MACF,GAAG,YAAY;AAAA,MACf,eAAe,MAAM;AACnB,cAAM,cAAc,QAAQ,QAAQ,GAAG;AAEvC,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AAEA,eAAO,YAAY,WAAW;AAAA,MAChC;AAAA,MACA,cAAc,MAAM;AAClB,gBAAQ,WAAW,GAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,eAAe;AAAA,IACf,eAAe;AAAA,IACf,cAAc;AAAA,EAChB;AACF;AAEA,SAAS,SACP,MACA,OAAO,KACP;AACA,MAAI,QAA8C;AAClD,MAAI;AACJ,SAAO,YAAa,MAAa;AAC/B,aAAS;AACT,QAAI,UAAU,MAAM;AAClB,cAAQ,WAAW,MAAM;AACvB,aAAK,GAAG,MAAM;AACd,gBAAQ;AAAA,MACV,GAAG,IAAI;AAAA,IACT;AAAA,EACF;AACF;", "names": []}