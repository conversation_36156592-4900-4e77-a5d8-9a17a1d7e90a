{"name": "workoutcool", "version": "1.2.3", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "email": "email dev", "start": "next start", "stripe-webhooks": "stripe listen --forward-to localhost:3000/api/webhooks/stripe", "vercel-build": "next build", "old-vercel-build": "prisma generate && prisma migrate deploy && next build", "postinstall": "prisma generate", "lint": "next lint", "import:exercises-full": "tsx scripts/import-exercises-with-attributes.ts", "db:seed": "pnpm run import:exercises-full ./data/sample-exercises.csv", "db:seed-plans": "tsx scripts/seed-subscription-plans-simple.ts", "migrate:prod": "env-cmd -f .env.production prisma migrate deploy"}, "resolutions": {"prettier": "^3.4.2"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@openpanel/nextjs": "^1.0.8", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-portal": "^1.1.5", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.1.8", "@react-email/components": "^0.0.35", "@react-email/html": "^0.0.11", "@react-email/render": "^1.1.2", "@react-email/tailwind": "^1.0.4", "@t3-oss/env-nextjs": "^0.12.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.74.3", "@tanstack/react-query-devtools": "^5.74.4", "@vercel/functions": "^2.0.3", "better-auth": "^1.2.7", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parser": "^3.2.0", "daisyui": "^5.0.43", "dayjs": "^1.11.13", "eslint-config-prettier": "^10.1.1", "framer-motion": "^12.7.2", "geist": "^1.3.1", "lucide-react": "^0.487.0", "next": "15.2.3", "next-international": "^1.3.1", "next-mdx-remote": "^5.0.0", "next-safe-action": "^7.10.4", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "npm": "^11.3.0", "nprogress": "^0.2.0", "nuqs": "^2.4.3", "pg": "^8.14.1", "pinyin-pro": "^3.26.0", "prisma": "^6.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "slugify": "^1.6.6", "sonner": "^2.0.3", "stripe": "18.2.1", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@next/bundle-analyzer": "^15.3.4", "@next/eslint-plugin-next": "^15.2.4", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "autoprefixer": "^10.4.21", "env-cmd": "^10.1.0", "eslint": "^9.23.0", "eslint-config-next": "15.2.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "prettier-plugin-sort-json": "^4.1.1", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "tslog": "^4.9.3", "tsx": "^4.19.4", "typescript": "^5", "typescript-eslint": "^8.29.0"}}