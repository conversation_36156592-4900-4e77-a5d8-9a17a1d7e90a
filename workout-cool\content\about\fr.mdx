import Link from "next/link";

# À propos de Workout.cool

## Pourquoi Workout.cool ?

Workout.cool est né de la volonté de proposer une plateforme d'entraînement fiable, moderne et maintenue, après l'abandon du projet <WorkoutLol variant="muted" />.

## L'histoire

Workout.cool est le fruit d'une aventure communautaire.

J'ai été le **premier contributeur open source** du projet <WorkoutLol variant="muted" />.

De ce fait, j'ai vu ce projet *naître*, *grandir*, puis être **vendu** et finalement **abandonné** par son nouveau propriétaire.

Comme beaucoup d'utilisateurs, j'ai ressenti une **grande frustration** et un *sentiment d'abandon* en voyant disparaître un outil auquel j'avais tant contribué, et en voyant les demandes d'évolution se perdre et prendre de l'âge.

---

*Pendant des mois*, j'ai tenté de contacter le nouveau propriétaire, **sans jamais obtenir de réponse** malgré de nombreux essais (*environ 15*).

Face à ce **silence** et à la **détresse de la communauté**, j'ai décidé de **prendre les choses en main** :

> Plutôt que de laisser ce travail disparaître, **j'ai relancé un projet encore plus ambitieux, moderne et ouvert à tous.**

Ce projet n'est pas motivé par le profit, mais par la **passion** et l'envie de servir la communauté fitness open source.

**Quelqu'un devait sauver la communauté, _j'ai décidé d'être ce quelqu'un_ !**

## Open source & communauté

Workout.cool est open source, garantir transparence, modularité et évolutivité.  
Toute contribution est la bienvenue, que ce soit pour le code, la documentation ou les idées !

- [Voir le projet sur GitHub](https://github.com/Snouzy/workout-cool)
- [Payer un café en guise de soutien](https://ko-fi.com/workoutcool)

## Rejoignez la mission !

Vous souhaitez contribuer, proposer une fonctionnalité ou simplement soutenir le projet ?  
Contactez-nous ou ouvrez une issue sur GitHub !

**[<EMAIL>](mailto:<EMAIL>)**