"use strict";(()=>{var e={};e.id=772,e.ids=[772],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},97886:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>i});var s=r(96559),n=r(48088),o=r(37719),u=r(32190);async function i(e){try{let e={status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",environment:"production",uptime:process.uptime(),memory:{used:Math.round(process.memoryUsage().heapUsed/1024/1024),total:Math.round(process.memoryUsage().heapTotal/1024/1024),external:Math.round(process.memoryUsage().external/1024/1024)},checks:{api:await c(),database:await h(),external:await p()}};Object.values(e.checks).every(e=>"healthy"===e.status)||(e.status="degraded");let t="healthy"===e.status?200:503;return u.NextResponse.json(e,{status:t,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return console.error("Health check failed:",e),u.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",version:"1.0.0",environment:"production"},{status:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}}async function c(){try{let e=Date.now(),t=Date.now()-e;return{status:"healthy",responseTime:t}}catch(e){return{status:"unhealthy",error:e instanceof Error?e.message:"Unknown error"}}}async function h(){try{return{status:"healthy",responseTime:0}}catch(e){return{status:"unhealthy",error:e instanceof Error?e.message:"Unknown error"}}}async function p(){try{let e={workoutApi:await l()};return{status:Object.values(e).every(e=>"healthy"===e.status)?"healthy":"degraded",services:e}}catch(e){return{status:"unhealthy",error:e instanceof Error?e.message:"Unknown error"}}}async function l(){try{let e=Date.now(),t=Date.now()-e;return{status:"healthy",responseTime:t}}catch(e){return{status:"degraded",error:e instanceof Error?e.message:"Connection failed"}}}let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\api\\health\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:y,serverHooks:g}=d;function x(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:y})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[96,76],()=>r(97886));module.exports=a})();