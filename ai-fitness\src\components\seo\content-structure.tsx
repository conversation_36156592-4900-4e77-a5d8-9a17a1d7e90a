import React from 'react';
import { cn } from '@/lib/utils';
import { SemanticHeader, Section, Article, Time, DefinitionList } from './semantic-html';
import { InternalLink } from './internal-links';

// Table of Contents component
interface TOCItem {
  id: string;
  title: string;
  level: number;
  children?: TOCItem[];
}

interface TableOfContentsProps {
  items: TOCItem[];
  className?: string;
}

export function TableOfContents({ items, className }: TableOfContentsProps) {
  const renderTOCItem = (item: TOCItem, index: number) => (
    <li key={index} className={cn("mb-1", item.level > 1 && "ml-4")}>
      <a
        href={`#${item.id}`}
        className="text-blue-600 hover:text-blue-800 text-sm block py-1"
        title={`Jump to ${item.title}`}
      >
        {item.title}
      </a>
      {item.children && item.children.length > 0 && (
        <ul className="mt-1">
          {item.children.map((child, childIndex) => renderTOCItem(child, childIndex))}
        </ul>
      )}
    </li>
  );

  return (
    <nav className={cn("bg-gray-50 p-4 rounded-lg", className)} aria-label="Table of contents">
      <h3 className="font-semibold mb-3 text-gray-900">Table of Contents</h3>
      <ul className="space-y-1">
        {items.map((item, index) => renderTOCItem(item, index))}
      </ul>
    </nav>
  );
}

// Exercise detail content structure
interface ExerciseContentProps {
  exercise: {
    name: string;
    description: string;
    targetMuscles: string[];
    equipment: string;
    difficulty: string;
    instructions: string[];
    tips?: string[];
    variations?: string[];
    safetyNotes?: string[];
  };
  className?: string;
}

export function ExerciseContent({ exercise, className }: ExerciseContentProps) {
  const tocItems: TOCItem[] = [
    { id: 'overview', title: 'Overview', level: 1 },
    { id: 'instructions', title: 'Instructions', level: 1 },
    { id: 'details', title: 'Exercise Details', level: 1 },
    ...(exercise.tips ? [{ id: 'tips', title: 'Tips & Form', level: 1 }] : []),
    ...(exercise.variations ? [{ id: 'variations', title: 'Variations', level: 1 }] : []),
    ...(exercise.safetyNotes ? [{ id: 'safety', title: 'Safety Notes', level: 1 }] : []),
  ];

  const exerciseDetails = [
    { term: 'Target Muscles', definition: exercise.targetMuscles.join(', ') },
    { term: 'Equipment', definition: exercise.equipment },
    { term: 'Difficulty Level', definition: exercise.difficulty },
  ];

  return (
    <Article className={cn("max-w-4xl mx-auto", className)} itemScope itemType="https://schema.org/ExerciseAction">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Table of Contents */}
        <div className="lg:col-span-1">
          <TableOfContents items={tocItems} className="sticky top-4" />
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-8">
          {/* Overview */}
          <Section id="overview" ariaLabel="Exercise overview">
            <SemanticHeader level={1} className="text-3xl font-bold mb-4" itemProp="name">
              {exercise.name}
            </SemanticHeader>
            <p className="text-lg text-gray-700 leading-relaxed" itemProp="description">
              {exercise.description}
            </p>
          </Section>

          {/* Instructions */}
          <Section id="instructions" ariaLabel="Exercise instructions">
            <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
              How to Perform {exercise.name}
            </SemanticHeader>
            <ol className="space-y-3" itemProp="instructions">
              {exercise.instructions.map((instruction, index) => (
                <li key={index} className="flex">
                  <span className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                    {index + 1}
                  </span>
                  <span className="text-gray-700">{instruction}</span>
                </li>
              ))}
            </ol>
          </Section>

          {/* Exercise Details */}
          <Section id="details" ariaLabel="Exercise details">
            <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
              Exercise Details
            </SemanticHeader>
            <DefinitionList items={exerciseDetails} className="bg-gray-50 p-4 rounded-lg" />
          </Section>

          {/* Tips */}
          {exercise.tips && (
            <Section id="tips" ariaLabel="Exercise tips">
              <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
                Tips for Better Form
              </SemanticHeader>
              <ul className="space-y-2">
                {exercise.tips.map((tip, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-600 mr-2">✓</span>
                    <span className="text-gray-700">{tip}</span>
                  </li>
                ))}
              </ul>
            </Section>
          )}

          {/* Variations */}
          {exercise.variations && (
            <Section id="variations" ariaLabel="Exercise variations">
              <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
                Exercise Variations
              </SemanticHeader>
              <ul className="space-y-2">
                {exercise.variations.map((variation, index) => (
                  <li key={index} className="text-gray-700">
                    <strong>{variation.split(':')[0]}:</strong> {variation.split(':')[1]}
                  </li>
                ))}
              </ul>
            </Section>
          )}

          {/* Safety Notes */}
          {exercise.safetyNotes && (
            <Section id="safety" ariaLabel="Safety notes">
              <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
                Safety Considerations
              </SemanticHeader>
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                <ul className="space-y-2">
                  {exercise.safetyNotes.map((note, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-yellow-600 mr-2">⚠️</span>
                      <span className="text-gray-700">{note}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </Section>
          )}
        </div>
      </div>
    </Article>
  );
}

// Workout program content structure
interface WorkoutContentProps {
  workout: {
    title: string;
    description: string;
    duration: number;
    difficulty: string;
    type: string;
    exercises: Array<{
      name: string;
      sets: number;
      reps: string;
      rest: string;
    }>;
    schedule?: string[];
    goals?: string[];
  };
  className?: string;
}

export function WorkoutContent({ workout, className }: WorkoutContentProps) {
  const tocItems: TOCItem[] = [
    { id: 'overview', title: 'Program Overview', level: 1 },
    { id: 'exercises', title: 'Exercise List', level: 1 },
    { id: 'details', title: 'Program Details', level: 1 },
    ...(workout.schedule ? [{ id: 'schedule', title: 'Training Schedule', level: 1 }] : []),
    ...(workout.goals ? [{ id: 'goals', title: 'Training Goals', level: 1 }] : []),
  ];

  const workoutDetails = [
    { term: 'Duration', definition: `${workout.duration} minutes` },
    { term: 'Difficulty', definition: workout.difficulty },
    { term: 'Type', definition: workout.type },
    { term: 'Total Exercises', definition: workout.exercises.length.toString() },
  ];

  return (
    <Article className={cn("max-w-4xl mx-auto", className)} itemScope itemType="https://schema.org/ExercisePlan">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Table of Contents */}
        <div className="lg:col-span-1">
          <TableOfContents items={tocItems} className="sticky top-4" />
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-8">
          {/* Overview */}
          <Section id="overview" ariaLabel="Workout overview">
            <SemanticHeader level={1} className="text-3xl font-bold mb-4" itemProp="name">
              {workout.title}
            </SemanticHeader>
            <p className="text-lg text-gray-700 leading-relaxed" itemProp="description">
              {workout.description}
            </p>
          </Section>

          {/* Exercise List */}
          <Section id="exercises" ariaLabel="Exercise list">
            <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
              Exercise List
            </SemanticHeader>
            <div className="space-y-4">
              {workout.exercises.map((exercise, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-lg">{exercise.name}</h3>
                      <div className="text-sm text-gray-600 mt-1">
                        {exercise.sets} sets × {exercise.reps} reps
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      Rest: {exercise.rest}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Section>

          {/* Program Details */}
          <Section id="details" ariaLabel="Program details">
            <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
              Program Details
            </SemanticHeader>
            <DefinitionList items={workoutDetails} className="bg-gray-50 p-4 rounded-lg" />
          </Section>

          {/* Schedule */}
          {workout.schedule && (
            <Section id="schedule" ariaLabel="Training schedule">
              <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
                Training Schedule
              </SemanticHeader>
              <ul className="space-y-2">
                {workout.schedule.map((day, index) => (
                  <li key={index} className="text-gray-700">
                    <strong>Day {index + 1}:</strong> {day}
                  </li>
                ))}
              </ul>
            </Section>
          )}

          {/* Goals */}
          {workout.goals && (
            <Section id="goals" ariaLabel="Training goals">
              <SemanticHeader level={2} className="text-2xl font-semibold mb-4">
                Training Goals
              </SemanticHeader>
              <ul className="space-y-2">
                {workout.goals.map((goal, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-600 mr-2">🎯</span>
                    <span className="text-gray-700">{goal}</span>
                  </li>
                ))}
              </ul>
            </Section>
          )}
        </div>
      </div>
    </Article>
  );
}
