/**
 * API Client for AI-fitness application
 * Handles HTTP requests, authentication, and error handling
 */

import { API_CONFIG } from './config';
import { ApiError, NetworkError, AuthenticationError, ValidationError } from './errors';

export interface ApiRequestOptions extends RequestInit {
  timeout?: number;
  retries?: number;
  requireAuth?: boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
}

class ApiClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  /**
   * Get authentication token from session storage or cookies
   */
  private getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      // Try to get token from localStorage first
      const token = localStorage.getItem('auth-token');
      if (token) {
        return token;
      }

      // Try to get user data and extract token
      const userData = localStorage.getItem('auth-user');
      if (userData) {
        try {
          const user = JSON.parse(userData);
          return user.token || null;
        } catch {
          return null;
        }
      }
    }
    return null;
  }

  /**
   * Build request headers with authentication if available
   */
  private buildHeaders(options: ApiRequestOptions = {}): HeadersInit {
    const headers = { ...this.defaultHeaders };

    // Add authentication header if required and available
    if (options.requireAuth !== false) {
      const token = this.getAuthToken();
      if (token) {
        (headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
      }
    }

    // Merge with custom headers
    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    return headers;
  }

  /**
   * Handle API response and errors
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      throw new ApiError('Failed to parse response', response.status);
    }

    if (!response.ok) {
      switch (response.status) {
        case 401:
          throw new AuthenticationError(data.message || 'Authentication required');
        case 422:
          throw new ValidationError(data.message || 'Validation failed', data.errors);
        case 404:
          throw new ApiError(data.message || 'Resource not found', 404);
        case 500:
          throw new ApiError(data.message || 'Internal server error', 500);
        default:
          throw new ApiError(data.message || 'Request failed', response.status);
      }
    }

    return data;
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest<T>(
    url: string,
    options: ApiRequestOptions = {}
  ): Promise<T> {
    const {
      timeout = API_CONFIG.TIMEOUT.DEFAULT,
      retries = API_CONFIG.RETRY.ATTEMPTS,
      ...requestOptions
    } = options;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const requestConfig: RequestInit = {
      ...requestOptions,
      headers: this.buildHeaders(options),
      signal: controller.signal,
    };

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}${url}`, requestConfig);
        clearTimeout(timeoutId);
        return await this.handleResponse<T>(response);
      } catch (error) {
        lastError = error as Error;

        // Don't retry on authentication or validation errors
        if (error instanceof AuthenticationError || error instanceof ValidationError) {
          throw error;
        }

        // Don't retry on the last attempt
        if (attempt === retries) {
          break;
        }

        // Wait before retrying
        const delay = API_CONFIG.RETRY.DELAY * Math.pow(API_CONFIG.RETRY.BACKOFF_FACTOR, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    clearTimeout(timeoutId);

    // Handle network errors
    if (lastError) {
      if (lastError.name === 'AbortError') {
        throw new NetworkError('Request timeout');
      }

      if (lastError.name === 'TypeError') {
        throw new NetworkError('Network error - please check your connection');
      }

      throw lastError;
    }

    throw new Error('Request failed after all retries');
  }

  /**
   * GET request
   */
  async get<T>(url: string, options: ApiRequestOptions = {}): Promise<T> {
    return this.makeRequest<T>(url, { ...options, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    return this.makeRequest<T>(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    return this.makeRequest<T>(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {
    return this.makeRequest<T>(url, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(url: string, options: ApiRequestOptions = {}): Promise<T> {
    return this.makeRequest<T>(url, { ...options, method: 'DELETE' });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
