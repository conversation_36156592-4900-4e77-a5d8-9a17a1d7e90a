import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, UserProfile } from '@/types'

interface AuthState {
  user: User | null
  profile: UserProfile | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  setUser: (user: User | null) => void
  setProfile: (profile: UserProfile | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  register: (email: string, password: string, name: string) => Promise<void>
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      profile: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      setUser: (user) => {
        set({ user, isAuthenticated: !!user })
      },

      setProfile: (profile) => {
        set({ profile })
      },

      setLoading: (isLoading) => {
        set({ isLoading })
      },

      setError: (error) => {
        set({ error })
      },

      login: async (email, password) => {
        set({ isLoading: true, error: null })
        try {
          // TODO: Implement actual API call
          // For now, simulate login
          const mockUser: User = {
            id: '1',
            email,
            name: 'Demo User',
            createdAt: new Date(),
            updatedAt: new Date(),
          }
          
          set({ 
            user: mockUser, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Login failed', 
            isLoading: false 
          })
        }
      },

      logout: () => {
        set({ 
          user: null, 
          profile: null, 
          isAuthenticated: false, 
          error: null 
        })
      },

      register: async (email, password, name) => {
        set({ isLoading: true, error: null })
        try {
          // TODO: Implement actual API call
          // For now, simulate registration
          const mockUser: User = {
            id: '1',
            email,
            name,
            createdAt: new Date(),
            updatedAt: new Date(),
          }
          
          set({ 
            user: mockUser, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Registration failed', 
            isLoading: false 
          })
        }
      },

      updateProfile: async (profileData) => {
        set({ isLoading: true, error: null })
        try {
          const { profile } = get()
          const updatedProfile: UserProfile = {
            ...profile!,
            ...profileData,
            updatedAt: new Date(),
          }
          
          set({ 
            profile: updatedProfile, 
            isLoading: false 
          })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Profile update failed', 
            isLoading: false 
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        profile: state.profile, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)
