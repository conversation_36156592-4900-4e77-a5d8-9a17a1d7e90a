{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/seo/utils.ts"], "sourcesContent": ["import { Metadata } from 'next';\nimport { siteConfig } from './config';\n\n// Generate canonical URL\nexport function generateCanonicalUrl(path: string): string {\n  const baseUrl = siteConfig.url;\n  const cleanPath = path.startsWith('/') ? path : `/${path}`;\n  return `${baseUrl}${cleanPath}`;\n}\n\n// Generate Open Graph image URL\nexport function generateOGImageUrl(title: string, description?: string): string {\n  const baseUrl = siteConfig.url;\n  const params = new URLSearchParams();\n  params.set('title', title);\n  if (description) {\n    params.set('description', description);\n  }\n  return `${baseUrl}/api/og?${params.toString()}`;\n}\n\n// Truncate text for meta descriptions\nexport function truncateText(text: string, maxLength: number = 160): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength - 3).trim() + '...';\n}\n\n// Generate keywords from content\nexport function generateKeywords(content: string, additionalKeywords: string[] = []): string[] {\n  const baseKeywords = siteConfig.keywords;\n  const contentWords = content\n    .toLowerCase()\n    .replace(/[^\\w\\s]/g, ' ')\n    .split(/\\s+/)\n    .filter(word => word.length > 3)\n    .slice(0, 10);\n\n  return [...baseKeywords, ...additionalKeywords, ...contentWords];\n}\n\n// Generate page metadata\nexport function generatePageMetadata({\n  title,\n  description,\n  path,\n  keywords = [],\n  image,\n  type = 'website'\n}: {\n  title: string;\n  description: string;\n  path: string;\n  keywords?: string[];\n  image?: string;\n  type?: 'website' | 'article';\n}): Metadata {\n  const fullTitle = title.includes(siteConfig.name) ? title : `${title} | ${siteConfig.name}`;\n  const canonicalUrl = generateCanonicalUrl(path);\n  const ogImage = image || generateOGImageUrl(title, description);\n\n  const allKeywords = generateKeywords(description, keywords);\n\n  return {\n    title: fullTitle,\n    description: truncateText(description),\n    keywords: allKeywords,\n    canonical: canonicalUrl,\n    openGraph: {\n      title: fullTitle,\n      description: truncateText(description),\n      url: canonicalUrl,\n      type,\n      images: [\n        {\n          url: ogImage,\n          width: 1200,\n          height: 630,\n          alt: title\n        }\n      ],\n      siteName: siteConfig.name\n    },\n    twitter: {\n      title: fullTitle,\n      description: truncateText(description),\n      card: 'summary_large_image',\n      images: [ogImage]\n    },\n    alternates: {\n      canonical: canonicalUrl\n    }\n  };\n}\n\n// Generate exercise-specific metadata\nexport function generateExerciseMetadata(exercise: {\n  name: string;\n  description: string;\n  targetMuscles?: string[];\n  equipment?: string;\n  difficulty?: string;\n  category?: string;\n}): Metadata {\n  const title = `${exercise.name} - Exercise Guide | ${siteConfig.name}`;\n  const description = truncateText(\n    `Learn how to perform ${exercise.name} correctly. ${exercise.description} Target muscles: ${exercise.targetMuscles?.join(', ') || 'Multiple'}. Equipment: ${exercise.equipment || 'Bodyweight'}.`\n  );\n  \n  const keywords = generateKeywords(\n    `${exercise.name} ${exercise.description}`,\n    [\n      exercise.category || 'exercise',\n      exercise.equipment || 'bodyweight',\n      exercise.difficulty || 'fitness',\n      ...(exercise.targetMuscles || [])\n    ]\n  );\n\n  return {\n    title,\n    description,\n    keywords,\n    openGraph: {\n      title,\n      description,\n      type: 'article',\n      images: [\n        {\n          url: generateOGImageUrl(exercise.name, `${exercise.category} exercise for ${exercise.targetMuscles?.join(', ')}`),\n          width: 1200,\n          height: 630,\n          alt: `${exercise.name} exercise demonstration`\n        }\n      ]\n    },\n    twitter: {\n      title,\n      description,\n      card: 'summary_large_image'\n    }\n  };\n}\n\n// Generate workout-specific metadata\nexport function generateWorkoutMetadata(workout: {\n  title: string;\n  description: string;\n  duration?: number;\n  difficulty?: string;\n  type?: string;\n  targetMuscles?: string[];\n}): Metadata {\n  const title = `${workout.title} - Workout Program | ${siteConfig.name}`;\n  const description = truncateText(\n    `${workout.description} Duration: ${workout.duration || 'Flexible'} minutes. Difficulty: ${workout.difficulty || 'All levels'}. Type: ${workout.type || 'General fitness'}.`\n  );\n  \n  const keywords = generateKeywords(\n    `${workout.title} ${workout.description}`,\n    [\n      'workout program',\n      'fitness plan',\n      workout.type || 'training',\n      workout.difficulty || 'exercise',\n      ...(workout.targetMuscles || [])\n    ]\n  );\n\n  return {\n    title,\n    description,\n    keywords,\n    openGraph: {\n      title,\n      description,\n      type: 'article',\n      images: [\n        {\n          url: generateOGImageUrl(workout.title, `${workout.type} workout program - ${workout.difficulty} level`),\n          width: 1200,\n          height: 630,\n          alt: `${workout.title} workout program`\n        }\n      ]\n    },\n    twitter: {\n      title,\n      description,\n      card: 'summary_large_image'\n    }\n  };\n}\n\n// Generate FAQ schema from common questions\nexport function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {\n  return {\n    '@context': 'https://schema.org',\n    '@type': 'FAQPage',\n    mainEntity: faqs.map(faq => ({\n      '@type': 'Question',\n      name: faq.question,\n      acceptedAnswer: {\n        '@type': 'Answer',\n        text: faq.answer\n      }\n    }))\n  };\n}\n\n// Common fitness-related FAQs\nexport const commonFitnessQAs = [\n  {\n    question: \"How often should I work out?\",\n    answer: \"For general fitness, aim for at least 150 minutes of moderate-intensity exercise or 75 minutes of vigorous-intensity exercise per week, plus muscle-strengthening activities on 2 or more days per week.\"\n  },\n  {\n    question: \"What's the best time to exercise?\",\n    answer: \"The best time to exercise is when you can be consistent. Some people prefer morning workouts for energy, while others prefer evening sessions. Choose a time that fits your schedule and stick to it.\"\n  },\n  {\n    question: \"Do I need equipment to get fit?\",\n    answer: \"No, you can achieve great fitness results with bodyweight exercises. However, equipment like dumbbells, resistance bands, or kettlebells can add variety and progression to your workouts.\"\n  },\n  {\n    question: \"How long before I see results?\",\n    answer: \"You may notice improvements in energy and mood within a few days. Physical changes typically become noticeable after 2-4 weeks of consistent exercise, with significant changes after 8-12 weeks.\"\n  },\n  {\n    question: \"Should I do cardio or strength training?\",\n    answer: \"Both are important for overall fitness. Cardio improves heart health and endurance, while strength training builds muscle and bone density. A balanced program includes both types of exercise.\"\n  }\n];\n\n// Generate rich snippets for search results\nexport function generateRichSnippets(type: 'exercise' | 'workout' | 'article', data: any) {\n  switch (type) {\n    case 'exercise':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'HowTo',\n        name: `How to do ${data.name}`,\n        description: data.description,\n        totalTime: data.duration ? `PT${data.duration}M` : undefined,\n        supply: data.equipment ? [data.equipment] : undefined,\n        tool: data.equipment ? [data.equipment] : undefined,\n        step: data.instructions?.map((instruction: string, index: number) => ({\n          '@type': 'HowToStep',\n          position: index + 1,\n          text: instruction\n        }))\n      };\n    \n    case 'workout':\n      return {\n        '@context': 'https://schema.org',\n        '@type': 'ExercisePlan',\n        name: data.title,\n        description: data.description,\n        category: 'Fitness',\n        exerciseType: data.type,\n        intensity: data.difficulty,\n        workload: data.duration ? `${data.duration} minutes` : undefined\n      };\n    \n    default:\n      return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;;AAGO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAU,2HAAA,CAAA,aAAU,CAAC,GAAG;IAC9B,MAAM,YAAY,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAC1D,OAAO,GAAG,UAAU,WAAW;AACjC;AAGO,SAAS,mBAAmB,KAAa,EAAE,WAAoB;IACpE,MAAM,UAAU,2HAAA,CAAA,aAAU,CAAC,GAAG;IAC9B,MAAM,SAAS,IAAI;IACnB,OAAO,GAAG,CAAC,SAAS;IACpB,IAAI,aAAa;QACf,OAAO,GAAG,CAAC,eAAe;IAC5B;IACA,OAAO,GAAG,QAAQ,QAAQ,EAAE,OAAO,QAAQ,IAAI;AACjD;AAGO,SAAS,aAAa,IAAY,EAAE,YAAoB,GAAG;IAChE,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,GAAG,IAAI,KAAK;AACnD;AAGO,SAAS,iBAAiB,OAAe,EAAE,qBAA+B,EAAE;IACjF,MAAM,eAAe,2HAAA,CAAA,aAAU,CAAC,QAAQ;IACxC,MAAM,eAAe,QAClB,WAAW,GACX,OAAO,CAAC,YAAY,KACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG;IAEZ,OAAO;WAAI;WAAiB;WAAuB;KAAa;AAClE;AAGO,SAAS,qBAAqB,EACnC,KAAK,EACL,WAAW,EACX,IAAI,EACJ,WAAW,EAAE,EACb,KAAK,EACL,OAAO,SAAS,EAQjB;IACC,MAAM,YAAY,MAAM,QAAQ,CAAC,2HAAA,CAAA,aAAU,CAAC,IAAI,IAAI,QAAQ,GAAG,MAAM,GAAG,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IAC3F,MAAM,eAAe,qBAAqB;IAC1C,MAAM,UAAU,SAAS,mBAAmB,OAAO;IAEnD,MAAM,cAAc,iBAAiB,aAAa;IAElD,OAAO;QACL,OAAO;QACP,aAAa,aAAa;QAC1B,UAAU;QACV,WAAW;QACX,WAAW;YACT,OAAO;YACP,aAAa,aAAa;YAC1B,KAAK;YACL;YACA,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,UAAU,2HAAA,CAAA,aAAU,CAAC,IAAI;QAC3B;QACA,SAAS;YACP,OAAO;YACP,aAAa,aAAa;YAC1B,MAAM;YACN,QAAQ;gBAAC;aAAQ;QACnB;QACA,YAAY;YACV,WAAW;QACb;IACF;AACF;AAGO,SAAS,yBAAyB,QAOxC;IACC,MAAM,QAAQ,GAAG,SAAS,IAAI,CAAC,oBAAoB,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACtE,MAAM,cAAc,aAClB,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,SAAS,WAAW,CAAC,iBAAiB,EAAE,SAAS,aAAa,EAAE,KAAK,SAAS,WAAW,aAAa,EAAE,SAAS,SAAS,IAAI,aAAa,CAAC,CAAC;IAGnM,MAAM,WAAW,iBACf,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,WAAW,EAAE,EAC1C;QACE,SAAS,QAAQ,IAAI;QACrB,SAAS,SAAS,IAAI;QACtB,SAAS,UAAU,IAAI;WACnB,SAAS,aAAa,IAAI,EAAE;KACjC;IAGH,OAAO;QACL;QACA;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,mBAAmB,SAAS,IAAI,EAAE,GAAG,SAAS,QAAQ,CAAC,cAAc,EAAE,SAAS,aAAa,EAAE,KAAK,OAAO;oBAChH,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,SAAS,IAAI,CAAC,uBAAuB,CAAC;gBAChD;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,MAAM;QACR;IACF;AACF;AAGO,SAAS,wBAAwB,OAOvC;IACC,MAAM,QAAQ,GAAG,QAAQ,KAAK,CAAC,qBAAqB,EAAE,2HAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACvE,MAAM,cAAc,aAClB,GAAG,QAAQ,WAAW,CAAC,WAAW,EAAE,QAAQ,QAAQ,IAAI,WAAW,sBAAsB,EAAE,QAAQ,UAAU,IAAI,aAAa,QAAQ,EAAE,QAAQ,IAAI,IAAI,kBAAkB,CAAC,CAAC;IAG9K,MAAM,WAAW,iBACf,GAAG,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,WAAW,EAAE,EACzC;QACE;QACA;QACA,QAAQ,IAAI,IAAI;QAChB,QAAQ,UAAU,IAAI;WAClB,QAAQ,aAAa,IAAI,EAAE;KAChC;IAGH,OAAO;QACL;QACA;QACA;QACA,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ;gBACN;oBACE,KAAK,mBAAmB,QAAQ,KAAK,EAAE,GAAG,QAAQ,IAAI,CAAC,mBAAmB,EAAE,QAAQ,UAAU,CAAC,MAAM,CAAC;oBACtG,OAAO;oBACP,QAAQ;oBACR,KAAK,GAAG,QAAQ,KAAK,CAAC,gBAAgB,CAAC;gBACzC;aACD;QACH;QACA,SAAS;YACP;YACA;YACA,MAAM;QACR;IACF;AACF;AAGO,SAAS,kBAAkB,IAAiD;IACjF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,SAAS;gBACT,MAAM,IAAI,QAAQ;gBAClB,gBAAgB;oBACd,SAAS;oBACT,MAAM,IAAI,MAAM;gBAClB;YACF,CAAC;IACH;AACF;AAGO,MAAM,mBAAmB;IAC9B;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAGM,SAAS,qBAAqB,IAAwC,EAAE,IAAS;IACtF,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;gBAC9B,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,QAAQ,GAAG,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG;gBACnD,QAAQ,KAAK,SAAS,GAAG;oBAAC,KAAK,SAAS;iBAAC,GAAG;gBAC5C,MAAM,KAAK,SAAS,GAAG;oBAAC,KAAK,SAAS;iBAAC,GAAG;gBAC1C,MAAM,KAAK,YAAY,EAAE,IAAI,CAAC,aAAqB,QAAkB,CAAC;wBACpE,SAAS;wBACT,UAAU,QAAQ;wBAClB,MAAM;oBACR,CAAC;YACH;QAEF,KAAK;YACH,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT,MAAM,KAAK,KAAK;gBAChB,aAAa,KAAK,WAAW;gBAC7B,UAAU;gBACV,cAAc,KAAK,IAAI;gBACvB,WAAW,KAAK,UAAU;gBAC1B,UAAU,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,CAAC,QAAQ,CAAC,GAAG;YACzD;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-home.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MuiHome = registerClientReference(\n    function() { throw new Error(\"Attempted to call MuiHome() from the server but MuiHome is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/mui-home.tsx <module evaluation>\",\n    \"MuiHome\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,mEACA", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-home.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MuiHome = registerClientReference(\n    function() { throw new Error(\"Attempted to call MuiHome() from the server but MuiHome is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/mui-home.tsx\",\n    \"MuiHome\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+CACA", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { generatePageMetadata, commonFitnessQAs } from \"@/lib/seo/utils\"\nimport { FAQStructuredData } from \"@/components/seo/structured-data\"\nimport { MuiHome } from '@/components/pages/mui-home';\n\nexport const metadata: Metadata = generatePageMetadata({\n  title: \"AI-fitness-singles - Smart Fitness Platform for Singles\",\n  description: \"Transform your fitness journey with AI-powered workout plans, comprehensive exercise database, and detailed progress tracking. Perfect for singles looking to achieve their fitness goals.\",\n  path: \"/\",\n});\n\nexport default function Home() {\n  return (\n    <>\n      <MuiHome />\n      <FAQStructuredData faqs={commonFitnessQAs} />\n    </>\n  );\n\n\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAEO,MAAM,WAAqB,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;IACrD,OAAO;IACP,aAAa;IACb,MAAM;AACR;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,0IAAA,CAAA,UAAO;;;;;0BACR,8OAAC,+IAAA,CAAA,oBAAiB;gBAAC,MAAM,0HAAA,CAAA,mBAAgB;;;;;;;;AAE5C", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}