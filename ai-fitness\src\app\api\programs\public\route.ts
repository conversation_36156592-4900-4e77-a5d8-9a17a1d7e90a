/**
 * Public Programs API Route
 * Calls workout-cool server action directly for public programs
 */

import { NextRequest, NextResponse } from 'next/server';

// Mock data for testing - replace with actual workout-cool integration
const mockPrograms = [
  {
    id: '1',
    slug: 'beginner-strength',
    slugEn: 'beginner-strength',
    title: 'Beginner Strength Training',
    titleEn: 'Beginner Strength Training',
    description: 'A comprehensive strength training program for beginners',
    descriptionEn: 'A comprehensive strength training program for beginners',
    category: 'Strength',
    image: '/images/programs/strength.jpg',
    level: 'BEGINNER',
    type: 'Strength Training',
    durationWeeks: 8,
    sessionsPerWeek: 3,
    sessionDurationMin: 45,
    equipment: ['Dumbbells', 'Barbell', 'Bench'],
    isPremium: false,
    participantCount: 150,
    totalWeeks: 8,
    totalSessions: 24,
    totalExercises: 12,
    totalEnrollments: 150,
  },
  {
    id: '2',
    slug: 'cardio-hiit',
    slugEn: 'cardio-hiit',
    title: 'HIIT Cardio Blast',
    titleEn: 'HIIT Cardio Blast',
    description: 'High-intensity interval training for maximum fat burn',
    descriptionEn: 'High-intensity interval training for maximum fat burn',
    category: 'Cardio',
    image: '/images/programs/hiit.jpg',
    level: 'INTERMEDIATE',
    type: 'HIIT',
    durationWeeks: 6,
    sessionsPerWeek: 4,
    sessionDurationMin: 30,
    equipment: ['None'],
    isPremium: true,
    participantCount: 89,
    totalWeeks: 6,
    totalSessions: 24,
    totalExercises: 20,
    totalEnrollments: 89,
  },
];

export async function GET(request: NextRequest) {
  try {
    // For now, return mock data
    // TODO: Integrate with workout-cool server actions when available
    return NextResponse.json(mockPrograms);
  } catch (error) {
    console.error('Error fetching public programs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch public programs' },
      { status: 500 }
    );
  }
}
