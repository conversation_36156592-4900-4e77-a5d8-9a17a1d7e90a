"use client"

import { useState } from 'react';
import { Navigation } from '@/components/Navigation';
import { SyncStatus, NotificationCenter } from '@/components/ui/sync-status';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Database,
  Settings,
  Zap,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Info,
  X
} from 'lucide-react';
import {
  useAppStore,
  useSettings,
  useWorkoutPreferences,
  useNotifications,
  useOfflineState
} from '@/lib/store/app-store';
import { useSyncManager } from '@/lib/sync/sync-manager';

export default function DemoPage() {
  const [testData, setTestData] = useState('');
  
  // State management hooks
  const {
    addNotification,
    updateSettings,
    updateWorkoutPreferences,
    clearNotifications
  } = useAppStore();
  
  const settings = useSettings();
  const workoutPreferences = useWorkoutPreferences();
  const notifications = useNotifications();
  const offlineState = useOfflineState();
  const { addToSyncQueue } = useSyncManager();

  // Demo functions
  const addTestNotification = (type: 'success' | 'error' | 'warning' | 'info') => {
    const messages = {
      success: { title: 'Success!', message: 'Operation completed successfully' },
      error: { title: 'Error!', message: 'Something went wrong' },
      warning: { title: 'Warning!', message: 'Please check your settings' },
      info: { title: 'Info', message: 'Here is some useful information' }
    };
    
    addNotification({
      type,
      ...messages[type]
    });
  };

  const toggleTheme = () => {
    const newTheme = settings.theme === 'light' ? 'dark' : 'light';
    updateSettings({ theme: newTheme });
  };

  const toggleNotifications = () => {
    updateSettings({
      notifications: {
        ...settings.notifications,
        workoutReminders: !settings.notifications.workoutReminders
      }
    });
  };

  const updateDifficulty = () => {
    const difficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'] as const;
    const currentIndex = difficulties.indexOf(workoutPreferences.preferredDifficulty);
    const nextIndex = (currentIndex + 1) % difficulties.length;
    
    updateWorkoutPreferences({
      preferredDifficulty: difficulties[nextIndex]
    });
  };

  const addTestSyncItem = () => {
    addToSyncQueue({
      type: 'workout',
      action: 'create',
      data: {
        type: 'session',
        name: 'Test Workout',
        duration: 30,
        exercises: []
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            State Management Demo
          </h1>
          <p className="text-xl text-gray-600">
            Demonstration of Zustand store, React Query, and sync management
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* App Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                App Settings
              </CardTitle>
              <CardDescription>
                Global application settings managed by Zustand
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Theme:</span>
                <Badge variant="outline">{settings.theme}</Badge>
              </div>
              <Button onClick={toggleTheme} variant="outline" className="w-full">
                Toggle Theme
              </Button>
              
              <div className="flex items-center justify-between">
                <span>Workout Reminders:</span>
                <Badge variant={settings.notifications.workoutReminders ? 'default' : 'secondary'}>
                  {settings.notifications.workoutReminders ? 'On' : 'Off'}
                </Badge>
              </div>
              <Button onClick={toggleNotifications} variant="outline" className="w-full">
                Toggle Notifications
              </Button>

              <div className="flex items-center justify-between">
                <span>Units:</span>
                <Badge variant="outline">{settings.units}</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span>Language:</span>
                <Badge variant="outline">{settings.language}</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Workout Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Workout Preferences
              </CardTitle>
              <CardDescription>
                User workout preferences stored in Zustand
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Preferred Difficulty:</span>
                <Badge variant="outline">{workoutPreferences.preferredDifficulty}</Badge>
              </div>
              <Button onClick={updateDifficulty} variant="outline" className="w-full">
                Change Difficulty
              </Button>
              
              <div className="flex items-center justify-between">
                <span>Default Duration:</span>
                <Badge variant="outline">{workoutPreferences.defaultDuration} min</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span>Rest Time:</span>
                <Badge variant="outline">{workoutPreferences.restTimeBetweenSets}s</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span>Auto Start Next:</span>
                <Badge variant={workoutPreferences.autoStartNextExercise ? 'default' : 'secondary'}>
                  {workoutPreferences.autoStartNextExercise ? 'Yes' : 'No'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Notifications Demo */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Notifications Demo
              </CardTitle>
              <CardDescription>
                Test the notification system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <Button 
                  onClick={() => addTestNotification('success')} 
                  variant="outline"
                  className="text-green-600"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Success
                </Button>
                <Button 
                  onClick={() => addTestNotification('error')} 
                  variant="outline"
                  className="text-red-600"
                >
                  <X className="h-4 w-4 mr-2" />
                  Error
                </Button>
                <Button 
                  onClick={() => addTestNotification('warning')} 
                  variant="outline"
                  className="text-orange-600"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Warning
                </Button>
                <Button 
                  onClick={() => addTestNotification('info')} 
                  variant="outline"
                  className="text-blue-600"
                >
                  <Info className="h-4 w-4 mr-2" />
                  Info
                </Button>
              </div>
              
              <Button onClick={clearNotifications} variant="destructive" className="w-full">
                Clear All Notifications
              </Button>
              
              <div className="text-sm text-gray-600">
                Current notifications: {notifications.length}
              </div>
            </CardContent>
          </Card>

          {/* Sync Management Demo */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Sync Management Demo
              </CardTitle>
              <CardDescription>
                Test offline sync functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Online Status:</span>
                <Badge variant={offlineState.isOnline ? 'default' : 'destructive'}>
                  {offlineState.isOnline ? 'Online' : 'Offline'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span>Pending Sync Items:</span>
                <Badge variant="outline">{offlineState.pendingSync.length}</Badge>
              </div>
              
              <Button onClick={addTestSyncItem} variant="outline" className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Add Test Sync Item
              </Button>
              
              {offlineState.lastSyncTime && (
                <div className="text-sm text-gray-600">
                  Last sync: {new Date(offlineState.lastSyncTime).toLocaleString()}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sync Status Component */}
          <div className="lg:col-span-1">
            <SyncStatus />
          </div>

          {/* Notification Center Component */}
          <div className="lg:col-span-1">
            <NotificationCenter />
          </div>
        </div>

        {/* State Debug Info */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>State Debug Information</CardTitle>
            <CardDescription>
              Current state values for debugging
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">Settings:</h4>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(settings, null, 2)}
                </pre>
              </div>
              <div>
                <h4 className="font-medium mb-2">Workout Preferences:</h4>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(workoutPreferences, null, 2)}
                </pre>
              </div>
              <div>
                <h4 className="font-medium mb-2">Offline State:</h4>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(offlineState, null, 2)}
                </pre>
              </div>
              <div>
                <h4 className="font-medium mb-2">Recent Notifications:</h4>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
                  {JSON.stringify(notifications.slice(0, 3), null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
