# Production Environment Configuration for AI-fitness-singles

# Application
NEXT_PUBLIC_APP_NAME=AI-fitness-singles
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_URL=https://ai-fitness-singles.vercel.app
NODE_ENV=production

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://workout-cool-api.herokuapp.com
NEXT_PUBLIC_API_TIMEOUT=10000

# Authentication (Better Auth)
AUTH_SECRET=your-production-auth-secret-here
AUTH_URL=https://ai-fitness-singles.vercel.app

# Database (if needed for session storage)
DATABASE_URL=postgresql://username:password@hostname:port/database

# Analytics (optional)
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=XXXXXXX

# Error Monitoring (optional)
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true

# Performance
NEXT_PUBLIC_ENABLE_SW=true
NEXT_PUBLIC_CACHE_MAX_AGE=3600

# Security
NEXT_PUBLIC_CSP_ENABLED=true
NEXT_PUBLIC_HSTS_ENABLED=true
