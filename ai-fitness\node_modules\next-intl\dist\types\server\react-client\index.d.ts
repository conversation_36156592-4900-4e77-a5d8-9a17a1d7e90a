import type { getFormatter as getFormatter_type, get<PERSON><PERSON><PERSON> as getLocale_type, getMessages as getMessages_type, getNow as getNow_type, getRequestConfig as getRequestConfig_type, getTimeZone as getTimeZone_type, setRequestLocale as setRequestLocale_type } from '../react-server/index.js';
export declare function getRequestConfig(...args: Parameters<typeof getRequestConfig_type>): ReturnType<typeof getRequestConfig_type>;
export declare const getFormatter: typeof getFormatter_type;
export declare const getNow: typeof getNow_type;
export declare const getTimeZone: typeof getTimeZone_type;
export declare const getMessages: typeof getMessages_type;
export declare const getLocale: typeof getLocale_type;
export declare const getTranslations: () => never;
export declare const setRequestLocale: typeof setRequestLocale_type;
