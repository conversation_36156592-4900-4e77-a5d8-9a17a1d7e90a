module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/providers/query-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QueryProvider": (()=>QueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
/**
 * React Query Provider for AI-fitness application
 * Provides global state management and caching for API requests
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function QueryProvider({ children }) {
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
            defaultOptions: {
                queries: {
                    // Stale time: how long data is considered fresh
                    staleTime: 5 * 60 * 1000,
                    // Cache time: how long data stays in cache after component unmounts
                    gcTime: 10 * 60 * 1000,
                    // Retry configuration
                    retry: (failureCount, error)=>{
                        // Don't retry on authentication errors
                        if (error?.status === 401 || error?.status === 403) {
                            return false;
                        }
                        // Don't retry on validation errors
                        if (error?.status === 422) {
                            return false;
                        }
                        // Retry up to 3 times for other errors
                        return failureCount < 3;
                    },
                    // Retry delay with exponential backoff
                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
                    // Refetch on window focus (useful for real-time data)
                    refetchOnWindowFocus: false,
                    // Refetch on reconnect
                    refetchOnReconnect: true,
                    // Background refetch interval (disabled by default)
                    refetchInterval: false
                },
                mutations: {
                    // Retry mutations on network errors
                    retry: (failureCount, error)=>{
                        // Don't retry on client errors (4xx)
                        if (error?.status >= 400 && error?.status < 500) {
                            return false;
                        }
                        // Retry up to 2 times for server errors
                        return failureCount < 2;
                    },
                    // Retry delay for mutations
                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 10000)
                }
            }
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {
                initialIsOpen: false,
                buttonPosition: "bottom-right"
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/query-provider.tsx",
                lineNumber: 80,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/lib/providers/query-provider.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/api/config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Configuration for AI-fitness application
 * Connects to workout-cool backend API
 */ __turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG)
});
const API_CONFIG = {
    // Base URL for the workout-cool backend API
    BASE_URL: process.env.NEXT_PUBLIC_WORKOUT_COOL_API_URL || 'http://localhost:3000',
    // API endpoints - Updated to match workout-cool API structure
    ENDPOINTS: {
        // Authentication - Better Auth endpoints
        AUTH: {
            SIGNIN: '/api/auth/sign-in',
            SIGNUP: '/api/auth/sign-up',
            SIGNOUT: '/api/auth/sign-out',
            SESSION: '/api/auth/session',
            RESET_PASSWORD: '/api/auth/reset-password',
            VERIFY_EMAIL: '/api/auth/verify-email'
        },
        // Public Programs - workout-cool public API
        PROGRAMS: {
            PUBLIC_LIST: '/api/programs/public',
            DETAILS: (slug)=>`/api/programs/${slug}`,
            ENROLL: (id)=>`/api/programs/${id}/enroll`,
            PROGRESS: (id)=>`/api/programs/${id}/progress`,
            SESSIONS: (programId)=>`/api/programs/${programId}/sessions`,
            SESSION_DETAIL: (programId, sessionId)=>`/api/programs/${programId}/sessions/${sessionId}`
        },
        // Exercises - workout-cool exercise API
        EXERCISES: {
            PUBLIC_LIST: '/api/exercises/public',
            SEARCH: '/api/exercises/search',
            DETAILS: (id)=>`/api/exercises/${id}`,
            ATTRIBUTES: '/api/exercises/attributes',
            BY_MUSCLE_GROUP: (muscleGroup)=>`/api/exercises/muscle-groups/${muscleGroup}`
        },
        // Workout Sessions - workout-cool session management
        WORKOUTS: {
            LIST: '/api/workout-sessions',
            CREATE: '/api/workout-sessions',
            DETAILS: (id)=>`/api/workout-sessions/${id}`,
            UPDATE: (id)=>`/api/workout-sessions/${id}`,
            DELETE: (id)=>`/api/workout-sessions/${id}`,
            COMPLETE: (id)=>`/api/workout-sessions/${id}/complete`,
            SYNC: '/api/workout-sessions/sync',
            USER_SESSIONS: (userId)=>`/api/users/${userId}/workout-sessions`
        },
        // User Progress - workout-cool progress tracking
        PROGRESS: {
            USER_STATS: (userId)=>`/api/users/${userId}/stats`,
            PROGRAM_PROGRESS: (userId, programId)=>`/api/users/${userId}/programs/${programId}/progress`,
            WORKOUT_HISTORY: (userId)=>`/api/users/${userId}/workout-history`,
            BODY_MEASUREMENTS: (userId)=>`/api/users/${userId}/body-measurements`,
            GOALS: (userId)=>`/api/users/${userId}/goals`
        },
        // Premium - workout-cool subscription system
        PREMIUM: {
            PLANS: '/api/premium/plans',
            STATUS: '/api/premium/status',
            SUBSCRIPTION: '/api/premium/subscription',
            CHECKOUT: '/api/premium/checkout',
            BILLING_PORTAL: '/api/premium/billing-portal'
        },
        // User Management - workout-cool user API
        USERS: {
            PROFILE: '/api/users/profile',
            UPDATE_PROFILE: '/api/users/profile',
            PREFERENCES: '/api/users/preferences',
            ENROLLMENTS: (userId)=>`/api/users/${userId}/program-enrollments`
        },
        // Health Check
        HEALTH: '/api/health'
    },
    // Request timeouts
    TIMEOUT: {
        DEFAULT: 10000,
        UPLOAD: 30000,
        DOWNLOAD: 60000
    },
    // Retry configuration
    RETRY: {
        ATTEMPTS: 3,
        DELAY: 1000,
        BACKOFF_FACTOR: 2
    }
};
}}),
"[project]/src/lib/api/errors.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Error Classes for AI-fitness application
 * Provides structured error handling for different types of API errors
 */ __turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "AuthenticationError": (()=>AuthenticationError),
    "AuthorizationError": (()=>AuthorizationError),
    "ConflictError": (()=>ConflictError),
    "NetworkError": (()=>NetworkError),
    "NotFoundError": (()=>NotFoundError),
    "RateLimitError": (()=>RateLimitError),
    "ValidationError": (()=>ValidationError),
    "getErrorCode": (()=>getErrorCode),
    "getErrorMessage": (()=>getErrorMessage),
    "isApiError": (()=>isApiError),
    "isAuthenticationError": (()=>isAuthenticationError),
    "isNetworkError": (()=>isNetworkError),
    "isValidationError": (()=>isValidationError),
    "shouldRetry": (()=>shouldRetry)
});
class ApiError extends Error {
    status;
    code;
    details;
    constructor(message, status = 500, code, details){
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.code = code;
        this.details = details;
        // Maintains proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ApiError);
        }
    }
    /**
   * Check if error is a client error (4xx)
   */ get isClientError() {
        return this.status >= 400 && this.status < 500;
    }
    /**
   * Check if error is a server error (5xx)
   */ get isServerError() {
        return this.status >= 500;
    }
    /**
   * Convert error to JSON for logging
   */ toJSON() {
        return {
            name: this.name,
            message: this.message,
            status: this.status,
            code: this.code,
            details: this.details,
            stack: this.stack
        };
    }
}
class NetworkError extends ApiError {
    constructor(message = 'Network error occurred'){
        super(message, 0, 'NETWORK_ERROR');
        this.name = 'NetworkError';
    }
}
class AuthenticationError extends ApiError {
    constructor(message = 'Authentication required'){
        super(message, 401, 'AUTHENTICATION_ERROR');
        this.name = 'AuthenticationError';
    }
}
class AuthorizationError extends ApiError {
    constructor(message = 'Access denied'){
        super(message, 403, 'AUTHORIZATION_ERROR');
        this.name = 'AuthorizationError';
    }
}
class ValidationError extends ApiError {
    fieldErrors;
    constructor(message = 'Validation failed', fieldErrors = {}){
        super(message, 422, 'VALIDATION_ERROR', fieldErrors);
        this.name = 'ValidationError';
        this.fieldErrors = fieldErrors;
    }
    /**
   * Get error message for a specific field
   */ getFieldError(field) {
        const errors = this.fieldErrors[field];
        return errors && errors.length > 0 ? errors[0] : null;
    }
    /**
   * Check if a specific field has errors
   */ hasFieldError(field) {
        return Boolean(this.fieldErrors[field]?.length);
    }
    /**
   * Get all field error messages as a flat array
   */ getAllFieldErrors() {
        return Object.values(this.fieldErrors).flat();
    }
}
class NotFoundError extends ApiError {
    constructor(message = 'Resource not found'){
        super(message, 404, 'NOT_FOUND_ERROR');
        this.name = 'NotFoundError';
    }
}
class ConflictError extends ApiError {
    constructor(message = 'Resource conflict'){
        super(message, 409, 'CONFLICT_ERROR');
        this.name = 'ConflictError';
    }
}
class RateLimitError extends ApiError {
    retryAfter;
    constructor(message = 'Rate limit exceeded', retryAfter){
        super(message, 429, 'RATE_LIMIT_ERROR', {
            retryAfter
        });
        this.name = 'RateLimitError';
        this.retryAfter = retryAfter;
    }
}
function isApiError(error) {
    return error instanceof ApiError;
}
function isNetworkError(error) {
    return error instanceof NetworkError;
}
function isAuthenticationError(error) {
    return error instanceof AuthenticationError;
}
function isValidationError(error) {
    return error instanceof ValidationError;
}
function getErrorMessage(error) {
    if (isApiError(error)) {
        return error.message;
    }
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'An unexpected error occurred';
}
function getErrorCode(error) {
    if (isApiError(error)) {
        return error.code || null;
    }
    return null;
}
function shouldRetry(error) {
    if (isNetworkError(error)) {
        return true;
    }
    if (isApiError(error)) {
        // Retry on server errors but not client errors
        return error.isServerError;
    }
    return false;
}
}}),
"[project]/src/lib/api/client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API Client for AI-fitness application
 * Handles HTTP requests, authentication, and error handling
 */ __turbopack_context__.s({
    "apiClient": (()=>apiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/errors.ts [app-ssr] (ecmascript)");
;
;
class ApiClient {
    baseUrl;
    defaultHeaders;
    constructor(){
        this.baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }
    /**
   * Get authentication token from session storage or cookies
   */ getAuthToken() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return null;
    }
    /**
   * Build request headers with authentication if available
   */ buildHeaders(options = {}) {
        const headers = {
            ...this.defaultHeaders
        };
        // Add authentication header if required and available
        if (options.requireAuth !== false) {
            const token = this.getAuthToken();
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }
        // Merge with custom headers
        if (options.headers) {
            Object.assign(headers, options.headers);
        }
        return headers;
    }
    /**
   * Handle API response and errors
   */ async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        const isJson = contentType?.includes('application/json');
        let data;
        try {
            data = isJson ? await response.json() : await response.text();
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]('Failed to parse response', response.status);
        }
        if (!response.ok) {
            switch(response.status){
                case 401:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthenticationError"](data.message || 'Authentication required');
                case 422:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationError"](data.message || 'Validation failed', data.errors);
                case 404:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Resource not found', 404);
                case 500:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Internal server error', 500);
                default:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](data.message || 'Request failed', response.status);
            }
        }
        return data;
    }
    /**
   * Make HTTP request with retry logic
   */ async makeRequest(url, options = {}) {
        const { timeout = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT.DEFAULT, retries = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.ATTEMPTS, ...requestOptions } = options;
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), timeout);
        const requestConfig = {
            ...requestOptions,
            headers: this.buildHeaders(options),
            signal: controller.signal
        };
        let lastError = null;
        for(let attempt = 0; attempt <= retries; attempt++){
            try {
                const response = await fetch(`${this.baseUrl}${url}`, requestConfig);
                clearTimeout(timeoutId);
                return await this.handleResponse(response);
            } catch (error) {
                lastError = error;
                // Don't retry on authentication or validation errors
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthenticationError"] || error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationError"]) {
                    throw error;
                }
                // Don't retry on the last attempt
                if (attempt === retries) {
                    break;
                }
                // Wait before retrying
                const delay = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.DELAY * Math.pow(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY.BACKOFF_FACTOR, attempt);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            }
        }
        clearTimeout(timeoutId);
        // Handle network errors
        if (lastError) {
            if (lastError.name === 'AbortError') {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NetworkError"]('Request timeout');
            }
            if (lastError.name === 'TypeError') {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NetworkError"]('Network error - please check your connection');
            }
            throw lastError;
        }
        throw new Error('Request failed after all retries');
    }
    /**
   * GET request
   */ async get(url, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'GET'
        });
    }
    /**
   * POST request
   */ async post(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * PUT request
   */ async put(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * PATCH request
   */ async patch(url, data, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    /**
   * DELETE request
   */ async delete(url, options = {}) {
        return this.makeRequest(url, {
            ...options,
            method: 'DELETE'
        });
    }
}
const apiClient = new ApiClient();
}}),
"[project]/src/lib/api/adapters/workout-cool.adapter.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Adapter for workout-cool API data structures
 * Converts workout-cool data models to our frontend types
 */ __turbopack_context__.s({
    "WorkoutCoolAdapter": (()=>WorkoutCoolAdapter)
});
class WorkoutCoolAdapter {
    /**
   * Convert workout-cool program to frontend Program type
   */ static adaptProgram(wcProgram) {
        return {
            id: wcProgram.id,
            title: wcProgram.titleEn || wcProgram.title,
            description: wcProgram.descriptionEn || wcProgram.description,
            slug: wcProgram.slugEn || wcProgram.slug,
            category: wcProgram.category,
            image: wcProgram.image,
            difficulty: wcProgram.level.toLowerCase(),
            duration: wcProgram.sessionDurationMin,
            durationWeeks: wcProgram.durationWeeks,
            sessionsPerWeek: wcProgram.sessionsPerWeek,
            equipment: wcProgram.equipment,
            isPremium: wcProgram.isPremium,
            isActive: wcProgram.isActive,
            participantCount: wcProgram.participantCount,
            createdAt: new Date(wcProgram.createdAt),
            updatedAt: new Date(wcProgram.updatedAt),
            coaches: wcProgram.coaches?.map((coach)=>({
                    id: coach.id,
                    name: coach.name,
                    image: coach.image,
                    order: coach.order
                })) || [],
            weeks: wcProgram.weeks?.map((week)=>({
                    id: week.id,
                    weekNumber: week.weekNumber,
                    sessions: week.sessions.map((session)=>this.adaptProgramSession(session))
                })) || []
        };
    }
    /**
   * Convert workout-cool program session to frontend ProgramSession type
   */ static adaptProgramSession(wcSession) {
        return {
            id: wcSession.id,
            sessionNumber: wcSession.sessionNumber,
            title: wcSession.titleEn || wcSession.title,
            description: wcSession.descriptionEn || wcSession.description,
            estimatedDuration: wcSession.estimatedMinutes,
            equipment: wcSession.equipment,
            isPremium: wcSession.isPremium,
            exercises: wcSession.exercises.map((ex)=>({
                    id: ex.id,
                    order: ex.order,
                    exerciseId: ex.exercise.id,
                    exercise: this.adaptExercise(ex.exercise),
                    sets: ex.suggestedSets.map((set)=>({
                            setIndex: set.setIndex,
                            reps: set.reps,
                            weight: set.weight,
                            duration: set.duration,
                            restTime: set.restTime
                        }))
                }))
        };
    }
    /**
   * Convert workout-cool exercise to frontend Exercise type
   */ static adaptExercise(wcExercise) {
        // Extract muscle groups and equipment from attributes
        const muscleGroups = [];
        const equipment = [];
        let category = 'strength';
        let difficulty = 'beginner';
        wcExercise.attributes.forEach((attr)=>{
            const attrName = attr.attributeName.nameEn || attr.attributeName.name;
            const attrValue = attr.attributeValue.valueEn || attr.attributeValue.value;
            if (attrName.toLowerCase().includes('muscle') || attrName.toLowerCase().includes('target')) {
                muscleGroups.push(attrValue);
            } else if (attrName.toLowerCase().includes('equipment')) {
                equipment.push(attrValue);
            } else if (attrName.toLowerCase().includes('category') || attrName.toLowerCase().includes('type')) {
                if (attrValue.toLowerCase().includes('cardio')) category = 'cardio';
                else if (attrValue.toLowerCase().includes('flexibility') || attrValue.toLowerCase().includes('stretch')) category = 'flexibility';
                else if (attrValue.toLowerCase().includes('balance')) category = 'balance';
            } else if (attrName.toLowerCase().includes('difficulty') || attrName.toLowerCase().includes('level')) {
                if (attrValue.toLowerCase().includes('intermediate')) difficulty = 'intermediate';
                else if (attrValue.toLowerCase().includes('advanced')) difficulty = 'advanced';
            }
        });
        return {
            id: wcExercise.id,
            name: wcExercise.nameEn || wcExercise.name,
            description: wcExercise.descriptionEn || wcExercise.description,
            category,
            muscleGroups,
            equipment,
            difficulty,
            instructions: (wcExercise.instructionsEn || wcExercise.instructions)?.split('\n') || [],
            tips: (wcExercise.tipsEn || wcExercise.tips)?.split('\n') || [],
            imageUrl: wcExercise.imageUrl,
            videoUrl: wcExercise.videoUrl
        };
    }
    /**
   * Convert workout-cool user to frontend User type
   */ static adaptUser(wcUser) {
        return {
            id: wcUser.id,
            email: wcUser.email,
            name: wcUser.name,
            firstName: wcUser.firstName,
            lastName: wcUser.lastName,
            avatar: wcUser.image,
            role: wcUser.role,
            isPremium: wcUser.isPremium || false,
            preferences: {
                language: wcUser.locale || 'en',
                timezone: 'UTC',
                units: 'metric',
                notifications: {
                    email: true,
                    push: true,
                    workout: true,
                    progress: true
                }
            },
            createdAt: new Date(wcUser.createdAt),
            updatedAt: new Date(wcUser.updatedAt)
        };
    }
    /**
   * Convert workout-cool workout session to frontend WorkoutSession type
   */ static adaptWorkoutSession(wcSession) {
        return {
            id: wcSession.id,
            userId: wcSession.userId,
            programId: wcSession.programId,
            sessionId: wcSession.sessionId,
            status: wcSession.status.toLowerCase(),
            startedAt: wcSession.startedAt ? new Date(wcSession.startedAt) : undefined,
            completedAt: wcSession.completedAt ? new Date(wcSession.completedAt) : undefined,
            duration: wcSession.duration,
            notes: wcSession.notes,
            exercises: wcSession.exercises.map((ex)=>({
                    id: ex.id,
                    exerciseId: ex.exerciseId,
                    order: ex.order,
                    exercise: this.adaptExercise(ex.exercise),
                    sets: ex.sets.map((set)=>({
                            id: set.id,
                            setIndex: set.setIndex,
                            reps: set.reps,
                            weight: set.weight,
                            duration: set.duration,
                            restTime: set.restTime,
                            completed: set.completed
                        }))
                }))
        };
    }
    /**
   * Convert frontend data to workout-cool format for API requests
   */ static toWorkoutCoolFormat = {
        /**
     * Convert frontend workout session to workout-cool format
     */ workoutSession: (session)=>({
                userId: session.userId,
                programId: session.programId,
                sessionId: session.sessionId,
                status: session.status?.toUpperCase(),
                startedAt: session.startedAt?.toISOString(),
                completedAt: session.completedAt?.toISOString(),
                duration: session.duration,
                notes: session.notes,
                exercises: session.exercises?.map((ex)=>({
                        exerciseId: ex.exerciseId,
                        order: ex.order,
                        sets: ex.sets.map((set)=>({
                                setIndex: set.setIndex,
                                reps: set.reps,
                                weight: set.weight,
                                duration: set.duration,
                                restTime: set.restTime,
                                completed: set.completed
                            }))
                    }))
            })
    };
}
}}),
"[project]/src/lib/api/services/auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Authentication API Service
 * Handles authentication-related API calls to workout-cool backend
 */ __turbopack_context__.s({
    "AuthService": (()=>AuthService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/adapters/workout-cool.adapter.ts [app-ssr] (ecmascript)");
;
;
;
class AuthService {
    /**
   * Sign in with email and password - adapted for Better Auth
   */ static async signIn(credentials) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNIN, {
            email: credentials.email,
            password: credentials.password
        }, {
            requireAuth: false
        });
        return {
            user: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptUser(response.user),
            sessionId: response.session.id,
            expiresAt: new Date(response.session.expiresAt)
        };
    }
    /**
   * Sign up with user data - adapted for Better Auth
   */ static async signUp(userData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNUP, {
            email: userData.email,
            password: userData.password,
            firstName: userData.firstName,
            lastName: userData.lastName,
            name: `${userData.firstName} ${userData.lastName}`
        }, {
            requireAuth: false
        });
        return {
            user: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptUser(response.user),
            sessionId: response.session.id,
            expiresAt: new Date(response.session.expiresAt)
        };
    }
    /**
   * Sign out current user - adapted for Better Auth
   */ static async signOut() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SIGNOUT);
    }
    /**
   * Get current session
   */ static async getSession() {
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.SESSION);
        } catch (error) {
            // Return null if not authenticated
            if (error.status === 401) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Request password reset
   */ static async requestPasswordReset(email) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.RESET_PASSWORD, {
            email
        }, {
            requireAuth: false
        });
    }
    /**
   * Reset password with token
   */ static async resetPassword(token, newPassword) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.AUTH.RESET_PASSWORD, {
            token,
            password: newPassword
        }, {
            requireAuth: false
        });
    }
    /**
   * Verify email with token
   */ static async verifyEmail(token) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/verify-email', {
            token
        }, {
            requireAuth: false
        });
    }
    /**
   * Refresh authentication token
   */ static async refreshToken() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/refresh');
    }
    /**
   * Update user profile
   */ static async updateProfile(updates) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch('/api/auth/profile', updates);
    }
    /**
   * Change password
   */ static async changePassword(currentPassword, newPassword) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/change-password', {
            currentPassword,
            newPassword
        });
    }
    /**
   * Delete account
   */ static async deleteAccount(password) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete('/api/auth/account', {
            body: JSON.stringify({
                password
            })
        });
    }
    /**
   * Get user sessions
   */ static async getUserSessions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/auth/sessions');
    }
    /**
   * Revoke a specific session
   */ static async revokeSession(sessionId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(`/api/auth/sessions/${sessionId}`);
    }
    /**
   * Revoke all other sessions
   */ static async revokeAllOtherSessions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/auth/sessions/revoke-all');
    }
}
}}),
"[project]/src/lib/hooks/use-auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Authentication React Query hooks
 */ __turbopack_context__.s({
    "authKeys": (()=>authKeys),
    "useAuth": (()=>useAuth),
    "useChangePassword": (()=>useChangePassword),
    "useIsAdmin": (()=>useIsAdmin),
    "useIsPremium": (()=>useIsPremium),
    "useRequestPasswordReset": (()=>useRequestPasswordReset),
    "useResetPassword": (()=>useResetPassword),
    "useRevokeSession": (()=>useRevokeSession),
    "useSession": (()=>useSession),
    "useSignIn": (()=>useSignIn),
    "useSignOut": (()=>useSignOut),
    "useSignUp": (()=>useSignUp),
    "useUpdateProfile": (()=>useUpdateProfile),
    "useUserSessions": (()=>useUserSessions),
    "useVerifyEmail": (()=>useVerifyEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/auth.ts [app-ssr] (ecmascript)");
;
;
const authKeys = {
    all: [
        'auth'
    ],
    session: ()=>[
            ...authKeys.all,
            'session'
        ],
    sessions: ()=>[
            ...authKeys.all,
            'sessions'
        ]
};
function useSession() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: authKeys.session(),
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].getSession(),
        staleTime: 5 * 60 * 1000,
        retry: false
    });
}
function useAuth() {
    const { data: session, isLoading, error } = useSession();
    return {
        user: session?.user || null,
        isAuthenticated: !!session?.user,
        isLoading,
        error,
        session
    };
}
function useIsAdmin() {
    const { user, isAuthenticated } = useAuth();
    return isAuthenticated && user?.role === 'admin';
}
function useIsPremium() {
    const { user, isAuthenticated } = useAuth();
    return isAuthenticated && user?.isPremium === true;
}
function useSignIn() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (credentials)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].signIn(credentials),
        onSuccess: (session)=>{
            // Update session cache
            queryClient.setQueryData(authKeys.session(), session);
            // Invalidate all queries to refetch with new auth state
            queryClient.invalidateQueries();
        },
        onError: (error)=>{
            console.error('Sign in failed:', error);
        }
    });
}
function useSignUp() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (userData)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].signUp(userData),
        onSuccess: (session)=>{
            // Update session cache
            queryClient.setQueryData(authKeys.session(), session);
            // Invalidate all queries to refetch with new auth state
            queryClient.invalidateQueries();
        },
        onError: (error)=>{
            console.error('Sign up failed:', error);
        }
    });
}
function useSignOut() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].signOut(),
        onSuccess: ()=>{
            // Clear session cache
            queryClient.setQueryData(authKeys.session(), null);
            // Clear all cached data
            queryClient.clear();
        },
        onError: (error)=>{
            console.error('Sign out failed:', error);
            // Even if sign out fails on server, clear local cache
            queryClient.setQueryData(authKeys.session(), null);
            queryClient.clear();
        }
    });
}
function useRequestPasswordReset() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (email)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].requestPasswordReset(email),
        onError: (error)=>{
            console.error('Password reset request failed:', error);
        }
    });
}
function useResetPassword() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ token, newPassword })=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].resetPassword(token, newPassword),
        onError: (error)=>{
            console.error('Password reset failed:', error);
        }
    });
}
function useVerifyEmail() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (token)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].verifyEmail(token),
        onError: (error)=>{
            console.error('Email verification failed:', error);
        }
    });
}
function useUpdateProfile() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (updates)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].updateProfile(updates),
        onSuccess: (updatedUser)=>{
            // Update session cache with new user data
            const currentSession = queryClient.getQueryData(authKeys.session());
            if (currentSession) {
                queryClient.setQueryData(authKeys.session(), {
                    ...currentSession,
                    user: updatedUser
                });
            }
        },
        onError: (error)=>{
            console.error('Profile update failed:', error);
        }
    });
}
function useChangePassword() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ currentPassword, newPassword })=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].changePassword(currentPassword, newPassword),
        onError: (error)=>{
            console.error('Password change failed:', error);
        }
    });
}
function useUserSessions() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: authKeys.sessions(),
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].getUserSessions(),
        staleTime: 2 * 60 * 1000
    });
}
function useRevokeSession() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (sessionId)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthService"].revokeSession(sessionId),
        onSuccess: ()=>{
            // Refetch sessions list
            queryClient.invalidateQueries({
                queryKey: authKeys.sessions()
            });
        },
        onError: (error)=>{
            console.error('Session revocation failed:', error);
        }
    });
}
}}),
"[project]/src/lib/providers/auth-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuthContext": (()=>useAuthContext),
    "withAdminAuth": (()=>withAdminAuth),
    "withAuth": (()=>withAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
/**
 * Authentication Provider for AI-fitness application
 * Provides authentication context and session management
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/use-auth.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const { user, isAuthenticated, isLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$use$2d$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const [refreshKey, setRefreshKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const refreshAuth = ()=>{
        setRefreshKey((prev)=>prev + 1);
    };
    // Update API client with auth token when user changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, [
        user
    ]);
    const contextValue = {
        user,
        isAuthenticated,
        isLoading,
        error,
        refreshAuth
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/providers/auth-provider.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
function useAuthContext() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}
function withAuth(Component) {
    return function AuthenticatedComponent(props) {
        const { isAuthenticated, isLoading } = useAuthContext();
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 82,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 81,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-md p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-center text-gray-900 mb-4",
                            children: "Authentication Required"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 91,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-center mb-6",
                            children: "Please sign in to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 94,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.href = '/auth/signin',
                                    className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
                                    children: "Sign In"
                                }, void 0, false, {
                                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                                    lineNumber: 98,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>window.location.href = '/auth/signup',
                                    className: "w-full bg-gray-200 text-gray-900 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors",
                                    children: "Sign Up"
                                }, void 0, false, {
                                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                                    lineNumber: 104,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 90,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 89,
                columnNumber: 9
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/lib/providers/auth-provider.tsx",
            lineNumber: 116,
            columnNumber: 12
        }, this);
    };
}
function withAdminAuth(Component) {
    return function AdminAuthenticatedComponent(props) {
        const { user, isAuthenticated, isLoading } = useAuthContext();
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
                }, void 0, false, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 132,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 131,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated || user?.role !== 'admin') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center bg-gray-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full bg-white rounded-lg shadow-md p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-bold text-center text-gray-900 mb-4",
                            children: "Admin Access Required"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 text-center mb-6",
                            children: "You need administrator privileges to access this page."
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>window.location.href = '/',
                            className: "w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",
                            children: "Go Home"
                        }, void 0, false, {
                            fileName: "[project]/src/lib/providers/auth-provider.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/lib/providers/auth-provider.tsx",
                    lineNumber: 140,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/lib/providers/auth-provider.tsx",
                lineNumber: 139,
                columnNumber: 9
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/src/lib/providers/auth-provider.tsx",
            lineNumber: 158,
            columnNumber: 12
        }, this);
    };
}
}}),
"[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Global Application State Management with Zustand
 * Handles app-wide state that needs to persist across components
 */ __turbopack_context__.s({
    "appActions": (()=>appActions),
    "useAppStore": (()=>useAppStore),
    "useHasPendingSync": (()=>useHasPendingSync),
    "useIsAuthenticated": (()=>useIsAuthenticated),
    "useIsOffline": (()=>useIsOffline),
    "useNotifications": (()=>useNotifications),
    "useOfflineState": (()=>useOfflineState),
    "useSettings": (()=>useSettings),
    "useUIState": (()=>useUIState),
    "useUnreadNotificationCount": (()=>useUnreadNotificationCount),
    "useUser": (()=>useUser),
    "useWorkoutPreferences": (()=>useWorkoutPreferences)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2f$immer$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware/immer.mjs [app-ssr] (ecmascript)");
;
;
;
// ============================================================================
// DEFAULT VALUES
// ============================================================================
const defaultSettings = {
    theme: 'system',
    language: 'en',
    units: 'metric',
    notifications: {
        workoutReminders: true,
        progressUpdates: true,
        achievements: true,
        marketing: false
    },
    privacy: {
        shareProgress: false,
        showInLeaderboards: true,
        allowDataCollection: true
    }
};
const defaultWorkoutPreferences = {
    defaultDuration: 45,
    preferredDifficulty: 'INTERMEDIATE',
    favoriteCategories: [],
    excludedEquipment: [],
    restTimeBetweenSets: 60,
    autoStartNextExercise: false,
    playWorkoutMusic: true,
    voiceInstructions: false
};
const defaultUIState = {
    sidebarCollapsed: false,
    activeWorkoutSession: null,
    currentPage: '/',
    breadcrumbs: [],
    notifications: [],
    modals: {
        workoutComplete: false,
        goalAchieved: false,
        subscriptionPrompt: false
    }
};
const defaultOfflineState = {
    isOnline: true,
    pendingSync: [],
    lastSyncTime: null
};
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2f$immer$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["immer"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        settings: defaultSettings,
        workoutPreferences: defaultWorkoutPreferences,
        ui: defaultUIState,
        offline: defaultOfflineState,
        // Actions
        setUser: (user)=>set((state)=>{
                state.user = user;
            }),
        setAuthenticated: (authenticated)=>set((state)=>{
                state.isAuthenticated = authenticated;
                if (!authenticated) {
                    state.user = null;
                }
            }),
        updateSettings: (newSettings)=>set((state)=>{
                Object.assign(state.settings, newSettings);
            }),
        updateWorkoutPreferences: (newPreferences)=>set((state)=>{
                Object.assign(state.workoutPreferences, newPreferences);
            }),
        updateUIState: (newUIState)=>set((state)=>{
                Object.assign(state.ui, newUIState);
            }),
        addNotification: (notification)=>set((state)=>{
                // Ensure notifications array exists
                if (!state.ui.notifications) {
                    state.ui.notifications = [];
                }
                const newNotification = {
                    ...notification,
                    id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now(),
                    read: false
                };
                state.ui.notifications.unshift(newNotification);
                // Keep only last 50 notifications
                if (state.ui.notifications.length > 50) {
                    state.ui.notifications = state.ui.notifications.slice(0, 50);
                }
            }),
        markNotificationRead: (id)=>set((state)=>{
                // Ensure notifications array exists
                if (!state.ui.notifications) {
                    state.ui.notifications = [];
                    return;
                }
                const notification = state.ui.notifications.find((n)=>n.id === id);
                if (notification) {
                    notification.read = true;
                }
            }),
        clearNotifications: ()=>set((state)=>{
                state.ui.notifications = [];
            }),
        setOnlineStatus: (isOnline)=>set((state)=>{
                state.offline.isOnline = isOnline;
            }),
        addPendingSync: (item)=>set((state)=>{
                const newItem = {
                    ...item,
                    id: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    timestamp: Date.now()
                };
                state.offline.pendingSync.push(newItem);
            }),
        removePendingSync: (id)=>set((state)=>{
                state.offline.pendingSync = state.offline.pendingSync.filter((item)=>item.id !== id);
            }),
        clearPendingSync: ()=>set((state)=>{
                state.offline.pendingSync = [];
            }),
        updateLastSyncTime: ()=>set((state)=>{
                state.offline.lastSyncTime = Date.now();
            }),
        reset: ()=>set(()=>({
                    user: null,
                    isAuthenticated: false,
                    settings: defaultSettings,
                    workoutPreferences: defaultWorkoutPreferences,
                    ui: defaultUIState,
                    offline: defaultOfflineState
                }))
    })), {
    name: 'ai-fitness-app-store',
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage),
    partialize: (state)=>({
            // Only persist certain parts of the state
            settings: state.settings,
            workoutPreferences: state.workoutPreferences,
            ui: {
                sidebarCollapsed: state.ui.sidebarCollapsed,
                // Don't persist notifications and modals
                notifications: []
            },
            offline: {
                pendingSync: state.offline.pendingSync,
                lastSyncTime: state.offline.lastSyncTime
            }
        })
}));
const useUser = ()=>useAppStore((state)=>state.user);
const useIsAuthenticated = ()=>useAppStore((state)=>state.isAuthenticated);
const useSettings = ()=>useAppStore((state)=>state.settings);
const useWorkoutPreferences = ()=>useAppStore((state)=>state.workoutPreferences);
const useUIState = ()=>useAppStore((state)=>state.ui);
const useNotifications = ()=>useAppStore((state)=>state.ui.notifications || []);
const useOfflineState = ()=>useAppStore((state)=>state.offline);
const useUnreadNotificationCount = ()=>useAppStore((state)=>(state.ui.notifications || []).filter((n)=>!n.read).length);
const useHasPendingSync = ()=>useAppStore((state)=>state.offline.pendingSync.length > 0);
const useIsOffline = ()=>useAppStore((state)=>!state.offline.isOnline);
const appActions = {
    setUser: (user)=>useAppStore.getState().setUser(user),
    setAuthenticated: (authenticated)=>useAppStore.getState().setAuthenticated(authenticated),
    updateSettings: (settings)=>useAppStore.getState().updateSettings(settings),
    updateWorkoutPreferences: (preferences)=>useAppStore.getState().updateWorkoutPreferences(preferences),
    addNotification: (notification)=>useAppStore.getState().addNotification(notification),
    setOnlineStatus: (isOnline)=>useAppStore.getState().setOnlineStatus(isOnline),
    addPendingSync: (item)=>useAppStore.getState().addPendingSync(item),
    removePendingSync: (id)=>useAppStore.getState().removePendingSync(id),
    clearPendingSync: ()=>useAppStore.getState().clearPendingSync(),
    updateLastSyncTime: ()=>useAppStore.getState().updateLastSyncTime(),
    reset: ()=>useAppStore.getState().reset()
};
}}),
"[project]/src/lib/api/services/workouts.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Workout API Service
 * Handles all workout-related API calls
 */ __turbopack_context__.s({
    "WorkoutService": (()=>WorkoutService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/adapters/workout-cool.adapter.ts [app-ssr] (ecmascript)");
;
;
;
class WorkoutService {
    /**
   * Get user's workout sessions with optional filtering - adapted for workout-cool API
   */ static async getWorkoutSessions(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.status) searchParams.append('status', params.status.toUpperCase());
        if (params.programId) searchParams.append('programId', params.programId);
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.LIST;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        // Adapt workout-cool sessions to our format
        const adaptedSessions = response.sessions?.map((session)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(session)) || [];
        return {
            data: adaptedSessions,
            pagination: {
                page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,
                limit: params.limit || 10,
                total: adaptedSessions.length,
                totalPages: Math.ceil(adaptedSessions.length / (params.limit || 10))
            }
        };
    }
    /**
   * Get workout session by ID - adapted for workout-cool API
   */ static async getWorkoutSession(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id));
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Create a new workout session - adapted for workout-cool API
   */ static async createWorkoutSession(data) {
        const workoutCoolData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].toWorkoutCoolFormat.workoutSession(data);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.CREATE, workoutCoolData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Update workout session - adapted for workout-cool API
   */ static async updateWorkoutSession(id, data) {
        const workoutCoolData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].toWorkoutCoolFormat.workoutSession(data);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.UPDATE(id), workoutCoolData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Delete workout session - adapted for workout-cool API
   */ static async deleteWorkoutSession(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DELETE(id));
    }
    /**
   * Start a workout session - adapted for workout-cool API
   */ static async startWorkoutSession(id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.DETAILS(id)}/start`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Complete a workout session - adapted for workout-cool API
   */ static async completeWorkoutSession(id, data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.WORKOUTS.COMPLETE(id), data);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptWorkoutSession(response);
    }
    /**
   * Get workout programs - adapted for workout-cool public API
   */ static async getWorkoutPrograms(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.category) searchParams.append('category', params.category);
        if (params.difficulty) searchParams.append('level', params.difficulty.toUpperCase());
        if (params.duration) searchParams.append('duration', params.duration);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.PUBLIC_LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.PUBLIC_LIST;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        // Adapt workout-cool programs to our format
        const adaptedPrograms = response.map((program)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$adapters$2f$workout$2d$cool$2e$adapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutCoolAdapter"].adaptProgram(program));
        return {
            data: adaptedPrograms,
            pagination: {
                page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,
                limit: params.limit || 10,
                total: adaptedPrograms.length,
                totalPages: Math.ceil(adaptedPrograms.length / (params.limit || 10))
            }
        };
    }
    /**
   * Get workout program by ID
   */ static async getWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id));
    }
    /**
   * Create a new workout program
   */ static async createWorkoutProgram(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.CREATE, data);
    }
    /**
   * Update workout program
   */ static async updateWorkoutProgram(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.UPDATE(id), data);
    }
    /**
   * Delete workout program
   */ static async deleteWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DELETE(id));
    }
    /**
   * Join a workout program
   */ static async joinWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id)}/join`);
    }
    /**
   * Leave a workout program
   */ static async leaveWorkoutProgram(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.DETAILS(id)}/leave`);
    }
    /**
   * Get user's joined programs
   */ static async getUserPrograms() {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/user/programs');
        return response.programs || [];
    }
    /**
   * Get popular workout programs
   */ static async getPopularPrograms(limit = 10) {
        const searchParams = new URLSearchParams({
            sort: 'popular',
            limit: limit.toString()
        });
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data || [];
    }
    /**
   * Get recommended workout programs for user
   */ static async getRecommendedPrograms(limit = 6) {
        const searchParams = new URLSearchParams({
            recommended: 'true',
            limit: limit.toString()
        });
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
        return response.data || [];
    }
    /**
   * Generate AI workout plan
   */ static async generateAIWorkout(preferences) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/workouts/generate', preferences);
    }
    /**
   * Get workout statistics
   */ static async getWorkoutStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/workouts/stats?period=${period}`);
    }
    /**
   * Get workout history
   */ static async getWorkoutHistory(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        const queryString = searchParams.toString();
        const url = queryString ? `/api/workouts/history?${queryString}` : '/api/workouts/history';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
}
}}),
"[project]/src/lib/api/services/progress.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Progress Tracking API Service
 * Handles all progress and analytics related API calls
 * Integrated with workout-cool backend
 */ __turbopack_context__.s({
    "ProgressService": (()=>ProgressService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/config.ts [app-ssr] (ecmascript)");
;
;
class ProgressService {
    /**
   * Get user's progress records with optional filtering
   */ static async getProgressRecords(params = {}) {
        const searchParams = new URLSearchParams();
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.offset) searchParams.append('offset', params.offset.toString());
        if (params.type) searchParams.append('type', params.type);
        if (params.startDate) searchParams.append('startDate', params.startDate);
        if (params.endDate) searchParams.append('endDate', params.endDate);
        if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);
        if (params.workoutId) searchParams.append('workoutId', params.workoutId);
        const queryString = searchParams.toString();
        const url = queryString ? `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.LIST}?${queryString}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.LIST;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(url);
    }
    /**
   * Get progress record by ID
   */ static async getProgressRecord(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.DETAILS(id));
    }
    /**
   * Create a new progress record
   */ static async createProgressRecord(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.CREATE, data);
    }
    /**
   * Update progress record
   */ static async updateProgressRecord(id, data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.UPDATE(id), data);
    }
    /**
   * Delete progress record
   */ static async deleteProgressRecord(id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.DELETE(id));
    }
    /**
   * Get comprehensive progress statistics
   */ static async getProgressStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].ENDPOINTS.PROGRESS.STATS}?period=${period}`);
    }
    /**
   * Get workout completion statistics
   */ static async getWorkoutStats(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/workout-stats?period=${period}`);
    }
    /**
   * Get exercise performance data
   */ static async getExerciseProgress(exerciseId, period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/exercise/${exerciseId}?period=${period}`);
    }
    /**
   * Get body measurements progress
   */ static async getBodyMeasurements(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/body-measurements?period=${period}`);
    }
    /**
   * Add body measurement record
   */ static async addBodyMeasurement(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/progress/body-measurements', data);
    }
    /**
   * Get fitness goals and progress
   */ static async getFitnessGoals() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/goals');
    }
    /**
   * Create a new fitness goal
   */ static async createFitnessGoal(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].post('/api/progress/goals', data);
    }
    /**
   * Update fitness goal progress
   */ static async updateGoalProgress(goalId, currentValue) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].patch(`/api/progress/goals/${goalId}`, {
            currentValue
        });
    }
    /**
   * Get achievement badges and milestones
   */ static async getAchievements() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/achievements');
    }
    /**
   * Get workout calendar data
   */ static async getWorkoutCalendar(year, month) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/calendar?year=${year}&month=${month}`);
    }
    /**
   * Get personal records (PRs)
   */ static async getPersonalRecords() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/api/progress/personal-records');
    }
    /**
   * Get strength progression data
   */ static async getStrengthProgression(exerciseIds) {
        const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/strength-progression${params}`);
    }
    /**
   * Export progress data
   */ static async exportProgressData(format = 'csv', period = 'all') {
        const response = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL}/api/progress/export?format=${format}&period=${period}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth-token')}`
            }
        });
        if (!response.ok) {
            throw new Error('Failed to export data');
        }
        return response.blob();
    }
    /**
   * Get workout intensity analysis
   */ static async getWorkoutIntensity(period = 'month') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/progress/workout-intensity?period=${period}`);
    }
}
}}),
"[project]/src/lib/query/config.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * React Query Configuration and Cache Management
 * Centralized configuration for data fetching, caching, and synchronization
 */ __turbopack_context__.s({
    "cacheUtils": (()=>cacheUtils),
    "default": (()=>queryClient),
    "offlineUtils": (()=>offlineUtils),
    "queryClient": (()=>queryClient),
    "queryKeys": (()=>queryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$sync$2d$storage$2d$persister$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-sync-storage-persister/build/modern/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)");
;
;
;
;
// ============================================================================
// QUERY CLIENT CONFIGURATION
// ============================================================================
const queryConfig = {
    queries: {
        // Global defaults for all queries
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: (failureCount, error)=>{
            // Don't retry on 4xx errors (client errors)
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry up to 3 times for other errors
            return failureCount < 3;
        },
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: true
    },
    mutations: {
        // Global defaults for all mutations
        retry: (failureCount, error)=>{
            // Don't retry mutations on client errors
            if (error?.status >= 400 && error?.status < 500) {
                return false;
            }
            // Retry once for server errors
            return failureCount < 1;
        },
        onError: (error)=>{
            // Global error handling for mutations
            console.error('Mutation error:', error);
            // Add error notification
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'error',
                title: 'Operation Failed',
                message: error?.message || 'An unexpected error occurred'
            });
        }
    }
};
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
    defaultOptions: queryConfig
});
// ============================================================================
// PERSISTENCE CONFIGURATION
// ============================================================================
const persister = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$sync$2d$storage$2d$persister$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSyncStoragePersister"])({
    storage: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : undefined,
    key: 'ai-fitness-query-cache',
    serialize: JSON.stringify,
    deserialize: JSON.parse
});
// Persist query client (only in browser)
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
const cacheUtils = {
    /**
   * Invalidate all queries for a specific entity type
   */ invalidateEntity: (entityType)=>{
        queryClient.invalidateQueries({
            queryKey: [
                entityType
            ]
        });
    },
    /**
   * Remove all cached data for a specific entity
   */ removeEntity: (entityType, id)=>{
        if (id) {
            queryClient.removeQueries({
                queryKey: [
                    entityType,
                    id
                ]
            });
        } else {
            queryClient.removeQueries({
                queryKey: [
                    entityType
                ]
            });
        }
    },
    /**
   * Prefetch data for better UX
   */ prefetch: async (queryKey, queryFn)=>{
        await queryClient.prefetchQuery({
            queryKey,
            queryFn,
            staleTime: 10 * 60 * 1000
        });
    },
    /**
   * Set query data manually (for optimistic updates)
   */ setQueryData: (queryKey, data)=>{
        queryClient.setQueryData(queryKey, data);
    },
    /**
   * Get cached query data
   */ getQueryData: (queryKey)=>{
        return queryClient.getQueryData(queryKey);
    },
    /**
   * Clear all cached data
   */ clearAll: ()=>{
        queryClient.clear();
    },
    /**
   * Reset queries to refetch fresh data
   */ resetQueries: (queryKey)=>{
        if (queryKey) {
            queryClient.resetQueries({
                queryKey
            });
        } else {
            queryClient.resetQueries();
        }
    },
    /**
   * Cancel ongoing queries
   */ cancelQueries: (queryKey)=>{
        if (queryKey) {
            queryClient.cancelQueries({
                queryKey
            });
        } else {
            queryClient.cancelQueries();
        }
    }
};
const queryKeys = {
    // Auth
    auth: {
        all: [
            'auth'
        ],
        session: ()=>[
                ...queryKeys.auth.all,
                'session'
            ],
        user: ()=>[
                ...queryKeys.auth.all,
                'user'
            ]
    },
    // Exercises
    exercises: {
        all: [
            'exercises'
        ],
        lists: ()=>[
                ...queryKeys.exercises.all,
                'list'
            ],
        list: (filters)=>[
                ...queryKeys.exercises.lists(),
                filters
            ],
        details: ()=>[
                ...queryKeys.exercises.all,
                'detail'
            ],
        detail: (id)=>[
                ...queryKeys.exercises.details(),
                id
            ],
        search: (query)=>[
                ...queryKeys.exercises.all,
                'search',
                query
            ],
        attributes: ()=>[
                ...queryKeys.exercises.all,
                'attributes'
            ]
    },
    // Workouts
    workouts: {
        all: [
            'workouts'
        ],
        sessions: ()=>[
                ...queryKeys.workouts.all,
                'sessions'
            ],
        session: (id)=>[
                ...queryKeys.workouts.sessions(),
                id
            ],
        programs: ()=>[
                ...queryKeys.workouts.all,
                'programs'
            ],
        program: (id)=>[
                ...queryKeys.workouts.programs(),
                id
            ],
        history: (filters)=>[
                ...queryKeys.workouts.all,
                'history',
                filters
            ],
        stats: (period)=>[
                ...queryKeys.workouts.all,
                'stats',
                period
            ]
    },
    // Progress
    progress: {
        all: [
            'progress'
        ],
        records: ()=>[
                ...queryKeys.progress.all,
                'records'
            ],
        record: (id)=>[
                ...queryKeys.progress.records(),
                id
            ],
        stats: (period)=>[
                ...queryKeys.progress.all,
                'stats',
                period
            ],
        goals: ()=>[
                ...queryKeys.progress.all,
                'goals'
            ],
        achievements: ()=>[
                ...queryKeys.progress.all,
                'achievements'
            ],
        calendar: (year, month)=>[
                ...queryKeys.progress.all,
                'calendar',
                year,
                month
            ]
    },
    // User
    user: {
        all: [
            'user'
        ],
        profile: ()=>[
                ...queryKeys.user.all,
                'profile'
            ],
        preferences: ()=>[
                ...queryKeys.user.all,
                'preferences'
            ],
        subscription: ()=>[
                ...queryKeys.user.all,
                'subscription'
            ]
    }
};
const offlineUtils = {
    /**
   * Check if we're online
   */ isOnline: ()=>{
        return typeof navigator !== 'undefined' ? navigator.onLine : true;
    },
    /**
   * Setup online/offline event listeners
   */ setupNetworkListeners: ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const handleOnline = undefined;
        const handleOffline = undefined;
    },
    /**
   * Queue mutation for offline sync
   */ queueOfflineMutation: (type, action, data)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addPendingSync({
            type: type,
            action: action,
            data
        });
    }
};
// ============================================================================
// QUERY CLIENT EVENTS
// ============================================================================
// Setup global query client event listeners
queryClient.getQueryCache().subscribe((event)=>{
    // Log query events in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Query event:', event);
    }
    // Handle specific events
    switch(event.type){
        case 'added':
            break;
        case 'removed':
            break;
        case 'updated':
            break;
    }
});
queryClient.getMutationCache().subscribe((event)=>{
    // Log mutation events in development
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('Mutation event:', event);
    }
    // Handle mutation success/error globally
    if (event.type === 'updated') {
        const mutation = event.mutation;
        if (mutation.state.status === 'success') {
            // Global success handling
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'success',
                title: 'Success',
                message: 'Operation completed successfully'
            });
        }
    }
});
;
}}),
"[project]/src/lib/sync/sync-manager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Sync Manager for Offline Data Synchronization
 * Handles syncing offline data when connection is restored
 */ __turbopack_context__.s({
    "SyncManager": (()=>SyncManager),
    "default": (()=>__TURBOPACK__default__export__),
    "optimisticUpdates": (()=>optimisticUpdates),
    "useSyncManager": (()=>useSyncManager),
    "useSyncStatus": (()=>useSyncStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/workouts.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/progress.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/config.ts [app-ssr] (ecmascript)");
;
;
;
;
class SyncManager {
    static instance;
    syncInProgress = false;
    syncQueue = [];
    constructor(){
        this.setupNetworkListeners();
    }
    static getInstance() {
        if (!SyncManager.instance) {
            SyncManager.instance = new SyncManager();
        }
        return SyncManager.instance;
    }
    /**
   * Setup network event listeners
   */ setupNetworkListeners() {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const handleOnline = undefined;
        const handleOffline = undefined;
    }
    /**
   * Add data to sync queue for offline processing
   */ addToSyncQueue(item) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addPendingSync(item);
        // Try to sync immediately if online
        if (navigator.onLine) {
            this.syncPendingData();
        }
    }
    /**
   * Sync all pending data
   */ async syncPendingData() {
        if (this.syncInProgress || !navigator.onLine) {
            return;
        }
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState();
        const pendingItems = state.offline.pendingSync;
        if (pendingItems.length === 0) {
            return;
        }
        this.syncInProgress = true;
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'info',
                title: 'Syncing Data',
                message: `Syncing ${pendingItems.length} pending items...`
            });
            const syncResults = await Promise.allSettled(pendingItems.map((item)=>this.syncSingleItem(item)));
            // Process results
            let successCount = 0;
            let failureCount = 0;
            syncResults.forEach((result, index)=>{
                const item = pendingItems[index];
                if (result.status === 'fulfilled') {
                    successCount++;
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].removePendingSync(item.id);
                } else {
                    failureCount++;
                    console.error(`Failed to sync item ${item.id}:`, result.reason);
                }
            });
            // Update last sync time
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].updateLastSyncTime();
            // Show result notification
            if (failureCount === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                    type: 'success',
                    title: 'Sync Complete',
                    message: `Successfully synced ${successCount} items`
                });
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                    type: 'warning',
                    title: 'Sync Partially Complete',
                    message: `Synced ${successCount} items, ${failureCount} failed`
                });
            }
            // Invalidate relevant queries to refetch fresh data
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].invalidateEntity('workouts');
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].invalidateEntity('progress');
        } catch (error) {
            console.error('Sync failed:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].addNotification({
                type: 'error',
                title: 'Sync Failed',
                message: 'Failed to sync offline data. Will retry later.'
            });
        } finally{
            this.syncInProgress = false;
        }
    }
    /**
   * Sync a single item
   */ async syncSingleItem(item) {
        switch(item.type){
            case 'workout':
                return this.syncWorkoutItem(item);
            case 'progress':
                return this.syncProgressItem(item);
            case 'goal':
                return this.syncGoalItem(item);
            default:
                throw new Error(`Unknown sync item type: ${item.type}`);
        }
    }
    /**
   * Sync workout-related items
   */ async syncWorkoutItem(item) {
        switch(item.action){
            case 'create':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].createWorkoutSession(item.data);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].createWorkoutProgram(item.data);
                }
                break;
            case 'update':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].updateWorkoutSession(item.data.id, item.data);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].updateWorkoutProgram(item.data.id, item.data);
                }
                break;
            case 'delete':
                if (item.data.type === 'session') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].deleteWorkoutSession(item.data.id);
                } else if (item.data.type === 'program') {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$workouts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WorkoutService"].deleteWorkoutProgram(item.data.id);
                }
                break;
        }
        throw new Error(`Unknown workout sync action: ${item.action}`);
    }
    /**
   * Sync progress-related items
   */ async syncProgressItem(item) {
        switch(item.action){
            case 'create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].createProgressRecord(item.data);
            case 'update':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].updateProgressRecord(item.data.id, item.data);
            case 'delete':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].deleteProgressRecord(item.data.id);
        }
        throw new Error(`Unknown progress sync action: ${item.action}`);
    }
    /**
   * Sync goal-related items
   */ async syncGoalItem(item) {
        switch(item.action){
            case 'create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].createFitnessGoal(item.data);
            case 'update':
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$progress$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgressService"].updateGoalProgress(item.data.id, item.data.currentValue);
            default:
                throw new Error(`Unknown goal sync action: ${item.action}`);
        }
    }
    /**
   * Force sync all data
   */ async forceSyncAll() {
        await this.syncPendingData();
    }
    /**
   * Clear all pending sync data
   */ clearPendingSync() {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["appActions"].clearPendingSync();
    }
    /**
   * Get sync status
   */ getSyncStatus() {
        const state = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState();
        return {
            isOnline: state.offline.isOnline,
            pendingCount: state.offline.pendingSync.length,
            lastSyncTime: state.offline.lastSyncTime,
            syncInProgress: this.syncInProgress
        };
    }
}
function useSyncManager() {
    const syncManager = SyncManager.getInstance();
    return {
        addToSyncQueue: (item)=>syncManager.addToSyncQueue(item),
        syncPendingData: ()=>syncManager.syncPendingData(),
        forceSyncAll: ()=>syncManager.forceSyncAll(),
        clearPendingSync: ()=>syncManager.clearPendingSync(),
        getSyncStatus: ()=>syncManager.getSyncStatus()
    };
}
function useSyncStatus() {
    const syncManager = SyncManager.getInstance();
    const status = syncManager.getSyncStatus();
    return status;
}
const optimisticUpdates = {
    /**
   * Optimistically update workout session
   */ updateWorkoutSession: (sessionId, updates)=>{
        const queryKey = [
            'workouts',
            'sessions',
            sessionId
        ];
        const previousData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].getQueryData(queryKey);
        // Apply optimistic update
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, {
            ...previousData || {},
            ...updates,
            updatedAt: new Date().toISOString()
        });
        return previousData;
    },
    /**
   * Optimistically add progress record
   */ addProgressRecord: (record)=>{
        const queryKey = [
            'progress',
            'records'
        ];
        const previousData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].getQueryData(queryKey);
        if (previousData?.data) {
            const newRecord = {
                ...record,
                id: `temp-${Date.now()}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, {
                ...previousData,
                data: [
                    newRecord,
                    ...previousData.data
                ]
            });
        }
        return previousData;
    },
    /**
   * Revert optimistic update
   */ revertUpdate: (queryKey, previousData)=>{
        if (previousData !== undefined) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cacheUtils"].setQueryData(queryKey, previousData);
        }
    }
};
const __TURBOPACK__default__export__ = SyncManager;
}}),
"[project]/src/lib/theme/mui-theme.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fitnessTheme": (()=>fitnessTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$createTheme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__createTheme$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/styles/createTheme.js [app-ssr] (ecmascript) <export default as createTheme>");
;
const fitnessTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$createTheme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__createTheme$3e$__["createTheme"])({
    palette: {
        primary: {
            main: '#FF6B35',
            light: '#FF8A65',
            dark: '#E64A19',
            contrastText: '#FFFFFF'
        },
        secondary: {
            main: '#4CAF50',
            light: '#81C784',
            dark: '#388E3C',
            contrastText: '#FFFFFF'
        },
        background: {
            default: '#FAFAFA',
            paper: '#FFFFFF'
        },
        text: {
            primary: '#212121',
            secondary: '#757575'
        },
        error: {
            main: '#F44336'
        },
        warning: {
            main: '#FF9800'
        },
        info: {
            main: '#2196F3'
        },
        success: {
            main: '#4CAF50'
        }
    },
    typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
            fontSize: '3rem',
            fontWeight: 700,
            lineHeight: 1.2
        },
        h2: {
            fontSize: '2.5rem',
            fontWeight: 600,
            lineHeight: 1.3
        },
        h3: {
            fontSize: '2rem',
            fontWeight: 600,
            lineHeight: 1.4
        },
        h4: {
            fontSize: '1.5rem',
            fontWeight: 500,
            lineHeight: 1.4
        },
        h5: {
            fontSize: '1.25rem',
            fontWeight: 500,
            lineHeight: 1.5
        },
        h6: {
            fontSize: '1rem',
            fontWeight: 500,
            lineHeight: 1.5
        },
        body1: {
            fontSize: '1rem',
            lineHeight: 1.6
        },
        body2: {
            fontSize: '0.875rem',
            lineHeight: 1.5
        },
        button: {
            textTransform: 'none',
            fontWeight: 600
        }
    },
    shape: {
        borderRadius: 12
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: 25,
                    padding: '12px 24px',
                    fontSize: '1rem',
                    fontWeight: 600,
                    textTransform: 'none',
                    boxShadow: 'none',
                    '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        transform: 'translateY(-2px)',
                        transition: 'all 0.3s ease'
                    }
                },
                contained: {
                    background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',
                    '&:hover': {
                        background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)'
                    }
                },
                outlined: {
                    borderWidth: 2,
                    '&:hover': {
                        borderWidth: 2
                    }
                }
            }
        },
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: 16,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    '&:hover': {
                        boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                        transform: 'translateY(-4px)',
                        transition: 'all 0.3s ease'
                    }
                }
            }
        },
        MuiAppBar: {
            styleOverrides: {
                root: {
                    backgroundColor: '#FFFFFF',
                    color: '#212121',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }
            }
        },
        MuiChip: {
            styleOverrides: {
                root: {
                    borderRadius: 20,
                    fontWeight: 500
                }
            }
        },
        MuiFab: {
            styleOverrides: {
                root: {
                    background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',
                    '&:hover': {
                        background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)',
                        transform: 'scale(1.1)'
                    }
                }
            }
        }
    }
});
}}),
"[project]/src/components/providers/app-providers.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppProviders": (()=>AppProviders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$ThemeProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ThemeProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/styles/ThemeProvider.js [app-ssr] (ecmascript) <export default as ThemeProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CssBaseline$2f$CssBaseline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CssBaseline$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/CssBaseline/CssBaseline.js [app-ssr] (ecmascript) <export default as CssBaseline>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/query-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/auth-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sync$2f$sync$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/sync/sync-manager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2f$mui$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/theme/mui-theme.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
function AppProviders({ children }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize sync manager
        const syncManager = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sync$2f$sync$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SyncManager"].getInstance();
        // Setup network listeners
        const cleanupNetworkListeners = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["offlineUtils"].setupNetworkListeners();
        // Cleanup on unmount
        return ()=>{
            if (cleanupNetworkListeners) {
                cleanupNetworkListeners();
            }
        };
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$ThemeProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ThemeProvider$3e$__["ThemeProvider"], {
        theme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2f$mui$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fitnessTheme"],
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CssBaseline$2f$CssBaseline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CssBaseline$3e$__["CssBaseline"], {}, void 0, false, {
                fileName: "[project]/src/components/providers/app-providers.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthProvider"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/components/providers/app-providers.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/providers/app-providers.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/providers/app-providers.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/components/navigation/mui-navigation.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MuiNavigation": (()=>MuiNavigation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$AppBar$2f$AppBar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AppBar$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/AppBar/AppBar.js [app-ssr] (ecmascript) <export default as AppBar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Toolbar$2f$Toolbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Toolbar$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Toolbar/Toolbar.js [app-ssr] (ecmascript) <export default as Toolbar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-ssr] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Button/Button.js [app-ssr] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/IconButton/IconButton.js [app-ssr] (ecmascript) <export default as IconButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-ssr] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Drawer$2f$Drawer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Drawer$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Drawer/Drawer.js [app-ssr] (ecmascript) <export default as Drawer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$List$2f$List$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/List/List.js [app-ssr] (ecmascript) <export default as List>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$ListItem$2f$ListItem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItem$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/ListItem/ListItem.js [app-ssr] (ecmascript) <export default as ListItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$ListItemIcon$2f$ListItemIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItemIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/ListItemIcon/ListItemIcon.js [app-ssr] (ecmascript) <export default as ListItemIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$ListItemText$2f$ListItemText$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItemText$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/ListItemText/ListItemText.js [app-ssr] (ecmascript) <export default as ListItemText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Badge$2f$Badge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Badge/Badge.js [app-ssr] (ecmascript) <export default as Badge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Avatar$2f$Avatar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Avatar$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Avatar/Avatar.js [app-ssr] (ecmascript) <export default as Avatar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$useMediaQuery$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__useMediaQuery$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/useMediaQuery/index.js [app-ssr] (ecmascript) <export default as useMediaQuery>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$useTheme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__useTheme$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/styles/useTheme.js [app-ssr] (ecmascript) <export default as useTheme>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Menu.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$FitnessCenter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/FitnessCenter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$DirectionsRun$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/DirectionsRun.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Assessment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Assessment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Notifications$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Notifications.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountCircle.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Close$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Close.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/store/app-store.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
const navigationItems = [
    {
        label: '训练计划',
        href: '/workouts',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$FitnessCenter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
            lineNumber: 34,
            columnNumber: 45
        }, this)
    },
    {
        label: '运动库',
        href: '/exercises',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$DirectionsRun$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
            lineNumber: 35,
            columnNumber: 45
        }, this)
    },
    {
        label: '进度追踪',
        href: '/progress',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Assessment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
            lineNumber: 36,
            columnNumber: 45
        }, this)
    }
];
function MuiNavigation() {
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$styles$2f$useTheme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__useTheme$3e$__["useTheme"])();
    const isMobile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$useMediaQuery$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__useMediaQuery$3e$__["useMediaQuery"])(theme.breakpoints.down('md'));
    const [mobileOpen, setMobileOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { user, isOnline } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])();
    const unreadCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>(state.ui.notifications || []).filter((n)=>!n.read).length);
    const handleDrawerToggle = ()=>{
        setMobileOpen(!mobileOpen);
    };
    const drawer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        sx: {
            width: 250
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    p: 2,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                        variant: "h6",
                        sx: {
                            color: 'primary.main',
                            fontWeight: 'bold'
                        },
                        children: "AI-fitness-singles"
                    }, void 0, false, {
                        fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                        lineNumber: 56,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                        onClick: handleDrawerToggle,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Close$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                            lineNumber: 60,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                        lineNumber: 59,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                lineNumber: 55,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$List$2f$List$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__["List"], {
                children: navigationItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$ListItem$2f$ListItem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItem$3e$__["ListItem"], {
                        component: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
                        href: item.href,
                        onClick: handleDrawerToggle,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$ListItemIcon$2f$ListItemIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItemIcon$3e$__["ListItemIcon"], {
                                sx: {
                                    color: 'primary.main'
                                },
                                children: item.icon
                            }, void 0, false, {
                                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                lineNumber: 66,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$ListItemText$2f$ListItemText$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItemText$3e$__["ListItemText"], {
                                primary: item.label,
                                sx: {
                                    '& .MuiListItemText-primary': {
                                        fontWeight: 500,
                                        color: 'text.primary'
                                    }
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                lineNumber: 69,
                                columnNumber: 13
                            }, this)
                        ]
                    }, item.href, true, {
                        fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/navigation/mui-navigation.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$AppBar$2f$AppBar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AppBar$3e$__["AppBar"], {
                position: "sticky",
                elevation: 0,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Toolbar$2f$Toolbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Toolbar$3e$__["Toolbar"], {
                    children: [
                        isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                            color: "inherit",
                            "aria-label": "open drawer",
                            edge: "start",
                            onClick: handleDrawerToggle,
                            sx: {
                                mr: 2
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                lineNumber: 97,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                            lineNumber: 90,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
                            href: "/",
                            sx: {
                                display: 'flex',
                                alignItems: 'center',
                                textDecoration: 'none',
                                color: 'inherit'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$FitnessCenter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    sx: {
                                        mr: 1,
                                        color: 'primary.main',
                                        fontSize: 32
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                    variant: "h6",
                                    component: "div",
                                    sx: {
                                        fontWeight: 'bold',
                                        color: 'primary.main',
                                        display: {
                                            xs: 'none',
                                            sm: 'block'
                                        }
                                    },
                                    children: "AI-fitness-singles"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 104,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                            lineNumber: 102,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                flexGrow: 1
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this),
                        !isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                display: 'flex',
                                gap: 1
                            },
                            children: navigationItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    component: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
                                    href: item.href,
                                    startIcon: item.icon,
                                    sx: {
                                        color: 'text.primary',
                                        fontWeight: 500,
                                        '&:hover': {
                                            backgroundColor: 'primary.light',
                                            color: 'white'
                                        }
                                    },
                                    children: item.label
                                }, item.href, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 123,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                ml: 2
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                                    sx: {
                                        width: 8,
                                        height: 8,
                                        borderRadius: '50%',
                                        backgroundColor: isOnline ? 'success.main' : 'error.main'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 146,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                                    color: "inherit",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Badge$2f$Badge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__["Badge"], {
                                        badgeContent: unreadCount,
                                        color: "error",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Notifications$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                            lineNumber: 158,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                        lineNumber: 157,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 156,
                                    columnNumber: 13
                                }, this),
                                user ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Avatar$2f$Avatar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Avatar$3e$__["Avatar"], {
                                    sx: {
                                        width: 32,
                                        height: 32,
                                        bgcolor: 'primary.main',
                                        fontSize: '0.875rem'
                                    },
                                    children: user.name?.charAt(0) || 'U'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 164,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                                    color: "inherit",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                        lineNumber: 176,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                                    lineNumber: 175,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                            lineNumber: 144,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                lineNumber: 86,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Drawer$2f$Drawer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Drawer$3e$__["Drawer"], {
                variant: "temporary",
                open: mobileOpen,
                onClose: handleDrawerToggle,
                ModalProps: {
                    keepMounted: true
                },
                sx: {
                    display: {
                        xs: 'block',
                        md: 'none'
                    },
                    '& .MuiDrawer-paper': {
                        boxSizing: 'border-box',
                        width: 250
                    }
                },
                children: drawer
            }, void 0, false, {
                fileName: "[project]/src/components/navigation/mui-navigation.tsx",
                lineNumber: 184,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__83862ace._.js.map