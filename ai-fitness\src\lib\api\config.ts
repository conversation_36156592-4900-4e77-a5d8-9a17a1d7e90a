/**
 * API Configuration for AI-fitness application
 * Connects to workout-cool backend API
 */

export const API_CONFIG = {
  // Base URL for the workout-cool backend API
  BASE_URL: process.env.NEXT_PUBLIC_WORKOUT_COOL_API_URL || 'http://localhost:3000',

  // API endpoints - Updated to match workout-cool API structure
  ENDPOINTS: {
    // Authentication - Better Auth endpoints
    AUTH: {
      SIGNIN: '/api/auth/sign-in',
      SIGNUP: '/api/auth/sign-up',
      SIGNOUT: '/api/auth/sign-out',
      SESSION: '/api/auth/session',
      RESET_PASSWORD: '/api/auth/reset-password',
      VERIFY_EMAIL: '/api/auth/verify-email',
    },

    // Public Programs - workout-cool public API
    PROGRAMS: {
      PUBLIC_LIST: '/api/programs/public',
      DETAILS: (slug: string) => `/api/programs/${slug}`,
      ENROLL: (id: string) => `/api/programs/${id}/enroll`,
      PROGRESS: (id: string) => `/api/programs/${id}/progress`,
      SESSIONS: (programId: string) => `/api/programs/${programId}/sessions`,
      SESSION_DETAIL: (programId: string, sessionId: string) => `/api/programs/${programId}/sessions/${sessionId}`,
    },

    // Exercises - workout-cool exercise API
    EXERCISES: {
      PUBLIC_LIST: '/api/exercises/public',
      SEARCH: '/api/exercises/search',
      DETAILS: (id: string) => `/api/exercises/${id}`,
      ATTRIBUTES: '/api/exercises/attributes',
      BY_MUSCLE_GROUP: (muscleGroup: string) => `/api/exercises/muscle-groups/${muscleGroup}`,
    },

    // Workout Sessions - workout-cool session management
    WORKOUTS: {
      LIST: '/api/workout-sessions',
      CREATE: '/api/workout-sessions',
      DETAILS: (id: string) => `/api/workout-sessions/${id}`,
      UPDATE: (id: string) => `/api/workout-sessions/${id}`,
      DELETE: (id: string) => `/api/workout-sessions/${id}`,
      COMPLETE: (id: string) => `/api/workout-sessions/${id}/complete`,
      SYNC: '/api/workout-sessions/sync',
      USER_SESSIONS: (userId: string) => `/api/users/${userId}/workout-sessions`,
    },

    // User Progress - workout-cool progress tracking
    PROGRESS: {
      USER_STATS: (userId: string) => `/api/users/${userId}/stats`,
      PROGRAM_PROGRESS: (userId: string, programId: string) => `/api/users/${userId}/programs/${programId}/progress`,
      WORKOUT_HISTORY: (userId: string) => `/api/users/${userId}/workout-history`,
      BODY_MEASUREMENTS: (userId: string) => `/api/users/${userId}/body-measurements`,
      GOALS: (userId: string) => `/api/users/${userId}/goals`,
    },

    // Premium - workout-cool subscription system
    PREMIUM: {
      PLANS: '/api/premium/plans',
      STATUS: '/api/premium/status',
      SUBSCRIPTION: '/api/premium/subscription',
      CHECKOUT: '/api/premium/checkout',
      BILLING_PORTAL: '/api/premium/billing-portal',
    },

    // User Management - workout-cool user API
    USERS: {
      PROFILE: '/api/users/profile',
      UPDATE_PROFILE: '/api/users/profile',
      PREFERENCES: '/api/users/preferences',
      ENROLLMENTS: (userId: string) => `/api/users/${userId}/program-enrollments`,
    },

    // Health Check
    HEALTH: '/api/health',
  },
  
  // Request timeouts
  TIMEOUT: {
    DEFAULT: 10000, // 10 seconds
    UPLOAD: 30000,  // 30 seconds
    DOWNLOAD: 60000, // 60 seconds
  },
  
  // Retry configuration
  RETRY: {
    ATTEMPTS: 3,
    DELAY: 1000, // 1 second
    BACKOFF_FACTOR: 2,
  },
} as const;

export type ApiEndpoints = typeof API_CONFIG.ENDPOINTS;
