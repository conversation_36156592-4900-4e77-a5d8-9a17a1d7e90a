/**
 * API Configuration for AI-fitness application
 * Connects to workout-cool backend API
 */

export const API_CONFIG = {
  // Base URL for the workout-cool backend API
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
  
  // API endpoints
  ENDPOINTS: {
    // Authentication
    AUTH: {
      SIGNIN: '/api/auth/signin',
      SIGNUP: '/api/auth/signup',
      SIGNOUT: '/api/auth/signout',
      SESSION: '/api/auth/session',
      RESET_PASSWORD: '/api/auth/reset-password',
    },
    
    // Exercises
    EXERCISES: {
      LIST: '/api/exercises',
      SEARCH: '/api/exercises/search',
      DETAILS: (id: string) => `/api/exercises/${id}`,
      ATTRIBUTES: '/api/exercises/attributes',
    },
    
    // Workout Sessions
    WORKOUTS: {
      LIST: '/api/workout-sessions',
      CREATE: '/api/workout-sessions',
      DETAILS: (id: string) => `/api/workout-sessions/${id}`,
      UPDATE: (id: string) => `/api/workout-sessions/${id}`,
      DELETE: (id: string) => `/api/workout-sessions/${id}`,
      COMPLETE: (id: string) => `/api/workout-sessions/${id}/complete`,
      SYNC: '/api/workout-sessions/sync',
    },
    
    // Programs
    PROGRAMS: {
      LIST: '/api/programs',
      CREATE: '/api/programs',
      DETAILS: (slug: string) => `/api/programs/${slug}`,
      UPDATE: (id: string) => `/api/programs/${id}`,
      DELETE: (id: string) => `/api/programs/${id}`,
      ENROLL: (id: string) => `/api/programs/${id}/enroll`,
      SESSIONS: (programId: string) => `/api/programs/${programId}/sessions`,
      START_SESSION: '/api/programs/sessions/start',
      COMPLETE_SESSION: '/api/programs/sessions/complete',
    },
    
    // User Progress
    PROGRESS: {
      LIST: '/api/progress',
      DETAILS: (id: string) => `/api/progress/${id}`,
      CREATE: '/api/progress',
      UPDATE: (id: string) => `/api/progress/${id}`,
      DELETE: (id: string) => `/api/progress/${id}`,
      OVERVIEW: '/api/progress/overview',
      STATS: '/api/progress/stats',
      HISTORY: '/api/progress/history',
      GOALS: '/api/progress/goals',
      EXPORT: '/api/progress/export',
    },
    
    // Premium
    PREMIUM: {
      PLANS: '/api/premium/plans',
      SUBSCRIPTION: '/api/premium/subscription',
      CHECKOUT: '/api/premium/checkout',
      BILLING_PORTAL: '/api/premium/billing-portal',
    },
    
    // User Management
    USERS: {
      PROFILE: '/api/users/profile',
      UPDATE_PROFILE: '/api/users/profile',
      PREFERENCES: '/api/users/preferences',
    },
  },
  
  // Request timeouts
  TIMEOUT: {
    DEFAULT: 10000, // 10 seconds
    UPLOAD: 30000,  // 30 seconds
    DOWNLOAD: 60000, // 60 seconds
  },
  
  // Retry configuration
  RETRY: {
    ATTEMPTS: 3,
    DELAY: 1000, // 1 second
    BACKOFF_FACTOR: 2,
  },
} as const;

export type ApiEndpoints = typeof API_CONFIG.ENDPOINTS;
