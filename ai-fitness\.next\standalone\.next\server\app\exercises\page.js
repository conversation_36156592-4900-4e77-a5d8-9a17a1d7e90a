(()=>{var e={};e.id=142,e.ids=[142],e.modules={958:(e,s,t)=>{Promise.resolve().then(t.bind(t,50482))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5686:(e,s,t)=>{Promise.resolve().then(t.bind(t,25548))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25548:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\exercises\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33244:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["exercises",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25548)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/exercises/page",pathname:"/exercises",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},50482:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(60687),r=t(43210),i=t(86246),n=t(44493),l=t(29523),c=t(96834),d=t(89667),o=t(52027),m=t(70293),x=t(35032),u=t(99270),h=t(80462),p=t(11860),g=t(67760),j=t(97840),f=t(96474),v=t(40487);function y(){let[e,s]=(0,r.useState)(""),[t,y]=(0,r.useState)(!1),[b,N]=(0,r.useState)({equipment:[],muscles:[],difficulty:[],category:[]}),{data:w,isLoading:C}=(0,v.IS)(e,20,e.length>0),{data:A,isLoading:P,error:k}=(0,v.Fb)(0===e.length?b:{},0===e.length),{data:q,isLoading:_}=(0,v.d9)(),E=e.length>0?w:A?.data,D=e.length>0?C:P,z=(e,s)=>{N(t=>{let a=t[e]||[],r=a.includes(s)?a.filter(e=>e!==s):[...a,s];return{...t,[e]:r}})},G=Object.values(b).some(e=>Array.isArray(e)&&e.length>0);return _?(0,a.jsx)(o.AV,{}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i.V,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Exercise Library"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover thousands of exercises with detailed instructions, muscle targeting, and difficulty levels"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,a.jsx)(d.p,{type:"text",placeholder:"Search exercises...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>y(!t),className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Filters",G&&(0,a.jsx)(c.E,{variant:"secondary",className:"ml-1",children:Object.values(b).reduce((e,s)=>e+(s?.length||0),0)})]})]}),t&&q&&(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Equipment"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:q.equipment?.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:b.equipment?.includes(e.id)||!1,onChange:()=>z("equipment",e.id),className:"rounded border-gray-300"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Muscles"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:q.muscles?.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:b.muscles?.includes(e.id)||!1,onChange:()=>z("muscles",e.id),className:"rounded border-gray-300"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Categories"}),(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:q.categories?.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:b.category?.includes(e.id)||!1,onChange:()=>z("category",e.id),className:"rounded border-gray-300"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Difficulty"}),(0,a.jsx)("div",{className:"space-y-2",children:q.difficulties?.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:b.difficulty?.includes(e.id)||!1,onChange:()=>z("difficulty",e.id),className:"rounded border-gray-300"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id))})]})]}),G&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{N({equipment:[],muscles:[],difficulty:[],category:[]})},className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Clear Filters"]})})]})]}),D?(0,a.jsx)(o.z0,{}):k?(0,a.jsx)(m.Kw,{title:"Failed to load exercises",message:"Please try again later",onRetry:()=>window.location.reload()}):E&&0!==E.length?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:E.map(e=>(0,a.jsxs)(n.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-lg",children:e.name}),(0,a.jsx)(n.BT,{children:e.nameEn})]}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.attributes?.slice(0,3).map((e,s)=>(0,a.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e.attributeName?.name||e.attributeValue?.value},s)),e.attributes&&e.attributes.length>3&&(0,a.jsxs)(c.E,{variant:"outline",className:"text-xs",children:["+",e.attributes.length-3," more"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{size:"sm",className:"flex-1",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})]})})]},e.id))}):e?(0,a.jsx)(x.wT,{searchTerm:e,onClearSearch:()=>s("")}):(0,a.jsx)(x.d2,{}),E&&E.length>0&&A?.pagination?.hasNext&&(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsx)(l.$,{variant:"outline",size:"lg",children:"Load More Exercises"})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[96,76],()=>t(33244));module.exports=a})();