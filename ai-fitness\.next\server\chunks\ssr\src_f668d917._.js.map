{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/seo/semantic-html.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\n// Semantic article component\ninterface ArticleProps {\n  children: React.ReactNode;\n  className?: string;\n  itemScope?: boolean;\n  itemType?: string;\n}\n\nexport function Article({ children, className, itemScope, itemType }: ArticleProps) {\n  return (\n    <article \n      className={cn(className)}\n      itemScope={itemScope}\n      itemType={itemType}\n    >\n      {children}\n    </article>\n  );\n}\n\n// Semantic section component\ninterface SectionProps {\n  children: React.ReactNode;\n  className?: string;\n  ariaLabel?: string;\n  ariaLabelledBy?: string;\n  role?: string;\n}\n\nexport function Section({ children, className, ariaLabel, ariaLabelledBy, role }: SectionProps) {\n  return (\n    <section \n      className={cn(className)}\n      aria-label={ariaLabel}\n      aria-labelledby={ariaLabelledBy}\n      role={role}\n    >\n      {children}\n    </section>\n  );\n}\n\n// Semantic header component\ninterface HeaderProps {\n  children: React.ReactNode;\n  className?: string;\n  level?: 1 | 2 | 3 | 4 | 5 | 6;\n  id?: string;\n}\n\nexport function SemanticHeader({ children, className, level = 1, id }: HeaderProps) {\n  const Tag = `h${level}` as keyof JSX.IntrinsicElements;\n  \n  return (\n    <Tag \n      className={cn(className)}\n      id={id}\n    >\n      {children}\n    </Tag>\n  );\n}\n\n// Semantic navigation component\ninterface NavProps {\n  children: React.ReactNode;\n  className?: string;\n  ariaLabel: string;\n  role?: string;\n}\n\nexport function Nav({ children, className, ariaLabel, role = 'navigation' }: NavProps) {\n  return (\n    <nav \n      className={cn(className)}\n      aria-label={ariaLabel}\n      role={role}\n    >\n      {children}\n    </nav>\n  );\n}\n\n// Semantic main content component\ninterface MainProps {\n  children: React.ReactNode;\n  className?: string;\n  id?: string;\n}\n\nexport function Main({ children, className, id = 'main-content' }: MainProps) {\n  return (\n    <main \n      className={cn(className)}\n      id={id}\n      role=\"main\"\n    >\n      {children}\n    </main>\n  );\n}\n\n// Semantic aside component\ninterface AsideProps {\n  children: React.ReactNode;\n  className?: string;\n  ariaLabel?: string;\n}\n\nexport function Aside({ children, className, ariaLabel }: AsideProps) {\n  return (\n    <aside \n      className={cn(className)}\n      aria-label={ariaLabel}\n    >\n      {children}\n    </aside>\n  );\n}\n\n// Semantic footer component\ninterface FooterProps {\n  children: React.ReactNode;\n  className?: string;\n  role?: string;\n}\n\nexport function Footer({ children, className, role = 'contentinfo' }: FooterProps) {\n  return (\n    <footer \n      className={cn(className)}\n      role={role}\n    >\n      {children}\n    </footer>\n  );\n}\n\n// Semantic figure component for images with captions\ninterface FigureProps {\n  children: React.ReactNode;\n  className?: string;\n  caption?: string;\n}\n\nexport function Figure({ children, className, caption }: FigureProps) {\n  return (\n    <figure className={cn(className)}>\n      {children}\n      {caption && (\n        <figcaption className=\"text-sm text-gray-600 mt-2\">\n          {caption}\n        </figcaption>\n      )}\n    </figure>\n  );\n}\n\n// Semantic time component\ninterface TimeProps {\n  dateTime: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function Time({ dateTime, children, className }: TimeProps) {\n  return (\n    <time \n      dateTime={dateTime}\n      className={cn(className)}\n    >\n      {children}\n    </time>\n  );\n}\n\n// Semantic address component\ninterface AddressProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function Address({ children, className }: AddressProps) {\n  return (\n    <address className={cn(className)}>\n      {children}\n    </address>\n  );\n}\n\n// Semantic definition list for key-value pairs\ninterface DefinitionListProps {\n  items: Array<{ term: string; definition: string | React.ReactNode }>;\n  className?: string;\n}\n\nexport function DefinitionList({ items, className }: DefinitionListProps) {\n  return (\n    <dl className={cn(className)}>\n      {items.map((item, index) => (\n        <div key={index} className=\"mb-2\">\n          <dt className=\"font-semibold\">{item.term}</dt>\n          <dd className=\"ml-4\">{item.definition}</dd>\n        </div>\n      ))}\n    </dl>\n  );\n}\n\n// Semantic blockquote component\ninterface BlockquoteProps {\n  children: React.ReactNode;\n  cite?: string;\n  author?: string;\n  className?: string;\n}\n\nexport function Blockquote({ children, cite, author, className }: BlockquoteProps) {\n  return (\n    <blockquote \n      className={cn(\"border-l-4 border-blue-500 pl-4 italic\", className)}\n      cite={cite}\n    >\n      {children}\n      {author && (\n        <footer className=\"text-sm text-gray-600 mt-2\">\n          — <cite>{author}</cite>\n        </footer>\n      )}\n    </blockquote>\n  );\n}\n\n// Semantic mark component for highlighting\ninterface MarkProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function Mark({ children, className }: MarkProps) {\n  return (\n    <mark className={cn(\"bg-yellow-200 px-1\", className)}>\n      {children}\n    </mark>\n  );\n}\n\n// Semantic abbreviation component\ninterface AbbrProps {\n  children: React.ReactNode;\n  title: string;\n  className?: string;\n}\n\nexport function Abbr({ children, title, className }: AbbrProps) {\n  return (\n    <abbr \n      title={title}\n      className={cn(\"border-b border-dotted border-gray-400 cursor-help\", className)}\n    >\n      {children}\n    </abbr>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AACA;;;AAUO,SAAS,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAgB;IAChF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,WAAW;QACX,UAAU;kBAET;;;;;;AAGP;AAWO,SAAS,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,IAAI,EAAgB;IAC5F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,cAAY;QACZ,mBAAiB;QACjB,MAAM;kBAEL;;;;;;AAGP;AAUO,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAe;IAChF,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,IAAI;kBAEH;;;;;;AAGP;AAUO,SAAS,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,YAAY,EAAY;IACnF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,cAAY;QACZ,MAAM;kBAEL;;;;;;AAGP;AASO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,cAAc,EAAa;IAC1E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,IAAI;QACJ,MAAK;kBAEJ;;;;;;AAGP;AASO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAc;IAClE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,cAAY;kBAEX;;;;;;AAGP;AASO,SAAS,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,aAAa,EAAe;IAC/E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,MAAM;kBAEL;;;;;;AAGP;AASO,SAAS,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAe;IAClE,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;;YACnB;YACA,yBACC,8OAAC;gBAAW,WAAU;0BACnB;;;;;;;;;;;;AAKX;AASO,SAAS,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAa;IAC/D,qBACE,8OAAC;QACC,UAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;kBAEb;;;;;;AAGP;AAQO,SAAS,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAgB;IAC3D,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;kBACpB;;;;;;AAGP;AAQO,SAAS,eAAe,EAAE,KAAK,EAAE,SAAS,EAAuB;IACtE,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;kBACf,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAAgB,WAAU;;kCACzB,8OAAC;wBAAG,WAAU;kCAAiB,KAAK,IAAI;;;;;;kCACxC,8OAAC;wBAAG,WAAU;kCAAQ,KAAK,UAAU;;;;;;;eAF7B;;;;;;;;;;AAOlB;AAUO,SAAS,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAmB;IAC/E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,MAAM;;YAEL;YACA,wBACC,8OAAC;gBAAO,WAAU;;oBAA6B;kCAC3C,8OAAC;kCAAM;;;;;;;;;;;;;;;;;;AAKnB;AAQO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAa;IACrD,qBACE,8OAAC;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;kBACvC;;;;;;AAGP;AASO,SAAS,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAa;IAC5D,qBACE,8OAAC;QACC,OAAO;QACP,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;kBAEnE;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/seo/internal-links.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { cn } from '@/lib/utils';\n\n// Internal link component with SEO optimization\ninterface InternalLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n  title?: string;\n  rel?: string;\n  prefetch?: boolean;\n  replace?: boolean;\n  scroll?: boolean;\n  shallow?: boolean;\n}\n\nexport function InternalLink({\n  href,\n  children,\n  className,\n  title,\n  rel,\n  prefetch = true,\n  replace = false,\n  scroll = true,\n  shallow = false,\n}: InternalLinkProps) {\n  return (\n    <Link\n      href={href}\n      className={cn(\"text-blue-600 hover:text-blue-800 underline\", className)}\n      title={title}\n      rel={rel}\n      prefetch={prefetch}\n      replace={replace}\n      scroll={scroll}\n      shallow={shallow}\n    >\n      {children}\n    </Link>\n  );\n}\n\n// Related content links\ninterface RelatedLinksProps {\n  title?: string;\n  links: Array<{\n    href: string;\n    title: string;\n    description?: string;\n    category?: string;\n  }>;\n  className?: string;\n}\n\nexport function RelatedLinks({ title = \"Related Content\", links, className }: RelatedLinksProps) {\n  if (links.length === 0) return null;\n\n  return (\n    <aside className={cn(\"bg-gray-50 p-6 rounded-lg\", className)} aria-label=\"Related content\">\n      <h3 className=\"text-lg font-semibold mb-4\">{title}</h3>\n      <nav aria-label=\"Related links\">\n        <ul className=\"space-y-3\">\n          {links.map((link, index) => (\n            <li key={index}>\n              <InternalLink\n                href={link.href}\n                title={link.description}\n                className=\"block hover:bg-white p-3 rounded transition-colors\"\n              >\n                <div className=\"font-medium\">{link.title}</div>\n                {link.description && (\n                  <div className=\"text-sm text-gray-600 mt-1\">{link.description}</div>\n                )}\n                {link.category && (\n                  <div className=\"text-xs text-blue-600 mt-1\">{link.category}</div>\n                )}\n              </InternalLink>\n            </li>\n          ))}\n        </ul>\n      </nav>\n    </aside>\n  );\n}\n\n// Exercise category links\nexport function ExerciseCategoryLinks({ className }: { className?: string }) {\n  const categories = [\n    { name: 'Strength Training', href: '/exercises?category=strength', description: 'Build muscle and increase strength' },\n    { name: 'Cardio', href: '/exercises?category=cardio', description: 'Improve cardiovascular health' },\n    { name: 'Flexibility', href: '/exercises?category=flexibility', description: 'Enhance mobility and flexibility' },\n    { name: 'Bodyweight', href: '/exercises?category=bodyweight', description: 'No equipment needed' },\n    { name: 'Weight Training', href: '/exercises?category=weights', description: 'Exercises with weights' },\n    { name: 'Functional', href: '/exercises?category=functional', description: 'Real-world movement patterns' },\n  ];\n\n  return (\n    <nav className={cn(\"grid grid-cols-2 md:grid-cols-3 gap-4\", className)} aria-label=\"Exercise categories\">\n      {categories.map((category) => (\n        <InternalLink\n          key={category.name}\n          href={category.href}\n          className=\"block p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all no-underline\"\n          title={category.description}\n        >\n          <div className=\"font-medium text-gray-900\">{category.name}</div>\n          <div className=\"text-sm text-gray-600 mt-1\">{category.description}</div>\n        </InternalLink>\n      ))}\n    </nav>\n  );\n}\n\n// Workout program links\nexport function WorkoutProgramLinks({ className }: { className?: string }) {\n  const programs = [\n    { name: 'Beginner Programs', href: '/workouts?level=beginner', description: 'Perfect for fitness newcomers' },\n    { name: 'Intermediate Programs', href: '/workouts?level=intermediate', description: 'Step up your training' },\n    { name: 'Advanced Programs', href: '/workouts?level=advanced', description: 'Challenge yourself' },\n    { name: 'Weight Loss', href: '/workouts?goal=weight-loss', description: 'Burn calories effectively' },\n    { name: 'Muscle Building', href: '/workouts?goal=muscle-gain', description: 'Build lean muscle mass' },\n    { name: 'Endurance', href: '/workouts?goal=endurance', description: 'Improve stamina and endurance' },\n  ];\n\n  return (\n    <nav className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\", className)} aria-label=\"Workout programs\">\n      {programs.map((program) => (\n        <InternalLink\n          key={program.name}\n          href={program.href}\n          className=\"block p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all no-underline\"\n          title={program.description}\n        >\n          <div className=\"font-medium text-gray-900\">{program.name}</div>\n          <div className=\"text-sm text-gray-600 mt-1\">{program.description}</div>\n        </InternalLink>\n      ))}\n    </nav>\n  );\n}\n\n// Contextual navigation links\ninterface ContextualNavProps {\n  currentPage: string;\n  className?: string;\n}\n\nexport function ContextualNav({ currentPage, className }: ContextualNavProps) {\n  const getContextualLinks = (page: string) => {\n    switch (page) {\n      case 'exercises':\n        return [\n          { href: '/workouts', text: 'Browse Workout Programs', description: 'Find complete workout routines' },\n          { href: '/progress', text: 'Track Your Progress', description: 'Monitor your fitness journey' },\n          { href: '/dashboard', text: 'Your Dashboard', description: 'Personal fitness overview' },\n        ];\n      case 'workouts':\n        return [\n          { href: '/exercises', text: 'Exercise Database', description: 'Explore individual exercises' },\n          { href: '/progress', text: 'Track Your Progress', description: 'Monitor workout performance' },\n          { href: '/dashboard', text: 'Your Dashboard', description: 'Personal fitness overview' },\n        ];\n      case 'progress':\n        return [\n          { href: '/exercises', text: 'Exercise Database', description: 'Find new exercises to try' },\n          { href: '/workouts', text: 'Workout Programs', description: 'Discover new training routines' },\n          { href: '/dashboard', text: 'Your Dashboard', description: 'Complete fitness overview' },\n        ];\n      default:\n        return [\n          { href: '/exercises', text: 'Exercise Database', description: 'Comprehensive exercise library' },\n          { href: '/workouts', text: 'Workout Programs', description: 'AI-powered training plans' },\n          { href: '/progress', text: 'Progress Tracking', description: 'Monitor your improvements' },\n        ];\n    }\n  };\n\n  const links = getContextualLinks(currentPage);\n\n  return (\n    <nav className={cn(\"bg-blue-50 p-6 rounded-lg\", className)} aria-label=\"Related pages\">\n      <h3 className=\"text-lg font-semibold mb-4 text-blue-900\">Explore More</h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        {links.map((link) => (\n          <InternalLink\n            key={link.href}\n            href={link.href}\n            className=\"block p-3 bg-white rounded border hover:border-blue-300 hover:shadow-sm transition-all no-underline\"\n            title={link.description}\n          >\n            <div className=\"font-medium text-gray-900\">{link.text}</div>\n            <div className=\"text-sm text-gray-600 mt-1\">{link.description}</div>\n          </InternalLink>\n        ))}\n      </div>\n    </nav>\n  );\n}\n\n// Pagination with SEO-friendly links\ninterface PaginationProps {\n  currentPage: number;\n  totalPages: number;\n  baseUrl: string;\n  className?: string;\n}\n\nexport function SEOPagination({ currentPage, totalPages, baseUrl, className }: PaginationProps) {\n  const getPageUrl = (page: number) => {\n    return page === 1 ? baseUrl : `${baseUrl}?page=${page}`;\n  };\n\n  const renderPageLink = (page: number, isCurrent: boolean = false) => (\n    <InternalLink\n      key={page}\n      href={getPageUrl(page)}\n      className={cn(\n        \"px-3 py-2 rounded border\",\n        isCurrent \n          ? \"bg-blue-600 text-white border-blue-600\" \n          : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\",\n        \"no-underline\"\n      )}\n      title={`Go to page ${page}`}\n      rel={isCurrent ? undefined : \"nofollow\"}\n    >\n      {page}\n    </InternalLink>\n  );\n\n  return (\n    <nav className={cn(\"flex justify-center items-center space-x-2\", className)} aria-label=\"Pagination\">\n      {currentPage > 1 && (\n        <InternalLink\n          href={getPageUrl(currentPage - 1)}\n          className=\"px-3 py-2 rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50 no-underline\"\n          title=\"Go to previous page\"\n          rel=\"prev\"\n        >\n          Previous\n        </InternalLink>\n      )}\n      \n      {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {\n        const page = Math.max(1, currentPage - 2) + i;\n        if (page <= totalPages) {\n          return renderPageLink(page, page === currentPage);\n        }\n        return null;\n      })}\n      \n      {currentPage < totalPages && (\n        <InternalLink\n          href={getPageUrl(currentPage + 1)}\n          className=\"px-3 py-2 rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50 no-underline\"\n          title=\"Go to next page\"\n          rel=\"next\"\n        >\n          Next\n        </InternalLink>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAeO,SAAS,aAAa,EAC3B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,GAAG,EACH,WAAW,IAAI,EACf,UAAU,KAAK,EACf,SAAS,IAAI,EACb,UAAU,KAAK,EACG;IAClB,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC7D,OAAO;QACP,KAAK;QACL,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;kBAER;;;;;;AAGP;AAcO,SAAS,aAAa,EAAE,QAAQ,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAqB;IAC7F,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBACE,8OAAC;QAAM,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAAY,cAAW;;0BACvE,8OAAC;gBAAG,WAAU;0BAA8B;;;;;;0BAC5C,8OAAC;gBAAI,cAAW;0BACd,cAAA,8OAAC;oBAAG,WAAU;8BACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAM,KAAK,IAAI;gCACf,OAAO,KAAK,WAAW;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAe,KAAK,KAAK;;;;;;oCACvC,KAAK,WAAW,kBACf,8OAAC;wCAAI,WAAU;kDAA8B,KAAK,WAAW;;;;;;oCAE9D,KAAK,QAAQ,kBACZ,8OAAC;wCAAI,WAAU;kDAA8B,KAAK,QAAQ;;;;;;;;;;;;2BAXvD;;;;;;;;;;;;;;;;;;;;;AAoBrB;AAGO,SAAS,sBAAsB,EAAE,SAAS,EAA0B;IACzE,MAAM,aAAa;QACjB;YAAE,MAAM;YAAqB,MAAM;YAAgC,aAAa;QAAqC;QACrH;YAAE,MAAM;YAAU,MAAM;YAA8B,aAAa;QAAgC;QACnG;YAAE,MAAM;YAAe,MAAM;YAAmC,aAAa;QAAmC;QAChH;YAAE,MAAM;YAAc,MAAM;YAAkC,aAAa;QAAsB;QACjG;YAAE,MAAM;YAAmB,MAAM;YAA+B,aAAa;QAAyB;QACtG;YAAE,MAAM;YAAc,MAAM;YAAkC,aAAa;QAA+B;KAC3G;IAED,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QAAY,cAAW;kBAChF,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gBAEC,MAAM,SAAS,IAAI;gBACnB,WAAU;gBACV,OAAO,SAAS,WAAW;;kCAE3B,8OAAC;wBAAI,WAAU;kCAA6B,SAAS,IAAI;;;;;;kCACzD,8OAAC;wBAAI,WAAU;kCAA8B,SAAS,WAAW;;;;;;;eAN5D,SAAS,IAAI;;;;;;;;;;AAW5B;AAGO,SAAS,oBAAoB,EAAE,SAAS,EAA0B;IACvE,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,MAAM;YAA4B,aAAa;QAAgC;QAC5G;YAAE,MAAM;YAAyB,MAAM;YAAgC,aAAa;QAAwB;QAC5G;YAAE,MAAM;YAAqB,MAAM;YAA4B,aAAa;QAAqB;QACjG;YAAE,MAAM;YAAe,MAAM;YAA8B,aAAa;QAA4B;QACpG;YAAE,MAAM;YAAmB,MAAM;YAA8B,aAAa;QAAyB;QACrG;YAAE,MAAM;YAAa,MAAM;YAA4B,aAAa;QAAgC;KACrG;IAED,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QAAY,cAAW;kBAC/F,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gBAEC,MAAM,QAAQ,IAAI;gBAClB,WAAU;gBACV,OAAO,QAAQ,WAAW;;kCAE1B,8OAAC;wBAAI,WAAU;kCAA6B,QAAQ,IAAI;;;;;;kCACxD,8OAAC;wBAAI,WAAU;kCAA8B,QAAQ,WAAW;;;;;;;eAN3D,QAAQ,IAAI;;;;;;;;;;AAW3B;AAQO,SAAS,cAAc,EAAE,WAAW,EAAE,SAAS,EAAsB;IAC1E,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL;wBAAE,MAAM;wBAAa,MAAM;wBAA2B,aAAa;oBAAiC;oBACpG;wBAAE,MAAM;wBAAa,MAAM;wBAAuB,aAAa;oBAA+B;oBAC9F;wBAAE,MAAM;wBAAc,MAAM;wBAAkB,aAAa;oBAA4B;iBACxF;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,MAAM;wBAAc,MAAM;wBAAqB,aAAa;oBAA+B;oBAC7F;wBAAE,MAAM;wBAAa,MAAM;wBAAuB,aAAa;oBAA8B;oBAC7F;wBAAE,MAAM;wBAAc,MAAM;wBAAkB,aAAa;oBAA4B;iBACxF;YACH,KAAK;gBACH,OAAO;oBACL;wBAAE,MAAM;wBAAc,MAAM;wBAAqB,aAAa;oBAA4B;oBAC1F;wBAAE,MAAM;wBAAa,MAAM;wBAAoB,aAAa;oBAAiC;oBAC7F;wBAAE,MAAM;wBAAc,MAAM;wBAAkB,aAAa;oBAA4B;iBACxF;YACH;gBACE,OAAO;oBACL;wBAAE,MAAM;wBAAc,MAAM;wBAAqB,aAAa;oBAAiC;oBAC/F;wBAAE,MAAM;wBAAa,MAAM;wBAAoB,aAAa;oBAA4B;oBACxF;wBAAE,MAAM;wBAAa,MAAM;wBAAqB,aAAa;oBAA4B;iBAC1F;QACL;IACF;IAEA,MAAM,QAAQ,mBAAmB;IAEjC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAAY,cAAW;;0BACrE,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAEC,MAAM,KAAK,IAAI;wBACf,WAAU;wBACV,OAAO,KAAK,WAAW;;0CAEvB,8OAAC;gCAAI,WAAU;0CAA6B,KAAK,IAAI;;;;;;0CACrD,8OAAC;gCAAI,WAAU;0CAA8B,KAAK,WAAW;;;;;;;uBANxD,KAAK,IAAI;;;;;;;;;;;;;;;;AAY1B;AAUO,SAAS,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAmB;IAC5F,MAAM,aAAa,CAAC;QAClB,OAAO,SAAS,IAAI,UAAU,GAAG,QAAQ,MAAM,EAAE,MAAM;IACzD;IAEA,MAAM,iBAAiB,CAAC,MAAc,YAAqB,KAAK,iBAC9D,8OAAC;YAEC,MAAM,WAAW;YACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA,YACI,2CACA,2DACJ;YAEF,OAAO,CAAC,WAAW,EAAE,MAAM;YAC3B,KAAK,YAAY,YAAY;sBAE5B;WAZI;;;;;IAgBT,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAAY,cAAW;;YACrF,cAAc,mBACb,8OAAC;gBACC,MAAM,WAAW,cAAc;gBAC/B,WAAU;gBACV,OAAM;gBACN,KAAI;0BACL;;;;;;YAKF,MAAM,IAAI,CAAC;gBAAE,QAAQ,KAAK,GAAG,CAAC,YAAY;YAAG,GAAG,CAAC,GAAG;gBACnD,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK;gBAC5C,IAAI,QAAQ,YAAY;oBACtB,OAAO,eAAe,MAAM,SAAS;gBACvC;gBACA,OAAO;YACT;YAEC,cAAc,4BACb,8OAAC;gBACC,MAAM,WAAW,cAAc;gBAC/B,WAAU;gBACV,OAAM;gBACN,KAAI;0BACL;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/not-found.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Navigation } from '@/components/Navigation';\nimport { Main, Section, SemanticHeader } from '@/components/seo/semantic-html';\nimport { ExerciseCategoryLinks, ContextualNav } from '@/components/seo/internal-links';\nimport { Home, Search, ArrowLeft } from 'lucide-react';\n\nexport const metadata: Metadata = {\n  title: 'Page Not Found - AI-fitness-singles',\n  description: 'The page you are looking for could not be found. Explore our exercise database, workout programs, and fitness tracking tools.',\n  robots: {\n    index: false,\n    follow: true,\n  },\n};\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n      <Main>\n        {/* 404 Hero Section */}\n        <Section className=\"py-20 bg-gradient-to-br from-blue-50 to-indigo-100\" ariaLabel=\"404 error section\">\n          <div className=\"container mx-auto px-4 text-center\">\n            <div className=\"max-w-2xl mx-auto\">\n              <div className=\"text-6xl font-bold text-blue-600 mb-4\">404</div>\n              <SemanticHeader level={1} className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n                Page Not Found\n              </SemanticHeader>\n              <p className=\"text-lg text-gray-600 mb-8\">\n                Oops! The page you're looking for doesn't exist. But don't worry, \n                there's plenty of great fitness content to explore.\n              </p>\n              \n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n                <Button asChild size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700\">\n                  <Link href=\"/\">\n                    <Home className=\"mr-2 h-5 w-5\" />\n                    Go Home\n                  </Link>\n                </Button>\n                <Button asChild variant=\"outline\" size=\"lg\">\n                  <Link href=\"/exercises\">\n                    <Search className=\"mr-2 h-5 w-5\" />\n                    Browse Exercises\n                  </Link>\n                </Button>\n              </div>\n            </div>\n          </div>\n        </Section>\n\n        {/* Helpful Links Section */}\n        <Section className=\"py-16 bg-white\" ariaLabel=\"Helpful links\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"text-center mb-12\">\n              <SemanticHeader level={2} className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Popular Fitness Categories\n              </SemanticHeader>\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                Explore our most popular exercise categories and find the perfect workout for your goals.\n              </p>\n            </div>\n            <ExerciseCategoryLinks />\n          </div>\n        </Section>\n\n        {/* Quick Navigation */}\n        <Section className=\"py-16 bg-gray-50\" ariaLabel=\"Quick navigation\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"text-center mb-12\">\n              <SemanticHeader level={2} className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Quick Navigation\n              </SemanticHeader>\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                Find what you're looking for with these helpful links to our main sections.\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto\">\n              <Link \n                href=\"/\"\n                className=\"block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors\">\n                    <Home className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">Homepage</h3>\n                  <p className=\"text-sm text-gray-600\">Start your fitness journey</p>\n                </div>\n              </Link>\n\n              <Link \n                href=\"/exercises\"\n                className=\"block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors\">\n                    <Search className=\"h-6 w-6 text-green-600\" />\n                  </div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">Exercise Database</h3>\n                  <p className=\"text-sm text-gray-600\">Comprehensive exercise library</p>\n                </div>\n              </Link>\n\n              <Link \n                href=\"/workouts\"\n                className=\"block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors\">\n                    <ArrowLeft className=\"h-6 w-6 text-purple-600\" />\n                  </div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">Workout Programs</h3>\n                  <p className=\"text-sm text-gray-600\">AI-powered training plans</p>\n                </div>\n              </Link>\n\n              <Link \n                href=\"/progress\"\n                className=\"block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors\">\n                    <ArrowLeft className=\"h-6 w-6 text-orange-600\" />\n                  </div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">Progress Tracking</h3>\n                  <p className=\"text-sm text-gray-600\">Monitor your improvements</p>\n                </div>\n              </Link>\n            </div>\n          </div>\n        </Section>\n\n        {/* Search Suggestions */}\n        <Section className=\"py-16 bg-white\" ariaLabel=\"Search suggestions\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"max-w-2xl mx-auto text-center\">\n              <SemanticHeader level={2} className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Looking for Something Specific?\n              </SemanticHeader>\n              <p className=\"text-gray-600 mb-8\">\n                Try searching for exercises, workout types, or muscle groups to find exactly what you need.\n              </p>\n              \n              <div className=\"flex flex-wrap gap-2 justify-center\">\n                {[\n                  'Push-ups', 'Squats', 'Deadlifts', 'Cardio', 'Strength Training',\n                  'Beginner Workouts', 'Weight Loss', 'Muscle Building', 'Flexibility'\n                ].map((term) => (\n                  <Link\n                    key={term}\n                    href={`/exercises?search=${encodeURIComponent(term.toLowerCase())}`}\n                    className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-blue-100 hover:text-blue-700 transition-colors\"\n                  >\n                    {term}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n        </Section>\n\n        {/* Contextual Navigation */}\n        <Section className=\"py-16 bg-gray-50\" ariaLabel=\"Related pages\">\n          <div className=\"container mx-auto px-4\">\n            <ContextualNav currentPage=\"404\" />\n          </div>\n        </Section>\n      </Main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC,6IAAA,CAAA,OAAI;;kCAEH,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAqD,WAAU;kCAChF,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC,6IAAA,CAAA,iBAAc;wCAAC,OAAO;wCAAG,WAAU;kDAAoD;;;;;;kDAGxF,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAK1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,MAAK;gDAAK,WAAU;0DAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,mMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIrC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,MAAK;0DACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6IAAA,CAAA,iBAAc;4CAAC,OAAO;4CAAG,WAAU;sDAAwC;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAIjD,8OAAC,8IAAA,CAAA,wBAAqB;;;;;;;;;;;;;;;;kCAK1B,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAmB,WAAU;kCAC9C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6IAAA,CAAA,iBAAc;4CAAC,OAAO;4CAAG,WAAU;sDAAwC;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAKjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,mMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;sDAIzC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;sDAIzC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;sDAIzC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6IAAA,CAAA,iBAAc;wCAAC,OAAO;wCAAG,WAAU;kDAAwC;;;;;;kDAG5E,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAIlC,8OAAC;wCAAI,WAAU;kDACZ;4CACC;4CAAY;4CAAU;4CAAa;4CAAU;4CAC7C;4CAAqB;4CAAe;4CAAmB;yCACxD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,KAAK,WAAW,KAAK;gDACnE,WAAU;0DAET;+CAJI;;;;;;;;;;;;;;;;;;;;;;;;;;kCAajB,8OAAC,6IAAA,CAAA,UAAO;wBAAC,WAAU;wBAAmB,WAAU;kCAC9C,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8IAAA,CAAA,gBAAa;gCAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}]}