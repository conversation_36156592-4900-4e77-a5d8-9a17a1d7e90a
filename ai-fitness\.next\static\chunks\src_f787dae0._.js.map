{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds\n      .toString()\n      .padStart(2, \"0\")}`\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`\n}\n\nexport function calculateBMI(weight: number, height: number): number {\n  // height in cm, weight in kg\n  const heightInMeters = height / 100\n  return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))\n}\n\nexport function getBMICategory(bmi: number): string {\n  if (bmi < 18.5) return \"Underweight\"\n  if (bmi < 25) return \"Normal weight\"\n  if (bmi < 30) return \"Overweight\"\n  return \"Obese\"\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBACvD,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM;IACvB;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAa,MAAc,EAAE,MAAc;IACzD,6BAA6B;IAC7B,MAAM,iBAAiB,SAAS;IAChC,OAAO,OAAO,CAAC,SAAS,CAAC,iBAAiB,cAAc,CAAC,EAAE,OAAO,CAAC;AACrE;AAEO,SAAS,eAAe,GAAW;IACxC,IAAI,MAAM,MAAM,OAAO;IACvB,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar<PERSON>hart3, User, LogOut, Wifi, Wif<PERSON>Off, <PERSON> } from \"lucide-react\"\nimport { useAuth, useSignOut } from \"@/lib/hooks/use-auth\"\nimport { \n  useOfflineState, \n  useUnreadNotificationCount, \n  useHasPendingSync \n} from \"@/lib/store/app-store\"\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const signOutMutation = useSignOut()\n  \n  // State management hooks\n  const offlineState = useOfflineState()\n  const unreadCount = useUnreadNotificationCount()\n  const hasPendingSync = useHasPendingSync()\n\n  const toggleMenu = () => setIsOpen(!isOpen)\n\n  const handleSignOut = async () => {\n    try {\n      await signOutMutation.mutateAsync()\n    } catch (error) {\n      console.error('Sign out failed:', error)\n    }\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Dumbbell className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">AI-fitness-singles</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/workouts\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Workouts\n            </Link>\n            <Link\n              href=\"/exercises\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Exercises\n            </Link>\n            <Link\n              href=\"/progress\"\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Progress\n            </Link>\n\n            {/* Status Indicators */}\n            <div className=\"flex items-center space-x-4\">\n              {/* Online/Offline Status */}\n              <div className=\"flex items-center space-x-1\">\n                {offlineState.isOnline ? (\n                  <Wifi className=\"h-4 w-4 text-green-600\" />\n                ) : (\n                  <WifiOff className=\"h-4 w-4 text-red-600\" />\n                )}\n                {hasPendingSync && (\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {offlineState.pendingSync.length}\n                  </Badge>\n                )}\n              </div>\n\n              {/* Notifications */}\n              <div className=\"relative\">\n                <Bell className=\"h-4 w-4 text-gray-600\" />\n                {unreadCount > 0 && (\n                  <Badge \n                    variant=\"destructive\" \n                    className=\"absolute -top-2 -right-2 h-4 w-4 p-0 text-xs flex items-center justify-center\"\n                  >\n                    {unreadCount > 9 ? '9+' : unreadCount}\n                  </Badge>\n                )}\n              </div>\n            </div>\n\n            {/* Auth Section */}\n            {isLoading ? (\n              <div className=\"w-8 h-8 bg-gray-200 rounded-full animate-pulse\" />\n            ) : isAuthenticated && user ? (\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-600\">\n                  Welcome, {user.name || user.email}\n                </span>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleSignOut}\n                  disabled={signOutMutation.isPending}\n                  className=\"flex items-center space-x-1\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Sign Out</span>\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button variant=\"ghost\" size=\"sm\" asChild>\n                  <Link href=\"/auth/signin\">Sign In</Link>\n                </Button>\n                <Button size=\"sm\" asChild>\n                  <Link href=\"/auth/signup\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleMenu}\n              className=\"p-2\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t\">\n              {/* Status Bar for Mobile */}\n              <div className=\"flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg mb-3\">\n                <div className=\"flex items-center space-x-2\">\n                  {offlineState.isOnline ? (\n                    <Wifi className=\"h-4 w-4 text-green-600\" />\n                  ) : (\n                    <WifiOff className=\"h-4 w-4 text-red-600\" />\n                  )}\n                  <span className=\"text-sm text-gray-600\">\n                    {offlineState.isOnline ? 'Online' : 'Offline'}\n                  </span>\n                  {hasPendingSync && (\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      {offlineState.pendingSync.length} pending\n                    </Badge>\n                  )}\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Bell className=\"h-4 w-4 text-gray-600\" />\n                  {unreadCount > 0 && (\n                    <Badge variant=\"destructive\" className=\"text-xs\">\n                      {unreadCount}\n                    </Badge>\n                  )}\n                </div>\n              </div>\n\n              {/* Navigation Links */}\n              <Link\n                href=\"/workouts\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                onClick={() => setIsOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <Dumbbell className=\"h-5 w-5\" />\n                  <span>Workouts</span>\n                </div>\n              </Link>\n              <Link\n                href=\"/exercises\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                onClick={() => setIsOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <BookOpen className=\"h-5 w-5\" />\n                  <span>Exercises</span>\n                </div>\n              </Link>\n              <Link\n                href=\"/progress\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                onClick={() => setIsOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  <BarChart3 className=\"h-5 w-5\" />\n                  <span>Progress</span>\n                </div>\n              </Link>\n\n              {/* Auth Section for Mobile */}\n              <div className=\"pt-4 border-t\">\n                {isLoading ? (\n                  <div className=\"px-3 py-2\">\n                    <div className=\"w-full h-8 bg-gray-200 rounded animate-pulse\" />\n                  </div>\n                ) : isAuthenticated && user ? (\n                  <div className=\"space-y-2\">\n                    <div className=\"px-3 py-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <User className=\"h-5 w-5 text-gray-400\" />\n                        <span className=\"text-sm text-gray-600\">\n                          {user.name || user.email}\n                        </span>\n                      </div>\n                    </div>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleSignOut}\n                      disabled={signOutMutation.isPending}\n                      className=\"w-full justify-start px-3\"\n                    >\n                      <LogOut className=\"h-4 w-4 mr-2\" />\n                      Sign Out\n                    </Button>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2 px-3\">\n                    <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                      <Link href=\"/auth/signin\" onClick={() => setIsOpen(false)}>\n                        Sign In\n                      </Link>\n                    </Button>\n                    <Button size=\"sm\" className=\"w-full\" asChild>\n                      <Link href=\"/auth/signup\" onClick={() => setIsOpen(false)}>\n                        Sign Up\n                      </Link>\n                    </Button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAcO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEjC,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD;IAC7C,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAEvC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,gBAAgB,WAAW;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAKD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;gDACZ,aAAa,QAAQ,iBACpB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAEhB,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAEpB,gCACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,aAAa,WAAW,CAAC,MAAM;;;;;;;;;;;;sDAMtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DAET,cAAc,IAAI,OAAO;;;;;;;;;;;;;;;;;;gCAOjC,0BACC,6LAAC;oCAAI,WAAU;;;;;2CACb,mBAAmB,qBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAwB;gDAC5B,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;;sDAEnC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB,SAAS;4CACnC,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;yDAIV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,OAAO;sDACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;sDAE5B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAOlC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,QAAQ,iBACpB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DAErB,6LAAC;gDAAK,WAAU;0DACb,aAAa,QAAQ,GAAG,WAAW;;;;;;4CAErC,gCACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC,aAAa,WAAW,CAAC,MAAM;oDAAC;;;;;;;;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;0DACpC;;;;;;;;;;;;;;;;;;0CAOT,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;0CACZ,0BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;2CAEf,mBAAmB,qBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;;;;;;;;;;;;sDAI9B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB,SAAS;4CACnC,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;yDAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;4CAAS,OAAO;sDAC1D,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,SAAS,IAAM,UAAU;0DAAQ;;;;;;;;;;;sDAI7D,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;4CAAS,OAAO;sDAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,SAAS,IAAM,UAAU;0DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajF;GA5OgB;;QAE+B,qIAAA,CAAA,UAAO;QAC5B,qIAAA,CAAA,aAAU;QAGb,sIAAA,CAAA,kBAAe;QAChB,sIAAA,CAAA,6BAA0B;QACvB,sIAAA,CAAA,oBAAiB;;;KAR1B", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nexport function LoadingSpinner({ size = \"md\", className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-8 w-8\", \n    lg: \"h-12 w-12\"\n  }\n\n  return (\n    <div className={cn(\"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\", sizeClasses[size], className)} />\n  )\n}\n\ninterface LoadingCardProps {\n  className?: string\n}\n\nexport function LoadingCard({ className }: LoadingCardProps) {\n  return (\n    <div className={cn(\"rounded-xl border bg-card text-card-foreground shadow animate-pulse\", className)}>\n      <div className=\"p-6\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-4\"></div>\n        <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-2\"></div>\n        <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n      </div>\n    </div>\n  )\n}\n\ninterface LoadingPageProps {\n  title?: string\n  description?: string\n}\n\nexport function LoadingPage({ title = \"Loading...\", description = \"Please wait while we load your content\" }: LoadingPageProps) {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <div className=\"h-12 bg-gray-200 rounded w-1/3 mx-auto mb-4 animate-pulse\"></div>\n            <div className=\"h-6 bg-gray-200 rounded w-1/2 mx-auto animate-pulse\"></div>\n          </div>\n        </div>\n      </section>\n      \n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\n          <LoadingSpinner size=\"lg\" />\n          <h2 className=\"text-xl font-semibold text-gray-900\">{title}</h2>\n          <p className=\"text-gray-600 text-center max-w-md\">{description}</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface LoadingSectionProps {\n  rows?: number\n  className?: string\n}\n\nexport function LoadingSection({ rows = 3, className }: LoadingSectionProps) {\n  return (\n    <div className={cn(\"space-y-4\", className)}>\n      {Array.from({ length: rows }).map((_, i) => (\n        <div key={i} className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-full mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\ninterface LoadingGridProps {\n  items?: number\n  className?: string\n}\n\nexport function LoadingGrid({ items = 6, className }: LoadingGridProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", className)}>\n      {Array.from({ length: items }).map((_, i) => (\n        <LoadingCard key={i} />\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wEAAwE,WAAW,CAAC,KAAK,EAAE;;;;;;AAElH;KAVgB;AAgBT,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uEAAuE;kBACxF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;MAVgB;AAiBT,SAAS,YAAY,EAAE,QAAQ,YAAY,EAAE,cAAc,wCAAwC,EAAoB;IAC5H,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAe,MAAK;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;;;;;;;;AAK7D;MArBgB;AA4BT,SAAS,eAAe,EAAE,OAAO,CAAC,EAAE,SAAS,EAAuB;IACzE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;gBAAY,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;eAFP;;;;;;;;;;AAOlB;MAXgB;AAkBT,SAAS,YAAY,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAoB;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;kBACxE,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,6LAAC,iBAAiB;;;;;;;;;;AAI1B;MARgB", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/error.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Home, ArrowLeft } from \"lucide-react\"\nimport { Button } from \"./button\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ErrorMessageProps {\n  title?: string\n  message?: string\n  onRetry?: () => void\n  className?: string\n}\n\nexport function ErrorMessage({ \n  title = \"Something went wrong\", \n  message = \"An unexpected error occurred. Please try again.\",\n  onRetry,\n  className \n}: ErrorMessageProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center p-8 text-center\", className)}>\n      <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n        <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">{message}</p>\n      {onRetry && (\n        <Button onClick={onRetry} className=\"flex items-center gap-2\">\n          <RefreshCw className=\"h-4 w-4\" />\n          Try Again\n        </Button>\n      )}\n    </div>\n  )\n}\n\ninterface ErrorPageProps {\n  title?: string\n  message?: string\n  onRetry?: () => void\n  showHomeButton?: boolean\n}\n\nexport function ErrorPage({ \n  title = \"Oops! Something went wrong\", \n  message = \"We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.\",\n  onRetry,\n  showHomeButton = true\n}: ErrorPageProps) {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <section className=\"bg-gradient-to-br from-red-50 to-orange-100 py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\">Error</h1>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">Something unexpected happened</p>\n          </div>\n        </div>\n      </section>\n      \n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex flex-col items-center justify-center min-h-[400px]\">\n          <div className=\"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-6\">\n            <AlertTriangle className=\"h-12 w-12 text-red-600\" />\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">{title}</h2>\n          <p className=\"text-gray-600 text-center max-w-2xl mb-8\">{message}</p>\n          \n          <div className=\"flex gap-4\">\n            {onRetry && (\n              <Button onClick={onRetry} className=\"flex items-center gap-2\">\n                <RefreshCw className=\"h-4 w-4\" />\n                Try Again\n              </Button>\n            )}\n            {showHomeButton && (\n              <Button variant=\"outline\" onClick={() => window.location.href = '/'} className=\"flex items-center gap-2\">\n                <Home className=\"h-4 w-4\" />\n                Go Home\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ErrorBoundaryFallbackProps {\n  error: Error\n  resetError: () => void\n}\n\nexport function ErrorBoundaryFallback({ error, resetError }: ErrorBoundaryFallbackProps) {\n  return (\n    <div className=\"min-h-screen bg-white flex items-center justify-center\">\n      <div className=\"text-center p-8\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <AlertTriangle className=\"h-10 w-10 text-red-600\" />\n        </div>\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Application Error</h1>\n        <p className=\"text-gray-600 mb-6 max-w-md\">\n          The application encountered an unexpected error. This has been logged and our team has been notified.\n        </p>\n        <details className=\"text-left mb-6 p-4 bg-gray-50 rounded-lg\">\n          <summary className=\"cursor-pointer font-medium text-gray-700 mb-2\">Error Details</summary>\n          <pre className=\"text-sm text-red-600 whitespace-pre-wrap\">{error.message}</pre>\n        </details>\n        <div className=\"flex gap-4 justify-center\">\n          <Button onClick={resetError} className=\"flex items-center gap-2\">\n            <RefreshCw className=\"h-4 w-4\" />\n            Try Again\n          </Button>\n          <Button variant=\"outline\" onClick={() => window.location.href = '/'} className=\"flex items-center gap-2\">\n            <Home className=\"h-4 w-4\" />\n            Go Home\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface NetworkErrorProps {\n  onRetry?: () => void\n  className?: string\n}\n\nexport function NetworkError({ onRetry, className }: NetworkErrorProps) {\n  return (\n    <ErrorMessage\n      title=\"Connection Error\"\n      message=\"Unable to connect to the server. Please check your internet connection and try again.\"\n      onRetry={onRetry}\n      className={className}\n    />\n  )\n}\n\ninterface NotFoundErrorProps {\n  resource?: string\n  onGoBack?: () => void\n  className?: string\n}\n\nexport function NotFoundError({ resource = \"page\", onGoBack, className }: NotFoundErrorProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center p-8 text-center\", className)}>\n      <div className=\"text-6xl font-bold text-gray-300 mb-4\">404</div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n        {resource.charAt(0).toUpperCase() + resource.slice(1)} Not Found\n      </h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">\n        The {resource} you're looking for doesn't exist or has been moved.\n      </p>\n      <div className=\"flex gap-4\">\n        {onGoBack && (\n          <Button variant=\"outline\" onClick={onGoBack} className=\"flex items-center gap-2\">\n            <ArrowLeft className=\"h-4 w-4\" />\n            Go Back\n          </Button>\n        )}\n        <Button onClick={() => window.location.href = '/'} className=\"flex items-center gap-2\">\n          <Home className=\"h-4 w-4\" />\n          Go Home\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AASO,SAAS,aAAa,EAC3B,QAAQ,sBAAsB,EAC9B,UAAU,iDAAiD,EAC3D,OAAO,EACP,SAAS,EACS;IAClB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;;0BAC9E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;0BAE3B,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAA+B;;;;;;YAC3C,yBACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS;gBAAS,WAAU;;kCAClC,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAY;;;;;;;;;;;;;AAM3C;KArBgB;AA8BT,SAAS,UAAU,EACxB,QAAQ,4BAA4B,EACpC,UAAU,gHAAgH,EAC1H,OAAO,EACP,iBAAiB,IAAI,EACN;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAEzD,6LAAC;4BAAI,WAAU;;gCACZ,yBACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAS,WAAU;;sDAClC,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;gCAIpC,gCACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCAAK,WAAU;;sDAC7E,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;MA3CgB;AAkDT,SAAS,sBAAsB,EAAE,KAAK,EAAE,UAAU,EAA8B;IACrF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;8BAE3B,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;8BAG3C,6LAAC;oBAAQ,WAAU;;sCACjB,6LAAC;4BAAQ,WAAU;sCAAgD;;;;;;sCACnE,6LAAC;4BAAI,WAAU;sCAA4C,MAAM,OAAO;;;;;;;;;;;;8BAE1E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,WAAU;;8CACrC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGnC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BAAK,WAAU;;8CAC7E,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;MA5BgB;AAmCT,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;IACpE,qBACE,6LAAC;QACC,OAAM;QACN,SAAQ;QACR,SAAS;QACT,WAAW;;;;;;AAGjB;MATgB;AAiBT,SAAS,cAAc,EAAE,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAsB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;;0BAC9E,6LAAC;gBAAI,WAAU;0BAAwC;;;;;;0BACvD,6LAAC;gBAAG,WAAU;;oBACX,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;oBAAG;;;;;;;0BAExD,6LAAC;gBAAE,WAAU;;oBAA8B;oBACpC;oBAAS;;;;;;;0BAEhB,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAU,WAAU;;0CACrD,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIrC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wBAAK,WAAU;;0CAC3D,6LAAC,sMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAMtC;MAxBgB", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/ui/empty-state.tsx"], "sourcesContent": ["import { Plus, Search, Target, Activity, Dumbbell, Calendar, TrendingUp } from \"lucide-react\"\nimport { Button } from \"./button\"\nimport { cn } from \"@/lib/utils\"\n\ninterface EmptyStateProps {\n  icon?: React.ReactNode\n  title: string\n  description: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n  className?: string\n}\n\nexport function EmptyState({ icon, title, description, action, className }: EmptyStateProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center p-12 text-center\", className)}>\n      <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6\">\n        {icon || <Search className=\"h-8 w-8 text-gray-400\" />}\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">{description}</p>\n      {action && (\n        <Button onClick={action.onClick} className=\"flex items-center gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          {action.label}\n        </Button>\n      )}\n    </div>\n  )\n}\n\nexport function EmptyWorkouts({ onCreateWorkout }: { onCreateWorkout?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Dumbbell className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No workouts yet\"\n      description=\"Start your fitness journey by creating your first workout plan. Choose from our templates or build your own custom routine.\"\n      action={onCreateWorkout ? {\n        label: \"Create First Workout\",\n        onClick: onCreateWorkout\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyExercises({ onAddExercise }: { onAddExercise?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Activity className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No exercises found\"\n      description=\"We couldn't find any exercises matching your criteria. Try adjusting your filters or browse our complete exercise database.\"\n      action={onAddExercise ? {\n        label: \"Browse All Exercises\",\n        onClick: onAddExercise\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyProgress({ onStartWorkout }: { onStartWorkout?: () => void }) {\n  return (\n    <EmptyState\n      icon={<TrendingUp className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No progress data yet\"\n      description=\"Complete your first workout to start tracking your fitness progress. We'll show you detailed analytics and insights as you build your routine.\"\n      action={onStartWorkout ? {\n        label: \"Start First Workout\",\n        onClick: onStartWorkout\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyGoals({ onCreateGoal }: { onCreateGoal?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Target className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No goals set\"\n      description=\"Set your first fitness goal to stay motivated and track your progress. Choose from weekly workouts, monthly targets, or create custom goals.\"\n      action={onCreateGoal ? {\n        label: \"Set First Goal\",\n        onClick: onCreateGoal\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptyHistory({ onStartWorkout }: { onStartWorkout?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Calendar className=\"h-8 w-8 text-gray-400\" />}\n      title=\"No workout history\"\n      description=\"Your workout history will appear here once you complete your first session. Start working out to build your fitness timeline.\"\n      action={onStartWorkout ? {\n        label: \"Start Working Out\",\n        onClick: onStartWorkout\n      } : undefined}\n    />\n  )\n}\n\nexport function EmptySearchResults({ searchTerm, onClearSearch }: { searchTerm: string, onClearSearch?: () => void }) {\n  return (\n    <EmptyState\n      icon={<Search className=\"h-8 w-8 text-gray-400\" />}\n      title={`No results for \"${searchTerm}\"`}\n      description=\"We couldn't find anything matching your search. Try different keywords or browse our categories to discover new content.\"\n      action={onClearSearch ? {\n        label: \"Clear Search\",\n        onClick: onClearSearch\n      } : undefined}\n    />\n  )\n}\n\ninterface EmptyCardProps {\n  title: string\n  description: string\n  className?: string\n}\n\nexport function EmptyCard({ title, description, className }: EmptyCardProps) {\n  return (\n    <div className={cn(\"rounded-xl border bg-card text-card-foreground shadow\", className)}>\n      <div className=\"p-6\">\n        <div className=\"flex flex-col items-center text-center py-8\">\n          <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n            <Search className=\"h-6 w-6 text-gray-400\" />\n          </div>\n          <h4 className=\"font-medium text-gray-900 mb-2\">{title}</h4>\n          <p className=\"text-sm text-gray-600\">{description}</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface EmptyListProps {\n  icon?: React.ReactNode\n  title: string\n  description: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n  className?: string\n}\n\nexport function EmptyList({ icon, title, description, action, className }: EmptyListProps) {\n  return (\n    <div className={cn(\"border-2 border-dashed border-gray-200 rounded-lg p-8\", className)}>\n      <div className=\"text-center\">\n        <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          {icon || <Plus className=\"h-6 w-6 text-gray-400\" />}\n        </div>\n        <h3 className=\"text-sm font-medium text-gray-900 mb-2\">{title}</h3>\n        <p className=\"text-sm text-gray-600 mb-4\">{description}</p>\n        {action && (\n          <Button variant=\"outline\" size=\"sm\" onClick={action.onClick}>\n            {action.label}\n          </Button>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAaO,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAmB;IACzF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;;0BAC/E,6LAAC;gBAAI,WAAU;0BACZ,sBAAQ,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAE7B,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAA+B;;;;;;YAC3C,wBACC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS,OAAO,OAAO;gBAAE,WAAU;;kCACzC,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,OAAO,KAAK;;;;;;;;;;;;;AAKvB;KAhBgB;AAkBT,SAAS,cAAc,EAAE,eAAe,EAAoC;IACjF,qBACE,6LAAC;QACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,aAAY;QACZ,QAAQ,kBAAkB;YACxB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,eAAe,EAAE,aAAa,EAAkC;IAC9E,qBACE,6LAAC;QACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,aAAY;QACZ,QAAQ,gBAAgB;YACtB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,cAAc,EAAE,cAAc,EAAmC;IAC/E,qBACE,6LAAC;QACC,oBAAM,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,OAAM;QACN,aAAY;QACZ,QAAQ,iBAAiB;YACvB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,WAAW,EAAE,YAAY,EAAiC;IACxE,qBACE,6LAAC;QACC,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,OAAM;QACN,aAAY;QACZ,QAAQ,eAAe;YACrB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,aAAa,EAAE,cAAc,EAAmC;IAC9E,qBACE,6LAAC;QACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAM;QACN,aAAY;QACZ,QAAQ,iBAAiB;YACvB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAcT,SAAS,mBAAmB,EAAE,UAAU,EAAE,aAAa,EAAsD;IAClH,qBACE,6LAAC;QACC,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;QACvC,aAAY;QACZ,QAAQ,gBAAgB;YACtB,OAAO;YACP,SAAS;QACX,IAAI;;;;;;AAGV;MAZgB;AAoBT,SAAS,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAkB;IACzE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;kBAC1E,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;;;;;AAKhD;MAdgB;AA2BT,SAAS,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAkB;IACvF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;kBAC1E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,sBAAQ,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;8BAE3B,6LAAC;oBAAG,WAAU;8BAA0C;;;;;;8BACxD,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;gBAC1C,wBACC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS,OAAO,OAAO;8BACxD,OAAO,KAAK;;;;;;;;;;;;;;;;;AAMzB;MAjBgB", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/exercises.ts"], "sourcesContent": ["/**\n * Exercise API Service\n * Handles all exercise-related API calls\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport type { Exercise, ExerciseSearchParams, PaginatedResponse } from '../types';\n\nexport class ExerciseService {\n  /**\n   * Get all exercises with optional filtering\n   */\n  static async getExercises(params: ExerciseSearchParams = {}): Promise<PaginatedResponse<Exercise>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.search) {\n      searchParams.append('search', params.search);\n    }\n    \n    if (params.equipment?.length) {\n      params.equipment.forEach(eq => searchParams.append('equipment', eq));\n    }\n    \n    if (params.muscles?.length) {\n      params.muscles.forEach(muscle => searchParams.append('muscles', muscle));\n    }\n    \n    if (params.difficulty?.length) {\n      params.difficulty.forEach(diff => searchParams.append('difficulty', diff));\n    }\n    \n    if (params.category?.length) {\n      params.category.forEach(cat => searchParams.append('category', cat));\n    }\n    \n    if (params.limit) {\n      searchParams.append('limit', params.limit.toString());\n    }\n    \n    if (params.offset) {\n      searchParams.append('offset', params.offset.toString());\n    }\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.EXERCISES.LIST;\n\n    return apiClient.get<PaginatedResponse<Exercise>>(url);\n  }\n\n  /**\n   * Search exercises by name or description\n   */\n  static async searchExercises(query: string, limit = 20): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      q: query,\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.SEARCH}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<{ exercises: Exercise[] }>(url);\n    return response.exercises || [];\n  }\n\n  /**\n   * Get exercise details by ID\n   */\n  static async getExerciseById(id: string): Promise<Exercise> {\n    const url = API_CONFIG.ENDPOINTS.EXERCISES.DETAILS(id);\n    return apiClient.get<Exercise>(url);\n  }\n\n  /**\n   * Get exercise attributes (categories, equipment, muscles, etc.)\n   */\n  static async getExerciseAttributes(): Promise<{\n    categories: Array<{ id: string; name: string; nameEn: string }>;\n    equipment: Array<{ id: string; name: string; nameEn: string }>;\n    muscles: Array<{ id: string; name: string; nameEn: string }>;\n    difficulties: Array<{ id: string; name: string; nameEn: string }>;\n  }> {\n    return apiClient.get(API_CONFIG.ENDPOINTS.EXERCISES.ATTRIBUTES);\n  }\n\n  /**\n   * Get exercises by equipment and muscles (for workout builder)\n   */\n  static async getExercisesByFilters(\n    equipment: string[],\n    muscles: string[],\n    limit = 3\n  ): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams();\n    \n    equipment.forEach(eq => searchParams.append('equipment', eq));\n    muscles.forEach(muscle => searchParams.append('muscles', muscle));\n    searchParams.append('limit', limit.toString());\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get random exercises for suggestions\n   */\n  static async getRandomExercises(count = 6): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      random: 'true',\n      limit: count.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get popular exercises\n   */\n  static async getPopularExercises(limit = 10): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      sort: 'popular',\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get exercises by muscle group\n   */\n  static async getExercisesByMuscleGroup(muscleGroup: string, limit = 20): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      muscles: muscleGroup,\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get exercises by equipment type\n   */\n  static async getExercisesByEquipment(equipment: string, limit = 20): Promise<Exercise[]> {\n    const searchParams = new URLSearchParams({\n      equipment: equipment,\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.EXERCISES.LIST}?${searchParams.toString()}`;\n    \n    const response = await apiClient.get<PaginatedResponse<Exercise>>(url);\n    return response.data || [];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAGO,MAAM;IACX;;GAEC,GACD,aAAa,aAAa,SAA+B,CAAC,CAAC,EAAwC;QACjG,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,MAAM,EAAE;YACjB,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7C;QAEA,IAAI,OAAO,SAAS,EAAE,QAAQ;YAC5B,OAAO,SAAS,CAAC,OAAO,CAAC,CAAA,KAAM,aAAa,MAAM,CAAC,aAAa;QAClE;QAEA,IAAI,OAAO,OAAO,EAAE,QAAQ;YAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA,SAAU,aAAa,MAAM,CAAC,WAAW;QAClE;QAEA,IAAI,OAAO,UAAU,EAAE,QAAQ;YAC7B,OAAO,UAAU,CAAC,OAAO,CAAC,CAAA,OAAQ,aAAa,MAAM,CAAC,cAAc;QACtE;QAEA,IAAI,OAAO,QAAQ,EAAE,QAAQ;YAC3B,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA,MAAO,aAAa,MAAM,CAAC,YAAY;QACjE;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpD;QAEA,IAAI,OAAO,MAAM,EAAE;YACjB,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtD;QAEA,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,GACvD,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI;QAEvC,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;IACpD;IAEA;;GAEC,GACD,aAAa,gBAAgB,KAAa,EAAE,QAAQ,EAAE,EAAuB;QAC3E,MAAM,eAAe,IAAI,gBAAgB;YACvC,GAAG;YACH,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAEjF,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA4B;QAChE,OAAO,SAAS,SAAS,IAAI,EAAE;IACjC;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAqB;QAC1D,MAAM,MAAM,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC;QACnD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAW;IACjC;IAEA;;GAEC,GACD,aAAa,wBAKV;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU;IAChE;IAEA;;GAEC,GACD,aAAa,sBACX,SAAmB,EACnB,OAAiB,EACjB,QAAQ,CAAC,EACY;QACrB,MAAM,eAAe,IAAI;QAEzB,UAAU,OAAO,CAAC,CAAA,KAAM,aAAa,MAAM,CAAC,aAAa;QACzD,QAAQ,OAAO,CAAC,CAAA,SAAU,aAAa,MAAM,CAAC,WAAW;QACzD,aAAa,MAAM,CAAC,SAAS,MAAM,QAAQ;QAE3C,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,mBAAmB,QAAQ,CAAC,EAAuB;QAC9D,MAAM,eAAe,IAAI,gBAAgB;YACvC,QAAQ;YACR,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,oBAAoB,QAAQ,EAAE,EAAuB;QAChE,MAAM,eAAe,IAAI,gBAAgB;YACvC,MAAM;YACN,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,0BAA0B,WAAmB,EAAE,QAAQ,EAAE,EAAuB;QAC3F,MAAM,eAAe,IAAI,gBAAgB;YACvC,SAAS;YACT,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,wBAAwB,SAAiB,EAAE,QAAQ,EAAE,EAAuB;QACvF,MAAM,eAAe,IAAI,gBAAgB;YACvC,WAAW;YACX,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA8B;QAClE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 2130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-exercises.ts"], "sourcesContent": ["/**\n * React Query hooks for exercise data\n */\n\nimport { useQuery, useInfiniteQuery } from '@tanstack/react-query';\nimport { ExerciseService } from '../api/services/exercises';\nimport type { ExerciseSearchParams } from '../api/types';\n\n// Query keys for consistent caching\nexport const exerciseKeys = {\n  all: ['exercises'] as const,\n  lists: () => [...exerciseKeys.all, 'list'] as const,\n  list: (params: ExerciseSearchParams) => [...exerciseKeys.lists(), params] as const,\n  details: () => [...exerciseKeys.all, 'detail'] as const,\n  detail: (id: string) => [...exerciseKeys.details(), id] as const,\n  search: (query: string) => [...exerciseKeys.all, 'search', query] as const,\n  attributes: () => [...exerciseKeys.all, 'attributes'] as const,\n  random: (count: number) => [...exerciseKeys.all, 'random', count] as const,\n  popular: (limit: number) => [...exerciseKeys.all, 'popular', limit] as const,\n  byMuscle: (muscle: string, limit: number) => [...exerciseKeys.all, 'muscle', muscle, limit] as const,\n  byEquipment: (equipment: string, limit: number) => [...exerciseKeys.all, 'equipment', equipment, limit] as const,\n  byFilters: (equipment: string[], muscles: string[], limit: number) => \n    [...exerciseKeys.all, 'filters', { equipment, muscles, limit }] as const,\n};\n\n/**\n * Hook to get exercises with filtering and pagination\n */\nexport function useExercises(params: ExerciseSearchParams = {}, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.list(params),\n    queryFn: () => ExerciseService.getExercises(params),\n    enabled,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n/**\n * Hook for infinite scrolling exercises list\n */\nexport function useInfiniteExercises(params: Omit<ExerciseSearchParams, 'offset'> = {}) {\n  return useInfiniteQuery({\n    queryKey: exerciseKeys.list(params),\n    queryFn: ({ pageParam = 0 }) => \n      ExerciseService.getExercises({ ...params, offset: pageParam }),\n    initialPageParam: 0,\n    getNextPageParam: (lastPage) => {\n      const { pagination } = lastPage;\n      return pagination.hasNext ? pagination.page * pagination.limit : undefined;\n    },\n    staleTime: 5 * 60 * 1000,\n  });\n}\n\n/**\n * Hook to search exercises by name\n */\nexport function useExerciseSearch(query: string, limit = 20, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.search(query),\n    queryFn: () => ExerciseService.searchExercises(query, limit),\n    enabled: enabled && query.length > 0,\n    staleTime: 2 * 60 * 1000, // 2 minutes for search results\n  });\n}\n\n/**\n * Hook to get exercise details by ID\n */\nexport function useExercise(id: string, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.detail(id),\n    queryFn: () => ExerciseService.getExerciseById(id),\n    enabled: enabled && !!id,\n    staleTime: 10 * 60 * 1000, // 10 minutes for exercise details\n  });\n}\n\n/**\n * Hook to get exercise attributes (categories, equipment, muscles, etc.)\n */\nexport function useExerciseAttributes() {\n  return useQuery({\n    queryKey: exerciseKeys.attributes(),\n    queryFn: () => ExerciseService.getExerciseAttributes(),\n    staleTime: 30 * 60 * 1000, // 30 minutes for attributes (rarely change)\n  });\n}\n\n/**\n * Hook to get exercises by equipment and muscles (for workout builder)\n */\nexport function useExercisesByFilters(\n  equipment: string[],\n  muscles: string[],\n  limit = 3,\n  enabled = true\n) {\n  return useQuery({\n    queryKey: exerciseKeys.byFilters(equipment, muscles, limit),\n    queryFn: () => ExerciseService.getExercisesByFilters(equipment, muscles, limit),\n    enabled: enabled && equipment.length > 0 && muscles.length > 0,\n    staleTime: 5 * 60 * 1000,\n  });\n}\n\n/**\n * Hook to get random exercises for suggestions\n */\nexport function useRandomExercises(count = 6, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.random(count),\n    queryFn: () => ExerciseService.getRandomExercises(count),\n    enabled,\n    staleTime: 2 * 60 * 1000, // 2 minutes for random exercises\n  });\n}\n\n/**\n * Hook to get popular exercises\n */\nexport function usePopularExercises(limit = 10, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.popular(limit),\n    queryFn: () => ExerciseService.getPopularExercises(limit),\n    enabled,\n    staleTime: 15 * 60 * 1000, // 15 minutes for popular exercises\n  });\n}\n\n/**\n * Hook to get exercises by muscle group\n */\nexport function useExercisesByMuscleGroup(muscleGroup: string, limit = 20, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.byMuscle(muscleGroup, limit),\n    queryFn: () => ExerciseService.getExercisesByMuscleGroup(muscleGroup, limit),\n    enabled: enabled && !!muscleGroup,\n    staleTime: 10 * 60 * 1000,\n  });\n}\n\n/**\n * Hook to get exercises by equipment type\n */\nexport function useExercisesByEquipment(equipment: string, limit = 20, enabled = true) {\n  return useQuery({\n    queryKey: exerciseKeys.byEquipment(equipment, limit),\n    queryFn: () => ExerciseService.getExercisesByEquipment(equipment, limit),\n    enabled: enabled && !!equipment,\n    staleTime: 10 * 60 * 1000,\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;AAED;AAAA;AACA;;;;AAIO,MAAM,eAAe;IAC1B,KAAK;QAAC;KAAY;IAClB,OAAO,IAAM;eAAI,aAAa,GAAG;YAAE;SAAO;IAC1C,MAAM,CAAC,SAAiC;eAAI,aAAa,KAAK;YAAI;SAAO;IACzE,SAAS,IAAM;eAAI,aAAa,GAAG;YAAE;SAAS;IAC9C,QAAQ,CAAC,KAAe;eAAI,aAAa,OAAO;YAAI;SAAG;IACvD,QAAQ,CAAC,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAU;SAAM;IACjE,YAAY,IAAM;eAAI,aAAa,GAAG;YAAE;SAAa;IACrD,QAAQ,CAAC,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAU;SAAM;IACjE,SAAS,CAAC,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAW;SAAM;IACnE,UAAU,CAAC,QAAgB,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAU;YAAQ;SAAM;IAC3F,aAAa,CAAC,WAAmB,QAAkB;eAAI,aAAa,GAAG;YAAE;YAAa;YAAW;SAAM;IACvG,WAAW,CAAC,WAAqB,SAAmB,QAClD;eAAI,aAAa,GAAG;YAAE;YAAW;gBAAE;gBAAW;gBAAS;YAAM;SAAE;AACnE;AAKO,SAAS,aAAa,SAA+B,CAAC,CAAC,EAAE,UAAU,IAAI;;IAC5E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,IAAI,CAAC;QAC5B,OAAO;qCAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;;QAC5C;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,qBAAqB,SAA+C,CAAC,CAAC;;IACpF,OAAO,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,aAAa,IAAI,CAAC;QAC5B,OAAO;qDAAE,CAAC,EAAE,YAAY,CAAC,EAAE,GACzB,6IAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU;;QAC9D,kBAAkB;QAClB,gBAAgB;qDAAE,CAAC;gBACjB,MAAM,EAAE,UAAU,EAAE,GAAG;gBACvB,OAAO,WAAW,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,KAAK,GAAG;YACnE;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IAZgB;;QACP,sLAAA,CAAA,mBAAgB;;;AAgBlB,SAAS,kBAAkB,KAAa,EAAE,QAAQ,EAAE,EAAE,UAAU,IAAI;;IACzE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;0CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,OAAO;;QACtD,SAAS,WAAW,MAAM,MAAM,GAAG;QACnC,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,YAAY,EAAU,EAAE,UAAU,IAAI;;IACpD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;oCAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;;QAC/C,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,UAAU;QACjC,OAAO;8CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,qBAAqB;;QACpD,WAAW,KAAK,KAAK;IACvB;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,sBACd,SAAmB,EACnB,OAAiB,EACjB,QAAQ,CAAC,EACT,UAAU,IAAI;;IAEd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,SAAS,CAAC,WAAW,SAAS;QACrD,OAAO;8CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,WAAW,SAAS;;QACzE,SAAS,WAAW,UAAU,MAAM,GAAG,KAAK,QAAQ,MAAM,GAAG;QAC7D,WAAW,IAAI,KAAK;IACtB;AACF;IAZgB;;QAMP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,mBAAmB,QAAQ,CAAC,EAAE,UAAU,IAAI;;IAC1D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;2CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;;QAClD;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,oBAAoB,QAAQ,EAAE,EAAE,UAAU,IAAI;;IAC5D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,OAAO,CAAC;QAC/B,OAAO;4CAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;;QACnD;QACA,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,0BAA0B,WAAmB,EAAE,QAAQ,EAAE,EAAE,UAAU,IAAI;;IACvF,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,QAAQ,CAAC,aAAa;QAC7C,OAAO;kDAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,aAAa;;QACtE,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS,wBAAwB,SAAiB,EAAE,QAAQ,EAAE,EAAE,UAAU,IAAI;;IACnF,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,WAAW,CAAC,WAAW;QAC9C,OAAO;gDAAE,IAAM,6IAAA,CAAA,kBAAe,CAAC,uBAAuB,CAAC,WAAW;;QAClE,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;IAPgB;;QACP,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 2389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/exercises/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Metadata } from 'next'\nimport { Navigation } from \"@/components/Navigation\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { LoadingPage, LoadingGrid } from \"@/components/ui/loading\"\nimport { ErrorMessage } from \"@/components/ui/error\"\nimport { EmptyExercises, EmptySearchResults } from \"@/components/ui/empty-state\"\nimport {\n  Search,\n  Filter,\n  Clock,\n  Target,\n  Play,\n  Heart,\n  Zap,\n  Star,\n  BookOpen,\n  Users,\n  Dumbbell,\n  X,\n  Eye,\n  Plus\n} from \"lucide-react\"\nimport { \n  useExercises, \n  useExerciseSearch, \n  useExerciseAttributes,\n  useInfiniteExercises \n} from \"@/lib/hooks/use-exercises\"\nimport type { ExerciseSearchParams } from \"@/lib/api/types\"\n\nexport default function ExercisesPage() {\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [showFilters, setShowFilters] = useState(false)\n  const [selectedFilters, setSelectedFilters] = useState<ExerciseSearchParams>({\n    equipment: [],\n    muscles: [],\n    difficulty: [],\n    category: []\n  })\n\n  // Use search when there's a query, otherwise use filtered exercises\n  const { data: searchResults, isLoading: isSearching } = useExerciseSearch(\n    searchQuery, \n    20, \n    searchQuery.length > 0\n  )\n\n  const { \n    data: exercisesData, \n    isLoading: isLoadingExercises,\n    error: exercisesError \n  } = useExercises(\n    searchQuery.length === 0 ? selectedFilters : {},\n    searchQuery.length === 0\n  )\n\n  const { \n    data: attributesData, \n    isLoading: isLoadingAttributes \n  } = useExerciseAttributes()\n\n  const exercises = searchQuery.length > 0 ? searchResults : exercisesData?.data\n  const isLoading = searchQuery.length > 0 ? isSearching : isLoadingExercises\n\n  const handleFilterChange = (type: keyof ExerciseSearchParams, value: string) => {\n    setSelectedFilters(prev => {\n      const currentValues = prev[type] as string[] || []\n      const newValues = currentValues.includes(value)\n        ? currentValues.filter(v => v !== value)\n        : [...currentValues, value]\n      \n      return {\n        ...prev,\n        [type]: newValues\n      }\n    })\n  }\n\n  const clearFilters = () => {\n    setSelectedFilters({\n      equipment: [],\n      muscles: [],\n      difficulty: [],\n      category: []\n    })\n  }\n\n  const hasActiveFilters = Object.values(selectedFilters).some(\n    arr => Array.isArray(arr) && arr.length > 0\n  )\n\n  if (isLoadingAttributes) {\n    return <LoadingPage />\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Exercise Library\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Discover thousands of exercises with detailed instructions, muscle targeting, and difficulty levels\n          </p>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6 mb-8\">\n          <div className=\"flex flex-col md:flex-row gap-4 mb-4\">\n            {/* Search Bar */}\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n              <Input\n                type=\"text\"\n                placeholder=\"Search exercises...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n            \n            {/* Filter Toggle */}\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center gap-2\"\n            >\n              <Filter className=\"h-4 w-4\" />\n              Filters\n              {hasActiveFilters && (\n                <Badge variant=\"secondary\" className=\"ml-1\">\n                  {Object.values(selectedFilters).reduce((acc, arr) => acc + (arr?.length || 0), 0)}\n                </Badge>\n              )}\n            </Button>\n          </div>\n\n          {/* Filter Panel */}\n          {showFilters && attributesData && (\n            <div className=\"border-t pt-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {/* Equipment Filter */}\n                <div>\n                  <h3 className=\"font-medium text-gray-900 mb-2\">Equipment</h3>\n                  <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n                    {attributesData.equipment?.map((item) => (\n                      <label key={item.id} className=\"flex items-center space-x-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedFilters.equipment?.includes(item.id) || false}\n                          onChange={() => handleFilterChange('equipment', item.id)}\n                          className=\"rounded border-gray-300\"\n                        />\n                        <span className=\"text-sm text-gray-700\">{item.name}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Muscles Filter */}\n                <div>\n                  <h3 className=\"font-medium text-gray-900 mb-2\">Muscles</h3>\n                  <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n                    {attributesData.muscles?.map((item) => (\n                      <label key={item.id} className=\"flex items-center space-x-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedFilters.muscles?.includes(item.id) || false}\n                          onChange={() => handleFilterChange('muscles', item.id)}\n                          className=\"rounded border-gray-300\"\n                        />\n                        <span className=\"text-sm text-gray-700\">{item.name}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Categories Filter */}\n                <div>\n                  <h3 className=\"font-medium text-gray-900 mb-2\">Categories</h3>\n                  <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n                    {attributesData.categories?.map((item) => (\n                      <label key={item.id} className=\"flex items-center space-x-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedFilters.category?.includes(item.id) || false}\n                          onChange={() => handleFilterChange('category', item.id)}\n                          className=\"rounded border-gray-300\"\n                        />\n                        <span className=\"text-sm text-gray-700\">{item.name}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Difficulty Filter */}\n                <div>\n                  <h3 className=\"font-medium text-gray-900 mb-2\">Difficulty</h3>\n                  <div className=\"space-y-2\">\n                    {attributesData.difficulties?.map((item) => (\n                      <label key={item.id} className=\"flex items-center space-x-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedFilters.difficulty?.includes(item.id) || false}\n                          onChange={() => handleFilterChange('difficulty', item.id)}\n                          className=\"rounded border-gray-300\"\n                        />\n                        <span className=\"text-sm text-gray-700\">{item.name}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {hasActiveFilters && (\n                <div className=\"mt-4 pt-4 border-t\">\n                  <Button variant=\"outline\" onClick={clearFilters} className=\"flex items-center gap-2\">\n                    <X className=\"h-4 w-4\" />\n                    Clear Filters\n                  </Button>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Results */}\n        {isLoading ? (\n          <LoadingGrid />\n        ) : exercisesError ? (\n          <ErrorMessage \n            title=\"Failed to load exercises\"\n            message=\"Please try again later\"\n            onRetry={() => window.location.reload()}\n          />\n        ) : !exercises || exercises.length === 0 ? (\n          searchQuery ? (\n            <EmptySearchResults\n              searchTerm={searchQuery}\n              onClearSearch={() => setSearchQuery(\"\")}\n            />\n          ) : (\n            <EmptyExercises />\n          )\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {exercises.map((exercise) => (\n              <Card key={exercise.id} className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader>\n                  <div className=\"flex justify-between items-start\">\n                    <div>\n                      <CardTitle className=\"text-lg\">{exercise.name}</CardTitle>\n                      <CardDescription>{exercise.nameEn}</CardDescription>\n                    </div>\n                    <Button variant=\"ghost\" size=\"icon\">\n                      <Heart className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    {/* Exercise Attributes */}\n                    <div className=\"flex flex-wrap gap-2\">\n                      {exercise.attributes?.slice(0, 3).map((attr, index) => (\n                        <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                          {attr.attributeName?.name || attr.attributeValue?.value}\n                        </Badge>\n                      ))}\n                      {exercise.attributes && exercise.attributes.length > 3 && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          +{exercise.attributes.length - 3} more\n                        </Badge>\n                      )}\n                    </div>\n\n                    {/* Action Buttons */}\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" className=\"flex-1\">\n                        <Play className=\"h-4 w-4 mr-2\" />\n                        View Details\n                      </Button>\n                      <Button variant=\"outline\" size=\"sm\">\n                        <Plus className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n\n        {/* Load More Button (if using pagination) */}\n        {exercises && exercises.length > 0 && exercisesData?.pagination?.hasNext && (\n          <div className=\"text-center mt-8\">\n            <Button variant=\"outline\" size=\"lg\">\n              Load More Exercises\n            </Button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AA5BA;;;;;;;;;;;;AAoCe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QAC3E,WAAW,EAAE;QACb,SAAS,EAAE;QACX,YAAY,EAAE;QACd,UAAU,EAAE;IACd;IAEA,oEAAoE;IACpE,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EACtE,aACA,IACA,YAAY,MAAM,GAAG;IAGvB,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,kBAAkB,EAC7B,OAAO,cAAc,EACtB,GAAG,CAAA,GAAA,0IAAA,CAAA,eAAY,AAAD,EACb,YAAY,MAAM,KAAK,IAAI,kBAAkB,CAAC,GAC9C,YAAY,MAAM,KAAK;IAGzB,MAAM,EACJ,MAAM,cAAc,EACpB,WAAW,mBAAmB,EAC/B,GAAG,CAAA,GAAA,0IAAA,CAAA,wBAAqB,AAAD;IAExB,MAAM,YAAY,YAAY,MAAM,GAAG,IAAI,gBAAgB,eAAe;IAC1E,MAAM,YAAY,YAAY,MAAM,GAAG,IAAI,cAAc;IAEzD,MAAM,qBAAqB,CAAC,MAAkC;QAC5D,mBAAmB,CAAA;YACjB,MAAM,gBAAgB,IAAI,CAAC,KAAK,IAAgB,EAAE;YAClD,MAAM,YAAY,cAAc,QAAQ,CAAC,SACrC,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM,SAChC;mBAAI;gBAAe;aAAM;YAE7B,OAAO;gBACL,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,eAAe;QACnB,mBAAmB;YACjB,WAAW,EAAE;YACb,SAAS,EAAE;YACX,YAAY,EAAE;YACd,UAAU,EAAE;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,iBAAiB,IAAI,CAC1D,CAAA,MAAO,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,GAAG;IAG5C,IAAI,qBAAqB;QACvB,qBAAO,6LAAC,sIAAA,CAAA,cAAW;;;;;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;4CAE7B,kCACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,KAAK,UAAU,CAAC,GAAG;;;;;;;;;;;;;;;;;;4BAOtF,eAAe,gCACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEACZ,eAAe,SAAS,EAAE,IAAI,CAAC,qBAC9B,6LAAC;gEAAoB,WAAU;;kFAC7B,6LAAC;wEACC,MAAK;wEACL,SAAS,gBAAgB,SAAS,EAAE,SAAS,KAAK,EAAE,KAAK;wEACzD,UAAU,IAAM,mBAAmB,aAAa,KAAK,EAAE;wEACvD,WAAU;;;;;;kFAEZ,6LAAC;wEAAK,WAAU;kFAAyB,KAAK,IAAI;;;;;;;+DAPxC,KAAK,EAAE;;;;;;;;;;;;;;;;0DAczB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEACZ,eAAe,OAAO,EAAE,IAAI,CAAC,qBAC5B,6LAAC;gEAAoB,WAAU;;kFAC7B,6LAAC;wEACC,MAAK;wEACL,SAAS,gBAAgB,OAAO,EAAE,SAAS,KAAK,EAAE,KAAK;wEACvD,UAAU,IAAM,mBAAmB,WAAW,KAAK,EAAE;wEACrD,WAAU;;;;;;kFAEZ,6LAAC;wEAAK,WAAU;kFAAyB,KAAK,IAAI;;;;;;;+DAPxC,KAAK,EAAE;;;;;;;;;;;;;;;;0DAczB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEACZ,eAAe,UAAU,EAAE,IAAI,CAAC,qBAC/B,6LAAC;gEAAoB,WAAU;;kFAC7B,6LAAC;wEACC,MAAK;wEACL,SAAS,gBAAgB,QAAQ,EAAE,SAAS,KAAK,EAAE,KAAK;wEACxD,UAAU,IAAM,mBAAmB,YAAY,KAAK,EAAE;wEACtD,WAAU;;;;;;kFAEZ,6LAAC;wEAAK,WAAU;kFAAyB,KAAK,IAAI;;;;;;;+DAPxC,KAAK,EAAE;;;;;;;;;;;;;;;;0DAczB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEACZ,eAAe,YAAY,EAAE,IAAI,CAAC,qBACjC,6LAAC;gEAAoB,WAAU;;kFAC7B,6LAAC;wEACC,MAAK;wEACL,SAAS,gBAAgB,UAAU,EAAE,SAAS,KAAK,EAAE,KAAK;wEAC1D,UAAU,IAAM,mBAAmB,cAAc,KAAK,EAAE;wEACxD,WAAU;;;;;;kFAEZ,6LAAC;wEAAK,WAAU;kFAAyB,KAAK,IAAI;;;;;;;+DAPxC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;oCAc1B,kCACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAc,WAAU;;8DACzD,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;oBAUpC,0BACC,6LAAC,sIAAA,CAAA,cAAW;;;;+BACV,+BACF,6LAAC,oIAAA,CAAA,eAAY;wBACX,OAAM;wBACN,SAAQ;wBACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;;;;;+BAErC,CAAC,aAAa,UAAU,MAAM,KAAK,IACrC,4BACE,6LAAC,6IAAA,CAAA,qBAAkB;wBACjB,YAAY;wBACZ,eAAe,IAAM,eAAe;;;;;6CAGtC,6LAAC,6IAAA,CAAA,iBAAc;;;;6CAGjB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,mIAAA,CAAA,OAAI;gCAAmB,WAAU;;kDAChC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW,SAAS,IAAI;;;;;;sEAC7C,6LAAC,mIAAA,CAAA,kBAAe;sEAAE,SAAS,MAAM;;;;;;;;;;;;8DAEnC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;8DAC3B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAIvB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;wDACZ,SAAS,UAAU,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,sBAC3C,6LAAC,oIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAY,WAAU;0EAC9C,KAAK,aAAa,EAAE,QAAQ,KAAK,cAAc,EAAE;+DADxC;;;;;wDAIb,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,mBACnD,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,SAAS,UAAU,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAMvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;;8EAC1B,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAC7B,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAnCf,SAAS,EAAE;;;;;;;;;;oBA8C3B,aAAa,UAAU,MAAM,GAAG,KAAK,eAAe,YAAY,yBAC/D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GArRwB;;QAWkC,0IAAA,CAAA,oBAAiB;QAUrE,0IAAA,CAAA,eAAY;QAQZ,0IAAA,CAAA,wBAAqB;;;KA7BH", "debugId": null}}]}