import { Metada<PERSON> } from 'next';
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Navigation } from "@/components/Navigation"
import { <PERSON><PERSON><PERSON>, <PERSON>, BarChart3, BookO<PERSON> } from "lucide-react"
import { generatePageMetadata, commonFitnessQAs } from "@/lib/seo/utils"
import { FAQStructuredData } from "@/components/seo/structured-data"
import { Main, Section, SemanticHeader } from "@/components/seo/semantic-html"
import { ExerciseCategoryLinks, WorkoutProgramLinks, ContextualNav } from "@/components/seo/internal-links"

export const metadata: Metadata = generatePageMetadata({
  title: "AI-fitness-singles - Smart Fitness Platform for Singles",
  description: "Transform your fitness journey with AI-powered workout plans, comprehensive exercise database, and detailed progress tracking. Perfect for singles looking to achieve their fitness goals.",
  path: "/",
});

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <Main>
        {/* Hero Section */}
        <Section className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100" ariaLabel="Hero section">
          <div className="container mx-auto px-4 py-20 sm:py-32">
            <div className="text-center">
              <SemanticHeader level={1} className="text-4xl sm:text-6xl font-bold text-gray-900 mb-6">
                AI-fitness-singles
                <span className="block text-blue-600">Smart Fitness Platform</span>
              </SemanticHeader>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Create custom workout plans, access comprehensive exercise database,
              and track your fitness progress with detailed analytics.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                <Play className="mr-2 h-5 w-5" />
                Start Training
              </Button>
              <Button variant="outline" size="lg" className="border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3">
                <BookOpen className="mr-2 h-5 w-5" />
                Browse Exercises
              </Button>
            </div>
          </div>
        </div>
        </Section>

        {/* Core Features Section */}
        <Section className="py-20 bg-white" ariaLabel="Core features section">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <SemanticHeader level={2} className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                Everything You Need to Train
              </SemanticHeader>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive fitness platform with workout builder, exercise library, and progress tracking.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Dumbbell className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Workout Builder</CardTitle>
                <CardDescription className="text-gray-600">
                  Create custom workout plans with our intuitive drag-and-drop builder.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <BookOpen className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Exercise Database</CardTitle>
                <CardDescription className="text-gray-600">
                  Access thousands of exercises with detailed instructions and video guides.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <BarChart3 className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Progress Tracking</CardTitle>
                <CardDescription className="text-gray-600">
                  Monitor your improvements with detailed analytics and visual reports.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                  <Play className="h-8 w-8 text-orange-600" />
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">Workout Sessions</CardTitle>
                <CardDescription className="text-gray-600">
                  Execute your workouts with guided sessions and real-time tracking.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
        </Section>

        {/* Exercise Categories Section */}
        <Section className="py-20 bg-gray-50" ariaLabel="Exercise categories">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <SemanticHeader level={2} className="text-3xl font-bold text-gray-900 mb-4">
                Explore Exercise Categories
              </SemanticHeader>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover exercises organized by type, muscle group, and equipment to build your perfect workout routine.
              </p>
            </div>
            <ExerciseCategoryLinks />
          </div>
        </Section>

        {/* Workout Programs Section */}
        <Section className="py-20 bg-white" ariaLabel="Workout programs">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <SemanticHeader level={2} className="text-3xl font-bold text-gray-900 mb-4">
                AI-Powered Workout Programs
              </SemanticHeader>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Choose from scientifically designed workout programs tailored to your fitness level and goals.
              </p>
            </div>
            <WorkoutProgramLinks />
          </div>
        </Section>

        {/* Quick Start Section */}
        <Section className="py-20 bg-gray-50" ariaLabel="Quick start section">
          <div className="container mx-auto px-4 text-center">
            <SemanticHeader level={2} className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Start Training Today
            </SemanticHeader>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Build your first workout plan in minutes and begin your fitness journey.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
              Create Workout Plan
            </Button>
            <Button variant="outline" size="lg" className="border-gray-300 text-gray-700 hover:bg-gray-100 px-8 py-3">
              Explore Exercises
            </Button>
          </div>
        </div>
        </Section>

        {/* Contextual Navigation */}
        <Section className="py-16 bg-white" ariaLabel="Related pages">
          <div className="container mx-auto px-4">
            <ContextualNav currentPage="home" />
          </div>
        </Section>
      </Main>

      {/* SEO: FAQ Structured Data */}
      <FAQStructuredData faqs={commonFitnessQAs} />
    </div>
  );
}
