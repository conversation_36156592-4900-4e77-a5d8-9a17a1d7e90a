# Database Configuration
POSTGRES_USER=my-user
POSTGRES_PASSWORD=my-password
POSTGRES_DB=workout-cool
DB_HOST=localhost
DB_PORT=5432
# Format: postgresql://USER:PASSWORD@HOST:PORT/DATABASE
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${DB_HOST}:${DB_PORT}/${POSTGRES_DB}

# Authentication
# The URL where your application is running
BETTER_AUTH_URL="http://localhost:3000"
# Generate a secure random string using: openssl rand -base64 32
BETTER_AUTH_SECRET="your-secret-key-here"

# Google OAuth
# Get these from Google Cloud Console: https://console.cloud.google.com
# Required scopes: email, profile
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# OpenPanel Integration
# Get these from your OpenPanel dashboard
OPENPANEL_SECRET_KEY=
NEXT_PUBLIC_OPENPANEL_CLIENT_ID=

# Environment
# Options: development, production, test
NODE_ENV="development"

#SMTP Configuration
# Using MailHog for example. https://github.com/mailhog/MailHog
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
SMTP_FROM="Workout Cool <<EMAIL>>"
SMTP_SECURE=false

# Whether to seed sample data on startup
SEED_SAMPLE_DATA=true

# ========================================
# BILLING CONFIGURATION
# ========================================

# Stripe Configuration (optional)
# Get these from https://dashboard.stripe.com
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."

# Stripe Price IDs
# Create products in Stripe Dashboard and add price IDs here
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_..."
NEXT_PUBLIC_STRIPE_PRICE_YEARLY="price_..."

# RevenueCat Configuration (for mobile app integration)
REVENUECAT_API_KEY=""
REVENUECAT_WEBHOOK_SECRET=""

# Billing Mode for self-hosted
# Options: DISABLED, LICENSE_KEY, SUBSCRIPTION, FREEMIUM
DEFAULT_BILLING_MODE="DISABLED"

NEXT_PUBLIC_APP_URL="http://localhost:3000"
