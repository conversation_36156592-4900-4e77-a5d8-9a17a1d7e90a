[{"name": "hot-reloader", "duration": 110, "timestamp": 1313704370, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751119273002, "traceId": "d80d7db6675386d4"}, {"name": "setup-dev-bundler", "duration": 557005, "timestamp": 1313500480, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751119272798, "traceId": "d80d7db6675386d4"}, {"name": "run-instrumentation-hook", "duration": 26, "timestamp": 1314105038, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751119273403, "traceId": "d80d7db6675386d4"}, {"name": "start-dev-server", "duration": 1104139, "timestamp": 1313019939, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "23871377408", "memory.totalMem": "34271535104", "memory.heapSizeLimit": "17185112064", "memory.rss": "184868864", "memory.heapTotal": "101629952", "memory.heapUsed": "69106632"}, "startTime": 1751119272318, "traceId": "d80d7db6675386d4"}, {"name": "compile-path", "duration": 1958205, "timestamp": 1345788273, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751119305086, "traceId": "d80d7db6675386d4"}, {"name": "ensure-page", "duration": 1959197, "timestamp": 1345787694, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751119305085, "traceId": "d80d7db6675386d4"}]