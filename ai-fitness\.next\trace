[{"name": "hot-reloader", "duration": 145, "timestamp": 25378313213, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751143337611, "traceId": "5e0c751529665682"}, {"name": "setup-dev-bundler", "duration": 615840, "timestamp": 25378114417, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751143337412, "traceId": "5e0c751529665682"}, {"name": "run-instrumentation-hook", "duration": 24, "timestamp": 25378775694, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751143338073, "traceId": "5e0c751529665682"}, {"name": "start-dev-server", "duration": 1199046, "timestamp": 25377589130, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "8363417600", "memory.totalMem": "34271535104", "memory.heapSizeLimit": "17185112064", "memory.rss": "188362752", "memory.heapTotal": "101892096", "memory.heapUsed": "70182064"}, "startTime": 1751143336887, "traceId": "5e0c751529665682"}, {"name": "compile-path", "duration": 2516990, "timestamp": 25413337181, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751143372635, "traceId": "5e0c751529665682"}, {"name": "ensure-page", "duration": 2517999, "timestamp": 25413336606, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751143372634, "traceId": "5e0c751529665682"}]