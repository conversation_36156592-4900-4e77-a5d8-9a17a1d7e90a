[{"name": "hot-reloader", "duration": 130, "timestamp": 23580628422, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751141539926, "traceId": "3e5e0d28781f0e18"}, {"name": "setup-dev-bundler", "duration": 634424, "timestamp": 23580406633, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751141539704, "traceId": "3e5e0d28781f0e18"}, {"name": "run-instrumentation-hook", "duration": 24, "timestamp": 23581089480, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751141540387, "traceId": "3e5e0d28781f0e18"}, {"name": "start-dev-server", "duration": 1272617, "timestamp": 23579830393, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "11868254208", "memory.totalMem": "34271535104", "memory.heapSizeLimit": "17185112064", "memory.rss": "189071360", "memory.heapTotal": "102154240", "memory.heapUsed": "70169696"}, "startTime": 1751141539128, "traceId": "3e5e0d28781f0e18"}, {"name": "compile-path", "duration": 2363339, "timestamp": 23621891949, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751141581190, "traceId": "3e5e0d28781f0e18"}, {"name": "ensure-page", "duration": 2364413, "timestamp": 23621891385, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751141581189, "traceId": "3e5e0d28781f0e18"}]