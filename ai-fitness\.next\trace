[{"name": "generate-buildid", "duration": 208, "timestamp": 20340043823, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751138299341, "traceId": "ddccd1f42755eb78"}, {"name": "load-custom-routes", "duration": 2569, "timestamp": 20340044116, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751138299341, "traceId": "ddccd1f42755eb78"}, {"name": "create-dist-dir", "duration": 2451, "timestamp": 20340121836, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751138299419, "traceId": "ddccd1f42755eb78"}, {"name": "create-pages-mapping", "duration": 220, "timestamp": 20340142229, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751138299439, "traceId": "ddccd1f42755eb78"}, {"name": "collect-app-paths", "duration": 1892, "timestamp": 20340142493, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751138299439, "traceId": "ddccd1f42755eb78"}, {"name": "create-app-mapping", "duration": 1968, "timestamp": 20340144408, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751138299441, "traceId": "ddccd1f42755eb78"}, {"name": "public-dir-conflict-check", "duration": 542, "timestamp": 20340147035, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751138299444, "traceId": "ddccd1f42755eb78"}, {"name": "generate-routes-manifest", "duration": 4859, "timestamp": 20340147743, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751138299445, "traceId": "ddccd1f42755eb78"}, {"name": "create-entrypoints", "duration": 69837, "timestamp": 20340178409, "id": 14, "parentId": 1, "tags": {}, "startTime": 1751138299475, "traceId": "ddccd1f42755eb78"}, {"name": "generate-webpack-config", "duration": 374934, "timestamp": 20340248299, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751138299545, "traceId": "ddccd1f42755eb78"}, {"name": "next-trace-entrypoint-plugin", "duration": 1688, "timestamp": 20340738937, "id": 17, "parentId": 16, "tags": {}, "startTime": 1751138300036, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 393322, "timestamp": 20340747287, "id": 21, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 470137, "timestamp": 20340747308, "id": 23, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 469891, "timestamp": 20340747574, "id": 35, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 470179, "timestamp": 20340747298, "id": 22, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 471878, "timestamp": 20340747265, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fhealth%2Froute&name=app%2Fapi%2Fhealth%2Froute&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fapi%2Fhealth%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 547245, "timestamp": 20340746743, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 548110, "timestamp": 20340747325, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fauth%2Fsignin%2Fpage&name=app%2Fauth%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fauth%2Fsignin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 548115, "timestamp": 20340747335, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fauth%2Fsignup%2Fpage&name=app%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fauth%2Fsignup%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 548107, "timestamp": 20340747349, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 547973, "timestamp": 20340747509, "id": 28, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdemo%2Fpage&name=app%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fdemo%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 547929, "timestamp": 20340747560, "id": 30, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fexercises%2Fpage&name=app%2Fexercises%2Fpage&pagePath=private-next-app-dir%2Fexercises%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fexercises%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 547941, "timestamp": 20340747552, "id": 29, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fexercises%2F%5Bid%5D%2Fpage&name=app%2Fexercises%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fexercises%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fexercises%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 547932, "timestamp": 20340747564, "id": 31, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 547933, "timestamp": 20340747566, "id": 32, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fprogress%2Fpage&name=app%2Fprogress%2Fpage&pagePath=private-next-app-dir%2Fprogress%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fprogress%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 548004, "timestamp": 20340747568, "id": 33, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fworkouts%2F%5Bid%5D%2Fpage&name=app%2Fworkouts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fworkouts%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fworkouts%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 548007, "timestamp": 20340747571, "id": 34, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fworkouts%2Fpage&name=app%2Fworkouts%2Fpage&pagePath=private-next-app-dir%2Fworkouts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fworkouts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 824205, "timestamp": 20340747317, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fauth%2F%5B...all%5D%2Froute&name=app%2Fapi%2Fauth%2F%5B...all%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...all%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fapi%2Fauth%2F%5B...all%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138300044, "traceId": "ddccd1f42755eb78"}, {"name": "build-module-ts", "duration": 33164, "timestamp": 20341929468, "id": 108, "parentId": 16, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\store\\app-store.ts", "layer": "ssr"}, "startTime": 1751138301226, "traceId": "ddccd1f42755eb78"}, {"name": "make", "duration": 1226993, "timestamp": 20340746424, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751138300043, "traceId": "ddccd1f42755eb78"}, {"name": "get-entries", "duration": 3629, "timestamp": 20341974607, "id": 110, "parentId": 109, "tags": {}, "startTime": 1751138301271, "traceId": "ddccd1f42755eb78"}, {"name": "node-file-trace-plugin", "duration": 63599, "timestamp": 20341984684, "id": 111, "parentId": 109, "tags": {"traceEntryCount": "30"}, "startTime": 1751138301282, "traceId": "ddccd1f42755eb78"}, {"name": "collect-traced-files", "duration": 612, "timestamp": 20342048298, "id": 112, "parentId": 109, "tags": {}, "startTime": 1751138301345, "traceId": "ddccd1f42755eb78"}, {"name": "finish-modules", "duration": 74484, "timestamp": 20341974431, "id": 109, "parentId": 17, "tags": {}, "startTime": 1751138301271, "traceId": "ddccd1f42755eb78"}, {"name": "chunk-graph", "duration": 26756, "timestamp": 20342110806, "id": 114, "parentId": 113, "tags": {}, "startTime": 1751138301408, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-modules", "duration": 25, "timestamp": 20342137683, "id": 116, "parentId": 113, "tags": {}, "startTime": 1751138301435, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-chunks", "duration": 46418, "timestamp": 20342137800, "id": 117, "parentId": 113, "tags": {}, "startTime": 1751138301435, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-tree", "duration": 130, "timestamp": 20342184311, "id": 118, "parentId": 113, "tags": {}, "startTime": 1751138301481, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-chunk-modules", "duration": 82190, "timestamp": 20342184525, "id": 119, "parentId": 113, "tags": {}, "startTime": 1751138301481, "traceId": "ddccd1f42755eb78"}, {"name": "optimize", "duration": 129179, "timestamp": 20342137634, "id": 115, "parentId": 113, "tags": {}, "startTime": 1751138301434, "traceId": "ddccd1f42755eb78"}, {"name": "module-hash", "duration": 29029, "timestamp": 20342285373, "id": 120, "parentId": 113, "tags": {}, "startTime": 1751138301582, "traceId": "ddccd1f42755eb78"}, {"name": "code-generation", "duration": 7485, "timestamp": 20342314464, "id": 121, "parentId": 113, "tags": {}, "startTime": 1751138301611, "traceId": "ddccd1f42755eb78"}, {"name": "hash", "duration": 7677, "timestamp": 20342326520, "id": 122, "parentId": 113, "tags": {}, "startTime": 1751138301623, "traceId": "ddccd1f42755eb78"}, {"name": "code-generation-jobs", "duration": 226, "timestamp": 20342334193, "id": 123, "parentId": 113, "tags": {}, "startTime": 1751138301631, "traceId": "ddccd1f42755eb78"}, {"name": "module-assets", "duration": 455, "timestamp": 20342334376, "id": 124, "parentId": 113, "tags": {}, "startTime": 1751138301631, "traceId": "ddccd1f42755eb78"}, {"name": "create-chunk-assets", "duration": 2726, "timestamp": 20342334843, "id": 125, "parentId": 113, "tags": {}, "startTime": 1751138301632, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4186, "timestamp": 20342346555, "id": 127, "parentId": 126, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751138301643, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4076, "timestamp": 20342346674, "id": 128, "parentId": 126, "tags": {"name": "../app/api/health/route.js", "cache": "HIT"}, "startTime": 1751138301643, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4068, "timestamp": 20342346684, "id": 129, "parentId": 126, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4063, "timestamp": 20342346689, "id": 130, "parentId": 126, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4060, "timestamp": 20342346694, "id": 131, "parentId": 126, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4055, "timestamp": 20342346699, "id": 132, "parentId": 126, "tags": {"name": "../app/api/auth/[...all]/route.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4052, "timestamp": 20342346704, "id": 133, "parentId": 126, "tags": {"name": "../app/auth/signin/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4048, "timestamp": 20342346708, "id": 134, "parentId": 126, "tags": {"name": "../app/auth/signup/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4045, "timestamp": 20342346712, "id": 135, "parentId": 126, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 4005, "timestamp": 20342346753, "id": 136, "parentId": 126, "tags": {"name": "../app/demo/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3992, "timestamp": 20342346767, "id": 137, "parentId": 126, "tags": {"name": "../app/exercises/[id]/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3985, "timestamp": 20342346775, "id": 138, "parentId": 126, "tags": {"name": "../app/exercises/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3982, "timestamp": 20342346779, "id": 139, "parentId": 126, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3978, "timestamp": 20342346784, "id": 140, "parentId": 126, "tags": {"name": "../app/progress/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3975, "timestamp": 20342346787, "id": 141, "parentId": 126, "tags": {"name": "../app/workouts/[id]/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3972, "timestamp": 20342346791, "id": 142, "parentId": 126, "tags": {"name": "../app/workouts/page.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3969, "timestamp": 20342346794, "id": 143, "parentId": 126, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3967, "timestamp": 20342346798, "id": 144, "parentId": 126, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 3963, "timestamp": 20342346803, "id": 145, "parentId": 126, "tags": {"name": "../vendors.js", "cache": "HIT"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 23188, "timestamp": 20342346810, "id": 146, "parentId": 126, "tags": {"name": "../common.js", "cache": "MISS"}, "startTime": 1751138301644, "traceId": "ddccd1f42755eb78"}, {"name": "minify-webpack-plugin-optimize", "duration": 29317, "timestamp": 20342340696, "id": 126, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751138301638, "traceId": "ddccd1f42755eb78"}, {"name": "css-minimizer-plugin", "duration": 390, "timestamp": 20342370162, "id": 147, "parentId": 16, "tags": {}, "startTime": 1751138301667, "traceId": "ddccd1f42755eb78"}, {"name": "create-trace-assets", "duration": 2192, "timestamp": 20342370830, "id": 148, "parentId": 17, "tags": {}, "startTime": 1751138301668, "traceId": "ddccd1f42755eb78"}, {"name": "seal", "duration": 302066, "timestamp": 20342083237, "id": 113, "parentId": 16, "tags": {}, "startTime": 1751138301380, "traceId": "ddccd1f42755eb78"}, {"name": "webpack-compilation", "duration": 1692763, "timestamp": 20340737424, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751138300034, "traceId": "ddccd1f42755eb78"}, {"name": "emit", "duration": 18654, "timestamp": 20342430761, "id": 149, "parentId": 13, "tags": {}, "startTime": 1751138301728, "traceId": "ddccd1f42755eb78"}, {"name": "webpack-close", "duration": 270486, "timestamp": 20342451937, "id": 150, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751138301749, "traceId": "ddccd1f42755eb78"}, {"name": "webpack-generate-error-stats", "duration": 2273, "timestamp": 20342722491, "id": 151, "parentId": 150, "tags": {}, "startTime": 1751138302019, "traceId": "ddccd1f42755eb78"}, {"name": "make", "duration": 186, "timestamp": 20342733043, "id": 153, "parentId": 152, "tags": {}, "startTime": 1751138302030, "traceId": "ddccd1f42755eb78"}, {"name": "chunk-graph", "duration": 39, "timestamp": 20342733876, "id": 155, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-modules", "duration": 10, "timestamp": 20342733968, "id": 157, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-chunks", "duration": 97, "timestamp": 20342734060, "id": 158, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-tree", "duration": 9, "timestamp": 20342734203, "id": 159, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "optimize-chunk-modules", "duration": 61, "timestamp": 20342734285, "id": 160, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "optimize", "duration": 458, "timestamp": 20342733930, "id": 156, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "module-hash", "duration": 12, "timestamp": 20342734560, "id": 161, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "code-generation", "duration": 11, "timestamp": 20342734581, "id": 162, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "hash", "duration": 59, "timestamp": 20342734628, "id": 163, "parentId": 154, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "code-generation-jobs", "duration": 40, "timestamp": 20342734687, "id": 164, "parentId": 154, "tags": {}, "startTime": 1751138302032, "traceId": "ddccd1f42755eb78"}, {"name": "module-assets", "duration": 15, "timestamp": 20342734717, "id": 165, "parentId": 154, "tags": {}, "startTime": 1751138302032, "traceId": "ddccd1f42755eb78"}, {"name": "create-chunk-assets", "duration": 13, "timestamp": 20342734737, "id": 166, "parentId": 154, "tags": {}, "startTime": 1751138302032, "traceId": "ddccd1f42755eb78"}, {"name": "minify-js", "duration": 31, "timestamp": 20342742914, "id": 168, "parentId": 167, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751138302040, "traceId": "ddccd1f42755eb78"}, {"name": "minify-webpack-plugin-optimize", "duration": 622, "timestamp": 20342742334, "id": 167, "parentId": 152, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751138302039, "traceId": "ddccd1f42755eb78"}, {"name": "css-minimizer-plugin", "duration": 7, "timestamp": 20342743019, "id": 169, "parentId": 152, "tags": {}, "startTime": 1751138302040, "traceId": "ddccd1f42755eb78"}, {"name": "seal", "duration": 11072, "timestamp": 20342733698, "id": 154, "parentId": 152, "tags": {}, "startTime": 1751138302031, "traceId": "ddccd1f42755eb78"}, {"name": "webpack-compilation", "duration": 13479, "timestamp": 20342731423, "id": 152, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751138302028, "traceId": "ddccd1f42755eb78"}, {"name": "emit", "duration": 1141, "timestamp": 20342744996, "id": 170, "parentId": 13, "tags": {}, "startTime": 1751138302042, "traceId": "ddccd1f42755eb78"}, {"name": "webpack-close", "duration": 175, "timestamp": 20342746489, "id": 171, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751138302043, "traceId": "ddccd1f42755eb78"}, {"name": "webpack-generate-error-stats", "duration": 546, "timestamp": 20342746669, "id": 172, "parentId": 171, "tags": {}, "startTime": 1751138302043, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 75041, "timestamp": 20342754024, "id": 183, "parentId": 174, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751138302051, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 75053, "timestamp": 20342754032, "id": 184, "parentId": 174, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751138302051, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 220235, "timestamp": 20342753999, "id": 178, "parentId": 174, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751138302051, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 254330, "timestamp": 20342754006, "id": 180, "parentId": 174, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751138302051, "traceId": "ddccd1f42755eb78"}, {"name": "build-module-ts", "duration": 21162, "timestamp": 20343014636, "id": 197, "parentId": 173, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\store\\app-store.ts", "layer": "app-pages-browser"}, "startTime": 1751138302311, "traceId": "ddccd1f42755eb78"}, {"name": "add-entry", "duration": 300973, "timestamp": 20342754003, "id": 179, "parentId": 174, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751138302051, "traceId": "ddccd1f42755eb78"}]