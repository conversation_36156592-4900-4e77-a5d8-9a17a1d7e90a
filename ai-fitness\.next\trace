[{"name": "generate-buildid", "duration": 375, "timestamp": 20454486621, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751138413784, "traceId": "878bc7efda998fbd"}, {"name": "load-custom-routes", "duration": 2312, "timestamp": 20454487082, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751138413785, "traceId": "878bc7efda998fbd"}, {"name": "create-dist-dir", "duration": 2286, "timestamp": 20454546916, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751138413845, "traceId": "878bc7efda998fbd"}, {"name": "create-pages-mapping", "duration": 182, "timestamp": 20454574618, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751138413872, "traceId": "878bc7efda998fbd"}, {"name": "collect-app-paths", "duration": 4330, "timestamp": 20454574837, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751138413873, "traceId": "878bc7efda998fbd"}, {"name": "create-app-mapping", "duration": 2199, "timestamp": 20454579211, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751138413877, "traceId": "878bc7efda998fbd"}, {"name": "public-dir-conflict-check", "duration": 509, "timestamp": 20454581947, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751138413880, "traceId": "878bc7efda998fbd"}, {"name": "generate-routes-manifest", "duration": 2428, "timestamp": 20454582607, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751138413880, "traceId": "878bc7efda998fbd"}, {"name": "create-entrypoints", "duration": 71692, "timestamp": 20454607773, "id": 14, "parentId": 1, "tags": {}, "startTime": 1751138413906, "traceId": "878bc7efda998fbd"}, {"name": "generate-webpack-config", "duration": 333517, "timestamp": 20454679506, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751138413977, "traceId": "878bc7efda998fbd"}, {"name": "next-trace-entrypoint-plugin", "duration": 1823, "timestamp": 20455122238, "id": 17, "parentId": 16, "tags": {}, "startTime": 1751138414420, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 360559, "timestamp": 20455129676, "id": 21, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 426974, "timestamp": 20455129689, "id": 22, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 426756, "timestamp": 20455129932, "id": 35, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 426997, "timestamp": 20455129700, "id": 23, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 428483, "timestamp": 20455129642, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fhealth%2Froute&name=app%2Fapi%2Fhealth%2Froute&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fapi%2Fhealth%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 489673, "timestamp": 20455129268, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496336, "timestamp": 20455129721, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fauth%2Fsignin%2Fpage&name=app%2Fauth%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fauth%2Fsignin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496339, "timestamp": 20455129732, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fauth%2Fsignup%2Fpage&name=app%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fauth%2Fsignup%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496330, "timestamp": 20455129745, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496217, "timestamp": 20455129876, "id": 28, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdemo%2Fpage&name=app%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fdemo%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496186, "timestamp": 20455129911, "id": 29, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fexercises%2F%5Bid%5D%2Fpage&name=app%2Fexercises%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fexercises%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fexercises%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496183, "timestamp": 20455129919, "id": 30, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fexercises%2Fpage&name=app%2Fexercises%2Fpage&pagePath=private-next-app-dir%2Fexercises%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fexercises%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496183, "timestamp": 20455129922, "id": 31, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496183, "timestamp": 20455129924, "id": 32, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fprogress%2Fpage&name=app%2Fprogress%2Fpage&pagePath=private-next-app-dir%2Fprogress%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fprogress%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496184, "timestamp": 20455129926, "id": 33, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fworkouts%2F%5Bid%5D%2Fpage&name=app%2Fworkouts%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fworkouts%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fworkouts%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 496186, "timestamp": 20455129929, "id": 34, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fworkouts%2Fpage&name=app%2Fworkouts%2Fpage&pagePath=private-next-app-dir%2Fworkouts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fworkouts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414428, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 734316, "timestamp": 20455129708, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fauth%2F%5B...all%5D%2Froute&name=app%2Fapi%2Fauth%2F%5B...all%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...all%5D%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5Caugment-projects%5Cfitness-singles%5Cai-fitness%5Csrc%5Capp&appPaths=%2Fapi%2Fauth%2F%5B...all%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "build-module-ts", "duration": 36774, "timestamp": 20456197091, "id": 108, "parentId": 16, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\store\\app-store.ts", "layer": "ssr"}, "startTime": 1751138415495, "traceId": "878bc7efda998fbd"}, {"name": "make", "duration": 1117252, "timestamp": 20455129003, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751138414427, "traceId": "878bc7efda998fbd"}, {"name": "get-entries", "duration": 3747, "timestamp": 20456247986, "id": 110, "parentId": 109, "tags": {}, "startTime": 1751138415546, "traceId": "878bc7efda998fbd"}, {"name": "node-file-trace-plugin", "duration": 63425, "timestamp": 20456258030, "id": 111, "parentId": 109, "tags": {"traceEntryCount": "30"}, "startTime": 1751138415556, "traceId": "878bc7efda998fbd"}, {"name": "collect-traced-files", "duration": 610, "timestamp": 20456321469, "id": 112, "parentId": 109, "tags": {}, "startTime": 1751138415619, "traceId": "878bc7efda998fbd"}, {"name": "finish-modules", "duration": 74273, "timestamp": 20456247811, "id": 109, "parentId": 17, "tags": {}, "startTime": 1751138415546, "traceId": "878bc7efda998fbd"}, {"name": "chunk-graph", "duration": 26365, "timestamp": 20456386599, "id": 114, "parentId": 113, "tags": {}, "startTime": 1751138415684, "traceId": "878bc7efda998fbd"}, {"name": "optimize-modules", "duration": 26, "timestamp": 20456413084, "id": 116, "parentId": 113, "tags": {}, "startTime": 1751138415711, "traceId": "878bc7efda998fbd"}, {"name": "optimize-chunks", "duration": 49698, "timestamp": 20456413180, "id": 117, "parentId": 113, "tags": {}, "startTime": 1751138415711, "traceId": "878bc7efda998fbd"}, {"name": "optimize-tree", "duration": 126, "timestamp": 20456462968, "id": 118, "parentId": 113, "tags": {}, "startTime": 1751138415761, "traceId": "878bc7efda998fbd"}, {"name": "optimize-chunk-modules", "duration": 83458, "timestamp": 20456463180, "id": 119, "parentId": 113, "tags": {}, "startTime": 1751138415761, "traceId": "878bc7efda998fbd"}, {"name": "optimize", "duration": 133706, "timestamp": 20456413034, "id": 115, "parentId": 113, "tags": {}, "startTime": 1751138415711, "traceId": "878bc7efda998fbd"}, {"name": "module-hash", "duration": 31867, "timestamp": 20456565511, "id": 120, "parentId": 113, "tags": {}, "startTime": 1751138415863, "traceId": "878bc7efda998fbd"}, {"name": "code-generation", "duration": 7968, "timestamp": 20456597477, "id": 121, "parentId": 113, "tags": {}, "startTime": 1751138415895, "traceId": "878bc7efda998fbd"}, {"name": "hash", "duration": 7578, "timestamp": 20456610763, "id": 122, "parentId": 113, "tags": {}, "startTime": 1751138415909, "traceId": "878bc7efda998fbd"}, {"name": "code-generation-jobs", "duration": 253, "timestamp": 20456618338, "id": 123, "parentId": 113, "tags": {}, "startTime": 1751138415916, "traceId": "878bc7efda998fbd"}, {"name": "module-assets", "duration": 528, "timestamp": 20456618544, "id": 124, "parentId": 113, "tags": {}, "startTime": 1751138415916, "traceId": "878bc7efda998fbd"}, {"name": "create-chunk-assets", "duration": 2831, "timestamp": 20456619084, "id": 125, "parentId": 113, "tags": {}, "startTime": 1751138415917, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4545, "timestamp": 20456643310, "id": 127, "parentId": 126, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4401, "timestamp": 20456643466, "id": 128, "parentId": 126, "tags": {"name": "../app/api/health/route.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4392, "timestamp": 20456643477, "id": 129, "parentId": 126, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4385, "timestamp": 20456643484, "id": 130, "parentId": 126, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4381, "timestamp": 20456643490, "id": 131, "parentId": 126, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4377, "timestamp": 20456643495, "id": 132, "parentId": 126, "tags": {"name": "../app/api/auth/[...all]/route.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4372, "timestamp": 20456643502, "id": 133, "parentId": 126, "tags": {"name": "../app/auth/signin/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4368, "timestamp": 20456643507, "id": 134, "parentId": 126, "tags": {"name": "../app/auth/signup/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4365, "timestamp": 20456643511, "id": 135, "parentId": 126, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4311, "timestamp": 20456643567, "id": 136, "parentId": 126, "tags": {"name": "../app/demo/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4295, "timestamp": 20456643583, "id": 137, "parentId": 126, "tags": {"name": "../app/exercises/[id]/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4287, "timestamp": 20456643592, "id": 138, "parentId": 126, "tags": {"name": "../app/exercises/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4284, "timestamp": 20456643597, "id": 139, "parentId": 126, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4282, "timestamp": 20456643600, "id": 140, "parentId": 126, "tags": {"name": "../app/progress/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4279, "timestamp": 20456643604, "id": 141, "parentId": 126, "tags": {"name": "../app/workouts/[id]/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4276, "timestamp": 20456643608, "id": 142, "parentId": 126, "tags": {"name": "../app/workouts/page.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4274, "timestamp": 20456643612, "id": 143, "parentId": 126, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4271, "timestamp": 20456643616, "id": 144, "parentId": 126, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 4269, "timestamp": 20456643619, "id": 145, "parentId": 126, "tags": {"name": "../vendors.js", "cache": "HIT"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 23872, "timestamp": 20456643622, "id": 146, "parentId": 126, "tags": {"name": "../common.js", "cache": "MISS"}, "startTime": 1751138415941, "traceId": "878bc7efda998fbd"}, {"name": "minify-webpack-plugin-optimize", "duration": 30725, "timestamp": 20456636795, "id": 126, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751138415935, "traceId": "878bc7efda998fbd"}, {"name": "css-minimizer-plugin", "duration": 162, "timestamp": 20456667696, "id": 147, "parentId": 16, "tags": {}, "startTime": 1751138415965, "traceId": "878bc7efda998fbd"}, {"name": "create-trace-assets", "duration": 1980, "timestamp": 20456668063, "id": 148, "parentId": 17, "tags": {}, "startTime": 1751138415966, "traceId": "878bc7efda998fbd"}, {"name": "seal", "duration": 324736, "timestamp": 20456357833, "id": 113, "parentId": 16, "tags": {}, "startTime": 1751138415656, "traceId": "878bc7efda998fbd"}, {"name": "webpack-compilation", "duration": 1608163, "timestamp": 20455120686, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751138414418, "traceId": "878bc7efda998fbd"}, {"name": "emit", "duration": 12130, "timestamp": 20456729410, "id": 149, "parentId": 13, "tags": {}, "startTime": 1751138416027, "traceId": "878bc7efda998fbd"}, {"name": "webpack-close", "duration": 43423, "timestamp": 20456744180, "id": 150, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751138416042, "traceId": "878bc7efda998fbd"}, {"name": "webpack-generate-error-stats", "duration": 2458, "timestamp": 20456787672, "id": 151, "parentId": 150, "tags": {}, "startTime": 1751138416085, "traceId": "878bc7efda998fbd"}, {"name": "make", "duration": 191, "timestamp": 20456800513, "id": 153, "parentId": 152, "tags": {}, "startTime": 1751138416098, "traceId": "878bc7efda998fbd"}, {"name": "chunk-graph", "duration": 38, "timestamp": 20456801316, "id": 155, "parentId": 154, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "optimize-modules", "duration": 10, "timestamp": 20456801410, "id": 157, "parentId": 154, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "optimize-chunks", "duration": 69, "timestamp": 20456801476, "id": 158, "parentId": 154, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "optimize-tree", "duration": 10, "timestamp": 20456801587, "id": 159, "parentId": 154, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "optimize-chunk-modules", "duration": 50, "timestamp": 20456801663, "id": 160, "parentId": 154, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "optimize", "duration": 381, "timestamp": 20456801370, "id": 156, "parentId": 154, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "module-hash", "duration": 12, "timestamp": 20456801909, "id": 161, "parentId": 154, "tags": {}, "startTime": 1751138416100, "traceId": "878bc7efda998fbd"}, {"name": "code-generation", "duration": 10, "timestamp": 20456801931, "id": 162, "parentId": 154, "tags": {}, "startTime": 1751138416100, "traceId": "878bc7efda998fbd"}, {"name": "hash", "duration": 61, "timestamp": 20456801981, "id": 163, "parentId": 154, "tags": {}, "startTime": 1751138416100, "traceId": "878bc7efda998fbd"}, {"name": "code-generation-jobs", "duration": 40, "timestamp": 20456802042, "id": 164, "parentId": 154, "tags": {}, "startTime": 1751138416100, "traceId": "878bc7efda998fbd"}, {"name": "module-assets", "duration": 16, "timestamp": 20456802073, "id": 165, "parentId": 154, "tags": {}, "startTime": 1751138416100, "traceId": "878bc7efda998fbd"}, {"name": "create-chunk-assets", "duration": 12, "timestamp": 20456802095, "id": 166, "parentId": 154, "tags": {}, "startTime": 1751138416100, "traceId": "878bc7efda998fbd"}, {"name": "minify-js", "duration": 22, "timestamp": 20456806661, "id": 168, "parentId": 167, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751138416104, "traceId": "878bc7efda998fbd"}, {"name": "minify-webpack-plugin-optimize", "duration": 519, "timestamp": 20456806171, "id": 167, "parentId": 152, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751138416104, "traceId": "878bc7efda998fbd"}, {"name": "css-minimizer-plugin", "duration": 6, "timestamp": 20456806741, "id": 169, "parentId": 152, "tags": {}, "startTime": 1751138416104, "traceId": "878bc7efda998fbd"}, {"name": "seal", "duration": 7103, "timestamp": 20456801174, "id": 154, "parentId": 152, "tags": {}, "startTime": 1751138416099, "traceId": "878bc7efda998fbd"}, {"name": "webpack-compilation", "duration": 9354, "timestamp": 20456799018, "id": 152, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751138416097, "traceId": "878bc7efda998fbd"}, {"name": "emit", "duration": 845, "timestamp": 20456808437, "id": 170, "parentId": 13, "tags": {}, "startTime": 1751138416106, "traceId": "878bc7efda998fbd"}, {"name": "webpack-close", "duration": 166, "timestamp": 20456809651, "id": 171, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751138416107, "traceId": "878bc7efda998fbd"}, {"name": "webpack-generate-error-stats", "duration": 466, "timestamp": 20456809822, "id": 172, "parentId": 171, "tags": {}, "startTime": 1751138416108, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 73247, "timestamp": 20456816727, "id": 183, "parentId": 174, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751138416114, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 73257, "timestamp": 20456816734, "id": 184, "parentId": 174, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751138416114, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 173505, "timestamp": 20456816712, "id": 178, "parentId": 174, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751138416114, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 210735, "timestamp": 20456816715, "id": 179, "parentId": 174, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751138416114, "traceId": "878bc7efda998fbd"}, {"name": "build-module-ts", "duration": 14105, "timestamp": 20457032573, "id": 197, "parentId": 173, "tags": {"name": "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\store\\app-store.ts", "layer": "app-pages-browser"}, "startTime": 1751138416330, "traceId": "878bc7efda998fbd"}, {"name": "add-entry", "duration": 240307, "timestamp": 20456816721, "id": 181, "parentId": 174, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751138416114, "traceId": "878bc7efda998fbd"}]