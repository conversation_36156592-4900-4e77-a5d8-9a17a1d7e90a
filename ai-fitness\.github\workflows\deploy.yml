name: Deploy AI-fitness-singles

on:
  push:
    branches: [main, production]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Test and Build
  test-and-build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npm run type-check

    - name: Run tests
      run: npm run test
      env:
        CI: true

    - name: Build application
      run: npm run build
      env:
        NODE_ENV: production
        NEXT_PUBLIC_APP_NAME: AI-fitness-singles

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: .next/
        retention-days: 1

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    needs: test-and-build
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run security audit
      run: npm audit --audit-level=high

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # Deploy to Vercel (Production)
  deploy-vercel:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        vercel-args: '--prod'
        working-directory: ./

  # Build and Push Docker Image
  docker-build-push:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Production Server (optional)
  deploy-production:
    runs-on: ubuntu-latest
    needs: [docker-build-push]
    if: github.ref == 'refs/heads/production'
    environment: production
    
    steps:
    - name: Deploy to production server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /opt/ai-fitness-singles
          docker-compose pull
          docker-compose up -d
          docker system prune -f

  # Lighthouse Performance Audit
  lighthouse-audit:
    runs-on: ubuntu-latest
    needs: deploy-vercel
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Wait for deployment
      run: sleep 60

    - name: Run Lighthouse audit
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          https://ai-fitness-singles.vercel.app
          https://ai-fitness-singles.vercel.app/workouts
          https://ai-fitness-singles.vercel.app/exercises
          https://ai-fitness-singles.vercel.app/progress
        configPath: './lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true

  # Notify deployment status
  notify:
    runs-on: ubuntu-latest
    needs: [deploy-vercel, lighthouse-audit]
    if: always()
    
    steps:
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
