import { QueryKey, QueryState, QueryFilters, QueryFunctionContext, Query, QueryClient } from '@tanstack/query-core';

interface PersistedQuery {
    buster: string;
    queryHash: string;
    queryKey: QueryKey;
    state: QueryState;
}
type MaybePromise<T> = T | Promise<T>;
interface AsyncStorage<TStorageValue = string> {
    getItem: (key: string) => MaybePromise<TStorageValue | undefined | null>;
    setItem: (key: string, value: TStorageValue) => MaybePromise<unknown>;
    removeItem: (key: string) => MaybePromise<void>;
    entries?: () => MaybePromise<Array<[key: string, value: TStorageValue]>>;
}
interface StoragePersisterOptions<TStorageValue = string> {
    /** The storage client used for setting and retrieving items from cache.
     * For SSR pass in `undefined`.
     */
    storage: AsyncStorage<TStorageValue> | undefined | null;
    /**
     * How to serialize the data to storage.
     * @default `JSON.stringify`
     */
    serialize?: (persistedQuery: PersistedQuery) => MaybePromise<TStorageValue>;
    /**
     * How to deserialize the data from storage.
     * @default `JSON.parse`
     */
    deserialize?: (cachedString: TStorageValue) => MaybePromise<PersistedQuery>;
    /**
     * A unique string that can be used to forcefully invalidate existing caches,
     * if they do not share the same buster string
     */
    buster?: string;
    /**
     * The max-allowed age of the cache in milliseconds.
     * If a persisted cache is found that is older than this
     * time, it will be discarded
     * @default 24 hours
     */
    maxAge?: number;
    /**
     * Prefix to be used for storage key.
     * Storage key is a combination of prefix and query hash in a form of `prefix-queryHash`.
     * @default 'tanstack-query'
     */
    prefix?: string;
    /**
     * Filters to narrow down which Queries should be persisted.
     */
    filters?: QueryFilters;
}
declare const PERSISTER_KEY_PREFIX = "tanstack-query";
/**
 * Warning: experimental feature.
 * This utility function enables fine-grained query persistence.
 * Simple add it as a `persister` parameter to `useQuery` or `defaultOptions` on `queryClient`.
 *
 * ```
 * useQuery({
     queryKey: ['myKey'],
     queryFn: fetcher,
     persister: createPersister({
       storage: localStorage,
     }),
   })
   ```
 */
declare function experimental_createQueryPersister<TStorageValue = string>({ storage, buster, maxAge, serialize, deserialize, prefix, filters, }: StoragePersisterOptions<TStorageValue>): {
    persisterFn: <T, TQueryKey extends QueryKey>(queryFn: (context: QueryFunctionContext<TQueryKey>) => T | Promise<T>, ctx: QueryFunctionContext<TQueryKey>, query: Query) => Promise<T>;
    persistQuery: (query: Query) => Promise<void>;
    persistQueryByKey: (queryKey: QueryKey, queryClient: QueryClient) => Promise<void>;
    retrieveQuery: <T>(queryHash: string, afterRestoreMacroTask?: (persistedQuery: PersistedQuery) => void) => Promise<T | undefined>;
    persisterGc: () => Promise<void>;
    restoreQueries: (queryClient: QueryClient, filters?: Pick<QueryFilters, "queryKey" | "exact">) => Promise<void>;
};

export { type AsyncStorage, type MaybePromise, PERSISTER_KEY_PREFIX, type PersistedQuery, type StoragePersisterOptions, experimental_createQueryPersister };
