### ✅ Review & Contribution Flow

- Before starting, **create an issue** for the task you want to work on.
- **Assign yourself** to the issue so it’s clear who’s working on it.
- Keep PRs focused: one issue = one PR (preferably small and scoped).
- All PRs need **at least one maintainer review**.
- We use **"Squash and merge"** to keep history clean.
- Address review comments quickly and respectfully.

---

### 🤔 Need Help?

- **General questions** → use GitHub Discussions
- **Bug reports or features** → open an Issue
- **Live chat** → [Join our Discord](https://discord.gg/NtrsUBuHUB)

---

### 📚 Useful Links

- [Feature-Sliced Design](https://feature-sliced.design/)
- [Next.js Docs](https://nextjs.org/docs)
- [Prisma Docs](https://www.prisma.io/docs/)

---

### 🌟 Recognition

We credit contributors in:

- the GitHub contributors list
- release notes (for impactful work)
- internal documentation if relevant

Thanks again for contributing to Workout Cool! 💪

Questions? Just open an issue or ping a maintainer.
