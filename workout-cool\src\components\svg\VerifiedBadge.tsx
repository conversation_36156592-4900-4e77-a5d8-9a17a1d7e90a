import type { ComponentPropsWithoutRef } from "react";

export const VerifiedBadge = (props: ComponentPropsWithoutRef<"svg">) => {
  return (
    <svg viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M8.572.149L6.32 1.89l-2.702.104a.7.7 0 00-.637.478l-.92 2.757L.258 7.036a.7.7 0 00-.105.855L1.476 10.1l-.444 2.664a.7.7 0 00.412.757l2.602 1.128 1.159 2.413a.7.7 0 00.835.366L9 16.524l2.954.902a.7.7 0 00.848-.394l.968-2.264 2.793-1.248a.7.7 0 00.405-.755l-.445-2.665 1.325-2.209a.7.7 0 00-.105-.855L15.937 5.23l-.919-2.757a.7.7 0 00-.637-.478l-2.702-.104L9.43.149a.7.7 0 00-.857 0zM5.853 9.147L7.5 10.793l4.646-4.646a.5.5 0 11.707.707L7.5 12.207 5.146 9.854a.5.5 0 01.707-.707z"></path>
    </svg>
  );
};
