// Simple custom i18n implementation for Next.js 15 compatibility

export type Locale = 'en' | 'zh';

export const locales: Locale[] = ['en', 'zh'];
export const defaultLocale: Locale = 'en';

// Get locale from cookies (client-side)
export function getLocale(): Locale {
  if (typeof window !== 'undefined') {
    const localeCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('locale='))
      ?.split('=')[1] as Locale;

    if (localeCookie && locales.includes(localeCookie)) {
      return localeCookie;
    }
  }

  return defaultLocale;
}

// Set locale in cookies
export function setLocale(locale: Locale) {
  if (typeof window !== 'undefined') {
    document.cookie = `locale=${locale}; path=/; max-age=31536000`; // 1 year
    window.location.reload();
  }
}

// Get locale from pathname
export function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/');
  const localeSegment = segments[1] as Locale;
  
  if (locales.includes(localeSegment)) {
    return localeSegment;
  }
  
  return defaultLocale;
}

// Remove locale from pathname
export function removeLocaleFromPathname(pathname: string): string {
  const segments = pathname.split('/');
  const localeSegment = segments[1] as Locale;
  
  if (locales.includes(localeSegment)) {
    return '/' + segments.slice(2).join('/');
  }
  
  return pathname;
}

// Add locale to pathname
export function addLocaleToPathname(pathname: string, locale: Locale): string {
  if (locale === defaultLocale) {
    return pathname;
  }
  
  const cleanPath = removeLocaleFromPathname(pathname);
  return `/${locale}${cleanPath}`;
}
