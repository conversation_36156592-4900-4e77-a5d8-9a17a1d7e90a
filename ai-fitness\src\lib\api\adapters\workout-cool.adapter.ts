/**
 * Adapter for workout-cool API data structures
 * Converts workout-cool data models to our frontend types
 */

import type { 
  Program, 
  Exercise, 
  WorkoutSession, 
  User,
  ProgramSession,
  ProgressStats 
} from '../types';

// workout-cool API response types
export interface WorkoutCoolProgram {
  id: string;
  title: string;
  titleEn?: string;
  description: string;
  descriptionEn?: string;
  slug: string;
  slugEn?: string;
  category: string;
  image: string;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  type: string;
  durationWeeks: number;
  sessionsPerWeek: number;
  sessionDurationMin: number;
  equipment: string[];
  isPremium: boolean;
  visibility: 'PUBLISHED' | 'DRAFT' | 'ARCHIVED';
  isActive: boolean;
  participantCount: number;
  createdAt: string;
  updatedAt: string;
  coaches?: Array<{
    id: string;
    name: string;
    image: string;
    order: number;
  }>;
  weeks?: Array<{
    id: string;
    weekNumber: number;
    sessions: WorkoutCoolProgramSession[];
  }>;
  enrollments?: Array<{ id: string }>;
}

export interface WorkoutCoolProgramSession {
  id: string;
  sessionNumber: number;
  title: string;
  titleEn?: string;
  description: string;
  descriptionEn?: string;
  estimatedMinutes: number;
  equipment: string[];
  isPremium: boolean;
  exercises: Array<{
    id: string;
    order: number;
    exercise: WorkoutCoolExercise;
    suggestedSets: Array<{
      setIndex: number;
      reps?: number;
      weight?: number;
      duration?: number;
      restTime?: number;
    }>;
  }>;
}

export interface WorkoutCoolExercise {
  id: string;
  name: string;
  nameEn?: string;
  description: string;
  descriptionEn?: string;
  instructions: string;
  instructionsEn?: string;
  tips?: string;
  tipsEn?: string;
  imageUrl?: string;
  videoUrl?: string;
  attributes: Array<{
    id: string;
    attributeName: {
      id: string;
      name: string;
      nameEn?: string;
    };
    attributeValue: {
      id: string;
      value: string;
      valueEn?: string;
    };
  }>;
}

export interface WorkoutCoolUser {
  id: string;
  email: string;
  name: string;
  firstName: string;
  lastName: string;
  image?: string;
  locale?: string;
  role: 'user' | 'admin';
  isPremium?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WorkoutCoolWorkoutSession {
  id: string;
  userId: string;
  programId?: string;
  sessionId?: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  notes?: string;
  exercises: Array<{
    id: string;
    exerciseId: string;
    order: number;
    exercise: WorkoutCoolExercise;
    sets: Array<{
      id: string;
      setIndex: number;
      reps?: number;
      weight?: number;
      duration?: number;
      restTime?: number;
      completed: boolean;
    }>;
  }>;
}

/**
 * Adapter class for converting workout-cool data to frontend types
 */
export class WorkoutCoolAdapter {
  /**
   * Convert workout-cool program to frontend Program type
   */
  static adaptProgram(wcProgram: WorkoutCoolProgram): Program {
    return {
      id: wcProgram.id,
      title: wcProgram.titleEn || wcProgram.title,
      description: wcProgram.descriptionEn || wcProgram.description,
      slug: wcProgram.slugEn || wcProgram.slug,
      category: wcProgram.category,
      image: wcProgram.image,
      difficulty: wcProgram.level.toLowerCase() as 'beginner' | 'intermediate' | 'advanced',
      duration: wcProgram.sessionDurationMin,
      durationWeeks: wcProgram.durationWeeks,
      sessionsPerWeek: wcProgram.sessionsPerWeek,
      equipment: wcProgram.equipment,
      isPremium: wcProgram.isPremium,
      isActive: wcProgram.isActive,
      participantCount: wcProgram.participantCount,
      createdAt: new Date(wcProgram.createdAt),
      updatedAt: new Date(wcProgram.updatedAt),
      coaches: wcProgram.coaches?.map(coach => ({
        id: coach.id,
        name: coach.name,
        image: coach.image,
        order: coach.order,
      })) || [],
      weeks: wcProgram.weeks?.map(week => ({
        id: week.id,
        weekNumber: week.weekNumber,
        sessions: week.sessions.map(session => this.adaptProgramSession(session)),
      })) || [],
    };
  }

  /**
   * Convert workout-cool program session to frontend ProgramSession type
   */
  static adaptProgramSession(wcSession: WorkoutCoolProgramSession): ProgramSession {
    return {
      id: wcSession.id,
      sessionNumber: wcSession.sessionNumber,
      title: wcSession.titleEn || wcSession.title,
      description: wcSession.descriptionEn || wcSession.description,
      estimatedDuration: wcSession.estimatedMinutes,
      equipment: wcSession.equipment,
      isPremium: wcSession.isPremium,
      exercises: wcSession.exercises.map(ex => ({
        id: ex.id,
        order: ex.order,
        exerciseId: ex.exercise.id,
        exercise: this.adaptExercise(ex.exercise),
        sets: ex.suggestedSets.map(set => ({
          setIndex: set.setIndex,
          reps: set.reps,
          weight: set.weight,
          duration: set.duration,
          restTime: set.restTime,
        })),
      })),
    };
  }

  /**
   * Convert workout-cool exercise to frontend Exercise type
   */
  static adaptExercise(wcExercise: WorkoutCoolExercise): Exercise {
    // Extract muscle groups and equipment from attributes
    const muscleGroups: string[] = [];
    const equipment: string[] = [];
    let category: 'strength' | 'cardio' | 'flexibility' | 'balance' = 'strength';
    let difficulty: 'beginner' | 'intermediate' | 'advanced' = 'beginner';

    wcExercise.attributes.forEach(attr => {
      const attrName = attr.attributeName.nameEn || attr.attributeName.name;
      const attrValue = attr.attributeValue.valueEn || attr.attributeValue.value;

      if (attrName.toLowerCase().includes('muscle') || attrName.toLowerCase().includes('target')) {
        muscleGroups.push(attrValue);
      } else if (attrName.toLowerCase().includes('equipment')) {
        equipment.push(attrValue);
      } else if (attrName.toLowerCase().includes('category') || attrName.toLowerCase().includes('type')) {
        if (attrValue.toLowerCase().includes('cardio')) category = 'cardio';
        else if (attrValue.toLowerCase().includes('flexibility') || attrValue.toLowerCase().includes('stretch')) category = 'flexibility';
        else if (attrValue.toLowerCase().includes('balance')) category = 'balance';
      } else if (attrName.toLowerCase().includes('difficulty') || attrName.toLowerCase().includes('level')) {
        if (attrValue.toLowerCase().includes('intermediate')) difficulty = 'intermediate';
        else if (attrValue.toLowerCase().includes('advanced')) difficulty = 'advanced';
      }
    });

    return {
      id: wcExercise.id,
      name: wcExercise.nameEn || wcExercise.name,
      description: wcExercise.descriptionEn || wcExercise.description,
      category,
      muscleGroups,
      equipment,
      difficulty,
      instructions: (wcExercise.instructionsEn || wcExercise.instructions)?.split('\n') || [],
      tips: (wcExercise.tipsEn || wcExercise.tips)?.split('\n') || [],
      imageUrl: wcExercise.imageUrl,
      videoUrl: wcExercise.videoUrl,
    };
  }

  /**
   * Convert workout-cool user to frontend User type
   */
  static adaptUser(wcUser: WorkoutCoolUser): User {
    return {
      id: wcUser.id,
      email: wcUser.email,
      name: wcUser.name,
      firstName: wcUser.firstName,
      lastName: wcUser.lastName,
      avatar: wcUser.image,
      role: wcUser.role,
      isPremium: wcUser.isPremium || false,
      preferences: {
        language: wcUser.locale || 'en',
        timezone: 'UTC',
        units: 'metric',
        notifications: {
          email: true,
          push: true,
          workout: true,
          progress: true,
        },
      },
      createdAt: new Date(wcUser.createdAt),
      updatedAt: new Date(wcUser.updatedAt),
    };
  }

  /**
   * Convert workout-cool workout session to frontend WorkoutSession type
   */
  static adaptWorkoutSession(wcSession: WorkoutCoolWorkoutSession): WorkoutSession {
    return {
      id: wcSession.id,
      userId: wcSession.userId,
      programId: wcSession.programId,
      sessionId: wcSession.sessionId,
      status: wcSession.status.toLowerCase() as 'planned' | 'in_progress' | 'completed' | 'cancelled',
      startedAt: wcSession.startedAt ? new Date(wcSession.startedAt) : undefined,
      completedAt: wcSession.completedAt ? new Date(wcSession.completedAt) : undefined,
      duration: wcSession.duration,
      notes: wcSession.notes,
      exercises: wcSession.exercises.map(ex => ({
        id: ex.id,
        exerciseId: ex.exerciseId,
        order: ex.order,
        exercise: this.adaptExercise(ex.exercise),
        sets: ex.sets.map(set => ({
          id: set.id,
          setIndex: set.setIndex,
          reps: set.reps,
          weight: set.weight,
          duration: set.duration,
          restTime: set.restTime,
          completed: set.completed,
        })),
      })),
    };
  }

  /**
   * Convert frontend data to workout-cool format for API requests
   */
  static toWorkoutCoolFormat = {
    /**
     * Convert frontend workout session to workout-cool format
     */
    workoutSession: (session: Partial<WorkoutSession>) => ({
      userId: session.userId,
      programId: session.programId,
      sessionId: session.sessionId,
      status: session.status?.toUpperCase(),
      startedAt: session.startedAt?.toISOString(),
      completedAt: session.completedAt?.toISOString(),
      duration: session.duration,
      notes: session.notes,
      exercises: session.exercises?.map(ex => ({
        exerciseId: ex.exerciseId,
        order: ex.order,
        sets: ex.sets.map(set => ({
          setIndex: set.setIndex,
          reps: set.reps,
          weight: set.weight,
          duration: set.duration,
          restTime: set.restTime,
          completed: set.completed,
        })),
      })),
    }),
  };
}
