(()=>{var e={};e.id=652,e.ids=[652],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13966:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(60687),i=r(16189),a=r(86246),n=r(44493),l=r(29523),c=r(96834),d=r(52027),o=r(70293),x=r(28559),m=r(67760),p=r(81620),u=r(97840),h=r(96474),j=r(82080),f=r(40487),g=r(85814),v=r.n(g);function N(){let e=(0,i.useParams)().id,{data:s,isLoading:r,error:g}=(0,f.A)(e);return r?(0,t.jsx)(d.AV,{}):g||!s?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(a.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)(o.Kw,{title:"Exercise not found",message:"The exercise you're looking for doesn't exist or has been removed.",onRetry:()=>window.location.reload()})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(a.V,{}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)(v(),{href:"/exercises",children:(0,t.jsxs)(l.$,{variant:"ghost",className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),"Back to Exercises"]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.ZB,{className:"text-2xl mb-2",children:s.name}),s.nameEn&&(0,t.jsx)(n.BT,{className:"text-lg",children:s.nameEn})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{variant:"outline",size:"icon",children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"icon",children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:s.attributes?.map((e,s)=>(0,t.jsx)(c.E,{variant:"secondary",children:e.attributeName?.name||e.attributeValue?.value},s))}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(l.$,{size:"lg",className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5"}),"Start Exercise"]}),(0,t.jsxs)(l.$,{variant:"outline",size:"lg",className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),"Add to Workout"]})]})]})]}),s.description&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Description"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("p",{className:"text-gray-700 leading-relaxed",children:s.description})})]}),s.introduction&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Instructions"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,t.jsx)("p",{children:s.introduction})})})]}),s.description&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Description"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,t.jsx)("p",{children:s.description})})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Exercise Info"})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Exercise ID"}),(0,t.jsx)("span",{className:"font-medium text-sm",children:s.id})]}),s.createdAt&&(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Created"}),(0,t.jsx)("span",{className:"font-medium text-sm",children:new Date(s.createdAt).toLocaleDateString()})]}),s.updatedAt&&(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Updated"}),(0,t.jsx)("span",{className:"font-medium text-sm",children:new Date(s.updatedAt).toLocaleDateString()})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Quick Actions"})}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsxs)(l.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add to Favorites"]}),(0,t.jsxs)(l.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Share Exercise"]}),(0,t.jsxs)(l.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"View Similar"]})]})]})]})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21968:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=r(65239),i=r(48088),a=r(88170),n=r.n(a),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let d={children:["",{children:["exercises",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44916)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\[id]\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/exercises/[id]/page",pathname:"/exercises/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44916:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\exercises\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\[id]\\page.tsx","default")},58971:(e,s,r)=>{Promise.resolve().then(r.bind(r,44916))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69587:(e,s,r)=>{Promise.resolve().then(r.bind(r,13966))},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[96,76],()=>r(21968));module.exports=t})();