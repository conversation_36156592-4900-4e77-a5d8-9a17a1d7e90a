name: ci

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm prisma generate

      - name: Run linting
        run: pnpm lint

  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm prisma generate

      - name: Build project
        run: pnpm build
        env:
          BETTER_AUTH_URL: http://localhost:3000
          DATABASE_URL: postgresql://user:password@localhost:5432/test_db
          GOOGLE_CLIENT_ID: test_client_id
          GOOGLE_CLIENT_SECRET: test_client_secret
          RESEND_API_KEY: re_test_key
          BETTER_AUTH_SECRET: test_secret_key_32_chars_minimum
          OPENPANEL_SECRET_KEY: test_secret
          NEXT_PUBLIC_OPENPANEL_CLIENT_ID: test_client_id
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: test_client_id
          NEXT_PUBLIC_STRIPE_PRICE_MONTHLY: test_price_monthly
          NEXT_PUBLIC_STRIPE_PRICE_YEARLY: test_price_yearly
          NEXT_PUBLIC_APP_URL: http://localhost:3000
          STRIPE_SECRET_KEY: test_secret_key
          STRIPE_WEBHOOK_SECRET: test_webhook_secret
