/**
 * Global Application State Management with Zustand
 * Handles app-wide state that needs to persist across components
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { User } from '../api/types';

// ============================================================================
// APP STATE INTERFACES
// ============================================================================

interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'zh';
  units: 'metric' | 'imperial';
  notifications: {
    workoutReminders: boolean;
    progressUpdates: boolean;
    achievements: boolean;
    marketing: boolean;
  };
  privacy: {
    shareProgress: boolean;
    showInLeaderboards: boolean;
    allowDataCollection: boolean;
  };
}

interface WorkoutPreferences {
  defaultDuration: number; // minutes
  preferredDifficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  favoriteCategories: string[];
  excludedEquipment: string[];
  restTimeBetweenSets: number; // seconds
  autoStartNextExercise: boolean;
  playWorkoutMusic: boolean;
  voiceInstructions: boolean;
}

interface UIState {
  sidebarCollapsed: boolean;
  activeWorkoutSession: string | null;
  currentPage: string;
  breadcrumbs: Array<{ label: string; href: string }>;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: number;
    read: boolean;
  }>;
  modals: {
    workoutComplete: boolean;
    goalAchieved: boolean;
    subscriptionPrompt: boolean;
  };
}

interface OfflineState {
  isOnline: boolean;
  pendingSync: Array<{
    id: string;
    type: 'workout' | 'progress' | 'goal';
    action: 'create' | 'update' | 'delete';
    data: any;
    timestamp: number;
  }>;
  lastSyncTime: number | null;
}

interface AppState {
  // User & Auth
  user: User | null;
  isAuthenticated: boolean;
  
  // App Settings
  settings: AppSettings;
  workoutPreferences: WorkoutPreferences;
  
  // UI State
  ui: UIState;
  
  // Offline Support
  offline: OfflineState;
  
  // Actions
  setUser: (user: User | null) => void;
  setAuthenticated: (authenticated: boolean) => void;
  updateSettings: (settings: Partial<AppSettings>) => void;
  updateWorkoutPreferences: (preferences: Partial<WorkoutPreferences>) => void;
  updateUIState: (ui: Partial<UIState>) => void;
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  setOnlineStatus: (isOnline: boolean) => void;
  addPendingSync: (item: Omit<OfflineState['pendingSync'][0], 'id' | 'timestamp'>) => void;
  removePendingSync: (id: string) => void;
  clearPendingSync: () => void;
  updateLastSyncTime: () => void;
  reset: () => void;
}

// ============================================================================
// DEFAULT VALUES
// ============================================================================

const defaultSettings: AppSettings = {
  theme: 'system',
  language: 'en',
  units: 'metric',
  notifications: {
    workoutReminders: true,
    progressUpdates: true,
    achievements: true,
    marketing: false,
  },
  privacy: {
    shareProgress: false,
    showInLeaderboards: true,
    allowDataCollection: true,
  },
};

const defaultWorkoutPreferences: WorkoutPreferences = {
  defaultDuration: 45,
  preferredDifficulty: 'INTERMEDIATE',
  favoriteCategories: [],
  excludedEquipment: [],
  restTimeBetweenSets: 60,
  autoStartNextExercise: false,
  playWorkoutMusic: true,
  voiceInstructions: false,
};

const defaultUIState: UIState = {
  sidebarCollapsed: false,
  activeWorkoutSession: null,
  currentPage: '/',
  breadcrumbs: [],
  notifications: [],
  modals: {
    workoutComplete: false,
    goalAchieved: false,
    subscriptionPrompt: false,
  },
};

const defaultOfflineState: OfflineState = {
  isOnline: true,
  pendingSync: [],
  lastSyncTime: null,
};

// ============================================================================
// ZUSTAND STORE
// ============================================================================

export const useAppStore = create<AppState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      settings: defaultSettings,
      workoutPreferences: defaultWorkoutPreferences,
      ui: defaultUIState,
      offline: defaultOfflineState,

      // Actions
      setUser: (user) => set((state) => {
        state.user = user;
      }),

      setAuthenticated: (authenticated) => set((state) => {
        state.isAuthenticated = authenticated;
        if (!authenticated) {
          state.user = null;
        }
      }),

      updateSettings: (newSettings) => set((state) => {
        Object.assign(state.settings, newSettings);
      }),

      updateWorkoutPreferences: (newPreferences) => set((state) => {
        Object.assign(state.workoutPreferences, newPreferences);
      }),

      updateUIState: (newUIState) => set((state) => {
        Object.assign(state.ui, newUIState);
      }),

      addNotification: (notification) => set((state) => {
        // Ensure notifications array exists
        if (!state.ui.notifications) {
          state.ui.notifications = [];
        }

        const newNotification = {
          ...notification,
          id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          read: false,
        };
        state.ui.notifications.unshift(newNotification);

        // Keep only last 50 notifications
        if (state.ui.notifications.length > 50) {
          state.ui.notifications = state.ui.notifications.slice(0, 50);
        }
      }),

      markNotificationRead: (id) => set((state) => {
        // Ensure notifications array exists
        if (!state.ui.notifications) {
          state.ui.notifications = [];
          return;
        }

        const notification = state.ui.notifications.find(n => n.id === id);
        if (notification) {
          notification.read = true;
        }
      }),

      clearNotifications: () => set((state) => {
        state.ui.notifications = [];
      }),

      setOnlineStatus: (isOnline) => set((state) => {
        state.offline.isOnline = isOnline;
      }),

      addPendingSync: (item) => set((state) => {
        const newItem = {
          ...item,
          id: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
        };
        state.offline.pendingSync.push(newItem);
      }),

      removePendingSync: (id) => set((state) => {
        state.offline.pendingSync = state.offline.pendingSync.filter(item => item.id !== id);
      }),

      clearPendingSync: () => set((state) => {
        state.offline.pendingSync = [];
      }),

      updateLastSyncTime: () => set((state) => {
        state.offline.lastSyncTime = Date.now();
      }),

      reset: () => set(() => ({
        user: null,
        isAuthenticated: false,
        settings: defaultSettings,
        workoutPreferences: defaultWorkoutPreferences,
        ui: defaultUIState,
        offline: defaultOfflineState,
      })),
    })),
    {
      name: 'ai-fitness-app-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Only persist certain parts of the state
        settings: state.settings,
        workoutPreferences: state.workoutPreferences,
        ui: {
          sidebarCollapsed: state.ui.sidebarCollapsed,
          // Don't persist notifications and modals
          notifications: [], // Always initialize as empty array
        },
        offline: {
          pendingSync: state.offline.pendingSync,
          lastSyncTime: state.offline.lastSyncTime,
          // Don't persist online status
        },
      }),
    }
  )
);

// ============================================================================
// SELECTORS
// ============================================================================

// Convenience selectors for commonly used state
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useSettings = () => useAppStore((state) => state.settings);
export const useWorkoutPreferences = () => useAppStore((state) => state.workoutPreferences);
export const useUIState = () => useAppStore((state) => state.ui);
export const useNotifications = () => useAppStore((state) => state.ui.notifications || []);
export const useOfflineState = () => useAppStore((state) => state.offline);

// Computed selectors
export const useUnreadNotificationCount = () =>
  useAppStore((state) => (state.ui.notifications || []).filter(n => !n.read).length);

export const useHasPendingSync = () => 
  useAppStore((state) => state.offline.pendingSync.length > 0);

export const useIsOffline = () => 
  useAppStore((state) => !state.offline.isOnline);

// ============================================================================
// STORE ACTIONS
// ============================================================================

// Export actions for use outside of components
export const appActions = {
  setUser: (user: User | null) => useAppStore.getState().setUser(user),
  setAuthenticated: (authenticated: boolean) => useAppStore.getState().setAuthenticated(authenticated),
  updateSettings: (settings: Partial<AppSettings>) => useAppStore.getState().updateSettings(settings),
  updateWorkoutPreferences: (preferences: Partial<WorkoutPreferences>) => useAppStore.getState().updateWorkoutPreferences(preferences),
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => useAppStore.getState().addNotification(notification),
  setOnlineStatus: (isOnline: boolean) => useAppStore.getState().setOnlineStatus(isOnline),
  addPendingSync: (item: Omit<OfflineState['pendingSync'][0], 'id' | 'timestamp'>) => useAppStore.getState().addPendingSync(item),
  removePendingSync: (id: string) => useAppStore.getState().removePendingSync(id),
  clearPendingSync: () => useAppStore.getState().clearPendingSync(),
  updateLastSyncTime: () => useAppStore.getState().updateLastSyncTime(),
  reset: () => useAppStore.getState().reset(),
};

// ============================================================================
// TYPES EXPORT
// ============================================================================

export type { AppSettings, WorkoutPreferences, UIState, OfflineState };
