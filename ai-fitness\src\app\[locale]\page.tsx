import { Metadata } from 'next';
import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { generatePageMetadata, commonFitnessQAs } from "@/lib/seo/utils"
import { FAQStructuredData } from "@/components/seo/structured-data"
import { MuiHome } from '@/components/pages/mui-home';

type Props = {
  params: { locale: string };
};

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'homepage' });

  return generatePageMetadata({
    title: `${t('title')} - ${t('subtitle')}`,
    description: t('description'),
    path: "/",
  });
}

export default function Home({ params: { locale } }: Props) {
  return (
    <>
      <MuiHome />
      <FAQStructuredData faqs={commonFitnessQAs} />
    </>
  );
}