(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[142],{4403:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>y});var l=a(5155),i=a(2115),t=a(7516),r=a(6695),c=a(5220),n=a(6126),d=a(2523),x=a(7023),m=a(9579),o=a(4092),h=a(7924),u=a(6932),j=a(4416),g=a(1976),v=a(5690),p=a(4616),N=a(4227);function y(){var e,s,a,y,f;let[b,w]=(0,i.useState)(""),[k,C]=(0,i.useState)(!1),[E,A]=(0,i.useState)({equipment:[],muscles:[],difficulty:[],category:[]}),{data:q,isLoading:S}=(0,N.IS)(b,20,b.length>0),{data:$,isLoading:_,error:z}=(0,N.Fb)(0===b.length?E:{},0===b.length),{data:L,isLoading:F}=(0,N.d9)(),O=b.length>0?q:null==$?void 0:$.data,V=b.length>0?S:_,D=(e,s)=>{A(a=>{let l=a[e]||[],i=l.includes(s)?l.filter(e=>e!==s):[...l,s];return{...a,[e]:i}})},T=Object.values(E).some(e=>Array.isArray(e)&&e.length>0);return F?(0,l.jsx)(x.AV,{}):(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)(t.V,{}),(0,l.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Exercise Library"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover thousands of exercises with detailed instructions, muscle targeting, and difficulty levels"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-8",children:[(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,l.jsxs)("div",{className:"flex-1 relative",children:[(0,l.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,l.jsx)(d.p,{type:"text",placeholder:"Search exercises...",value:b,onChange:e=>w(e.target.value),className:"pl-10"})]}),(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>C(!k),className:"flex items-center gap-2",children:[(0,l.jsx)(u.A,{className:"h-4 w-4"}),"Filters",T&&(0,l.jsx)(n.E,{variant:"secondary",className:"ml-1",children:Object.values(E).reduce((e,s)=>e+((null==s?void 0:s.length)||0),0)})]})]}),k&&L&&(0,l.jsxs)("div",{className:"border-t pt-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Equipment"}),(0,l.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:null==(e=L.equipment)?void 0:e.map(e=>{var s;return(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:(null==(s=E.equipment)?void 0:s.includes(e.id))||!1,onChange:()=>D("equipment",e.id),className:"rounded border-gray-300"}),(0,l.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Muscles"}),(0,l.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:null==(s=L.muscles)?void 0:s.map(e=>{var s;return(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:(null==(s=E.muscles)?void 0:s.includes(e.id))||!1,onChange:()=>D("muscles",e.id),className:"rounded border-gray-300"}),(0,l.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Categories"}),(0,l.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:null==(a=L.categories)?void 0:a.map(e=>{var s;return(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:(null==(s=E.category)?void 0:s.includes(e.id))||!1,onChange:()=>D("category",e.id),className:"rounded border-gray-300"}),(0,l.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Difficulty"}),(0,l.jsx)("div",{className:"space-y-2",children:null==(y=L.difficulties)?void 0:y.map(e=>{var s;return(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:(null==(s=E.difficulty)?void 0:s.includes(e.id))||!1,onChange:()=>D("difficulty",e.id),className:"rounded border-gray-300"}),(0,l.jsx)("span",{className:"text-sm text-gray-700",children:e.name})]},e.id)})})]})]}),T&&(0,l.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,l.jsxs)(c.$,{variant:"outline",onClick:()=>{A({equipment:[],muscles:[],difficulty:[],category:[]})},className:"flex items-center gap-2",children:[(0,l.jsx)(j.A,{className:"h-4 w-4"}),"Clear Filters"]})})]})]}),V?(0,l.jsx)(x.z0,{}):z?(0,l.jsx)(m.Kw,{title:"Failed to load exercises",message:"Please try again later",onRetry:()=>window.location.reload()}):O&&0!==O.length?(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:O.map(e=>{var s;return(0,l.jsxs)(r.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,l.jsx)(r.aR,{children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(r.ZB,{className:"text-lg",children:e.name}),(0,l.jsx)(r.BT,{children:e.nameEn})]}),(0,l.jsx)(c.$,{variant:"ghost",size:"icon",children:(0,l.jsx)(g.A,{className:"h-4 w-4"})})]})}),(0,l.jsx)(r.Wu,{children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[null==(s=e.attributes)?void 0:s.slice(0,3).map((e,s)=>{var a,i;return(0,l.jsx)(n.E,{variant:"secondary",className:"text-xs",children:(null==(a=e.attributeName)?void 0:a.name)||(null==(i=e.attributeValue)?void 0:i.value)},s)}),e.attributes&&e.attributes.length>3&&(0,l.jsxs)(n.E,{variant:"outline",className:"text-xs",children:["+",e.attributes.length-3," more"]})]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(c.$,{size:"sm",className:"flex-1",children:[(0,l.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",children:(0,l.jsx)(p.A,{className:"h-4 w-4"})})]})]})})]},e.id)})}):b?(0,l.jsx)(o.wT,{searchTerm:b,onClearSearch:()=>w("")}):(0,l.jsx)(o.d2,{}),O&&O.length>0&&(null==$||null==(f=$.pagination)?void 0:f.hasNext)&&(0,l.jsx)("div",{className:"text-center mt-8",children:(0,l.jsx)(c.$,{variant:"outline",size:"lg",children:"Load More Exercises"})})]})]})}},8602:(e,s,a)=>{Promise.resolve().then(a.bind(a,4403))}},e=>{var s=s=>e(e.s=s);e.O(0,[76,96,358],()=>s(8602)),_N_E=e.O()}]);