export declare const data: {
    supplemental: {
        languageMatching: {
            'written-new': readonly [{
                readonly paradigmLocales: {
                    readonly _locales: "en en_GB es es_419 pt_BR pt_PT";
                };
            }, {
                readonly $enUS: {
                    readonly _value: "AS+CA+GU+MH+MP+PH+PR+UM+US+VI";
                };
            }, {
                readonly $cnsar: {
                    readonly _value: "HK+MO";
                };
            }, {
                readonly $americas: {
                    readonly _value: "019";
                };
            }, {
                readonly $maghreb: {
                    readonly _value: "MA+DZ+TN+LY+MR+EH";
                };
            }, {
                readonly no: {
                    readonly _desired: "nb";
                    readonly _distance: "1";
                };
            }, {
                readonly bs: {
                    readonly _desired: "hr";
                    readonly _distance: "4";
                };
            }, {
                readonly bs: {
                    readonly _desired: "sh";
                    readonly _distance: "4";
                };
            }, {
                readonly hr: {
                    readonly _desired: "sh";
                    readonly _distance: "4";
                };
            }, {
                readonly sr: {
                    readonly _desired: "sh";
                    readonly _distance: "4";
                };
            }, {
                readonly aa: {
                    readonly _desired: "ssy";
                    readonly _distance: "4";
                };
            }, {
                readonly de: {
                    readonly _desired: "gsw";
                    readonly _distance: "4";
                    readonly _oneway: "true";
                };
            }, {
                readonly de: {
                    readonly _desired: "lb";
                    readonly _distance: "4";
                    readonly _oneway: "true";
                };
            }, {
                readonly no: {
                    readonly _desired: "da";
                    readonly _distance: "8";
                };
            }, {
                readonly nb: {
                    readonly _desired: "da";
                    readonly _distance: "8";
                };
            }, {
                readonly ru: {
                    readonly _desired: "ab";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ach";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly nl: {
                    readonly _desired: "af";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ak";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "am";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly es: {
                    readonly _desired: "ay";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "az";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ur: {
                    readonly _desired: "bal";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "be";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "bem";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly hi: {
                    readonly _desired: "bh";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "bn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "bo";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "br";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly es: {
                    readonly _desired: "ca";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly fil: {
                    readonly _desired: "ceb";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "chr";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ckb";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "co";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "crs";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly sk: {
                    readonly _desired: "cs";
                    readonly _distance: "20";
                };
            }, {
                readonly en: {
                    readonly _desired: "cy";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ee";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "eo";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly es: {
                    readonly _desired: "eu";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly da: {
                    readonly _desired: "fo";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly nl: {
                    readonly _desired: "fy";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ga";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "gaa";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "gd";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly es: {
                    readonly _desired: "gl";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly es: {
                    readonly _desired: "gn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly hi: {
                    readonly _desired: "gu";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ha";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "haw";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "ht";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "hy";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ia";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ig";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "is";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly id: {
                    readonly _desired: "jv";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ka";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "kg";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "kk";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "km";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "kn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "kri";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly tr: {
                    readonly _desired: "ku";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "ky";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly it: {
                    readonly _desired: "la";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "lg";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "ln";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "lo";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "loz";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "lua";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly hi: {
                    readonly _desired: "mai";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "mfe";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "mg";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "mi";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ml";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "mn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly hi: {
                    readonly _desired: "mr";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly id: {
                    readonly _desired: "ms";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "mt";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "my";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ne";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly nb: {
                    readonly _desired: "nn";
                    readonly _distance: "20";
                };
            }, {
                readonly no: {
                    readonly _desired: "nn";
                    readonly _distance: "20";
                };
            }, {
                readonly en: {
                    readonly _desired: "nso";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ny";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "nyn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "oc";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "om";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "or";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "pa";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "pcm";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ps";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly es: {
                    readonly _desired: "qu";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly de: {
                    readonly _desired: "rm";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "rn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "rw";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly hi: {
                    readonly _desired: "sa";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "sd";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "si";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "sn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "so";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "sq";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "st";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly id: {
                    readonly _desired: "su";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "sw";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ta";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "te";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "tg";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ti";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "tk";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "tlh";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "tn";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "to";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "tt";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "tum";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "ug";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "uk";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "ur";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ru: {
                    readonly _desired: "uz";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly fr: {
                    readonly _desired: "wo";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "xh";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "yi";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "yo";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "za";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly en: {
                    readonly _desired: "zu";
                    readonly _distance: "30";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "aao";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "abh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "abv";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "acm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "acq";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "acw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "acx";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "acy";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "adf";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "aeb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "aec";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "afb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ajp";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "apc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "apd";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "arq";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ars";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ary";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "arz";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "auz";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "avl";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ayh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ayl";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ayn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ayp";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "bbz";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "pga";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "shu";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ar: {
                    readonly _desired: "ssh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly az: {
                    readonly _desired: "azb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly et: {
                    readonly _desired: "vro";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "ffm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fub";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fue";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fuf";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fuh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fui";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fuq";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ff: {
                    readonly _desired: "fuv";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly gn: {
                    readonly _desired: "gnw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly gn: {
                    readonly _desired: "gui";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly gn: {
                    readonly _desired: "gun";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly gn: {
                    readonly _desired: "nhd";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly iu: {
                    readonly _desired: "ikt";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "enb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "eyo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "niq";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "oki";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "pko";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "sgc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "tec";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kln: {
                    readonly _desired: "tuy";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kok: {
                    readonly _desired: "gom";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly kpe: {
                    readonly _desired: "gkp";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "ida";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lkb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lko";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lks";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lri";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lrm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lsm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lto";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lts";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "lwg";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "nle";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "nyd";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly luy: {
                    readonly _desired: "rag";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly lv: {
                    readonly _desired: "ltg";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "bhr";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "bjq";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "bmm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "bzc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "msh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "skg";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "tdx";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "tkg";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "txy";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "xmv";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mg: {
                    readonly _desired: "xmw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly mn: {
                    readonly _desired: "mvf";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "bjn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "btj";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "bve";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "bvu";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "coa";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "dup";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "hji";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "id";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "jak";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "jax";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "kvb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "kvr";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "kxd";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "lce";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "lcf";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "liw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "max";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "meo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "mfa";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "mfb";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "min";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "mqg";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "msi";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "mui";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "orn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "ors";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "pel";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "pse";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "tmw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "urk";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "vkk";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "vkt";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "xmm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "zlm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ms: {
                    readonly _desired: "zmi";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ne: {
                    readonly _desired: "dty";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly om: {
                    readonly _desired: "gax";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly om: {
                    readonly _desired: "hae";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly om: {
                    readonly _desired: "orc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly or: {
                    readonly _desired: "spv";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ps: {
                    readonly _desired: "pbt";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly ps: {
                    readonly _desired: "pst";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qub";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qud";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "quf";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qug";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "quh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "quk";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qul";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qup";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qur";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qus";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "quw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qux";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "quy";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qva";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qve";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvi";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvj";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvl";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvm";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvp";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvs";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qvz";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qwa";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qwc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qwh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qws";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxa";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxl";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxp";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxr";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxt";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxu";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly qu: {
                    readonly _desired: "qxw";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly sc: {
                    readonly _desired: "sdc";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly sc: {
                    readonly _desired: "sdn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly sc: {
                    readonly _desired: "sro";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly sq: {
                    readonly _desired: "aae";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly sq: {
                    readonly _desired: "aat";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly sq: {
                    readonly _desired: "aln";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly syr: {
                    readonly _desired: "aii";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly uz: {
                    readonly _desired: "uzs";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly yi: {
                    readonly _desired: "yih";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "cdo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "cjy";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "cpx";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "czh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "czo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "gan";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "hak";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "hsn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "lzh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "mnp";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "nan";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "wuu";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly zh: {
                    readonly _desired: "yue";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly '*': {
                    readonly _desired: "*";
                    readonly _distance: "80";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "am-Ethi";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ru-Cyrl': {
                    readonly _desired: "az-Latn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "bn-Beng";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'zh-Hans': {
                    readonly _desired: "bo-Tibt";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ru-Cyrl': {
                    readonly _desired: "hy-Armn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ka-Geor";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "km-Khmr";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "kn-Knda";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "lo-Laoo";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ml-Mlym";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "my-Mymr";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ne-Deva";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "or-Orya";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "pa-Guru";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ps-Arab";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "sd-Arab";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "si-Sinh";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ta-Taml";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "te-Telu";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ti-Ethi";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ru-Cyrl': {
                    readonly _desired: "tk-Latn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "ur-Arab";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ru-Cyrl': {
                    readonly _desired: "uz-Latn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'en-Latn': {
                    readonly _desired: "yi-Hebr";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'sr-Cyrl': {
                    readonly _desired: "sr-Latn";
                    readonly _distance: "5";
                };
            }, {
                readonly 'zh-Hans': {
                    readonly _desired: "za-Latn";
                    readonly _distance: "10";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'zh-Hans': {
                    readonly _desired: "zh-Hani";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'zh-Hant': {
                    readonly _desired: "zh-Hani";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ar-Arab': {
                    readonly _desired: "ar-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'bn-Beng': {
                    readonly _desired: "bn-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'gu-Gujr': {
                    readonly _desired: "gu-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'hi-Deva': {
                    readonly _desired: "hi-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'kn-Knda': {
                    readonly _desired: "kn-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ml-Mlym': {
                    readonly _desired: "ml-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'mr-Deva': {
                    readonly _desired: "mr-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ta-Taml': {
                    readonly _desired: "ta-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'te-Telu': {
                    readonly _desired: "te-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'zh-Hans': {
                    readonly _desired: "zh-Latn";
                    readonly _distance: "20";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Jpan': {
                    readonly _desired: "ja-Latn";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Jpan': {
                    readonly _desired: "ja-Hani";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Jpan': {
                    readonly _desired: "ja-Hira";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Jpan': {
                    readonly _desired: "ja-Kana";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Jpan': {
                    readonly _desired: "ja-Hrkt";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Hrkt': {
                    readonly _desired: "ja-Hira";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ja-Hrkt': {
                    readonly _desired: "ja-Kana";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ko-Kore': {
                    readonly _desired: "ko-Hani";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ko-Kore': {
                    readonly _desired: "ko-Hang";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ko-Kore': {
                    readonly _desired: "ko-Jamo";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly 'ko-Hang': {
                    readonly _desired: "ko-Jamo";
                    readonly _distance: "5";
                    readonly _oneway: "true";
                };
            }, {
                readonly '*-*': {
                    readonly _desired: "*-*";
                    readonly _distance: "50";
                };
            }, {
                readonly 'ar-*-$maghreb': {
                    readonly _desired: "ar-*-$maghreb";
                    readonly _distance: "4";
                };
            }, {
                readonly 'ar-*-$!maghreb': {
                    readonly _desired: "ar-*-$!maghreb";
                    readonly _distance: "4";
                };
            }, {
                readonly 'ar-*-*': {
                    readonly _desired: "ar-*-*";
                    readonly _distance: "5";
                };
            }, {
                readonly 'en-*-$enUS': {
                    readonly _desired: "en-*-$enUS";
                    readonly _distance: "4";
                };
            }, {
                readonly 'en-*-GB': {
                    readonly _desired: "en-*-$!enUS";
                    readonly _distance: "3";
                };
            }, {
                readonly 'en-*-$!enUS': {
                    readonly _desired: "en-*-$!enUS";
                    readonly _distance: "4";
                };
            }, {
                readonly 'en-*-*': {
                    readonly _desired: "en-*-*";
                    readonly _distance: "5";
                };
            }, {
                readonly 'es-*-$americas': {
                    readonly _desired: "es-*-$americas";
                    readonly _distance: "4";
                };
            }, {
                readonly 'es-*-$!americas': {
                    readonly _desired: "es-*-$!americas";
                    readonly _distance: "4";
                };
            }, {
                readonly 'es-*-*': {
                    readonly _desired: "es-*-*";
                    readonly _distance: "5";
                };
            }, {
                readonly 'pt-*-$americas': {
                    readonly _desired: "pt-*-$americas";
                    readonly _distance: "4";
                };
            }, {
                readonly 'pt-*-$!americas': {
                    readonly _desired: "pt-*-$!americas";
                    readonly _distance: "4";
                };
            }, {
                readonly 'pt-*-*': {
                    readonly _desired: "pt-*-*";
                    readonly _distance: "5";
                };
            }, {
                readonly 'zh-Hant-$cnsar': {
                    readonly _desired: "zh-Hant-$cnsar";
                    readonly _distance: "4";
                };
            }, {
                readonly 'zh-Hant-$!cnsar': {
                    readonly _desired: "zh-Hant-$!cnsar";
                    readonly _distance: "4";
                };
            }, {
                readonly 'zh-Hant-*': {
                    readonly _desired: "zh-Hant-*";
                    readonly _distance: "5";
                };
            }, {
                readonly '*-*-*': {
                    readonly _desired: "*-*-*";
                    readonly _distance: "4";
                };
            }];
        };
    };
};
