module.exports = {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@tanstack_query-devtools_build_1847f53a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@tanstack_query-devtools_build_0c634a60._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-ssr] (ecmascript)");
    });
});
}}),
"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}}),

};