import { create } from 'zustand'
import { WorkoutPlan, WorkoutSession, Exercise } from '@/types'

interface WorkoutState {
  workoutPlans: WorkoutPlan[]
  currentSession: WorkoutSession | null
  exercises: Exercise[]
  isLoading: boolean
  error: string | null
}

interface WorkoutActions {
  setWorkoutPlans: (plans: WorkoutPlan[]) => void
  setCurrentSession: (session: WorkoutSession | null) => void
  setExercises: (exercises: Exercise[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  startWorkout: (planId: string) => Promise<void>
  completeWorkout: () => Promise<void>
  pauseWorkout: () => void
  resumeWorkout: () => void
  addWorkoutPlan: (plan: Omit<WorkoutPlan, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateWorkoutPlan: (id: string, plan: Partial<WorkoutPlan>) => Promise<void>
  deleteWorkoutPlan: (id: string) => Promise<void>
  fetchWorkoutPlans: () => Promise<void>
  fetchExercises: () => Promise<void>
}

type WorkoutStore = WorkoutState & WorkoutActions

export const useWorkoutStore = create<WorkoutStore>((set, get) => ({
  // State
  workoutPlans: [],
  currentSession: null,
  exercises: [],
  isLoading: false,
  error: null,

  // Actions
  setWorkoutPlans: (workoutPlans) => set({ workoutPlans }),
  setCurrentSession: (currentSession) => set({ currentSession }),
  setExercises: (exercises) => set({ exercises }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),

  startWorkout: async (planId) => {
    set({ isLoading: true, error: null })
    try {
      const { workoutPlans } = get()
      const plan = workoutPlans.find(p => p.id === planId)
      
      if (!plan) {
        throw new Error('Workout plan not found')
      }

      const session: WorkoutSession = {
        id: `session-${Date.now()}`,
        userId: 'current-user', // TODO: Get from auth store
        workoutPlanId: planId,
        workoutPlan: plan,
        startTime: new Date(),
        status: 'in-progress',
        exercises: plan.exercises.map(ex => ({
          exerciseId: ex.exerciseId,
          exercise: ex.exercise,
          sets: Array.from({ length: ex.sets }, () => ({
            reps: ex.reps,
            weight: ex.weight,
            duration: ex.duration,
            restTime: ex.restTime,
            completed: false,
          })),
          completed: false,
        })),
      }

      set({ currentSession: session, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to start workout',
        isLoading: false 
      })
    }
  },

  completeWorkout: async () => {
    const { currentSession } = get()
    if (!currentSession) return

    set({ isLoading: true })
    try {
      const completedSession: WorkoutSession = {
        ...currentSession,
        endTime: new Date(),
        status: 'completed',
      }

      // TODO: Save to backend
      set({ currentSession: null, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to complete workout',
        isLoading: false 
      })
    }
  },

  pauseWorkout: () => {
    const { currentSession } = get()
    if (currentSession) {
      set({ 
        currentSession: { 
          ...currentSession, 
          status: 'planned' 
        } 
      })
    }
  },

  resumeWorkout: () => {
    const { currentSession } = get()
    if (currentSession) {
      set({ 
        currentSession: { 
          ...currentSession, 
          status: 'in-progress' 
        } 
      })
    }
  },

  addWorkoutPlan: async (planData) => {
    set({ isLoading: true, error: null })
    try {
      const newPlan: WorkoutPlan = {
        ...planData,
        id: `plan-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      const { workoutPlans } = get()
      set({ 
        workoutPlans: [...workoutPlans, newPlan],
        isLoading: false 
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to add workout plan',
        isLoading: false 
      })
    }
  },

  updateWorkoutPlan: async (id, planData) => {
    set({ isLoading: true, error: null })
    try {
      const { workoutPlans } = get()
      const updatedPlans = workoutPlans.map(plan => 
        plan.id === id 
          ? { ...plan, ...planData, updatedAt: new Date() }
          : plan
      )

      set({ 
        workoutPlans: updatedPlans,
        isLoading: false 
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update workout plan',
        isLoading: false 
      })
    }
  },

  deleteWorkoutPlan: async (id) => {
    set({ isLoading: true, error: null })
    try {
      const { workoutPlans } = get()
      const filteredPlans = workoutPlans.filter(plan => plan.id !== id)

      set({ 
        workoutPlans: filteredPlans,
        isLoading: false 
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete workout plan',
        isLoading: false 
      })
    }
  },

  fetchWorkoutPlans: async () => {
    set({ isLoading: true, error: null })
    try {
      // TODO: Implement actual API call
      // Mock data for now
      const mockPlans: WorkoutPlan[] = []
      
      set({ 
        workoutPlans: mockPlans,
        isLoading: false 
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch workout plans',
        isLoading: false 
      })
    }
  },

  fetchExercises: async () => {
    set({ isLoading: true, error: null })
    try {
      // TODO: Implement actual API call
      // Mock data for now
      const mockExercises: Exercise[] = []
      
      set({ 
        exercises: mockExercises,
        isLoading: false 
      })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch exercises',
        isLoading: false 
      })
    }
  },
}))
