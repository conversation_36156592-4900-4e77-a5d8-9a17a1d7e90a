{"name": "ai-fitness", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "deploy": "./scripts/deploy.sh", "deploy:vercel": "./scripts/deploy.sh production vercel", "deploy:docker": "./scripts/deploy.sh production docker", "docker:build": "docker build -t ai-fitness-singles .", "docker:run": "docker run -p 3000:3000 ai-fitness-singles", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "health-check": "curl -f http://localhost:3000/api/health || exit 1", "analyze": "cross-env ANALYZE=true npm run build", "clean": "rm -rf .next out dist", "postinstall": "echo 'Installation completed successfully'"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/query-sync-storage-persister": "^5.81.5", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-query-persist-client": "^5.81.5", "axios": "^1.10.0", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.19.2", "immer": "^10.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.3", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "typescript": "^5"}}