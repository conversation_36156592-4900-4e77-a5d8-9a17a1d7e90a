(()=>{var e={};e.id=436,e.ids=[436],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6994:(e,s,t)=>{Promise.resolve().then(t.bind(t,57858))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23920:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\demo\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},57858:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var n=t(60687),i=t(43210),a=t(86246),r=t(29523),l=t(96834),c=t(44493),d=t(87891),o=t(70663),m=t(84027),x=t(78122),h=t(48730),u=t(97051),j=t(22511),f=t(5336),p=t(93613),g=t(52316),N=t(61604);function y(){let[e,s]=(0,i.useState)(!1),t=(0,g.eC)(),a=(0,N.dW)(),u=(0,g.wZ)(),{syncPendingData:j,forceSyncAll:f,clearPendingSync:p}=(0,N.aF)(),y=async()=>{try{await j()}catch(e){console.error("Manual sync failed:",e)}},v=async()=>{try{await f()}catch(e){console.error("Force sync failed:",e)}};return(0,n.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(c.aR,{className:"pb-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[t.isOnline?(0,n.jsx)(d.A,{className:"h-4 w-4 text-green-600"}):(0,n.jsx)(o.A,{className:"h-4 w-4 text-red-600"}),(0,n.jsx)(c.ZB,{className:"text-sm",children:t.isOnline?"Online":"Offline"})]}),(0,n.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>s(!e),children:(0,n.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,n.jsx)(c.BT,{children:u?(0,n.jsxs)("span",{className:"text-orange-600",children:[t.pendingSync.length," items pending sync"]}):"All data synchronized"})]}),e&&(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,n.jsx)("span",{children:"Sync Status:"}),(0,n.jsx)(l.E,{variant:u?"destructive":"default",children:u?"Pending":"Up to date"})]}),t.lastSyncTime&&(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,n.jsx)("span",{children:"Last Sync:"}),(0,n.jsx)("span",{className:"text-gray-600",children:new Date(t.lastSyncTime).toLocaleTimeString()})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,n.jsx)("span",{children:"Pending Items:"}),(0,n.jsx)("span",{className:"text-gray-600",children:t.pendingSync.length})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(r.$,{onClick:y,disabled:!t.isOnline||a.syncInProgress,className:"w-full",size:"sm",children:a.syncInProgress?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(x.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Syncing..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sync Now"]})}),u&&(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(r.$,{onClick:v,disabled:!t.isOnline,variant:"outline",size:"sm",className:"flex-1",children:"Force Sync"}),(0,n.jsx)(r.$,{onClick:p,variant:"destructive",size:"sm",className:"flex-1",children:"Clear Queue"})]})]}),t.pendingSync.length>0&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Pending Items:"}),(0,n.jsxs)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:[t.pendingSync.slice(0,5).map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between text-xs p-2 bg-gray-50 rounded",children:[(0,n.jsxs)("span",{className:"capitalize",children:[e.action," ",e.type]}),(0,n.jsx)(h.A,{className:"h-3 w-3 text-gray-400"})]},e.id)),t.pendingSync.length>5&&(0,n.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["+",t.pendingSync.length-5," more items"]})]})]})]})]})}function v(){let[e,s]=(0,i.useState)(!1),t=(0,g.E$)(),a=(0,g.J9)(),d=(0,g.t0)(),{markNotificationRead:o,clearNotifications:x,updateSettings:h}=(0,g.CU)();return(0,n.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,n.jsx)(c.aR,{className:"pb-3",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[d.notifications.workoutReminders?(0,n.jsx)(u.A,{className:"h-4 w-4 text-blue-600"}):(0,n.jsx)(j.A,{className:"h-4 w-4 text-gray-400"}),(0,n.jsx)(c.ZB,{className:"text-sm",children:"Notifications"}),a>0&&(0,n.jsx)(l.E,{variant:"destructive",className:"text-xs",children:a})]}),(0,n.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>s(!e),children:(0,n.jsx)(m.A,{className:"h-4 w-4"})})]})}),e&&(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm",children:"Workout Reminders"}),(0,n.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>{h({notifications:{...d.notifications,workoutReminders:!d.notifications.workoutReminders}})},children:d.notifications.workoutReminders?"On":"Off"})]})}),t.length>0&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h4",{className:"text-sm font-medium",children:"Recent"}),(0,n.jsx)(r.$,{variant:"ghost",size:"sm",onClick:x,children:"Clear All"})]}),(0,n.jsx)("div",{className:"space-y-1 max-h-40 overflow-y-auto",children:t.slice(0,5).map(e=>(0,n.jsx)("div",{className:`p-2 rounded text-xs ${e.read?"bg-gray-50":"bg-blue-50"}`,onClick:()=>o(e.id),children:(0,n.jsxs)("div",{className:"flex items-start gap-2",children:["success"===e.type&&(0,n.jsx)(f.A,{className:"h-3 w-3 text-green-600 mt-0.5"}),"error"===e.type&&(0,n.jsx)(p.A,{className:"h-3 w-3 text-red-600 mt-0.5"}),"warning"===e.type&&(0,n.jsx)(p.A,{className:"h-3 w-3 text-orange-600 mt-0.5"}),"info"===e.type&&(0,n.jsx)(p.A,{className:"h-3 w-3 text-blue-600 mt-0.5"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"font-medium",children:e.title}),(0,n.jsx)("div",{className:"text-gray-600",children:e.message}),(0,n.jsx)("div",{className:"text-gray-400 mt-1",children:new Date(e.timestamp).toLocaleTimeString()})]})]})},e.id))})]}),0===t.length&&(0,n.jsx)("div",{className:"text-center py-4 text-gray-500 text-sm",children:"No notifications"})]})]})}var w=t(45583),b=t(43649),S=t(11860),k=t(96882),A=t(61611);function C(){let[e,s]=(0,i.useState)(""),{addNotification:t,updateSettings:d,updateWorkoutPreferences:o,clearNotifications:h}=(0,g.CU)(),u=(0,g.t0)(),j=(0,g.n8)(),p=(0,g.E$)(),C=(0,g.eC)(),{addToSyncQueue:E}=(0,N.aF)(),P=e=>{t({type:e,...{success:{title:"Success!",message:"Operation completed successfully"},error:{title:"Error!",message:"Something went wrong"},warning:{title:"Warning!",message:"Please check your settings"},info:{title:"Info",message:"Here is some useful information"}}[e]})};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)(a.V,{}),(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"State Management Demo"}),(0,n.jsx)("p",{className:"text-xl text-gray-600",children:"Demonstration of Zustand store, React Query, and sync management"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)(c.Zp,{children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(m.A,{className:"h-5 w-5"}),"App Settings"]}),(0,n.jsx)(c.BT,{children:"Global application settings managed by Zustand"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Theme:"}),(0,n.jsx)(l.E,{variant:"outline",children:u.theme})]}),(0,n.jsx)(r.$,{onClick:()=>{d({theme:"light"===u.theme?"dark":"light"})},variant:"outline",className:"w-full",children:"Toggle Theme"}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Workout Reminders:"}),(0,n.jsx)(l.E,{variant:u.notifications.workoutReminders?"default":"secondary",children:u.notifications.workoutReminders?"On":"Off"})]}),(0,n.jsx)(r.$,{onClick:()=>{d({notifications:{...u.notifications,workoutReminders:!u.notifications.workoutReminders}})},variant:"outline",className:"w-full",children:"Toggle Notifications"}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Units:"}),(0,n.jsx)(l.E,{variant:"outline",children:u.units})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Language:"}),(0,n.jsx)(l.E,{variant:"outline",children:u.language})]})]})]}),(0,n.jsxs)(c.Zp,{children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(w.A,{className:"h-5 w-5"}),"Workout Preferences"]}),(0,n.jsx)(c.BT,{children:"User workout preferences stored in Zustand"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Preferred Difficulty:"}),(0,n.jsx)(l.E,{variant:"outline",children:j.preferredDifficulty})]}),(0,n.jsx)(r.$,{onClick:()=>{let e=["BEGINNER","INTERMEDIATE","ADVANCED"],s=(e.indexOf(j.preferredDifficulty)+1)%e.length;o({preferredDifficulty:e[s]})},variant:"outline",className:"w-full",children:"Change Difficulty"}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Default Duration:"}),(0,n.jsxs)(l.E,{variant:"outline",children:[j.defaultDuration," min"]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Rest Time:"}),(0,n.jsxs)(l.E,{variant:"outline",children:[j.restTimeBetweenSets,"s"]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Auto Start Next:"}),(0,n.jsx)(l.E,{variant:j.autoStartNextExercise?"default":"secondary",children:j.autoStartNextExercise?"Yes":"No"})]})]})]}),(0,n.jsxs)(c.Zp,{children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(b.A,{className:"h-5 w-5"}),"Notifications Demo"]}),(0,n.jsx)(c.BT,{children:"Test the notification system"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,n.jsxs)(r.$,{onClick:()=>P("success"),variant:"outline",className:"text-green-600",children:[(0,n.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Success"]}),(0,n.jsxs)(r.$,{onClick:()=>P("error"),variant:"outline",className:"text-red-600",children:[(0,n.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Error"]}),(0,n.jsxs)(r.$,{onClick:()=>P("warning"),variant:"outline",className:"text-orange-600",children:[(0,n.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Warning"]}),(0,n.jsxs)(r.$,{onClick:()=>P("info"),variant:"outline",className:"text-blue-600",children:[(0,n.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Info"]})]}),(0,n.jsx)(r.$,{onClick:h,variant:"destructive",className:"w-full",children:"Clear All Notifications"}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Current notifications: ",p.length]})]})]}),(0,n.jsxs)(c.Zp,{children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,n.jsx)(A.A,{className:"h-5 w-5"}),"Sync Management Demo"]}),(0,n.jsx)(c.BT,{children:"Test offline sync functionality"})]}),(0,n.jsxs)(c.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Online Status:"}),(0,n.jsx)(l.E,{variant:C.isOnline?"default":"destructive",children:C.isOnline?"Online":"Offline"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:"Pending Sync Items:"}),(0,n.jsx)(l.E,{variant:"outline",children:C.pendingSync.length})]}),(0,n.jsxs)(r.$,{onClick:()=>{E({type:"workout",action:"create",data:{type:"session",name:"Test Workout",duration:30,exercises:[]}})},variant:"outline",className:"w-full",children:[(0,n.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add Test Sync Item"]}),C.lastSyncTime&&(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Last sync: ",new Date(C.lastSyncTime).toLocaleString()]})]})]}),(0,n.jsx)("div",{className:"lg:col-span-1",children:(0,n.jsx)(y,{})}),(0,n.jsx)("div",{className:"lg:col-span-1",children:(0,n.jsx)(v,{})})]}),(0,n.jsxs)(c.Zp,{className:"mt-6",children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsx)(c.ZB,{children:"State Debug Information"}),(0,n.jsx)(c.BT,{children:"Current state values for debugging"})]}),(0,n.jsx)(c.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Settings:"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto",children:JSON.stringify(u,null,2)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Workout Preferences:"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto",children:JSON.stringify(j,null,2)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Offline State:"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto",children:JSON.stringify(C,null,2)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Recent Notifications:"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32",children:JSON.stringify(p.slice(0,3),null,2)})]})]})})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67242:(e,s,t)=>{Promise.resolve().then(t.bind(t,23920))},79551:e=>{"use strict";e.exports=require("url")},96912:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var n=t(65239),i=t(48088),a=t(88170),r=t.n(a),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23920)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\demo\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/demo/page",pathname:"/demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),n=s.X(0,[96,76],()=>t(96912));module.exports=n})();