import{r as e,e as s,f as r,b as t,d as o,g as n}from"./initializeConfig-zfMDfl5R.js";export{I as IntlError,a as IntlErrorCode,c as createFormatter,i as initializeConfig}from"./initializeConfig-zfMDfl5R.js";function m({_cache:a=o(),_formatters:i=t(a),getMessageFallback:m=r,messages:c,namespace:f,onError:g=n,...l}){return function({messages:a,namespace:r,...t},o){return a=a[o],r=e(r,o),s({...t,messages:a,namespace:r})}({...l,onError:g,cache:a,formatters:i,getMessageFallback:m,messages:{"!":c},namespace:f?`!.${f}`:"!"},"!")}function f(e,a){return e.includes(a)}export{o as _createCache,t as _createIntlFormatters,m as createTranslator,f as hasLocale};
