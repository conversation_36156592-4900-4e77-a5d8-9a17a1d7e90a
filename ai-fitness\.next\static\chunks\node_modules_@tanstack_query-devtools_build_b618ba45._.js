(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@tanstack_query-devtools_build_4f9de65b._.js",
  "static/chunks/node_modules_@tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_8555a00a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@tanstack_query-devtools_build_894e336e._.js",
  "static/chunks/dd92d_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_8555a00a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript)");
    });
});
}}),
}]);