module.exports = {

"[project]/.next-internal/server/app/api/exercises/public/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/exercises/public/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Public Exercises API Route
 * Calls workout-cool server action directly for public exercises
 */ __turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Mock data for testing - replace with actual workout-cool integration
const mockExercises = [
    {
        id: '1',
        name: 'Push-ups',
        nameEn: 'Push-ups',
        description: 'Classic bodyweight exercise for chest, shoulders, and triceps',
        descriptionEn: 'Classic bodyweight exercise for chest, shoulders, and triceps',
        instructions: 'Start in plank position, lower body to ground, push back up',
        instructionsEn: 'Start in plank position, lower body to ground, push back up',
        tips: 'Keep core tight, maintain straight line from head to heels',
        tipsEn: 'Keep core tight, maintain straight line from head to heels',
        imageUrl: '/images/exercises/pushups.jpg',
        videoUrl: '/videos/exercises/pushups.mp4',
        attributes: [
            {
                id: '1',
                attributeName: {
                    id: '1',
                    name: 'Muscle Group',
                    nameEn: 'Muscle Group'
                },
                attributeValue: {
                    id: '1',
                    value: 'Chest',
                    valueEn: 'Chest'
                }
            },
            {
                id: '2',
                attributeName: {
                    id: '2',
                    name: 'Equipment',
                    nameEn: 'Equipment'
                },
                attributeValue: {
                    id: '2',
                    value: 'None',
                    valueEn: 'None'
                }
            }
        ]
    },
    {
        id: '2',
        name: 'Squats',
        nameEn: 'Squats',
        description: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings',
        descriptionEn: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings',
        instructions: 'Stand with feet shoulder-width apart, lower hips back and down, return to standing',
        instructionsEn: 'Stand with feet shoulder-width apart, lower hips back and down, return to standing',
        tips: 'Keep knees aligned with toes, chest up, weight in heels',
        tipsEn: 'Keep knees aligned with toes, chest up, weight in heels',
        imageUrl: '/images/exercises/squats.jpg',
        videoUrl: '/videos/exercises/squats.mp4',
        attributes: [
            {
                id: '3',
                attributeName: {
                    id: '1',
                    name: 'Muscle Group',
                    nameEn: 'Muscle Group'
                },
                attributeValue: {
                    id: '3',
                    value: 'Legs',
                    valueEn: 'Legs'
                }
            },
            {
                id: '4',
                attributeName: {
                    id: '2',
                    name: 'Equipment',
                    nameEn: 'Equipment'
                },
                attributeValue: {
                    id: '2',
                    value: 'None',
                    valueEn: 'None'
                }
            }
        ]
    }
];
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const search = searchParams.get('search');
        let filteredExercises = mockExercises;
        // Apply search filter if provided
        if (search) {
            filteredExercises = mockExercises.filter((exercise)=>exercise.name.toLowerCase().includes(search.toLowerCase()) || exercise.description.toLowerCase().includes(search.toLowerCase()));
        }
        // For now, return mock data
        // TODO: Integrate with workout-cool server actions when available
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(filteredExercises);
    } catch (error) {
        console.error('Error fetching public exercises:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch public exercises'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5a52e4d4._.js.map