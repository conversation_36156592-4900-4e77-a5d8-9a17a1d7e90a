export const SiteConfig = {
  title: "Workout Cool",
  description: "Modern fitness coaching platform with comprehensive exercise database",
  keywords: [
    "fitness",
    "workout",
    "exercise",
    "training",
    "muscle building",
    "strength training",
    "bodybuilding",
    "fitness app",
    "workout planner",
    "exercise database",
  ],
  prodUrl: "https://workout.cool",
  domain: "workout.cool",
  appIcon: "/images/logo4.jpg",
  cdnIcon: "https://cdn.workout.cool/images/53992ead-81ad-43d9-bc89-9abe9a6ed800/public", // TODO
  company: {
    name: "Workout Cool",
    address: "34 avenue des champ Elysée 75008 Paris, France",
  },
  brand: {
    primary: "#007291",
  },
  email: {
    from: "Workout Cool <<EMAIL>>",
    contact: "<EMAIL>",
  },
  maker: {
    image: "https://workout.cool/images/me/twitter-en.jpg",
    website: "https://workout.cool",
    twitter: "https://twitter.com/workout_cool",
    name: "Workout Cool",
  },
  auth: {
    password: false,
  },
  seo: {
    ogImage: {
      width: 1200,
      height: 630,
    },
    twitterHandle: "@snouzy_biceps",
    applicationName: "Workout Cool",
    category: "fitness",
    classification: "Fitness & Health",
  },
};
