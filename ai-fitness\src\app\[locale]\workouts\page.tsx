import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { generatePageMetadata } from "@/lib/seo/utils";
import { MuiWorkouts } from "@/components/pages/mui-workouts";

type Props = {
  params: { locale: string };
};

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'workouts' });

  return generatePageMetadata({
    title: t('title'),
    description: t('subtitle'),
    path: "/workouts",
  });
}

export default function WorkoutsPage({ params: { locale } }: Props) {
  return <MuiWorkouts />;
}
