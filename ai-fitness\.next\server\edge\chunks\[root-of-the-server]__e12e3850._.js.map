{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/i18n/index.ts"], "sourcesContent": ["// Simple custom i18n implementation for Next.js 15 compatibility\n\nexport type Locale = 'en' | 'zh';\n\nexport const locales: Locale[] = ['en', 'zh'];\nexport const defaultLocale: Locale = 'en';\n\n// Get locale from cookies (client-side)\nexport function getLocale(): Locale {\n  if (typeof window !== 'undefined') {\n    const localeCookie = document.cookie\n      .split('; ')\n      .find(row => row.startsWith('locale='))\n      ?.split('=')[1] as Locale;\n\n    if (localeCookie && locales.includes(localeCookie)) {\n      return localeCookie;\n    }\n  }\n\n  return defaultLocale;\n}\n\n// Set locale in cookies\nexport function setLocale(locale: Locale) {\n  if (typeof window !== 'undefined') {\n    document.cookie = `locale=${locale}; path=/; max-age=31536000`; // 1 year\n    window.location.reload();\n  }\n}\n\n// Get locale from pathname\nexport function getLocaleFromPathname(pathname: string): Locale {\n  const segments = pathname.split('/');\n  const localeSegment = segments[1] as Locale;\n  \n  if (locales.includes(localeSegment)) {\n    return localeSegment;\n  }\n  \n  return defaultLocale;\n}\n\n// Remove locale from pathname\nexport function removeLocaleFromPathname(pathname: string): string {\n  const segments = pathname.split('/');\n  const localeSegment = segments[1] as Locale;\n  \n  if (locales.includes(localeSegment)) {\n    return '/' + segments.slice(2).join('/');\n  }\n  \n  return pathname;\n}\n\n// Add locale to pathname\nexport function addLocaleToPathname(pathname: string, locale: Locale): string {\n  if (locale === defaultLocale) {\n    return pathname;\n  }\n  \n  const cleanPath = removeLocaleFromPathname(pathname);\n  return `/${locale}${cleanPath}`;\n}\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;;;;;;;AAI1D,MAAM,UAAoB;IAAC;IAAM;CAAK;AACtC,MAAM,gBAAwB;AAG9B,SAAS;IACd,uCAAmC;;IASnC;IAEA,OAAO;AACT;AAGO,SAAS,UAAU,MAAc;IACtC,uCAAmC;;IAGnC;AACF;AAGO,SAAS,sBAAsB,QAAgB;IACpD,MAAM,WAAW,SAAS,KAAK,CAAC;IAChC,MAAM,gBAAgB,QAAQ,CAAC,EAAE;IAEjC,IAAI,QAAQ,QAAQ,CAAC,gBAAgB;QACnC,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,yBAAyB,QAAgB;IACvD,MAAM,WAAW,SAAS,KAAK,CAAC;IAChC,MAAM,gBAAgB,QAAQ,CAAC,EAAE;IAEjC,IAAI,QAAQ,QAAQ,CAAC,gBAAgB;QACnC,OAAO,MAAM,SAAS,KAAK,CAAC,GAAG,IAAI,CAAC;IACtC;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,QAAgB,EAAE,MAAc;IAClE,IAAI,WAAW,eAAe;QAC5B,OAAO;IACT;IAEA,MAAM,YAAY,yBAAyB;IAC3C,OAAO,CAAC,CAAC,EAAE,SAAS,WAAW;AACjC"}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { locales, defaultLocale, getLocaleFromPathname } from './lib/i18n';\n\nexport function middleware(request: NextRequest) {\n  const pathname = request.nextUrl.pathname;\n\n  // Check if pathname already has a locale\n  const pathnameHasLocale = locales.some(\n    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`\n  );\n\n  // If no locale in pathname, redirect to default locale\n  if (!pathnameHasLocale) {\n    // Get locale from cookie or use default\n    const locale = request.cookies.get('locale')?.value || defaultLocale;\n\n    // Only add locale prefix for non-default locale\n    if (locale !== defaultLocale) {\n      const newUrl = new URL(`/${locale}${pathname}`, request.url);\n      return NextResponse.redirect(newUrl);\n    }\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    // Skip all internal paths (_next)\n    '/((?!_next|api|favicon.ico|robots.txt|sitemap.xml).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,yCAAyC;IACzC,MAAM,oBAAoB,mIAAA,CAAA,UAAO,CAAC,IAAI,CACpC,CAAC,SAAW,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ;IAG7E,uDAAuD;IACvD,IAAI,CAAC,mBAAmB;QACtB,wCAAwC;QACxC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,mIAAA,CAAA,gBAAa;QAEpE,gDAAgD;QAChD,IAAI,WAAW,mIAAA,CAAA,gBAAa,EAAE;YAC5B,MAAM,SAAS,IAAI,IAAI,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,QAAQ,GAAG;YAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,kCAAkC;QAClC;KACD;AACH"}}]}