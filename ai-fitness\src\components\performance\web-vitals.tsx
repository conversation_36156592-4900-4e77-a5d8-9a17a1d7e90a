'use client';

import { useEffect } from 'react';
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

interface WebVitalsMetric {
  id: string;
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  entries: PerformanceEntry[];
}

// Send metrics to analytics service
function sendToAnalytics(metric: WebVitalsMetric) {
  // In a real application, you would send this to your analytics service
  // For example: Google Analytics, Vercel Analytics, or custom endpoint
  
  if (process.env.NODE_ENV === 'development') {
    console.log('Web Vitals Metric:', {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      id: metric.id
    });
  }

  // Example: Send to Google Analytics
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    });
  }

  // Example: Send to custom analytics endpoint
  if (process.env.NEXT_PUBLIC_ANALYTICS_URL) {
    fetch(process.env.NEXT_PUBLIC_ANALYTICS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        rating: metric.rating,
        id: metric.id,
        url: window.location.href,
        timestamp: Date.now(),
      }),
    }).catch(console.error);
  }
}

export function WebVitals() {
  useEffect(() => {
    // Largest Contentful Paint (LCP)
    getLCP(sendToAnalytics);
    
    // First Input Delay (FID)
    getFID(sendToAnalytics);
    
    // Cumulative Layout Shift (CLS)
    getCLS(sendToAnalytics);
    
    // First Contentful Paint (FCP)
    getFCP(sendToAnalytics);
    
    // Time to First Byte (TTFB)
    getTTFB(sendToAnalytics);
  }, []);

  return null; // This component doesn't render anything
}

// Performance observer for custom metrics
export function usePerformanceObserver() {
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    // Observe navigation timing
    const navObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          
          // Calculate custom metrics
          const metrics = {
            dns: navEntry.domainLookupEnd - navEntry.domainLookupStart,
            tcp: navEntry.connectEnd - navEntry.connectStart,
            ssl: navEntry.connectEnd - navEntry.secureConnectionStart,
            ttfb: navEntry.responseStart - navEntry.requestStart,
            download: navEntry.responseEnd - navEntry.responseStart,
            domParse: navEntry.domContentLoadedEventStart - navEntry.responseEnd,
            domReady: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
            loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
          };

          if (process.env.NODE_ENV === 'development') {
            console.log('Navigation Timing:', metrics);
          }
        }
      }
    });

    navObserver.observe({ entryTypes: ['navigation'] });

    // Observe resource timing
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming;
          
          // Track slow resources
          if (resourceEntry.duration > 1000) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Slow resource detected:', {
                name: resourceEntry.name,
                duration: resourceEntry.duration,
                size: resourceEntry.transferSize,
              });
            }
          }
        }
      }
    });

    resourceObserver.observe({ entryTypes: ['resource'] });

    return () => {
      navObserver.disconnect();
      resourceObserver.disconnect();
    };
  }, []);
}

// Hook for monitoring Core Web Vitals in components
export function useCoreWebVitals() {
  useEffect(() => {
    const metrics: Record<string, number> = {};

    const updateMetric = (metric: WebVitalsMetric) => {
      metrics[metric.name] = metric.value;
    };

    getLCP(updateMetric);
    getFID(updateMetric);
    getCLS(updateMetric);
    getFCP(updateMetric);
    getTTFB(updateMetric);

    // Return cleanup function
    return () => {
      // Cleanup if needed
    };
  }, []);
}

// Performance budget checker
export function checkPerformanceBudget() {
  if (typeof window === 'undefined') return;

  const budget = {
    LCP: 2500, // 2.5 seconds
    FID: 100,  // 100 milliseconds
    CLS: 0.1,  // 0.1
    FCP: 1800, // 1.8 seconds
    TTFB: 800, // 800 milliseconds
  };

  const checkMetric = (metric: WebVitalsMetric) => {
    const threshold = budget[metric.name as keyof typeof budget];
    if (threshold && metric.value > threshold) {
      console.warn(`Performance budget exceeded for ${metric.name}:`, {
        value: metric.value,
        budget: threshold,
        excess: metric.value - threshold,
      });
    }
  };

  getLCP(checkMetric);
  getFID(checkMetric);
  getCLS(checkMetric);
  getFCP(checkMetric);
  getTTFB(checkMetric);
}
