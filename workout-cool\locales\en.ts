export default {
  programs: {
    exercises_in_session: "Exercises in session",
    start_session: "Start session",
    starting_session: "Starting...",
    more_than: "more than",
    my_progress: "My progress",
    session: "session",
    completed_feminine: "completed",
    completed_sets: "completed sets",
    "set#zero": "set",
    "set#one": "set",
    "set#other": "sets",
    error_starting_session: "Error starting session",
    premium_session: "Premium session",
    premium_session_description: "This session is part of the premium content. You can see the details but not perform the workout.",
    premium_session_exercises: "Included exercises",
    workout_description: "Workout description",
    connect_to_access: "Connect to access",
    become_premium: "Become Premium",
    back_to_program: "Back to program",
    no_equipment: "No equipment",
    workout_programs_title: "Workout programs (+ in progress)",
    workout_programs: "Workout programs",
    workout_programs_description: "Choose your challenge and become stronger! 💪",
    no_programs_available: "No programs available",
    no_programs_available_description: "Programs will be available soon!",
    completed: "Completed",
    about: "About",
    program: "Program",
    not_found: "Program not found",
    characteristics: "Characteristics",
    weeks: "weeks",
    sessions_per_week: "sessions/week",
    session_duration: "min/session",
    "your_coach#zero": "Your cool coach",
    "your_coach#one": "Your cool coach",
    "your_coach#other": "Your cool coaches",
    community: "Active community",
    community_count: "coolbuilders have joined",
    week_short: "Week",
    week: "Week",
    exercises: "exercises",
    min_short: "min",
    premium: "Premium",
    free: "Free",
    join_cta: "Join the challenge",
    continue: "Continue",
    sessions: "Sessions",
    auth_required: "Authentication Required",
    auth_required_description: "You need to sign in to access this workout session.",
    login_to_continue: "Sign In to Continue",
    signup_to_continue: "Sign Up to Continue",
    premium_required: "Premium Required",
    upgrade_to_premium: "Upgrade to Premium",
    program_completed: "Program completed",
    check_out_program: "Check out this workout program!",
    share_success: "Shared successfully!",
    copied_to_clipboard: "Link copied!",
    share_failed: "Share failed",
    premium_required_description: "This is a premium access. Upgrade to access all premium content.",
    important_info: "Important information",
    donation_teaser:
      "At first, we were running on donations. But as you can imagine, donations weren't sufficient to cover development and running costs. So we made you a package that will help us keep the lights on — and unlock a few superpowers along the way.",
    new: "NEW",
    more_programs_coming_title: "More programs coming soon!",
    more_programs_coming_description:
      "We're working hard to create new programs. By upgrading to premium now, you'll have them all automatically. Thanks for your support. 🚀",
    coming_strength: "Force & Muscle",
    coming_cardio: "Cardio HIIT",
    coming_yoga: "Yoga & Mobility",
    sessions_coming_soon: "Sessions coming soon!",
    sessions_in_creation: "Our team is working hard to create quality sessions for this week. Come back soon! 🚀",
    welcome_modal: {
      welcome_title: "Welcome to {programTitle}!",
      subtitle: "Get ready to push your limits! 💪",
      level_label: "Level",
      duration_label: "Duration",
      frequency_label: "Frequency",
      later_button: "Later",
      start_button: "Let's go!",
    },
  },
  premium: {
    checkout_error: "Error during checkout",
    premium_required_title: "Premium Required",
    premium_required_subtitle: "This is a premium access. Upgrade to access all premium content.",
    premium_required_button: "Upgrade to Premium",
    // PremiumUpgradeCard translations
    premium_active_title: "Premium Active",
    premium_active_subtitle: "All features unlocked",
    free_intro_title: "You're already getting a lot for free...",
    free_intro_text:
      "Workout.cool is a free, open-source fitness app used daily by 60,000+ users. It's built with love (not VC money ^^) and it costs us real time and money to keep it running.",
    donation_story_text:
      "At first, we were running on donations. But as you can imagine, donations weren't sufficient to cover development and running costs. So we made you a package that will help us keep the lights on — and unlock a few superpowers along the way.",
    health_upgrade_text: "If Workout.cool helps you level up your health, please consider going Premium :D !",
    unlock_features_text: "Unlock advanced features & support open-source fitness.",
    invest_yourself_quote: "Never skimp on fitness & books — invest in yourself !",
    support_mission: "Support the mission",
    best_value_badge: "BEST VALUE",
    annual_plan: "Annual",
    monthly_plan: "Monthly",
    discount_badge: "40% off",
    per_month: "/month",
    feature_all_programs: "All workout programs",
    feature_progress_tracking: "Progress tracking",
    coming_soon: "(soon)",
    feature_future_updates: "All future programs & updates",
    feature_priority_support: "Priority support",
    save_yearly: "Save 40% yearly",
    processing: "Processing...",
    cta_annual: "I want to support + save 40%",
    cta_monthly: "Let's unlock my full plan",
    thank_supporting: "Thank you for supporting.",
    no_pressure: "No pressure. You can upgrade anytime.",
    keep_pushing: "keep pushing ! huhu",
    still_unsure: "Still not sure? No worries. Workout.cool will always remain free and open-source.",
    support_helps: "But if you believe in what we're building and you can afford it, your support will help 💚",
    self_hosting: "Self-hosting",
    community: "Community",
    mit_license: "MIT License",
    pricing_year: "year",
    pricing_month: "month",
  },
  bottom_navigation: {
    programs: "Programs",
    programs_tooltip: "Browse programs",
    workouts: "Workouts",
    workouts_tooltip: "Create your own workout",
  },
  levels: {
    BEGINNER: "Beginner",
    INTERMEDIATE: "Intermediate",
    ADVANCED: "Advanced",
  },
  email_sent: "Email sent",
  cant_send_email: "Can't send email",
  logout: "Logout",
  verify_email: "Verify your email. ⚠️ Don't forget to check your spam folder.",
  verify_email_subtitle: "Please verify your email to continue.",
  resend_email: "Resend email",
  resend_email_countdown: "Resend email in {seconds} seconds",
  signin_error_subtitle: "Please check your credentials and try again.",
  register_title: "Create an account",
  register_description: "Enter your information below to create your account",
  register_terms: "By signing up, you agree to our",
  register_privacy: "Privacy Policy",
  register_privacy_link: "and our",
  register_privacy_link_2: "Privacy Policy",
  password_forgot_title: "Forgot password?",
  password_forgot_subtitle: "Enter your email to reset your password",
  new_password: "New password",
  new_password_placeholder: "Enter your new password",
  current_password: "Current password",
  current_password_placeholder: "Enter your current password",
  confirm_password: "Confirm password",
  confirm_password_placeholder: "Confirm your password",

  success: {
    feedback_sent: "Feedback sent",
    password_forgot_success: "Email sent",
    reset_password_success: "Password reset successfully",
    password_updated_successfully: "Password updated successfully",
  },

  error: {
    invalid_credentials: "Invalid credentials or account does not exist",
    upload_failed: "Upload failed",
    generic_error: "Error during operation",
    sending_email: "Error sending email",
  },

  backend_errors: {
    EMAIL_ALREADY_EXISTS: "Email already exists",
    INVALID_FILE_TYPE: "Invalid file type",
    FILE_TOO_LARGE: "File too large",
    NO_FILE_UPLOADED: "No file uploaded",
    IMAGE_PROCESSING_ERROR: "Image processing error",
    upload_failed: "Upload failed",
  },

  profile: {
    new_workout: "New Workout",
    alert: {
      title: "Your progress is stored in your browser.",
      create_account: "Create an account",
      log_in: "Log in",
      to_ensure_it_is_not_getting_lost: "to ensure it is not getting lost.",
    },
  },

  // Release Notes
  release_notes: {
    title: "What's New",
    release_notes: "Release Notes",
    notes: {
      note_2025_06_23: {
        title: "🇵🇹 Portuguese Support & Donation Banner",
        content:
          "The app now supports <strong>Portuguese</strong>! We've also added a <em>donation banner</em> to help support the ongoing costs of the project via <a href='https://github.com/sponsors/snouzy' target='_blank' rel='noopener' class='text-blue-500 hover:underline'>GitHub Sponsors</a> or <a href='https://ko-fi.com/workoutcool' target='_blank' rel='noopener' class='text-blue-500 hover:underline'>Ko-fi</a>. 🙏",
      },
      note_2025_06_22: {
        title: "🌍 New Languages & Performance Boost!",
        content:
          "The app is now available in Chinese and Russian! We've also improved drag'n'drop performance for a smoother experience. ⚡",
      },
      note_2025_06_19: {
        title: "📱 Now Available as a PWA!",
        content:
          "Workout.cool v1.2 is now a Progressive Web App! Install it on your phone for a native app experience with offline access. 🚀",
      },
      note_2025_06_18: {
        title:
          "🚀 Featured #1 on <a href='https://news.ycombinator.com/item?id=44309320' target='_blank' rel='noopener' class='text-blue-500 hover:underline'>Hacker News</a>!",
        content:
          "Workout.cool reached the top spot on Hacker News! Thanks to everyone for the amazing support and welcome to all the new users! 💪",
      },
      note_2025_06_01: {
        title: "🎉 New: Release Notes Dialog",
        content: "You can now view what's new directly from the header! Stay tuned for more updates.",
      },
      note_2025_05_20: {
        title: "UI Improvements",
        content: "Improved mobile responsiveness and added subtle hover effects to buttons.",
      },
    },
  },

  // Donation Alert
  donation_alert: {
    title: "Keep Workout.cool free. Support us via",
    or: "or",
  },

  // Donation Modal
  donation_modal: {
    support_via: "Support via...",
    title: "Support the project",
    congrats: "Congratulations on your workout! 🎉",
    subtitle: "This app helps you for free, but it has a real cost for me...",
    costs_title: "The reality of costs",
    costs_description: "Currently, donations don't even cover basic costs: servers, authentication, infrastructure, database, etc.",
    open_source_title: "100% Open Source",
    open_source_description:
      "This app is completely free, ads free and open source. No profit is generated - it's a passion project to help the community and help people exercise.",
    no_ads: "No ads",
    no_tracking: "No tracking",
    impact_title: "Your impact",
    impact_3_euros: "• Even €3 covers 1 week of server",
    impact_support: "• Your support keeps the app free for everyone",
    impact_footer: "Every donation, even small, makes a real difference! 🙏",
    later_button: "Later",
    support_button: "Support the project",
  },

  // Contact Support
  contact_support: "Contact Support",
  contact_support_subtitle: "Describe your issue and we'll help you as soon as possible. You can also write to us directly at",

  // Social Platforms
  social_platforms: {
    x: "X (Twitter)",
    facebook: "Facebook",
    email: "Email",
    whatsapp: "WhatsApp",
    website: "Website",
    phone: "Phone",
    youtube: "YouTube",
    linkedin: "LinkedIn",
    snapchat: "Snapchat",
    instagram: "Instagram",
    tiktok: "TikTok",
    threads: "Threads",
  },

  // Workout Builder
  workout_builder: {
    confirm_delete: "Are you sure you want to delete this workout session?",
    steps: {
      equipment: {
        title: "Equipment",
        description: "Select your equipment",
      },
      muscles: {
        title: "Muscles",
        description: "Choose your training",
      },
      exercises: {
        title: "Exercises",
        description: "Customize your workout",
      },
    },
    muscles: {
      back: "Back",
      abdominals: "Abdominals",
      abductors: "Abductors",
      adductors: "Adductors",
      biceps: "Biceps",
      triceps: "Triceps",
      chest: "Chest",
      shoulders: "Shoulders",
      quadriceps: "Quadriceps",
      hamstrings: "Hamstrings",
      glutes: "Glutes",
      calves: "Calves",
      forearms: "Forearms",
      traps: "Traps",
      obliques: "Obliques",
    },
    exercise: {
      watch_video: "Watch video",
      shuffle: "Shuffle",
      pick: "Pick",
      remove: "Remove",
      no_video_available: "No video available.",
    },
    loading: {
      exercises: "Loading exercises...",
    },
    error: {
      loading_exercises: "Error loading exercises",
    },
    no_exercises_found: "No exercises found. Try to change your equipment or muscles selection.",
    equipment: {
      bodyweight: {
        label: "Bodyweight",
        description: "Exercises using only your body weight",
      },
      dumbbell: {
        label: "Dumbbell",
        description: "Free weight exercises with dumbbells",
      },
      barbell: {
        label: "Barbell",
        description: "Compound movements with a barbell",
      },
      kettlebell: {
        label: "Kettlebell",
        description: "Dynamic exercises with kettlebells",
      },
      band: {
        label: "Band",
        description: "Resistance band exercises",
      },
      plate: {
        label: "Plate",
        description: "Exercises using weight plates",
      },
      pullup_bar: {
        label: "Pull-up bar",
        description: "Upper body exercises with a pull-up bar",
      },
      bench: {
        label: "Bench",
        description: "Bench exercises and support",
      },
    },
    navigation: {
      previous: "Previous",
      continue: "Continue",
      complete: "Complete",
    },
    stats: {
      "muscle_selected#zero": "0 muscle selected",
      "muscle_selected#one": "1 muscle selected",
      "muscle_selected#other": "{count} muscles selected",
      "equipment_selected#zero": "0 equipment selected",
      "equipment_selected#one": "1 equipment selected",
      "equipment_selected#other": "{count} equipments selected",
      selected: "Selected",
      total: "Total",
      equipment_ready: "equipment ready",
      equipment_ready_plural: "equipment ready",
    },
    selection: {
      choose_your_arsenal: "Choose Your Arsenal",
      select_equipment_description: "Select equipment to unlock personalized workouts",
      clear_all: "Clear all",
      muscle_selection_coming_soon: "Muscle Selection (Coming Soon)",
      muscle_selection_description: "Select the muscle(s) you want to train by clicking on them.",
      exercise_selection_coming_soon: "Exercise Selection (Coming Soon)",
      exercise_selection_description: "This step will show you personalized exercise recommendations.",
    },
    session: {
      back_to_workout: "Back to workout",
      congrats: "Congratulations, workout finished! 🎉",
      congrats_subtitle: "You've done it !",
      see_instructions: "See instructions",
      finish_set: "Finish Set",
      finish_session: "Finish Session",
      bodyweight: "Bodyweight",
      weight: "Weight",
      reps: "Reps",
      time: "Time",
      next_exercise: "Next Exercise",
      add_set: "Add set",
      add_column: "Add column",
      add_row: "Add row",
      remove_column: "Remove column",
      set_number: "Set {number}",
      set_number_plural: "Sets {number}",
      set_number_singular: "Set {number}",
      set_number_plural_singular: "Sets {number}",
      workout_in_progress: "Workout in Progress",
      started_at: "Started at",
      quit_workout: "Quit Workout",
      elapsed_time: "Elapsed Time",
      chronometer: "Chronometer",
      exercise_progress: "Exercise Progress",
      total_volume: "Total Volume",
      current_exercise: "Current Exercise",
      complete: "Complete",
      active: "Active",
      already_have_a_active_session: "You already have an active session. Impossible to repeat without finishing or quitting the workout.",
      no_exercise_selected: "No exercise selected",
      quit_workout_title: "Quit Workout?",
      progress: "Progress",
      quit_warning: "Are you sure you want to quit? You can save your progress or lose it completely.",
      save_and_quit: "Save & Quit",
      quit_without_save: "Quit Without Saving",
      continue_workout: "Continue Workout",
      history: "Workout History [{count}]",
      no_workout_yet: "No workout yet.",
      start: "start",
      end: "end",
      exercise: "EXERCISE",
      repeat: "Repeat",
      delete: "Delete",
    },
    attribute_value: {
      bodyweight: "Bodyweight",
      strength: "Strength",
      powerlifting: "Powerlifting",
      calisthenic: "Calisthenics",
      plyometrics: "Plyometrics",
      stretching: "Stretching",
      strongman: "Strongman",
      cardio: "Cardio",
      stabilization: "Stabilization",
      power: "Power",
      resistance: "Resistance",
      crossfit: "CrossFit",
      weightlifting: "Weightlifting",
      neck: "Neck",
      lats: "Lats",
      adductors: "Adductors",
      abductors: "Abductors",
      groin: "Groin",
      full_body: "Full body",
      rotator_cuff: "Rotator cuff",
      hip_flexor: "Hip flexor",
      achilles_tendon: "Achilles tendon",
      fingers: "Fingers",
      smith_machine: "Smith machine",
      other: "Other",
      ez_bar: "EZ bar",
      machine: "Machine",
      desk: "Desk",
      none: "None",
      cable: "Cable",
      medicine_ball: "Medicine ball",
      swiss_ball: "Swiss ball",
      foam_roll: "Foam roll",
      trx: "TRX",
      box: "Box",
      ropes: "Ropes",
      spin_bike: "Spin bike",
      step: "Step",
      bosu: "BOSU",
      tyre: "Tyre",
      sandbag: "Sandbag",
      pole: "Pole",
      wall: "Wall",
      bar: "Bar",
      rack: "Rack",
      car: "Car",
      sled: "Sled",
      chain: "Chain",
      skierg: "SkiErg",
      rope: "Rope",
      na: "N/A",
      isolation: "Isolation",
      compound: "Compound",
    },
  },
  commons: {
    signup_with: "Sign up with {provider}",
    signin_with: "Sign in with {provider}",
    signup: "Sign up",
    login: "Login",
    connecting: "Connecting...",
    login_to_your_account_title: "Login to your account",
    login_to_your_account_subtitle: "Enter your credentials below to login",
    password_forgot: "Forgot password?",
    password_reset_success: "Password reset successfully",
    dont_have_account: "Don't have an account?",
    already_have_account: "Already have an account?",
    or: "Or",
    add: "Add",
    your_feminine: "your",
    password: "Password",
    email: "Email",
    logout: "Logout",
    first_name: "First name",
    last_name: "Last name",
    verify_password: "Verify password",
    submit: "Submit",
    upload: "Upload",
    cancel: "Cancel",
    save_changes: "Save changes",
    change: "Change",
    subject: "Subject",
    message: "Message",
    saving: "Saving...",
    edit: "Edit",
    more_options: "More options",
    open_link: "Open link",
    hide: "Hide",
    make_visible: "Make visible",
    delete: "Delete",
    share: "Share",
    title: "Title",
    subtitle: "Subtitle",
    content: "Content",
    save: "Save",
    button: "Button",
    card: "Card",
    go_back: "Go back",
    next: "Next",
    choose_image: "Choose image",
    soon: "Soon",
    coming_soon_with_emoji: "Coming soon 🤫",
    no_image: "No image",
    description: "Description",
    price: "Price",
    duration: "Duration",
    location: "Location",
    schedule: "Schedule",
    participants_info: "Participants info",
    description_placeholder: "Enter the description",
    title_placeholder: "Enter the title",
    changes_saved: "Changes saved",
    replace: "Replace",
    loading: "Loading...",
    image_deleted: "The image has been deleted",
    discover_workoutcool: "Discover Workout Cool",
    received_just_now: "Received just now",
    copied: "Copied",
    url_copied: "The URL has been copied",
    copy_failed: "Copy failed",
    accordion: "Accordion",
    image: "Image",
    other: "Other",
    register: "Register",
    instantly: "instantly",
    immediately: "immediately",
    link: "Link",
    accept: "Accept",
    deny: "Deny",
    invalid_input: "Invalid input. Please check the errors.",
    copy_url: "Copy URL",
    page_url: "Page URL",
    saving_short: "Saving...",
    saved_short: "OK",
    looks_like_you_are_lost: "Looks like you are lost",
    the_page_you_are_looking_for_is_not_available: "The page you are looking for is not available",
    go_to_home: "Go to home",
    go_to_profile: "Go to profile",
    terms: "Terms of Service",
    privacy: "Privacy Policy",
    sales_terms: "Sales Terms",
    consent_banner: "We use cookies to improve your experience. By clicking Accept, you agree to our use of cookies.",
    about: "About us",
    profile: "Profile",
    donate: "Donate",
    my_account: "My account",
    dashboard: "Dashboard",
    home: "Home",
    changelog: "Changelog",
    stop_impersonation_button: "Stop impersonation",
    impersonating_user_label: "Impersonating user",
    re_hello: "Re Hello",
    back_to_login: "Back to login",
    sending: "Sending...",
    send_me_link: "Send me a link",
    subscription: "Subscription",
    manage_subscription: "Manage subscription",
    become_premium: "Become Premium",
    extremely_dissatisfied: "Extremely dissatisfied",
    somewhat_dissatisfied: "Somewhat dissatisfied",
    neutral: "Neutral",
    satisfied: "Satisfied",
    support: "Support",
    change_language: "Change language",
    in_progress: "In progress",
    premium: "Premium",
    free: "Free",
    new: "New",
  },
} as const;
