{"version": 2, "name": "ai-fitness-singles", "alias": ["ai-fitness-singles"], "regions": ["iad1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NODE_ENV": "production"}, "build": {"env": {"NEXT_PUBLIC_APP_NAME": "AI-fitness-singles", "NEXT_PUBLIC_APP_VERSION": "1.0.0"}}, "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/auth/(.*)", "destination": "/api/auth/$1"}], "redirects": [{"source": "/login", "destination": "/auth/signin", "permanent": true}, {"source": "/register", "destination": "/auth/signup", "permanent": true}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}]}