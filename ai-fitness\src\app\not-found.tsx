import { Metadata } from 'next';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Navigation } from '@/components/Navigation';
import { Main, Section, SemanticHeader } from '@/components/seo/semantic-html';
import { ExerciseCategoryLinks, ContextualNav } from '@/components/seo/internal-links';
import { Home, Search, ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Page Not Found - AI-fitness-singles',
  description: 'The page you are looking for could not be found. Explore our exercise database, workout programs, and fitness tracking tools.',
  robots: {
    index: false,
    follow: true,
  },
};

export default function NotFound() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <Main>
        {/* 404 Hero Section */}
        <Section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100" ariaLabel="404 error section">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-2xl mx-auto">
              <div className="text-6xl font-bold text-blue-600 mb-4">404</div>
              <SemanticHeader level={1} className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                Page Not Found
              </SemanticHeader>
              <p className="text-lg text-gray-600 mb-8">
                Oops! The page you're looking for doesn't exist. But don't worry, 
                there's plenty of great fitness content to explore.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
                  <Link href="/">
                    <Home className="mr-2 h-5 w-5" />
                    Go Home
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/exercises">
                    <Search className="mr-2 h-5 w-5" />
                    Browse Exercises
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </Section>

        {/* Helpful Links Section */}
        <Section className="py-16 bg-white" ariaLabel="Helpful links">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <SemanticHeader level={2} className="text-2xl font-bold text-gray-900 mb-4">
                Popular Fitness Categories
              </SemanticHeader>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Explore our most popular exercise categories and find the perfect workout for your goals.
              </p>
            </div>
            <ExerciseCategoryLinks />
          </div>
        </Section>

        {/* Quick Navigation */}
        <Section className="py-16 bg-gray-50" ariaLabel="Quick navigation">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <SemanticHeader level={2} className="text-2xl font-bold text-gray-900 mb-4">
                Quick Navigation
              </SemanticHeader>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Find what you're looking for with these helpful links to our main sections.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <Link 
                href="/"
                className="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                    <Home className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Homepage</h3>
                  <p className="text-sm text-gray-600">Start your fitness journey</p>
                </div>
              </Link>

              <Link 
                href="/exercises"
                className="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
                    <Search className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Exercise Database</h3>
                  <p className="text-sm text-gray-600">Comprehensive exercise library</p>
                </div>
              </Link>

              <Link 
                href="/workouts"
                className="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
                    <ArrowLeft className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Workout Programs</h3>
                  <p className="text-sm text-gray-600">AI-powered training plans</p>
                </div>
              </Link>

              <Link 
                href="/progress"
                className="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group"
              >
                <div className="text-center">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors">
                    <ArrowLeft className="h-6 w-6 text-orange-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Progress Tracking</h3>
                  <p className="text-sm text-gray-600">Monitor your improvements</p>
                </div>
              </Link>
            </div>
          </div>
        </Section>

        {/* Search Suggestions */}
        <Section className="py-16 bg-white" ariaLabel="Search suggestions">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center">
              <SemanticHeader level={2} className="text-2xl font-bold text-gray-900 mb-4">
                Looking for Something Specific?
              </SemanticHeader>
              <p className="text-gray-600 mb-8">
                Try searching for exercises, workout types, or muscle groups to find exactly what you need.
              </p>
              
              <div className="flex flex-wrap gap-2 justify-center">
                {[
                  'Push-ups', 'Squats', 'Deadlifts', 'Cardio', 'Strength Training',
                  'Beginner Workouts', 'Weight Loss', 'Muscle Building', 'Flexibility'
                ].map((term) => (
                  <Link
                    key={term}
                    href={`/exercises?search=${encodeURIComponent(term.toLowerCase())}`}
                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-blue-100 hover:text-blue-700 transition-colors"
                  >
                    {term}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </Section>

        {/* Contextual Navigation */}
        <Section className="py-16 bg-gray-50" ariaLabel="Related pages">
          <div className="container mx-auto px-4">
            <ContextualNav currentPage="404" />
          </div>
        </Section>
      </Main>
    </div>
  );
}
