(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7089:(e,s,t)=>{Promise.resolve().then(t.bind(t,75694))},7761:(e,s,t)=>{Promise.resolve().then(t.bind(t,21204))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},61208:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75694:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(60687),a=t(43210),i=t(29523),n=t(44493),o=t(86246),l=t(52027),d=t(70293),c=t(97840),x=t(82080),m=t(21067),u=t(53411);function p(){let[e,s]=(0,a.useState)(!0),[t,p]=(0,a.useState)(null);return e?(0,r.jsx)(l.AV,{title:"Loading AI-fitness-singles",description:"Preparing your fitness platform..."}):t?(0,r.jsx)(d.M,{title:"Failed to load homepage",message:t,onRetry:()=>{p(null),s(!0),setTimeout(()=>{s(!1)},1e3)}}):(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(o.V,{}),(0,r.jsx)("section",{className:"relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-20 sm:py-32",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl sm:text-6xl font-bold text-gray-900 mb-6",children:["AI-fitness-singles",(0,r.jsx)("span",{className:"block text-blue-600",children:"Smart Fitness Platform"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Create custom workout plans, access comprehensive exercise database, and track your fitness progress with detailed analytics."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(i.$,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:[(0,r.jsx)(c.A,{className:"mr-2 h-5 w-5"}),"Start Training"]}),(0,r.jsxs)(i.$,{variant:"outline",size:"lg",className:"border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3",children:[(0,r.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"Browse Exercises"]})]})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Everything You Need to Train"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Comprehensive fitness platform with workout builder, exercise library, and progress tracking."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsx)(n.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(n.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsx)(n.ZB,{className:"text-xl font-semibold text-gray-900",children:"Workout Builder"}),(0,r.jsx)(n.BT,{className:"text-gray-600",children:"Create custom workout plans with our intuitive drag-and-drop builder."})]})}),(0,r.jsx)(n.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(n.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)(n.ZB,{className:"text-xl font-semibold text-gray-900",children:"Exercise Database"}),(0,r.jsx)(n.BT,{className:"text-gray-600",children:"Access thousands of exercises with detailed instructions and video guides."})]})}),(0,r.jsx)(n.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(n.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsx)(n.ZB,{className:"text-xl font-semibold text-gray-900",children:"Progress Tracking"}),(0,r.jsx)(n.BT,{className:"text-gray-600",children:"Monitor your improvements with detailed analytics and visual reports."})]})}),(0,r.jsx)(n.Zp,{className:"text-center p-6 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow",children:(0,r.jsxs)(n.aR,{children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-orange-600"})}),(0,r.jsx)(n.ZB,{className:"text-xl font-semibold text-gray-900",children:"Workout Sessions"}),(0,r.jsx)(n.BT,{className:"text-gray-600",children:"Execute your workouts with guided sessions and real-time tracking."})]})})]})]})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Start Training Today"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Build your first workout plan in minutes and begin your fitness journey."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(i.$,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:"Create Workout Plan"}),(0,r.jsx)(i.$,{variant:"outline",size:"lg",className:"border-gray-300 text-gray-700 hover:bg-gray-100 px-8 py-3",children:"Explore Exercises"})]})]})})]})}},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[96,76],()=>t(61208));module.exports=r})();