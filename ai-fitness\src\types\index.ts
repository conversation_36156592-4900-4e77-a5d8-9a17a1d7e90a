export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface UserProfile {
  id: string
  userId: string
  age?: number
  height?: number // in cm
  weight?: number // in kg
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced'
  goals: string[]
  preferences: {
    workoutDuration: number // in minutes
    workoutFrequency: number // per week
    preferredTime: 'morning' | 'afternoon' | 'evening'
    equipment: string[]
  }
  createdAt: Date
  updatedAt: Date
}

export interface Exercise {
  id: string
  name: string
  description: string
  category: 'strength' | 'cardio' | 'flexibility' | 'balance'
  muscleGroups: string[]
  equipment: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  instructions: string[]
  tips: string[]
  imageUrl?: string
  videoUrl?: string
  duration?: number // in seconds
  reps?: number
  sets?: number
  restTime?: number // in seconds
}

export interface WorkoutPlan {
  id: string
  name: string
  description: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  duration: number // in minutes
  exercises: WorkoutExercise[]
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface WorkoutExercise {
  exerciseId: string
  exercise: Exercise
  sets: number
  reps: number
  weight?: number // in kg
  duration?: number // in seconds
  restTime: number // in seconds
  notes?: string
}

export interface WorkoutSession {
  id: string
  userId: string
  workoutPlanId: string
  workoutPlan: WorkoutPlan
  startTime: Date
  endTime?: Date
  status: 'planned' | 'in-progress' | 'completed' | 'skipped'
  exercises: WorkoutSessionExercise[]
  notes?: string
  rating?: number // 1-5
}

export interface WorkoutSessionExercise {
  exerciseId: string
  exercise: Exercise
  sets: WorkoutSet[]
  notes?: string
  completed: boolean
}

export interface WorkoutSet {
  reps: number
  weight?: number
  duration?: number
  restTime?: number
  completed: boolean
  notes?: string
}

export interface ProgressMetric {
  id: string
  userId: string
  type: 'weight' | 'body_fat' | 'muscle_mass' | 'measurement'
  value: number
  unit: string
  bodyPart?: string // for measurements
  date: Date
  notes?: string
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'workout' | 'consistency' | 'strength' | 'endurance'
  criteria: {
    type: string
    value: number
    timeframe?: string
  }
  unlockedAt?: Date
}
