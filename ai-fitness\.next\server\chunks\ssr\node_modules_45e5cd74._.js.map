{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": ";AACA,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,YAAY,eAAe,QAAQ,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,OAAO,WAAW,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,YAAY,CAAC,aAAa;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK;YAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,CAAC,QAAQ;YACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACzB,wCAAa;oBACT,QAAQ,IAAI,CAAC;gBACjB;gBACA,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,SAAS,GAAG;YACrB;YACA;QACJ;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACzB,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;YACjC;YACA,kDAAkD;YAClD,4FAA4F;YAC5F,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,OAAO,CAAC;YACZ;QACJ,OAAO;YACH,IAAI,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;QACxD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,WAAW,aAAa;YACzD,IAAI,QAAQ,OAAO,WAAW,cAAc,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,wBAAwB,QAAQ;YACjD,IAAI,WAAW,GAAG;QACtB;QACA,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5B,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,oBAAoB,QAAQ;YAC7C,IAAI,UAAU,CAAC,WAAW,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACxB;IACJ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG;gBAC3B,OAAO,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC;YAC7C;YACA,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB,OAAO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,OAAO,WAAW,aAAa;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,KAAK,EAAE,GAAG;YACxC,IAAI,KAAK;gBACL,QAAQ,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC,KAAK,QAAQ,EAAE,SAAS,IAAI;oBAC3F,OAAO,KAAK,OAAO,KAAK,MAAM,uBAAuB,GAAG,OAAO;gBACnE;YACJ,OAAO;gBACH,MAAM,IAAI,CAAC;YACf;YACA,OAAO;QACX,GAAG,EAAE;IACT;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,IAAI,OAAO,WAAW,aAAa;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,OAAO,WAAW,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;YACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB;YACxC,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;gBAC9E,GAAG,CAAC,QAAQ,GAAG;gBACf,OAAO;YACX,GAAG,CAAC;QACR;QACA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ,CAAC;QACrB,OAAO,gBAAgB,sBAAsB;IACjD,IAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,OAAO,WAAW,cAAc,wBAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,IAAI,OAAO,WAAW,aAAa;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;IACA,mBAAmB;QACf,SAAS,GAAG,CAAC;QACb,OAAO;YACH,SAAS,MAAM,CAAC;QACpB;IACJ,wEAAwE;IACxE,GAAG;QACC,MAAM,EAAE;QACR,OAAO,MAAM,OAAO;KACvB;IACD,OAAO;AACX;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,wGAAwB,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40emotion/is-prop-valid/dist/emotion-is-prop-valid.esm.js"], "sourcesContent": ["import memoize from '@emotion/memoize';\n\n// eslint-disable-next-line no-undef\nvar reactPropsRegex = /^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/; // https://esbench.com/bench/5bfee68a4cd7e6009ef61d23\n\nvar isPropValid = /* #__PURE__ */memoize(function (prop) {\n  return reactPropsRegex.test(prop) || prop.charCodeAt(0) === 111\n  /* o */\n  && prop.charCodeAt(1) === 110\n  /* n */\n  && prop.charCodeAt(2) < 91;\n}\n/* Z+1 */\n);\n\nexport { isPropValid as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,oCAAoC;AACpC,IAAI,kBAAkB,ugIAAugI,qDAAqD;AAEllI,IAAI,cAAc,aAAa,GAAE,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE,SAAU,IAAI;IACrD,OAAO,gBAAgB,IAAI,CAAC,SAAS,KAAK,UAAU,CAAC,OAAO,OAEzD,KAAK,UAAU,CAAC,OAAO,OAEvB,KAAK,UAAU,CAAC,KAAK;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40emotion/styled/base/dist/emotion-styled-base.development.esm.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { withEmotionCache, ThemeContext } from '@emotion/react';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport * as React from 'react';\nimport isPropValid from '@emotion/is-prop-valid';\n\nvar isBrowser = typeof document !== 'undefined';\n\nvar isDevelopment = true;\n\nvar testOmitPropsOnStringTag = isPropValid;\n\nvar testOmitPropsOnComponent = function testOmitPropsOnComponent(key) {\n  return key !== 'theme';\n};\n\nvar getDefaultShouldForwardProp = function getDefaultShouldForwardProp(tag) {\n  return typeof tag === 'string' && // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96 ? testOmitPropsOnStringTag : testOmitPropsOnComponent;\n};\nvar composeShouldForwardProps = function composeShouldForwardProps(tag, options, isReal) {\n  var shouldForwardProp;\n\n  if (options) {\n    var optionsShouldForwardProp = options.shouldForwardProp;\n    shouldForwardProp = tag.__emotion_forwardProp && optionsShouldForwardProp ? function (propName) {\n      return tag.__emotion_forwardProp(propName) && optionsShouldForwardProp(propName);\n    } : optionsShouldForwardProp;\n  }\n\n  if (typeof shouldForwardProp !== 'function' && isReal) {\n    shouldForwardProp = tag.__emotion_forwardProp;\n  }\n\n  return shouldForwardProp;\n};\n\nvar ILLEGAL_ESCAPE_SEQUENCE_ERROR = \"You have illegal escape sequence in your template literal, most likely inside content's property value.\\nBecause you write your CSS inside a JavaScript string you actually have to do double escaping, so for example \\\"content: '\\\\00d7';\\\" should become \\\"content: '\\\\\\\\00d7';\\\".\\nYou can read more about this here:\\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#ES2018_revision_of_illegal_escape_sequences\";\n\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n      serialized = _ref.serialized,\n      isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  var rules = useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n\n  if (!isBrowser && rules !== undefined) {\n    var _ref2;\n\n    var serializedNames = serialized.name;\n    var next = serialized.next;\n\n    while (next !== undefined) {\n      serializedNames += ' ' + next.name;\n      next = next.next;\n    }\n\n    return /*#__PURE__*/React.createElement(\"style\", (_ref2 = {}, _ref2[\"data-emotion\"] = cache.key + \" \" + serializedNames, _ref2.dangerouslySetInnerHTML = {\n      __html: rules\n    }, _ref2.nonce = cache.sheet.nonce, _ref2));\n  }\n\n  return null;\n};\n\nvar createStyled = function createStyled(tag, options) {\n  {\n    if (tag === undefined) {\n      throw new Error('You are trying to create a styled element with an undefined component.\\nYou may have forgotten to import it.');\n    }\n  }\n\n  var isReal = tag.__emotion_real === tag;\n  var baseTag = isReal && tag.__emotion_base || tag;\n  var identifierName;\n  var targetClassName;\n\n  if (options !== undefined) {\n    identifierName = options.label;\n    targetClassName = options.target;\n  }\n\n  var shouldForwardProp = composeShouldForwardProps(tag, options, isReal);\n  var defaultShouldForwardProp = shouldForwardProp || getDefaultShouldForwardProp(baseTag);\n  var shouldUseAs = !defaultShouldForwardProp('as');\n  return function () {\n    // eslint-disable-next-line prefer-rest-params\n    var args = arguments;\n    var styles = isReal && tag.__emotion_styles !== undefined ? tag.__emotion_styles.slice(0) : [];\n\n    if (identifierName !== undefined) {\n      styles.push(\"label:\" + identifierName + \";\");\n    }\n\n    if (args[0] == null || args[0].raw === undefined) {\n      // eslint-disable-next-line prefer-spread\n      styles.push.apply(styles, args);\n    } else {\n      var templateStringsArr = args[0];\n\n      if (templateStringsArr[0] === undefined) {\n        console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR);\n      }\n\n      styles.push(templateStringsArr[0]);\n      var len = args.length;\n      var i = 1;\n\n      for (; i < len; i++) {\n        if (templateStringsArr[i] === undefined) {\n          console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR);\n        }\n\n        styles.push(args[i], templateStringsArr[i]);\n      }\n    }\n\n    var Styled = withEmotionCache(function (props, cache, ref) {\n      var FinalTag = shouldUseAs && props.as || baseTag;\n      var className = '';\n      var classInterpolations = [];\n      var mergedProps = props;\n\n      if (props.theme == null) {\n        mergedProps = {};\n\n        for (var key in props) {\n          mergedProps[key] = props[key];\n        }\n\n        mergedProps.theme = React.useContext(ThemeContext);\n      }\n\n      if (typeof props.className === 'string') {\n        className = getRegisteredStyles(cache.registered, classInterpolations, props.className);\n      } else if (props.className != null) {\n        className = props.className + \" \";\n      }\n\n      var serialized = serializeStyles(styles.concat(classInterpolations), cache.registered, mergedProps);\n      className += cache.key + \"-\" + serialized.name;\n\n      if (targetClassName !== undefined) {\n        className += \" \" + targetClassName;\n      }\n\n      var finalShouldForwardProp = shouldUseAs && shouldForwardProp === undefined ? getDefaultShouldForwardProp(FinalTag) : defaultShouldForwardProp;\n      var newProps = {};\n\n      for (var _key in props) {\n        if (shouldUseAs && _key === 'as') continue;\n\n        if (finalShouldForwardProp(_key)) {\n          newProps[_key] = props[_key];\n        }\n      }\n\n      newProps.className = className;\n\n      if (ref) {\n        newProps.ref = ref;\n      }\n\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n        cache: cache,\n        serialized: serialized,\n        isStringTag: typeof FinalTag === 'string'\n      }), /*#__PURE__*/React.createElement(FinalTag, newProps));\n    });\n    Styled.displayName = identifierName !== undefined ? identifierName : \"Styled(\" + (typeof baseTag === 'string' ? baseTag : baseTag.displayName || baseTag.name || 'Component') + \")\";\n    Styled.defaultProps = tag.defaultProps;\n    Styled.__emotion_real = Styled;\n    Styled.__emotion_base = baseTag;\n    Styled.__emotion_styles = styles;\n    Styled.__emotion_forwardProp = shouldForwardProp;\n    Object.defineProperty(Styled, 'toString', {\n      value: function value() {\n        if (targetClassName === undefined && isDevelopment) {\n          return 'NO_COMPONENT_SELECTOR';\n        }\n\n        return \".\" + targetClassName;\n      }\n    });\n\n    Styled.withComponent = function (nextTag, nextOptions) {\n      var newStyled = createStyled(nextTag, _extends({}, options, nextOptions, {\n        shouldForwardProp: composeShouldForwardProps(Styled, nextOptions, true)\n      }));\n      return newStyled.apply(void 0, styles);\n    };\n\n    return Styled;\n  };\n};\n\nexport { createStyled as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,IAAI,YAAY,OAAO,aAAa;AAEpC,IAAI,gBAAgB;AAEpB,IAAI,2BAA2B,iMAAA,CAAA,UAAW;AAE1C,IAAI,2BAA2B,SAAS,yBAAyB,GAAG;IAClE,OAAO,QAAQ;AACjB;AAEA,IAAI,8BAA8B,SAAS,4BAA4B,GAAG;IACxE,OAAO,OAAO,QAAQ,YAAY,oCAAoC;IACtE,mCAAmC;IACnC,6BAA6B;IAC7B,IAAI,UAAU,CAAC,KAAK,KAAK,2BAA2B;AACtD;AACA,IAAI,4BAA4B,SAAS,0BAA0B,GAAG,EAAE,OAAO,EAAE,MAAM;IACrF,IAAI;IAEJ,IAAI,SAAS;QACX,IAAI,2BAA2B,QAAQ,iBAAiB;QACxD,oBAAoB,IAAI,qBAAqB,IAAI,2BAA2B,SAAU,QAAQ;YAC5F,OAAO,IAAI,qBAAqB,CAAC,aAAa,yBAAyB;QACzE,IAAI;IACN;IAEA,IAAI,OAAO,sBAAsB,cAAc,QAAQ;QACrD,oBAAoB,IAAI,qBAAqB;IAC/C;IAEA,OAAO;AACT;AAEA,IAAI,gCAAgC;AAEpC,IAAI,YAAY,SAAS,UAAU,IAAI;IACrC,IAAI,QAAQ,KAAK,KAAK,EAClB,aAAa,KAAK,UAAU,EAC5B,cAAc,KAAK,WAAW;IAClC,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,YAAY;IAClC,IAAI,QAAQ,CAAA,GAAA,yPAAA,CAAA,2CAAwC,AAAD,EAAE;QACnD,OAAO,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY;IACzC;IAEA,IAAI,CAAC,aAAa,UAAU,WAAW;QACrC,IAAI;QAEJ,IAAI,kBAAkB,WAAW,IAAI;QACrC,IAAI,OAAO,WAAW,IAAI;QAE1B,MAAO,SAAS,UAAW;YACzB,mBAAmB,MAAM,KAAK,IAAI;YAClC,OAAO,KAAK,IAAI;QAClB;QAEA,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,eAAe,GAAG,MAAM,GAAG,GAAG,MAAM,iBAAiB,MAAM,uBAAuB,GAAG;YACvJ,QAAQ;QACV,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,KAAK;IAC3C;IAEA,OAAO;AACT;AAEA,IAAI,eAAe,SAAS,aAAa,GAAG,EAAE,OAAO;IACnD;QACE,IAAI,QAAQ,WAAW;YACrB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,SAAS,IAAI,cAAc,KAAK;IACpC,IAAI,UAAU,UAAU,IAAI,cAAc,IAAI;IAC9C,IAAI;IACJ,IAAI;IAEJ,IAAI,YAAY,WAAW;QACzB,iBAAiB,QAAQ,KAAK;QAC9B,kBAAkB,QAAQ,MAAM;IAClC;IAEA,IAAI,oBAAoB,0BAA0B,KAAK,SAAS;IAChE,IAAI,2BAA2B,qBAAqB,4BAA4B;IAChF,IAAI,cAAc,CAAC,yBAAyB;IAC5C,OAAO;QACL,8CAA8C;QAC9C,IAAI,OAAO;QACX,IAAI,SAAS,UAAU,IAAI,gBAAgB,KAAK,YAAY,IAAI,gBAAgB,CAAC,KAAK,CAAC,KAAK,EAAE;QAE9F,IAAI,mBAAmB,WAAW;YAChC,OAAO,IAAI,CAAC,WAAW,iBAAiB;QAC1C;QAEA,IAAI,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,WAAW;YAChD,yCAAyC;YACzC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;QAC5B,OAAO;YACL,IAAI,qBAAqB,IAAI,CAAC,EAAE;YAEhC,IAAI,kBAAkB,CAAC,EAAE,KAAK,WAAW;gBACvC,QAAQ,KAAK,CAAC;YAChB;YAEA,OAAO,IAAI,CAAC,kBAAkB,CAAC,EAAE;YACjC,IAAI,MAAM,KAAK,MAAM;YACrB,IAAI,IAAI;YAER,MAAO,IAAI,KAAK,IAAK;gBACnB,IAAI,kBAAkB,CAAC,EAAE,KAAK,WAAW;oBACvC,QAAQ,KAAK,CAAC;gBAChB;gBAEA,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,kBAAkB,CAAC,EAAE;YAC5C;QACF;QAEA,IAAI,SAAS,CAAA,GAAA,2OAAA,CAAA,mBAAgB,AAAD,EAAE,SAAU,KAAK,EAAE,KAAK,EAAE,GAAG;YACvD,IAAI,WAAW,eAAe,MAAM,EAAE,IAAI;YAC1C,IAAI,YAAY;YAChB,IAAI,sBAAsB,EAAE;YAC5B,IAAI,cAAc;YAElB,IAAI,MAAM,KAAK,IAAI,MAAM;gBACvB,cAAc,CAAC;gBAEf,IAAK,IAAI,OAAO,MAAO;oBACrB,WAAW,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;gBAC/B;gBAEA,YAAY,KAAK,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,uOAAA,CAAA,eAAY;YACnD;YAEA,IAAI,OAAO,MAAM,SAAS,KAAK,UAAU;gBACvC,YAAY,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,UAAU,EAAE,qBAAqB,MAAM,SAAS;YACxF,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM;gBAClC,YAAY,MAAM,SAAS,GAAG;YAChC;YAEA,IAAI,aAAa,CAAA,GAAA,4LAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,MAAM,CAAC,sBAAsB,MAAM,UAAU,EAAE;YACvF,aAAa,MAAM,GAAG,GAAG,MAAM,WAAW,IAAI;YAE9C,IAAI,oBAAoB,WAAW;gBACjC,aAAa,MAAM;YACrB;YAEA,IAAI,yBAAyB,eAAe,sBAAsB,YAAY,4BAA4B,YAAY;YACtH,IAAI,WAAW,CAAC;YAEhB,IAAK,IAAI,QAAQ,MAAO;gBACtB,IAAI,eAAe,SAAS,MAAM;gBAElC,IAAI,uBAAuB,OAAO;oBAChC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;gBAC9B;YACF;YAEA,SAAS,SAAS,GAAG;YAErB,IAAI,KAAK;gBACP,SAAS,GAAG,GAAG;YACjB;YAEA,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,WAAW;gBACxG,OAAO;gBACP,YAAY;gBACZ,aAAa,OAAO,aAAa;YACnC,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,UAAU;QACjD;QACA,OAAO,WAAW,GAAG,mBAAmB,YAAY,iBAAiB,YAAY,CAAC,OAAO,YAAY,WAAW,UAAU,QAAQ,WAAW,IAAI,QAAQ,IAAI,IAAI,WAAW,IAAI;QAChL,OAAO,YAAY,GAAG,IAAI,YAAY;QACtC,OAAO,cAAc,GAAG;QACxB,OAAO,cAAc,GAAG;QACxB,OAAO,gBAAgB,GAAG;QAC1B,OAAO,qBAAqB,GAAG;QAC/B,OAAO,cAAc,CAAC,QAAQ,YAAY;YACxC,OAAO,SAAS;gBACd,IAAI,oBAAoB,aAAa,eAAe;oBAClD,OAAO;gBACT;gBAEA,OAAO,MAAM;YACf;QACF;QAEA,OAAO,aAAa,GAAG,SAAU,OAAO,EAAE,WAAW;YACnD,IAAI,YAAY,aAAa,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,aAAa;gBACvE,mBAAmB,0BAA0B,QAAQ,aAAa;YACpE;YACA,OAAO,UAAU,KAAK,CAAC,KAAK,GAAG;QACjC;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40emotion/styled/dist/emotion-styled.development.esm.js"], "sourcesContent": ["import createStyled from '../base/dist/emotion-styled-base.development.esm.js';\nimport '@babel/runtime/helpers/extends';\nimport '@emotion/react';\nimport '@emotion/serialize';\nimport '@emotion/use-insertion-effect-with-fallbacks';\nimport '@emotion/utils';\nimport 'react';\nimport '@emotion/is-prop-valid';\n\nvar tags = ['a', 'abbr', 'address', 'area', 'article', 'aside', 'audio', 'b', 'base', 'bdi', 'bdo', 'big', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'cite', 'code', 'col', 'colgroup', 'data', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div', 'dl', 'dt', 'em', 'embed', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'keygen', 'label', 'legend', 'li', 'link', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meta', 'meter', 'nav', 'noscript', 'object', 'ol', 'optgroup', 'option', 'output', 'p', 'param', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'script', 'section', 'select', 'small', 'source', 'span', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'title', 'tr', 'track', 'u', 'ul', 'var', 'video', 'wbr', // SVG\n'circle', 'clipPath', 'defs', 'ellipse', 'foreignObject', 'g', 'image', 'line', 'linearGradient', 'mask', 'path', 'pattern', 'polygon', 'polyline', 'radialGradient', 'rect', 'stop', 'svg', 'text', 'tspan'];\n\n// bind it to avoid mutating the original function\nvar styled = createStyled.bind(null);\ntags.forEach(function (tagName) {\n  styled[tagName] = styled(tagName);\n});\n\nexport { styled as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,IAAI,OAAO;IAAC;IAAK;IAAQ;IAAW;IAAQ;IAAW;IAAS;IAAS;IAAK;IAAQ;IAAO;IAAO;IAAO;IAAc;IAAQ;IAAM;IAAU;IAAU;IAAW;IAAQ;IAAQ;IAAO;IAAY;IAAQ;IAAY;IAAM;IAAO;IAAW;IAAO;IAAU;IAAO;IAAM;IAAM;IAAM;IAAS;IAAY;IAAc;IAAU;IAAU;IAAQ;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAQ;IAAU;IAAU;IAAM;IAAQ;IAAK;IAAU;IAAO;IAAS;IAAO;IAAO;IAAU;IAAS;IAAU;IAAM;IAAQ;IAAQ;IAAO;IAAQ;IAAW;IAAQ;IAAY;IAAQ;IAAS;IAAO;IAAY;IAAU;IAAM;IAAY;IAAU;IAAU;IAAK;IAAS;IAAW;IAAO;IAAY;IAAK;IAAM;IAAM;IAAQ;IAAK;IAAQ;IAAU;IAAW;IAAU;IAAS;IAAU;IAAQ;IAAU;IAAS;IAAO;IAAW;IAAO;IAAS;IAAS;IAAM;IAAY;IAAS;IAAM;IAAS;IAAQ;IAAS;IAAM;IAAS;IAAK;IAAM;IAAO;IAAS;IAC77B;IAAU;IAAY;IAAQ;IAAW;IAAiB;IAAK;IAAS;IAAQ;IAAkB;IAAQ;IAAQ;IAAW;IAAW;IAAY;IAAkB;IAAQ;IAAQ;IAAO;IAAQ;CAAQ;AAE7M,kDAAkD;AAClD,IAAI,SAAS,sMAAA,CAAA,UAAY,CAAC,IAAI,CAAC;AAC/B,KAAK,OAAO,CAAC,SAAU,OAAO;IAC5B,MAAM,CAAC,QAAQ,GAAG,OAAO;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/styled-engine/esm/index.js"], "sourcesContent": ["/**\n * @mui/styled-engine v7.1.1\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use client';\n\n/* eslint-disable no-underscore-dangle */\nimport emStyled from '@emotion/styled';\nimport { serializeStyles as emSerializeStyles } from '@emotion/serialize';\nexport default function styled(tag, options) {\n  const stylesFactory = emStyled(tag, options);\n  if (process.env.NODE_ENV !== 'production') {\n    return (...styles) => {\n      const component = typeof tag === 'string' ? `\"${tag}\"` : 'component';\n      if (styles.length === 0) {\n        console.error([`MUI: Seems like you called \\`styled(${component})()\\` without a \\`style\\` argument.`, 'You must provide a `styles` argument: `styled(\"div\")(styleYouForgotToPass)`.'].join('\\n'));\n      } else if (styles.some(style => style === undefined)) {\n        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);\n      }\n      return stylesFactory(...styles);\n    };\n  }\n  return stylesFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function internal_mutateStyles(tag, processor) {\n  // Emotion attaches all the styles as `__emotion_styles`.\n  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186\n  if (Array.isArray(tag.__emotion_styles)) {\n    tag.__emotion_styles = processor(tag.__emotion_styles);\n  }\n}\n\n// Emotion only accepts an array, but we want to avoid allocations\nconst wrapper = [];\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function internal_serializeStyles(styles) {\n  wrapper[0] = styles;\n  return emSerializeStyles(wrapper);\n}\nexport { ThemeContext, keyframes, css } from '@emotion/react';\nexport { default as StyledEngineProvider } from \"./StyledEngineProvider/index.js\";\nexport { default as GlobalStyles } from \"./GlobalStyles/index.js\";"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;AAGD,uCAAuC,GACvC;AACA;AAJA;;;AAKe,SAAS,OAAO,GAAG,EAAE,OAAO;IACzC,MAAM,gBAAgB,CAAA,GAAA,sLAAA,CAAA,UAAQ,AAAD,EAAE,KAAK;IACpC,wCAA2C;QACzC,OAAO,CAAC,GAAG;YACT,MAAM,YAAY,OAAO,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG;YACzD,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,QAAQ,KAAK,CAAC;oBAAC,CAAC,oCAAoC,EAAE,UAAU,mCAAmC,CAAC;oBAAE;iBAA+E,CAAC,IAAI,CAAC;YAC7L,OAAO,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,UAAU,YAAY;gBACpD,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,UAAU,mDAAmD,CAAC;YACjG;YACA,OAAO,iBAAiB;QAC1B;IACF;;AAEF;AAGO,SAAS,sBAAsB,GAAG,EAAE,SAAS;IAClD,yDAAyD;IACzD,4HAA4H;IAC5H,IAAI,MAAM,OAAO,CAAC,IAAI,gBAAgB,GAAG;QACvC,IAAI,gBAAgB,GAAG,UAAU,IAAI,gBAAgB;IACvD;AACF;AAEA,kEAAkE;AAClE,MAAM,UAAU,EAAE;AAEX,SAAS,yBAAyB,MAAM;IAC7C,OAAO,CAAC,EAAE,GAAG;IACb,OAAO,CAAA,GAAA,4LAAA,CAAA,kBAAiB,AAAD,EAAE;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/createBox/createBox.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const {\n      className,\n      component = 'div',\n      ...other\n    } = extendSxProp(inProps);\n    return /*#__PURE__*/_jsx(BoxRoot, {\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme,\n      ...other\n    });\n  });\n  return Box;\n}"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAPA;;;;;;;AAQe,SAAS,UAAU,UAAU,CAAC,CAAC;IAC5C,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,mBAAmB,aAAa,EAChC,iBAAiB,EAClB,GAAG;IACJ,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,UAAM,AAAD,EAAE,OAAO;QAC5B,mBAAmB,CAAA,OAAQ,SAAS,WAAW,SAAS,QAAQ,SAAS;IAC3E,GAAG,4KAAA,CAAA,UAAe;IAClB,MAAM,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,IAAI,OAAO,EAAE,GAAG;QACjE,MAAM,QAAQ,CAAA,GAAA,8JAAA,CAAA,UAAQ,AAAD,EAAE;QACvB,MAAM,EACJ,SAAS,EACT,YAAY,KAAK,EACjB,GAAG,OACJ,GAAG,CAAA,GAAA,oNAAA,CAAA,eAAY,AAAD,EAAE;QACjB,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,SAAS;YAChC,IAAI;YACJ,KAAK;YACL,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,oBAAoB,kBAAkB,oBAAoB;YACrF,OAAO,UAAU,KAAK,CAAC,QAAQ,IAAI,QAAQ;YAC3C,GAAG,KAAK;QACV;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/useThemeProps/getThemeProps.js"], "sourcesContent": ["import resolveProps from '@mui/utils/resolveProps';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,cAAc,MAAM;IAC1C,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACN,GAAG;IACJ,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE;QAClG,OAAO;IACT;IACA,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE,MAAM,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/useThemeProps/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAIe,SAAS,cAAc,EACpC,KAAK,EACL,IAAI,EACJ,YAAY,EACZ,OAAO,EACR;IACC,IAAI,QAAQ,CAAA,GAAA,8JAAA,CAAA,UAAQ,AAAD,EAAE;IACrB,IAAI,SAAS;QACX,QAAQ,KAAK,CAAC,QAAQ,IAAI;IAC5B;IACA,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;QACnB;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/preprocessStyles.js"], "sourcesContent": ["import { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n    variants,\n    ...style\n  } = input;\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,iBAAiB,KAAK;IAC5C,MAAM,EACJ,QAAQ,EACR,GAAG,OACJ,GAAG;IACJ,MAAM,SAAS;QACb;QACA,OAAO,CAAA,GAAA,yKAAA,CAAA,2BAAwB,AAAD,EAAE;QAChC,aAAa;IACf;IAEA,qCAAqC;IACrC,IAAI,OAAO,KAAK,KAAK,OAAO;QAC1B,OAAO;IACT;IACA,IAAI,UAAU;QACZ,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;gBACvC,QAAQ,KAAK,GAAG,CAAA,GAAA,yKAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,KAAK;YACxD;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/createStyled/createStyled.js"], "sourcesContent": ["import styledEngineStyled, { internal_mutateStyles as mutateStyles } from '@mui/styled-engine';\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport capitalize from '@mui/utils/capitalize';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport createTheme from \"../createTheme/index.js\";\nimport styleFunctionSx from \"../styleFunctionSx/index.js\";\nimport preprocessStyles from \"../preprocessStyles.js\";\n\n/* eslint-disable no-underscore-dangle */\n/* eslint-disable no-labels */\n/* eslint-disable no-lone-blocks */\n\nexport const systemDefaultTheme = createTheme();\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (_props, styles) => styles[slot];\n}\nfunction attachTheme(props, themeId, defaultTheme) {\n  props.theme = isObjectEmpty(props.theme) ? defaultTheme : props.theme[themeId] || props.theme;\n}\nfunction processStyle(props, style) {\n  /*\n   * Style types:\n   *  - null/undefined\n   *  - string\n   *  - CSS style object: { [cssKey]: [cssValue], variants }\n   *  - Processed style object: { style, variants, isProcessed: true }\n   *  - Array of any of the above\n   */\n\n  const resolvedStyle = typeof style === 'function' ? style(props) : style;\n  if (Array.isArray(resolvedStyle)) {\n    return resolvedStyle.flatMap(subStyle => processStyle(props, subStyle));\n  }\n  if (Array.isArray(resolvedStyle?.variants)) {\n    let rootStyle;\n    if (resolvedStyle.isProcessed) {\n      rootStyle = resolvedStyle.style;\n    } else {\n      const {\n        variants,\n        ...otherStyles\n      } = resolvedStyle;\n      rootStyle = otherStyles;\n    }\n    return processStyleVariants(props, resolvedStyle.variants, [rootStyle]);\n  }\n  if (resolvedStyle?.isProcessed) {\n    return resolvedStyle.style;\n  }\n  return resolvedStyle;\n}\nfunction processStyleVariants(props, variants, results = []) {\n  let mergedState; // We might not need it, initialized lazily\n\n  variantLoop: for (let i = 0; i < variants.length; i += 1) {\n    const variant = variants[i];\n    if (typeof variant.props === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      if (!variant.props(mergedState)) {\n        continue;\n      }\n    } else {\n      for (const key in variant.props) {\n        if (props[key] !== variant.props[key] && props.ownerState?.[key] !== variant.props[key]) {\n          continue variantLoop;\n        }\n      }\n    }\n    if (typeof variant.style === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      results.push(variant.style(mergedState));\n    } else {\n      results.push(variant.style);\n    }\n  }\n  return results;\n}\nexport default function createStyled(input = {}) {\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  function styleAttachTheme(props) {\n    attachTheme(props, themeId, defaultTheme);\n  }\n  const styled = (tag, inputOptions = {}) => {\n    // If `tag` is already a styled component, filter out the `sx` style function\n    // to prevent unnecessary styles generated by the composite components.\n    mutateStyles(tag, styles => styles.filter(style => style !== styleFunctionSx));\n    const {\n      name: componentName,\n      slot: componentSlot,\n      skipVariantsResolver: inputSkipVariantsResolver,\n      skipSx: inputSkipSx,\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot)),\n      ...options\n    } = inputOptions;\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, {\n      shouldForwardProp: shouldForwardPropOption,\n      label: generateStyledLabel(componentName, componentSlot),\n      ...options\n    });\n    const transformStyle = style => {\n      // - On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      //   component stays as a function. This condition makes sure that we do not interpolate functions\n      //   which are basically components used as a selectors.\n      // - `style` could be a styled component from a babel plugin for component selectors, This condition\n      //   makes sure that we do not interpolate them.\n      if (style.__emotion_real === style) {\n        return style;\n      }\n      if (typeof style === 'function') {\n        return function styleFunctionProcessor(props) {\n          return processStyle(props, style);\n        };\n      }\n      if (isPlainObject(style)) {\n        const serialized = preprocessStyles(style);\n        if (!serialized.variants) {\n          return serialized.style;\n        }\n        return function styleObjectProcessor(props) {\n          return processStyle(props, serialized);\n        };\n      }\n      return style;\n    };\n    const muiStyledResolver = (...expressionsInput) => {\n      const expressionsHead = [];\n      const expressionsBody = expressionsInput.map(transformStyle);\n      const expressionsTail = [];\n\n      // Preprocess `props` to set the scoped theme value.\n      // This must run before any other expression.\n      expressionsHead.push(styleAttachTheme);\n      if (componentName && overridesResolver) {\n        expressionsTail.push(function styleThemeOverrides(props) {\n          const theme = props.theme;\n          const styleOverrides = theme.components?.[componentName]?.styleOverrides;\n          if (!styleOverrides) {\n            return null;\n          }\n          const resolvedStyleOverrides = {};\n\n          // TODO: v7 remove iteration and use `resolveStyleArg(styleOverrides[slot])` directly\n          // eslint-disable-next-line guard-for-in\n          for (const slotKey in styleOverrides) {\n            resolvedStyleOverrides[slotKey] = processStyle(props, styleOverrides[slotKey]);\n          }\n          return overridesResolver(props, resolvedStyleOverrides);\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsTail.push(function styleThemeVariants(props) {\n          const theme = props.theme;\n          const themeVariants = theme?.components?.[componentName]?.variants;\n          if (!themeVariants) {\n            return null;\n          }\n          return processStyleVariants(props, themeVariants);\n        });\n      }\n      if (!skipSx) {\n        expressionsTail.push(styleFunctionSx);\n      }\n\n      // This function can be called as a tagged template, so the first argument would contain\n      // CSS `string[]` values.\n      if (Array.isArray(expressionsBody[0])) {\n        const inputStrings = expressionsBody.shift();\n\n        // We need to add placeholders in the tagged template for the custom functions we have\n        // possibly added (attachTheme, overrides, variants, and sx).\n        const placeholdersHead = new Array(expressionsHead.length).fill('');\n        const placeholdersTail = new Array(expressionsTail.length).fill('');\n        let outputStrings;\n        // prettier-ignore\n        {\n          outputStrings = [...placeholdersHead, ...inputStrings, ...placeholdersTail];\n          outputStrings.raw = [...placeholdersHead, ...inputStrings.raw, ...placeholdersTail];\n        }\n\n        // The only case where we put something before `attachTheme`\n        expressionsHead.unshift(outputStrings);\n      }\n      const expressions = [...expressionsHead, ...expressionsBody, ...expressionsTail];\n      const Component = defaultStyledResolver(...expressions);\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        Component.displayName = generateDisplayName(componentName, componentSlot, tag);\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n  return styled;\n}\nfunction generateDisplayName(componentName, componentSlot, tag) {\n  if (componentName) {\n    return `${componentName}${capitalize(componentSlot || '')}`;\n  }\n  return `Styled(${getDisplayName(tag)})`;\n}\nfunction generateStyledLabel(componentName, componentSlot) {\n  let label;\n  if (process.env.NODE_ENV !== 'production') {\n    if (componentName) {\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      label = `${componentName}-${lowercaseFirstLetter(componentSlot || 'Root')}`;\n    }\n  }\n  return label;\n}\nfunction isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nfunction lowercaseFirstLetter(string) {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,oKAAA,CAAA,UAAW,AAAD;AAGrC,SAAS,kBAAkB,IAAI;IACpC,OAAO,SAAS,gBAAgB,SAAS,WAAW,SAAS,QAAQ,SAAS;AAChF;AACA,SAAS,yBAAyB,IAAI;IACpC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,OAAO,CAAC,QAAQ,SAAW,MAAM,CAAC,KAAK;AACzC;AACA,SAAS,YAAY,KAAK,EAAE,OAAO,EAAE,YAAY;IAC/C,MAAM,KAAK,GAAG,cAAc,MAAM,KAAK,IAAI,eAAe,MAAM,KAAK,CAAC,QAAQ,IAAI,MAAM,KAAK;AAC/F;AACA,SAAS,aAAa,KAAK,EAAE,KAAK;IAChC;;;;;;;GAOC,GAED,MAAM,gBAAgB,OAAO,UAAU,aAAa,MAAM,SAAS;IACnE,IAAI,MAAM,OAAO,CAAC,gBAAgB;QAChC,OAAO,cAAc,OAAO,CAAC,CAAA,WAAY,aAAa,OAAO;IAC/D;IACA,IAAI,MAAM,OAAO,CAAC,eAAe,WAAW;QAC1C,IAAI;QACJ,IAAI,cAAc,WAAW,EAAE;YAC7B,YAAY,cAAc,KAAK;QACjC,OAAO;YACL,MAAM,EACJ,QAAQ,EACR,GAAG,aACJ,GAAG;YACJ,YAAY;QACd;QACA,OAAO,qBAAqB,OAAO,cAAc,QAAQ,EAAE;YAAC;SAAU;IACxE;IACA,IAAI,eAAe,aAAa;QAC9B,OAAO,cAAc,KAAK;IAC5B;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;IACzD,IAAI,aAAa,2CAA2C;IAE5D,aAAa,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;QACxD,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;YACvC,gBAAgB;gBACd,GAAG,KAAK;gBACR,GAAG,MAAM,UAAU;gBACnB,YAAY,MAAM,UAAU;YAC9B;YACA,IAAI,CAAC,QAAQ,KAAK,CAAC,cAAc;gBAC/B;YACF;QACF,OAAO;YACL,IAAK,MAAM,OAAO,QAAQ,KAAK,CAAE;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,IAAI,MAAM,UAAU,EAAE,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,EAAE;oBACvF,SAAS;gBACX;YACF;QACF;QACA,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;YACvC,gBAAgB;gBACd,GAAG,KAAK;gBACR,GAAG,MAAM,UAAU;gBACnB,YAAY,MAAM,UAAU;YAC9B;YACA,QAAQ,IAAI,CAAC,QAAQ,KAAK,CAAC;QAC7B,OAAO;YACL,QAAQ,IAAI,CAAC,QAAQ,KAAK;QAC5B;IACF;IACA,OAAO;AACT;AACe,SAAS,aAAa,QAAQ,CAAC,CAAC;IAC7C,MAAM,EACJ,OAAO,EACP,eAAe,kBAAkB,EACjC,wBAAwB,iBAAiB,EACzC,wBAAwB,iBAAiB,EAC1C,GAAG;IACJ,SAAS,iBAAiB,KAAK;QAC7B,YAAY,OAAO,SAAS;IAC9B;IACA,MAAM,SAAS,CAAC,KAAK,eAAe,CAAC,CAAC;QACpC,6EAA6E;QAC7E,uEAAuE;QACvE,CAAA,GAAA,yKAAA,CAAA,wBAAY,AAAD,EAAE,KAAK,CAAA,SAAU,OAAO,MAAM,CAAC,CAAA,QAAS,UAAU,4KAAA,CAAA,UAAe;QAC5E,MAAM,EACJ,MAAM,aAAa,EACnB,MAAM,aAAa,EACnB,sBAAsB,yBAAyB,EAC/C,QAAQ,WAAW,EACnB,qEAAqE;QACrE,kEAAkE;QAClE,oBAAoB,yBAAyB,qBAAqB,eAAe,EACjF,GAAG,SACJ,GAAG;QAEJ,iHAAiH;QACjH,MAAM,uBAAuB,8BAA8B,YAAY,4BACvE,mDAAmD;QACnD,kEAAkE;QAClE,iBAAiB,kBAAkB,UAAU,kBAAkB,UAAU;QACzE,MAAM,SAAS,eAAe;QAC9B,IAAI,0BAA0B;QAE9B,mDAAmD;QACnD,kEAAkE;QAClE,IAAI,kBAAkB,UAAU,kBAAkB,QAAQ;YACxD,0BAA0B;QAC5B,OAAO,IAAI,eAAe;YACxB,2BAA2B;YAC3B,0BAA0B;QAC5B,OAAO,IAAI,YAAY,MAAM;YAC3B,+EAA+E;YAC/E,0BAA0B;QAC5B;QACA,MAAM,wBAAwB,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE,KAAK;YACpD,mBAAmB;YACnB,OAAO,oBAAoB,eAAe;YAC1C,GAAG,OAAO;QACZ;QACA,MAAM,iBAAiB,CAAA;YACrB,+FAA+F;YAC/F,kGAAkG;YAClG,wDAAwD;YACxD,oGAAoG;YACpG,gDAAgD;YAChD,IAAI,MAAM,cAAc,KAAK,OAAO;gBAClC,OAAO;YACT;YACA,IAAI,OAAO,UAAU,YAAY;gBAC/B,OAAO,SAAS,uBAAuB,KAAK;oBAC1C,OAAO,aAAa,OAAO;gBAC7B;YACF;YACA,IAAI,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACxB,MAAM,aAAa,CAAA,GAAA,0JAAA,CAAA,UAAgB,AAAD,EAAE;gBACpC,IAAI,CAAC,WAAW,QAAQ,EAAE;oBACxB,OAAO,WAAW,KAAK;gBACzB;gBACA,OAAO,SAAS,qBAAqB,KAAK;oBACxC,OAAO,aAAa,OAAO;gBAC7B;YACF;YACA,OAAO;QACT;QACA,MAAM,oBAAoB,CAAC,GAAG;YAC5B,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB,iBAAiB,GAAG,CAAC;YAC7C,MAAM,kBAAkB,EAAE;YAE1B,oDAAoD;YACpD,6CAA6C;YAC7C,gBAAgB,IAAI,CAAC;YACrB,IAAI,iBAAiB,mBAAmB;gBACtC,gBAAgB,IAAI,CAAC,SAAS,oBAAoB,KAAK;oBACrD,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,iBAAiB,MAAM,UAAU,EAAE,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,gBAAgB;wBACnB,OAAO;oBACT;oBACA,MAAM,yBAAyB,CAAC;oBAEhC,qFAAqF;oBACrF,wCAAwC;oBACxC,IAAK,MAAM,WAAW,eAAgB;wBACpC,sBAAsB,CAAC,QAAQ,GAAG,aAAa,OAAO,cAAc,CAAC,QAAQ;oBAC/E;oBACA,OAAO,kBAAkB,OAAO;gBAClC;YACF;YACA,IAAI,iBAAiB,CAAC,sBAAsB;gBAC1C,gBAAgB,IAAI,CAAC,SAAS,mBAAmB,KAAK;oBACpD,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,gBAAgB,OAAO,YAAY,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,eAAe;wBAClB,OAAO;oBACT;oBACA,OAAO,qBAAqB,OAAO;gBACrC;YACF;YACA,IAAI,CAAC,QAAQ;gBACX,gBAAgB,IAAI,CAAC,4KAAA,CAAA,UAAe;YACtC;YAEA,wFAAwF;YACxF,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,GAAG;gBACrC,MAAM,eAAe,gBAAgB,KAAK;gBAE1C,sFAAsF;gBACtF,6DAA6D;gBAC7D,MAAM,mBAAmB,IAAI,MAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC;gBAChE,MAAM,mBAAmB,IAAI,MAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC;gBAChE,IAAI;gBACJ,kBAAkB;gBAClB;oBACE,gBAAgB;2BAAI;2BAAqB;2BAAiB;qBAAiB;oBAC3E,cAAc,GAAG,GAAG;2BAAI;2BAAqB,aAAa,GAAG;2BAAK;qBAAiB;gBACrF;gBAEA,4DAA4D;gBAC5D,gBAAgB,OAAO,CAAC;YAC1B;YACA,MAAM,cAAc;mBAAI;mBAAoB;mBAAoB;aAAgB;YAChF,MAAM,YAAY,yBAAyB;YAC3C,IAAI,IAAI,OAAO,EAAE;gBACf,UAAU,OAAO,GAAG,IAAI,OAAO;YACjC;YACA,wCAA2C;gBACzC,UAAU,WAAW,GAAG,oBAAoB,eAAe,eAAe;YAC5E;YACA,OAAO;QACT;QACA,IAAI,sBAAsB,UAAU,EAAE;YACpC,kBAAkB,UAAU,GAAG,sBAAsB,UAAU;QACjE;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,aAAa,EAAE,aAAa,EAAE,GAAG;IAC5D,IAAI,eAAe;QACjB,OAAO,GAAG,gBAAgB,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,iBAAiB,KAAK;IAC7D;IACA,OAAO,CAAC,OAAO,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,KAAK,CAAC,CAAC;AACzC;AACA,SAAS,oBAAoB,aAAa,EAAE,aAAa;IACvD,IAAI;IACJ,wCAA2C;QACzC,IAAI,eAAe;YACjB,qEAAqE;YACrE,kEAAkE;YAClE,QAAQ,GAAG,cAAc,CAAC,EAAE,qBAAqB,iBAAiB,SAAS;QAC7E;IACF;IACA,OAAO;AACT;AACA,SAAS,cAAc,MAAM;IAC3B,2BAA2B;IAC3B,IAAK,MAAM,KAAK,OAAQ;QACtB,OAAO;IACT;IACA,OAAO;AACT;AAEA,uHAAuH;AACvH,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,QAAQ,YACtB,oCAAoC;IACpC,mCAAmC;IACnC,6BAA6B;IAC7B,IAAI,UAAU,CAAC,KAAK;AACtB;AACA,SAAS,qBAAqB,MAAM;IAClC,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/styled/styled.js"], "sourcesContent": ["import createStyled from \"../createStyled/index.js\";\nconst styled = createStyled();\nexport default styled;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD;uCACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/Container/createContainer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useThemePropsSystem from \"../useThemeProps/index.js\";\nimport systemStyled from \"../styled/index.js\";\nimport createTheme from \"../createTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: '<PERSON>i<PERSON>ontainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => ({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    ...(!ownerState.disableGutters && {\n      paddingLeft: theme.spacing(2),\n      paddingRight: theme.spacing(2),\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up('sm')]: {\n        paddingLeft: theme.spacing(3),\n        paddingRight: theme.spacing(3)\n      }\n    })\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => ({\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    ...(ownerState.maxWidth === 'xs' && {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up('xs')]: {\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n      }\n    }),\n    ...(ownerState.maxWidth &&\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    ownerState.maxWidth !== 'xs' && {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up(ownerState.maxWidth)]: {\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n      }\n    })\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n      className,\n      component = 'div',\n      disableGutters = false,\n      fixed = false,\n      maxWidth = 'lg',\n      classes: classesProp,\n      ...other\n    } = props;\n    const ownerState = {\n      ...props,\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    };\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (\n      /*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, {\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref,\n        ...other\n      })\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAYA,MAAM,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAW,AAAD;AAC/B,MAAM,+BAA+B,CAAA,GAAA,0JAAA,CAAA,UAAY,AAAD,EAAE,OAAO;IACvD,MAAM;IACN,MAAM;IACN,mBAAmB,CAAC,OAAO;QACzB,MAAM,EACJ,UAAU,EACX,GAAG;QACJ,OAAO;YAAC,OAAO,IAAI;YAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,OAAO,WAAW,QAAQ,IAAI,CAAC;YAAE,WAAW,KAAK,IAAI,OAAO,KAAK;YAAE,WAAW,cAAc,IAAI,OAAO,cAAc;SAAC;IAC1K;AACF;AACA,MAAM,uBAAuB,CAAA,UAAW,CAAA,GAAA,wKAAA,CAAA,UAAmB,AAAD,EAAE;QAC1D,OAAO;QACP,MAAM;QACN;IACF;AACA,MAAM,oBAAoB,CAAC,YAAY;IACrC,MAAM,2BAA2B,CAAA;QAC/B,OAAO,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,eAAe;IAC7C;IACA,MAAM,EACJ,OAAO,EACP,KAAK,EACL,cAAc,EACd,QAAQ,EACT,GAAG;IACJ,MAAM,QAAQ;QACZ,MAAM;YAAC;YAAQ,YAAY,CAAC,QAAQ,EAAE,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,OAAO,YAAY;YAAE,SAAS;YAAS,kBAAkB;SAAiB;IAC7H;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,0BAA0B;AACzD;AACe,SAAS,gBAAgB,UAAU,CAAC,CAAC;IAClD,MAAM,EACJ,qFAAqF;IACrF,wBAAwB,4BAA4B,EACpD,gBAAgB,oBAAoB,EACpC,gBAAgB,cAAc,EAC/B,GAAG;IACJ,MAAM,gBAAgB,sBAAsB,CAAC,EAC3C,KAAK,EACL,UAAU,EACX,GAAK,CAAC;YACL,OAAO;YACP,YAAY;YACZ,WAAW;YACX,aAAa;YACb,GAAI,CAAC,WAAW,cAAc,IAAI;gBAChC,aAAa,MAAM,OAAO,CAAC;gBAC3B,cAAc,MAAM,OAAO,CAAC;gBAC5B,sEAAsE;gBACtE,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE;oBAC5B,aAAa,MAAM,OAAO,CAAC;oBAC3B,cAAc,MAAM,OAAO,CAAC;gBAC9B;YACF,CAAC;QACH,CAAC,GAAG,CAAC,EACH,KAAK,EACL,UAAU,EACX,GAAK,WAAW,KAAK,IAAI,OAAO,IAAI,CAAC,MAAM,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,KAAK;YAC3E,MAAM,aAAa;YACnB,MAAM,QAAQ,MAAM,WAAW,CAAC,MAAM,CAAC,WAAW;YAClD,IAAI,UAAU,GAAG;gBACf,aAAa;gBACb,GAAG,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,YAAY,GAAG;oBACtC,UAAU,GAAG,QAAQ,MAAM,WAAW,CAAC,IAAI,EAAE;gBAC/C;YACF;YACA,OAAO;QACT,GAAG,CAAC,IAAI,CAAC,EACP,KAAK,EACL,UAAU,EACX,GAAK,CAAC;YACL,sEAAsE;YACtE,GAAI,WAAW,QAAQ,KAAK,QAAQ;gBAClC,sEAAsE;gBACtE,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE;oBAC5B,sEAAsE;oBACtE,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE;gBAClD;YACF,CAAC;YACD,GAAI,WAAW,QAAQ,IACvB,sEAAsE;YACtE,WAAW,QAAQ,KAAK,QAAQ;gBAC9B,sEAAsE;gBACtE,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,WAAW,QAAQ,EAAE,EAAE;oBAC3C,sEAAsE;oBACtE,UAAU,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,WAAW,QAAQ,CAAC,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE;gBACvF;YACF,CAAC;QACH,CAAC;IACD,MAAM,YAAY,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,UAAU,OAAO,EAAE,GAAG;QAC7E,MAAM,QAAQ,cAAc;QAC5B,MAAM,EACJ,SAAS,EACT,YAAY,KAAK,EACjB,iBAAiB,KAAK,EACtB,QAAQ,KAAK,EACb,WAAW,IAAI,EACf,SAAS,WAAW,EACpB,GAAG,OACJ,GAAG;QACJ,MAAM,aAAa;YACjB,GAAG,KAAK;YACR;YACA;YACA;YACA;QACF;QAEA,sEAAsE;QACtE,MAAM,UAAU,kBAAkB,YAAY;QAC9C,OACE,WAAW,GACX,kDAAkD;QAClD,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,eAAe;YAClB,IAAI;YAGJ,YAAY;YACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;YAC9B,KAAK;YACL,GAAG,KAAK;QACV;IAEJ;IACA,uCAAwC,UAAU,SAAS,GAA0B;QACnF,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;QACxB,SAAS,sIAAA,CAAA,UAAS,CAAC,MAAM;QACzB,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;QAC3B,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;QAChC,gBAAgB,sIAAA,CAAA,UAAS,CAAC,IAAI;QAC9B,OAAO,sIAAA,CAAA,UAAS,CAAC,IAAI;QACrB,UAAU,uIAAU,mCAAmC,GAA7C,CAAA,UAAS,CAAuC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAM;YAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAC9I,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;IACxJ;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/memoTheme.js"], "sourcesContent": ["import preprocessStyles from \"./preprocessStyles.js\";\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n// We need to pass an argument as `{ theme }` for PigmentCSS, but we don't want to\n// allocate more objects.\nconst arg = {\n  theme: undefined\n};\n\n/**\n * Memoize style function on theme.\n * Intended to be used in styled() calls that only need access to the theme.\n */\nexport default function unstable_memoTheme(styleFn) {\n  let lastValue;\n  let lastTheme;\n  return function styleMemoized(props) {\n    let value = lastValue;\n    if (value === undefined || props.theme !== lastTheme) {\n      arg.theme = props.theme;\n      value = preprocessStyles(styleFn(arg));\n      lastValue = value;\n      lastTheme = props.theme;\n    }\n    return value;\n  };\n}"], "names": [], "mappings": ";;;AAAA;;AAEA,uDAAuD,GAEvD,kFAAkF;AAClF,yBAAyB;AACzB,MAAM,MAAM;IACV,OAAO;AACT;AAMe,SAAS,mBAAmB,OAAO;IAChD,IAAI;IACJ,IAAI;IACJ,OAAO,SAAS,cAAc,KAAK;QACjC,IAAI,QAAQ;QACZ,IAAI,UAAU,aAAa,MAAM,KAAK,KAAK,WAAW;YACpD,IAAI,KAAK,GAAG,MAAM,KAAK;YACvB,QAAQ,CAAA,GAAA,0JAAA,CAAA,UAAgB,AAAD,EAAE,QAAQ;YACjC,YAAY;YACZ,YAAY,MAAM,KAAK;QACzB;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/Grid/traverseBreakpoints.js"], "sourcesContent": ["export const filterBreakpointKeys = (breakpointsKeys, responsiveKeys) => breakpointsKeys.filter(key => responsiveKeys.includes(key));\nexport const traverseBreakpoints = (breakpoints, responsive, iterator) => {\n  const smallestBreakpoint = breakpoints.keys[0]; // the keys is sorted from smallest to largest by `createBreakpoints`.\n\n  if (Array.isArray(responsive)) {\n    responsive.forEach((breakpointValue, index) => {\n      iterator((responsiveStyles, style) => {\n        if (index <= breakpoints.keys.length - 1) {\n          if (index === 0) {\n            Object.assign(responsiveStyles, style);\n          } else {\n            responsiveStyles[breakpoints.up(breakpoints.keys[index])] = style;\n          }\n        }\n      }, breakpointValue);\n    });\n  } else if (responsive && typeof responsive === 'object') {\n    // prevent null\n    // responsive could be a very big object, pick the smallest responsive values\n\n    const keys = Object.keys(responsive).length > breakpoints.keys.length ? breakpoints.keys : filterBreakpointKeys(breakpoints.keys, Object.keys(responsive));\n    keys.forEach(key => {\n      if (breakpoints.keys.includes(key)) {\n        // @ts-ignore already checked that responsive is an object\n        const breakpointValue = responsive[key];\n        if (breakpointValue !== undefined) {\n          iterator((responsiveStyles, style) => {\n            if (smallestBreakpoint === key) {\n              Object.assign(responsiveStyles, style);\n            } else {\n              responsiveStyles[breakpoints.up(key)] = style;\n            }\n          }, breakpointValue);\n        }\n      }\n    });\n  } else if (typeof responsive === 'number' || typeof responsive === 'string') {\n    iterator((responsiveStyles, style) => {\n      Object.assign(responsiveStyles, style);\n    }, responsive);\n  }\n};"], "names": [], "mappings": ";;;;AAAO,MAAM,uBAAuB,CAAC,iBAAiB,iBAAmB,gBAAgB,MAAM,CAAC,CAAA,MAAO,eAAe,QAAQ,CAAC;AACxH,MAAM,sBAAsB,CAAC,aAAa,YAAY;IAC3D,MAAM,qBAAqB,YAAY,IAAI,CAAC,EAAE,EAAE,sEAAsE;IAEtH,IAAI,MAAM,OAAO,CAAC,aAAa;QAC7B,WAAW,OAAO,CAAC,CAAC,iBAAiB;YACnC,SAAS,CAAC,kBAAkB;gBAC1B,IAAI,SAAS,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;oBACxC,IAAI,UAAU,GAAG;wBACf,OAAO,MAAM,CAAC,kBAAkB;oBAClC,OAAO;wBACL,gBAAgB,CAAC,YAAY,EAAE,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE,GAAG;oBAC9D;gBACF;YACF,GAAG;QACL;IACF,OAAO,IAAI,cAAc,OAAO,eAAe,UAAU;QACvD,eAAe;QACf,6EAA6E;QAE7E,MAAM,OAAO,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,YAAY,IAAI,CAAC,MAAM,GAAG,YAAY,IAAI,GAAG,qBAAqB,YAAY,IAAI,EAAE,OAAO,IAAI,CAAC;QAC9I,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAClC,0DAA0D;gBAC1D,MAAM,kBAAkB,UAAU,CAAC,IAAI;gBACvC,IAAI,oBAAoB,WAAW;oBACjC,SAAS,CAAC,kBAAkB;wBAC1B,IAAI,uBAAuB,KAAK;4BAC9B,OAAO,MAAM,CAAC,kBAAkB;wBAClC,OAAO;4BACL,gBAAgB,CAAC,YAAY,EAAE,CAAC,KAAK,GAAG;wBAC1C;oBACF,GAAG;gBACL;YACF;QACF;IACF,OAAO,IAAI,OAAO,eAAe,YAAY,OAAO,eAAe,UAAU;QAC3E,SAAS,CAAC,kBAAkB;YAC1B,OAAO,MAAM,CAAC,kBAAkB;QAClC,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/Grid/gridGenerator.js"], "sourcesContent": ["import { traverseBreakpoints } from \"./traverseBreakpoints.js\";\nfunction getSelfSpacingVar(axis) {\n  return `--Grid-${axis}Spacing`;\n}\nfunction getParentSpacingVar(axis) {\n  return `--Grid-parent-${axis}Spacing`;\n}\nconst selfColumnsVar = '--Grid-columns';\nconst parentColumnsVar = '--Grid-parent-columns';\nexport const generateGridSizeStyles = ({\n  theme,\n  ownerState\n}) => {\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.size, (appendStyle, value) => {\n    let style = {};\n    if (value === 'grow') {\n      style = {\n        flexBasis: 0,\n        flexGrow: 1,\n        maxWidth: '100%'\n      };\n    }\n    if (value === 'auto') {\n      style = {\n        flexBasis: 'auto',\n        flexGrow: 0,\n        flexShrink: 0,\n        maxWidth: 'none',\n        width: 'auto'\n      };\n    }\n    if (typeof value === 'number') {\n      style = {\n        flexGrow: 0,\n        flexBasis: 'auto',\n        width: `calc(100% * ${value} / var(${parentColumnsVar}) - (var(${parentColumnsVar}) - ${value}) * (var(${getParentSpacingVar('column')}) / var(${parentColumnsVar})))`\n      };\n    }\n    appendStyle(styles, style);\n  });\n  return styles;\n};\nexport const generateGridOffsetStyles = ({\n  theme,\n  ownerState\n}) => {\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.offset, (appendStyle, value) => {\n    let style = {};\n    if (value === 'auto') {\n      style = {\n        marginLeft: 'auto'\n      };\n    }\n    if (typeof value === 'number') {\n      style = {\n        marginLeft: value === 0 ? '0px' : `calc(100% * ${value} / var(${parentColumnsVar}) + var(${getParentSpacingVar('column')}) * ${value} / var(${parentColumnsVar}))`\n      };\n    }\n    appendStyle(styles, style);\n  });\n  return styles;\n};\nexport const generateGridColumnsStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {\n    [selfColumnsVar]: 12\n  };\n  traverseBreakpoints(theme.breakpoints, ownerState.columns, (appendStyle, value) => {\n    const columns = value ?? 12;\n    appendStyle(styles, {\n      [selfColumnsVar]: columns,\n      '> *': {\n        [parentColumnsVar]: columns\n      }\n    });\n  });\n  return styles;\n};\nexport const generateGridRowSpacingStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.rowSpacing, (appendStyle, value) => {\n    const spacing = typeof value === 'string' ? value : theme.spacing?.(value);\n    appendStyle(styles, {\n      [getSelfSpacingVar('row')]: spacing,\n      '> *': {\n        [getParentSpacingVar('row')]: spacing\n      }\n    });\n  });\n  return styles;\n};\nexport const generateGridColumnSpacingStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.columnSpacing, (appendStyle, value) => {\n    const spacing = typeof value === 'string' ? value : theme.spacing?.(value);\n    appendStyle(styles, {\n      [getSelfSpacingVar('column')]: spacing,\n      '> *': {\n        [getParentSpacingVar('column')]: spacing\n      }\n    });\n  });\n  return styles;\n};\nexport const generateGridDirectionStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.direction, (appendStyle, value) => {\n    appendStyle(styles, {\n      flexDirection: value\n    });\n  });\n  return styles;\n};\nexport const generateGridStyles = ({\n  ownerState\n}) => {\n  return {\n    minWidth: 0,\n    boxSizing: 'border-box',\n    ...(ownerState.container && {\n      display: 'flex',\n      flexWrap: 'wrap',\n      ...(ownerState.wrap && ownerState.wrap !== 'wrap' && {\n        flexWrap: ownerState.wrap\n      }),\n      gap: `var(${getSelfSpacingVar('row')}) var(${getSelfSpacingVar('column')})`\n    })\n  };\n};\nexport const generateSizeClassNames = size => {\n  const classNames = [];\n  Object.entries(size).forEach(([key, value]) => {\n    if (value !== false && value !== undefined) {\n      classNames.push(`grid-${key}-${String(value)}`);\n    }\n  });\n  return classNames;\n};\nexport const generateSpacingClassNames = (spacing, smallestBreakpoint = 'xs') => {\n  function isValidSpacing(val) {\n    if (val === undefined) {\n      return false;\n    }\n    return typeof val === 'string' && !Number.isNaN(Number(val)) || typeof val === 'number' && val > 0;\n  }\n  if (isValidSpacing(spacing)) {\n    return [`spacing-${smallestBreakpoint}-${String(spacing)}`];\n  }\n  if (typeof spacing === 'object' && !Array.isArray(spacing)) {\n    const classNames = [];\n    Object.entries(spacing).forEach(([key, value]) => {\n      if (isValidSpacing(value)) {\n        classNames.push(`spacing-${key}-${String(value)}`);\n      }\n    });\n    return classNames;\n  }\n  return [];\n};\nexport const generateDirectionClasses = direction => {\n  if (direction === undefined) {\n    return [];\n  }\n  if (typeof direction === 'object') {\n    return Object.entries(direction).map(([key, value]) => `direction-${key}-${value}`);\n  }\n  return [`direction-xs-${String(direction)}`];\n};"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC;AAChC;AACA,SAAS,oBAAoB,IAAI;IAC/B,OAAO,CAAC,cAAc,EAAE,KAAK,OAAO,CAAC;AACvC;AACA,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AAClB,MAAM,yBAAyB,CAAC,EACrC,KAAK,EACL,UAAU,EACX;IACC,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,IAAI,EAAE,CAAC,aAAa;QACpE,IAAI,QAAQ,CAAC;QACb,IAAI,UAAU,QAAQ;YACpB,QAAQ;gBACN,WAAW;gBACX,UAAU;gBACV,UAAU;YACZ;QACF;QACA,IAAI,UAAU,QAAQ;YACpB,QAAQ;gBACN,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,UAAU;gBACV,OAAO;YACT;QACF;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ;gBACN,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,YAAY,EAAE,MAAM,OAAO,EAAE,iBAAiB,SAAS,EAAE,iBAAiB,IAAI,EAAE,MAAM,SAAS,EAAE,oBAAoB,UAAU,QAAQ,EAAE,iBAAiB,GAAG,CAAC;YACxK;QACF;QACA,YAAY,QAAQ;IACtB;IACA,OAAO;AACT;AACO,MAAM,2BAA2B,CAAC,EACvC,KAAK,EACL,UAAU,EACX;IACC,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,MAAM,EAAE,CAAC,aAAa;QACtE,IAAI,QAAQ,CAAC;QACb,IAAI,UAAU,QAAQ;YACpB,QAAQ;gBACN,YAAY;YACd;QACF;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ;gBACN,YAAY,UAAU,IAAI,QAAQ,CAAC,YAAY,EAAE,MAAM,OAAO,EAAE,iBAAiB,QAAQ,EAAE,oBAAoB,UAAU,IAAI,EAAE,MAAM,OAAO,EAAE,iBAAiB,EAAE,CAAC;YACpK;QACF;QACA,YAAY,QAAQ;IACtB;IACA,OAAO;AACT;AACO,MAAM,4BAA4B,CAAC,EACxC,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS;QACb,CAAC,eAAe,EAAE;IACpB;IACA,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,OAAO,EAAE,CAAC,aAAa;QACvE,MAAM,UAAU,SAAS;QACzB,YAAY,QAAQ;YAClB,CAAC,eAAe,EAAE;YAClB,OAAO;gBACL,CAAC,iBAAiB,EAAE;YACtB;QACF;IACF;IACA,OAAO;AACT;AACO,MAAM,+BAA+B,CAAC,EAC3C,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,UAAU,EAAE,CAAC,aAAa;QAC1E,MAAM,UAAU,OAAO,UAAU,WAAW,QAAQ,MAAM,OAAO,GAAG;QACpE,YAAY,QAAQ;YAClB,CAAC,kBAAkB,OAAO,EAAE;YAC5B,OAAO;gBACL,CAAC,oBAAoB,OAAO,EAAE;YAChC;QACF;IACF;IACA,OAAO;AACT;AACO,MAAM,kCAAkC,CAAC,EAC9C,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,aAAa,EAAE,CAAC,aAAa;QAC7E,MAAM,UAAU,OAAO,UAAU,WAAW,QAAQ,MAAM,OAAO,GAAG;QACpE,YAAY,QAAQ;YAClB,CAAC,kBAAkB,UAAU,EAAE;YAC/B,OAAO;gBACL,CAAC,oBAAoB,UAAU,EAAE;YACnC;QACF;IACF;IACA,OAAO;AACT;AACO,MAAM,8BAA8B,CAAC,EAC1C,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,SAAS,EAAE,CAAC,aAAa;QACzE,YAAY,QAAQ;YAClB,eAAe;QACjB;IACF;IACA,OAAO;AACT;AACO,MAAM,qBAAqB,CAAC,EACjC,UAAU,EACX;IACC,OAAO;QACL,UAAU;QACV,WAAW;QACX,GAAI,WAAW,SAAS,IAAI;YAC1B,SAAS;YACT,UAAU;YACV,GAAI,WAAW,IAAI,IAAI,WAAW,IAAI,KAAK,UAAU;gBACnD,UAAU,WAAW,IAAI;YAC3B,CAAC;YACD,KAAK,CAAC,IAAI,EAAE,kBAAkB,OAAO,MAAM,EAAE,kBAAkB,UAAU,CAAC,CAAC;QAC7E,CAAC;IACH;AACF;AACO,MAAM,yBAAyB,CAAA;IACpC,MAAM,aAAa,EAAE;IACrB,OAAO,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACxC,IAAI,UAAU,SAAS,UAAU,WAAW;YAC1C,WAAW,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;QAChD;IACF;IACA,OAAO;AACT;AACO,MAAM,4BAA4B,CAAC,SAAS,qBAAqB,IAAI;IAC1E,SAAS,eAAe,GAAG;QACzB,IAAI,QAAQ,WAAW;YACrB,OAAO;QACT;QACA,OAAO,OAAO,QAAQ,YAAY,CAAC,OAAO,KAAK,CAAC,OAAO,SAAS,OAAO,QAAQ,YAAY,MAAM;IACnG;IACA,IAAI,eAAe,UAAU;QAC3B,OAAO;YAAC,CAAC,QAAQ,EAAE,mBAAmB,CAAC,EAAE,OAAO,UAAU;SAAC;IAC7D;IACA,IAAI,OAAO,YAAY,YAAY,CAAC,MAAM,OAAO,CAAC,UAAU;QAC1D,MAAM,aAAa,EAAE;QACrB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,eAAe,QAAQ;gBACzB,WAAW,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;YACnD;QACF;QACA,OAAO;IACT;IACA,OAAO,EAAE;AACX;AACO,MAAM,2BAA2B,CAAA;IACtC,IAAI,cAAc,WAAW;QAC3B,OAAO,EAAE;IACX;IACA,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,OAAO;IACpF;IACA,OAAO;QAAC,CAAC,aAAa,EAAE,OAAO,YAAY;KAAC;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/Grid/deleteLegacyGridProps.js"], "sourcesContent": ["const getLegacyGridWarning = propName => {\n  if (['item', 'zeroMinWidth'].includes(propName)) {\n    return `The \\`${propName}\\` prop has been removed and is no longer necessary. You can safely remove it.`;\n  }\n\n  // #host-reference\n  return `The \\`${propName}\\` prop has been removed. See https://mui.com/material-ui/migration/upgrade-to-grid-v2/ for migration instructions.`;\n};\nconst warnedAboutProps = [];\n\n/**\n * Deletes the legacy Grid component props from the `props` object and warns once about them if found.\n *\n * @param {object} props The props object to remove the legacy Grid props from.\n * @param {Breakpoints} breakpoints The breakpoints object.\n */\nexport default function deleteLegacyGridProps(props, breakpoints) {\n  const propsToWarn = [];\n  if (props.item !== undefined) {\n    delete props.item;\n    propsToWarn.push('item');\n  }\n  if (props.zeroMinWidth !== undefined) {\n    delete props.zeroMinWidth;\n    propsToWarn.push('zeroMinWidth');\n  }\n  breakpoints.keys.forEach(breakpoint => {\n    if (props[breakpoint] !== undefined) {\n      propsToWarn.push(breakpoint);\n      delete props[breakpoint];\n    }\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    propsToWarn.forEach(prop => {\n      if (!warnedAboutProps.includes(prop)) {\n        warnedAboutProps.push(prop);\n        console.warn(`MUI Grid: ${getLegacyGridWarning(prop)}\\n`);\n      }\n    });\n  }\n}"], "names": [], "mappings": ";;;AAAA,MAAM,uBAAuB,CAAA;IAC3B,IAAI;QAAC;QAAQ;KAAe,CAAC,QAAQ,CAAC,WAAW;QAC/C,OAAO,CAAC,MAAM,EAAE,SAAS,8EAA8E,CAAC;IAC1G;IAEA,kBAAkB;IAClB,OAAO,CAAC,MAAM,EAAE,SAAS,mHAAmH,CAAC;AAC/I;AACA,MAAM,mBAAmB,EAAE;AAQZ,SAAS,sBAAsB,KAAK,EAAE,WAAW;IAC9D,MAAM,cAAc,EAAE;IACtB,IAAI,MAAM,IAAI,KAAK,WAAW;QAC5B,OAAO,MAAM,IAAI;QACjB,YAAY,IAAI,CAAC;IACnB;IACA,IAAI,MAAM,YAAY,KAAK,WAAW;QACpC,OAAO,MAAM,YAAY;QACzB,YAAY,IAAI,CAAC;IACnB;IACA,YAAY,IAAI,CAAC,OAAO,CAAC,CAAA;QACvB,IAAI,KAAK,CAAC,WAAW,KAAK,WAAW;YACnC,YAAY,IAAI,CAAC;YACjB,OAAO,KAAK,CAAC,WAAW;QAC1B;IACF;IACA,wCAA2C;QACzC,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,CAAC,iBAAiB,QAAQ,CAAC,OAAO;gBACpC,iBAAiB,IAAI,CAAC;gBACtB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,qBAAqB,MAAM,EAAE,CAAC;YAC1D;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/system/esm/Grid/createGrid.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport isMuiElement from '@mui/utils/isMuiElement';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport systemStyled from \"../styled/index.js\";\nimport useThemePropsSystem from \"../useThemeProps/index.js\";\nimport useThemeSystem from \"../useTheme/index.js\";\nimport { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport createTheme from \"../createTheme/index.js\";\nimport { generateGridStyles, generateGridSizeStyles, generateGridColumnsStyles, generateGridColumnSpacingStyles, generateGridRowSpacingStyles, generateGridDirectionStyles, generateGridOffsetStyles, generateSizeClassNames, generateSpacingClassNames, generateDirectionClasses } from \"./gridGenerator.js\";\nimport deleteLegacyGridProps from \"./deleteLegacyGridProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\n\n// widening Theme to any so that the consumer can own the theme structure.\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: 'MuiGrid',\n  slot: 'Root'\n});\nfunction useThemePropsDefault(props) {\n  return useThemePropsSystem({\n    props,\n    name: 'MuiGrid',\n    defaultTheme\n  });\n}\nexport default function createGrid(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    useTheme = useThemeSystem,\n    componentName = 'MuiGrid'\n  } = options;\n  const useUtilityClasses = (ownerState, theme) => {\n    const {\n      container,\n      direction,\n      spacing,\n      wrap,\n      size\n    } = ownerState;\n    const slots = {\n      root: ['root', container && 'container', wrap !== 'wrap' && `wrap-xs-${String(wrap)}`, ...generateDirectionClasses(direction), ...generateSizeClassNames(size), ...(container ? generateSpacingClassNames(spacing, theme.breakpoints.keys[0]) : [])]\n    };\n    return composeClasses(slots, slot => generateUtilityClass(componentName, slot), {});\n  };\n  function parseResponsiveProp(propValue, breakpoints, shouldUseValue = () => true) {\n    const parsedProp = {};\n    if (propValue === null) {\n      return parsedProp;\n    }\n    if (Array.isArray(propValue)) {\n      propValue.forEach((value, index) => {\n        if (value !== null && shouldUseValue(value) && breakpoints.keys[index]) {\n          parsedProp[breakpoints.keys[index]] = value;\n        }\n      });\n    } else if (typeof propValue === 'object') {\n      Object.keys(propValue).forEach(key => {\n        const value = propValue[key];\n        if (value !== null && value !== undefined && shouldUseValue(value)) {\n          parsedProp[key] = value;\n        }\n      });\n    } else {\n      parsedProp[breakpoints.keys[0]] = propValue;\n    }\n    return parsedProp;\n  }\n  const GridRoot = createStyledComponent(generateGridColumnsStyles, generateGridColumnSpacingStyles, generateGridRowSpacingStyles, generateGridSizeStyles, generateGridDirectionStyles, generateGridStyles, generateGridOffsetStyles);\n  const Grid = /*#__PURE__*/React.forwardRef(function Grid(inProps, ref) {\n    const theme = useTheme();\n    const themeProps = useThemeProps(inProps);\n    const props = extendSxProp(themeProps); // `color` type conflicts with html color attribute.\n\n    // TODO v8: Remove when removing the legacy Grid component\n    deleteLegacyGridProps(props, theme.breakpoints);\n    const {\n      className,\n      children,\n      columns: columnsProp = 12,\n      container = false,\n      component = 'div',\n      direction = 'row',\n      wrap = 'wrap',\n      size: sizeProp = {},\n      offset: offsetProp = {},\n      spacing: spacingProp = 0,\n      rowSpacing: rowSpacingProp = spacingProp,\n      columnSpacing: columnSpacingProp = spacingProp,\n      unstable_level: level = 0,\n      ...other\n    } = props;\n    const size = parseResponsiveProp(sizeProp, theme.breakpoints, val => val !== false);\n    const offset = parseResponsiveProp(offsetProp, theme.breakpoints);\n    const columns = inProps.columns ?? (level ? undefined : columnsProp);\n    const spacing = inProps.spacing ?? (level ? undefined : spacingProp);\n    const rowSpacing = inProps.rowSpacing ?? inProps.spacing ?? (level ? undefined : rowSpacingProp);\n    const columnSpacing = inProps.columnSpacing ?? inProps.spacing ?? (level ? undefined : columnSpacingProp);\n    const ownerState = {\n      ...props,\n      level,\n      columns,\n      container,\n      direction,\n      wrap,\n      spacing,\n      rowSpacing,\n      columnSpacing,\n      size,\n      offset\n    };\n    const classes = useUtilityClasses(ownerState, theme);\n    return /*#__PURE__*/_jsx(GridRoot, {\n      ref: ref,\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ...other,\n      children: React.Children.map(children, child => {\n        if (/*#__PURE__*/React.isValidElement(child) && isMuiElement(child, ['Grid']) && container && child.props.container) {\n          return /*#__PURE__*/React.cloneElement(child, {\n            unstable_level: child.props?.unstable_level ?? level + 1\n          });\n        }\n        return child;\n      })\n    });\n  });\n  process.env.NODE_ENV !== \"production\" ? Grid.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    className: PropTypes.string,\n    columns: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number, PropTypes.object]),\n    columnSpacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n    component: PropTypes.elementType,\n    container: PropTypes.bool,\n    direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n    offset: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])), PropTypes.object]),\n    rowSpacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n    size: PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number])), PropTypes.object]),\n    spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    wrap: PropTypes.oneOf(['nowrap', 'wrap-reverse', 'wrap'])\n  } : void 0;\n\n  // @ts-ignore internal logic for nested grid\n  Grid.muiName = 'Grid';\n  return Grid;\n}"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;AAgBA,MAAM,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAW,AAAD;AAE/B,0EAA0E;AAC1E,MAAM,+BAA+B,CAAA,GAAA,0JAAA,CAAA,UAAY,AAAD,EAAE,OAAO;IACvD,MAAM;IACN,MAAM;AACR;AACA,SAAS,qBAAqB,KAAK;IACjC,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAmB,AAAD,EAAE;QACzB;QACA,MAAM;QACN;IACF;AACF;AACe,SAAS,WAAW,UAAU,CAAC,CAAC;IAC7C,MAAM,EACJ,qFAAqF;IACrF,wBAAwB,4BAA4B,EACpD,gBAAgB,oBAAoB,EACpC,WAAW,8JAAA,CAAA,UAAc,EACzB,gBAAgB,SAAS,EAC1B,GAAG;IACJ,MAAM,oBAAoB,CAAC,YAAY;QACrC,MAAM,EACJ,SAAS,EACT,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACL,GAAG;QACJ,MAAM,QAAQ;YACZ,MAAM;gBAAC;gBAAQ,aAAa;gBAAa,SAAS,UAAU,CAAC,QAAQ,EAAE,OAAO,OAAO;mBAAK,CAAA,GAAA,+JAAA,CAAA,2BAAwB,AAAD,EAAE;mBAAe,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EAAE;mBAAW,YAAY,CAAA,GAAA,+JAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE;aAAE;QACtP;QACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,CAAA,OAAQ,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,eAAe,OAAO,CAAC;IACnF;IACA,SAAS,oBAAoB,SAAS,EAAE,WAAW,EAAE,iBAAiB,IAAM,IAAI;QAC9E,MAAM,aAAa,CAAC;QACpB,IAAI,cAAc,MAAM;YACtB,OAAO;QACT;QACA,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,UAAU,OAAO,CAAC,CAAC,OAAO;gBACxB,IAAI,UAAU,QAAQ,eAAe,UAAU,YAAY,IAAI,CAAC,MAAM,EAAE;oBACtE,UAAU,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG;gBACxC;YACF;QACF,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAA;gBAC7B,MAAM,QAAQ,SAAS,CAAC,IAAI;gBAC5B,IAAI,UAAU,QAAQ,UAAU,aAAa,eAAe,QAAQ;oBAClE,UAAU,CAAC,IAAI,GAAG;gBACpB;YACF;QACF,OAAO;YACL,UAAU,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG;QACpC;QACA,OAAO;IACT;IACA,MAAM,WAAW,sBAAsB,+JAAA,CAAA,4BAAyB,EAAE,+JAAA,CAAA,kCAA+B,EAAE,+JAAA,CAAA,+BAA4B,EAAE,+JAAA,CAAA,yBAAsB,EAAE,+JAAA,CAAA,8BAA2B,EAAE,+JAAA,CAAA,qBAAkB,EAAE,+JAAA,CAAA,2BAAwB;IAClO,MAAM,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,KAAK,OAAO,EAAE,GAAG;QACnE,MAAM,QAAQ;QACd,MAAM,aAAa,cAAc;QACjC,MAAM,QAAQ,CAAA,GAAA,oNAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oDAAoD;QAE5F,0DAA0D;QAC1D,CAAA,GAAA,uKAAA,CAAA,UAAqB,AAAD,EAAE,OAAO,MAAM,WAAW;QAC9C,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,cAAc,EAAE,EACzB,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,OAAO,MAAM,EACb,MAAM,WAAW,CAAC,CAAC,EACnB,QAAQ,aAAa,CAAC,CAAC,EACvB,SAAS,cAAc,CAAC,EACxB,YAAY,iBAAiB,WAAW,EACxC,eAAe,oBAAoB,WAAW,EAC9C,gBAAgB,QAAQ,CAAC,EACzB,GAAG,OACJ,GAAG;QACJ,MAAM,OAAO,oBAAoB,UAAU,MAAM,WAAW,EAAE,CAAA,MAAO,QAAQ;QAC7E,MAAM,SAAS,oBAAoB,YAAY,MAAM,WAAW;QAChE,MAAM,UAAU,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,WAAW;QACnE,MAAM,UAAU,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,WAAW;QACnE,MAAM,aAAa,QAAQ,UAAU,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,cAAc;QAC/F,MAAM,gBAAgB,QAAQ,aAAa,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,iBAAiB;QACxG,MAAM,aAAa;YACjB,GAAG,KAAK;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QACA,MAAM,UAAU,kBAAkB,YAAY;QAC9C,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,UAAU;YACjC,KAAK;YACL,IAAI;YACJ,YAAY;YACZ,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;YAC9B,GAAG,KAAK;YACR,UAAU,qMAAA,CAAA,WAAc,CAAC,GAAG,CAAC,UAAU,CAAA;gBACrC,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,UAAU,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE,OAAO;oBAAC;iBAAO,KAAK,aAAa,MAAM,KAAK,CAAC,SAAS,EAAE;oBACnH,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,OAAO;wBAC5C,gBAAgB,MAAM,KAAK,EAAE,kBAAkB,QAAQ;oBACzD;gBACF;gBACA,OAAO;YACT;QACF;IACF;IACA,uCAAwC,KAAK,SAAS,GAA0B;QAC9E,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;QACxB,WAAW,sIAAA,CAAA,UAAS,CAAC,MAAM;QAC3B,SAAS,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAG,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACtG,eAAe,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACvK,WAAW,sIAAA,CAAA,UAAS,CAAC,WAAW;QAChC,WAAW,sIAAA,CAAA,UAAS,CAAC,IAAI;QACzB,WAAW,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;gBAAC;gBAAkB;gBAAU;gBAAe;aAAM;YAAG,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;gBAAC;gBAAkB;gBAAU;gBAAe;aAAM;YAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAC9M,QAAQ,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAChK,YAAY,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACpK,MAAM,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAC9L,SAAS,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACjK,IAAI,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,sIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;gBAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,sIAAA,CAAA,UAAS,CAAC,IAAI;aAAC;YAAI,sIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACtJ,MAAM,sIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAU;YAAgB;SAAO;IAC1D;IAEA,4CAA4C;IAC5C,KAAK,OAAO,GAAG;IACf,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/generateUtilityClasses/generateUtilityClasses.js"], "sourcesContent": ["import generateUtilityClass from \"../generateUtilityClass/index.js\";\nexport default function generateUtilityClasses(componentName, slots, globalStatePrefix = 'Mui') {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);\n  });\n  return result;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,uBAAuB,aAAa,EAAE,KAAK,EAAE,oBAAoB,KAAK;IAC5F,MAAM,SAAS,CAAC;IAChB,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,qLAAA,CAAA,UAAoB,AAAD,EAAE,eAAe,MAAM;IAC3D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/composeClasses/composeClasses.js"], "sourcesContent": ["/* eslint no-restricted-syntax: 0, prefer-template: 0, guard-for-in: 0\n   ---\n   These rules are preventing the performance optimizations below.\n */\n\n/**\n * Compose classes from multiple sources.\n *\n * @example\n * ```tsx\n * const slots = {\n *  root: ['root', 'primary'],\n *  label: ['label'],\n * };\n *\n * const getUtilityClass = (slot) => `MuiButton-${slot}`;\n *\n * const classes = {\n *   root: 'my-root-class',\n * };\n *\n * const output = composeClasses(slots, getUtilityClass, classes);\n * // {\n * //   root: 'MuiButton-root MuiButton-primary my-root-class',\n * //   label: 'MuiButton-label',\n * // }\n * ```\n *\n * @param slots a list of classes for each possible slot\n * @param getUtilityClass a function to resolve the class based on the slot name\n * @param classes the input classes from props\n * @returns the resolved classes for all slots\n */\nexport default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  for (const slotName in slots) {\n    const slot = slots[slotName];\n    let buffer = '';\n    let start = true;\n    for (let i = 0; i < slot.length; i += 1) {\n      const value = slot[i];\n      if (value) {\n        buffer += (start === true ? '' : ' ') + getUtilityClass(value);\n        start = false;\n        if (classes && classes[value]) {\n          buffer += ' ' + classes[value];\n        }\n      }\n    }\n    output[slotName] = buffer;\n  }\n  return output;\n}"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC;;;AACc,SAAS,eAAe,KAAK,EAAE,eAAe,EAAE,UAAU,SAAS;IAChF,MAAM,SAAS,CAAC;IAChB,IAAK,MAAM,YAAY,MAAO;QAC5B,MAAM,OAAO,KAAK,CAAC,SAAS;QAC5B,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,QAAQ,IAAI,CAAC,EAAE;YACrB,IAAI,OAAO;gBACT,UAAU,CAAC,UAAU,OAAO,KAAK,GAAG,IAAI,gBAAgB;gBACxD,QAAQ;gBACR,IAAI,WAAW,OAAO,CAAC,MAAM,EAAE;oBAC7B,UAAU,MAAM,OAAO,CAAC,MAAM;gBAChC;YACF;QACF;QACA,MAAM,CAAC,SAAS,GAAG;IACrB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/getDisplayName/getDisplayName.js"], "sourcesContent": ["import { ForwardRef, Memo } from 'react-is';\nfunction getFunctionComponentName(Component, fallback = '') {\n  return Component.displayName || Component.name || fallback;\n}\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  const functionName = getFunctionComponentName(innerType);\n  return outerType.displayName || (functionName !== '' ? `${wrapperName}(${functionName})` : wrapperName);\n}\n\n/**\n * cherry-pick from\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n * originally forked from recompose/getDisplayName\n */\nexport default function getDisplayName(Component) {\n  if (Component == null) {\n    return undefined;\n  }\n  if (typeof Component === 'string') {\n    return Component;\n  }\n  if (typeof Component === 'function') {\n    return getFunctionComponentName(Component, 'Component');\n  }\n\n  // TypeScript can't have components as objects but they exist in the form of `memo` or `Suspense`\n  if (typeof Component === 'object') {\n    switch (Component.$$typeof) {\n      case ForwardRef:\n        return getWrappedName(Component, Component.render, 'ForwardRef');\n      case Memo:\n        return getWrappedName(Component, Component.type, 'memo');\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n}"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,yBAAyB,SAAS,EAAE,WAAW,EAAE;IACxD,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI;AACpD;AACA,SAAS,eAAe,SAAS,EAAE,SAAS,EAAE,WAAW;IACvD,MAAM,eAAe,yBAAyB;IAC9C,OAAO,UAAU,WAAW,IAAI,CAAC,iBAAiB,KAAK,GAAG,YAAY,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,WAAW;AACxG;AAOe,SAAS,eAAe,SAAS;IAC9C,IAAI,aAAa,MAAM;QACrB,OAAO;IACT;IACA,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO;IACT;IACA,IAAI,OAAO,cAAc,YAAY;QACnC,OAAO,yBAAyB,WAAW;IAC7C;IAEA,iGAAiG;IACjG,IAAI,OAAO,cAAc,UAAU;QACjC,OAAQ,UAAU,QAAQ;YACxB,KAAK,sKAAA,CAAA,aAAU;gBACb,OAAO,eAAe,WAAW,UAAU,MAAM,EAAE;YACrD,KAAK,sKAAA,CAAA,OAAI;gBACP,OAAO,eAAe,WAAW,UAAU,IAAI,EAAE;YACnD;gBACE,OAAO;QACX;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/useId/useId.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\n\n// TODO React 17: Remove `useGlobalId` once React 17 support is removed\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(`mui-${globalId}`);\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseId = safeReact.useId;\n\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  // React.useId() is only available from React 17.0.0.\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride ?? reactId;\n  }\n\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,IAAI,WAAW;AAEf,uEAAuE;AACvE,SAAS,YAAY,UAAU;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,sMAAM,QAAQ,CAAC;IACjD,MAAM,KAAK,cAAc;IACzB,sMAAM,SAAS,CAAC;QACd,IAAI,aAAa,MAAM;YACrB,6CAA6C;YAC7C,6DAA6D;YAC7D,+BAA+B;YAC/B,wHAAwH;YACxH,YAAY;YACZ,aAAa,CAAC,IAAI,EAAE,UAAU;QAChC;IACF,GAAG;QAAC;KAAU;IACd,OAAO;AACT;AAEA,sFAAsF;AACtF,MAAM,YAAY;IAChB,GAAG,qMAAK;AACV;AACA,MAAM,kBAAkB,UAAU,KAAK;AAQxB,SAAS,MAAM,UAAU;IACtC,qDAAqD;IACrD,IAAI,oBAAoB,WAAW;QACjC,MAAM,UAAU;QAChB,OAAO,cAAc;IACvB;IAEA,wHAAwH;IACxH,gGAAgG;IAChG,OAAO,YAAY;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/refType/refType.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,UAAU,sIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;IAAC,sIAAA,CAAA,UAAS,CAAC,IAAI;IAAE,sIAAA,CAAA,UAAS,CAAC,MAAM;CAAC;uCACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/chainPropTypes/chainPropTypes.js"], "sourcesContent": ["export default function chainPropTypes(propType1, propType2) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n  return function validate(...args) {\n    return propType1(...args) || propType2(...args);\n  };\n}"], "names": [], "mappings": ";;;AAAe,SAAS,eAAe,SAAS,EAAE,SAAS;IACzD,uCAA2C;;IAE3C;IACA,OAAO,SAAS,SAAS,GAAG,IAAI;QAC9B,OAAO,aAAa,SAAS,aAAa;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/elementTypeAcceptingRef/elementTypeAcceptingRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport chainPropTypes from \"../chainPropTypes/index.js\";\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction elementTypeAcceptingRef(props, propName, componentName, location, propFullName) {\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof propValue === 'function' && !isClassComponent(propValue)) {\n    warningHint = 'Did you accidentally provide a plain function component instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element type that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nexport default chainPropTypes(PropTypes.elementType, elementTypeAcceptingRef);"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,iBAAiB,WAAW;IACnC,0CAA0C;IAC1C,MAAM,EACJ,YAAY,CAAC,CAAC,EACf,GAAG;IACJ,OAAO,QAAQ,UAAU,gBAAgB;AAC3C;AACA,SAAS,wBAAwB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;IACrF,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,MAAM,eAAe,gBAAgB;IACrC,IAAI,aAAa,QACjB,wDAAwD;IACxD,yCAAyC;IACzC,4CAA4C;IAC5C,iFAAiF;IACjF,OAAO,WAAW,aAAa;QAC7B,OAAO;IACT;IACA,IAAI;IAEJ;;;;;;;;GAQC,GACD,IAAI,OAAO,cAAc,cAAc,CAAC,iBAAiB,YAAY;QACnE,cAAc;IAChB;IACA,IAAI,gBAAgB,WAAW;QAC7B,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,aAAa,iBAAiB,EAAE,cAAc,IAAI,CAAC,GAAG,CAAC,8CAA8C,EAAE,YAAY,CAAC,CAAC,GAAG;IACpK;IACA,OAAO;AACT;uCACe,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,sIAAA,CAAA,UAAS,CAAC,WAAW,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/isFocusVisible/isFocusVisible.js"], "sourcesContent": ["/**\n * Returns a boolean indicating if the event's target has :focus-visible\n */\nexport default function isFocusVisible(element) {\n  try {\n    return element.matches(':focus-visible');\n  } catch (error) {\n    // Do not warn on jsdom tests, otherwise all tests that rely on focus have to be skipped\n    // Tests that rely on `:focus-visible` will still have to be skipped in jsdom\n    if (process.env.NODE_ENV !== 'production' && !/jsdom/.test(window.navigator.userAgent)) {\n      console.warn(['MUI: The `:focus-visible` pseudo class is not supported in this browser.', 'Some components rely on this feature to work properly.'].join('\\n'));\n    }\n  }\n  return false;\n}"], "names": [], "mappings": "AAAA;;CAEC;;;AACc,SAAS,eAAe,OAAO;IAC5C,IAAI;QACF,OAAO,QAAQ,OAAO,CAAC;IACzB,EAAE,OAAO,OAAO;QACd,wFAAwF;QACxF,6EAA6E;QAC7E,IAAI,oDAAyB,gBAAgB,CAAC,QAAQ,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,GAAG;YACtF,QAAQ,IAAI,CAAC;gBAAC;gBAA4E;aAAyD,CAAC,IAAI,CAAC;QAC3J;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAmBe,SAAS,WAAW,GAAG,IAAI;IACxC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAChC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAA;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA;YACxB,IAAI,OAAO,MAAM;gBACf,OAAO;YACT;YACA,IAAI,OAAO,QAAQ,YAAY;gBAC7B,MAAM,cAAc;gBACpB,MAAM,aAAa,YAAY;gBAC/B,OAAO,OAAO,eAAe,aAAa,aAAa;oBACrD,YAAY;gBACd;YACF;YACA,IAAI,OAAO,GAAG;YACd,OAAO;gBACL,IAAI,OAAO,GAAG;YAChB;QACF;QACA,OAAO;YACL,SAAS,OAAO,CAAC,CAAA,aAAc;QACjC;IACA,uDAAuD;IACzD,GAAG;IACH,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnB,IAAI,KAAK,KAAK,CAAC,CAAA,MAAO,OAAO,OAAO;YAClC,OAAO;QACT;QACA,OAAO,CAAA;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,WAAW,OAAO;gBAClB,WAAW,OAAO,GAAG;YACvB;YACA,IAAI,SAAS,MAAM;gBACjB,WAAW,OAAO,GAAG,UAAU;YACjC;QACF;IACA,qMAAqM;IACrM,uDAAuD;IACzD,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/useEventCallback/useEventCallback.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from \"../useEnhancedEffect/index.js\";\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-*********\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef((...args) =>\n  // @ts-expect-error hide `this`\n  (0, ref.current)(...args)).current;\n}\nexport default useEventCallback;"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKA;;;CAGC,GAED,SAAS,iBAAiB,EAAE;IAC1B,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACzB,CAAA,GAAA,+KAAA,CAAA,UAAiB,AAAD,EAAE;QAChB,IAAI,OAAO,GAAG;IAChB;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE,CAAC,GAAG,OACxB,+BAA+B;QAC/B,CAAC,GAAG,IAAI,OAAO,KAAK,OAAO,OAAO;AACpC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/useLazyRef/useLazyRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,MAAM,gBAAgB,CAAC;AASR,SAAS,WAAW,IAAI,EAAE,OAAO;IAC9C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACzB,IAAI,IAAI,OAAO,KAAK,eAAe;QACjC,IAAI,OAAO,GAAG,KAAK;IACrB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/useOnMount/useOnMount.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- no need to put `fn` in the dependency array\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,MAAM,QAAQ,EAAE;AAKD,SAAS,WAAW,EAAE;IACnC,uKAAuK;IACvK,8CAA8C,GAC9C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE,IAAI;AACpB,6CAA6C,GAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/useTimeout/useTimeout.js"], "sourcesContent": ["'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAIO,MAAM;IACX,OAAO,SAAS;QACd,OAAO,IAAI;IACb;IACA,YAAY,KAAK;IAEjB;;GAEC,GACD,MAAM,KAAK,EAAE,EAAE,EAAE;QACf,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,SAAS,GAAG,WAAW;YAC1B,IAAI,CAAC,SAAS,GAAG;YACjB;QACF,GAAG;IACL;IACA,QAAQ;QACN,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YAC3B,aAAa,IAAI,CAAC,SAAS;YAC3B,IAAI,CAAC,SAAS,GAAG;QACnB;IACF,EAAE;IACF,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK;IACnB,EAAE;AACJ;AACe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,QAAQ,MAAM,EAAE,OAAO;IAClD,CAAA,GAAA,iKAAA,CAAA,UAAU,AAAD,EAAE,QAAQ,aAAa;IAChC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/integerPropType/integerPropType.js"], "sourcesContent": ["export function getTypeByValue(value) {\n  const valueType = typeof value;\n  switch (valueType) {\n    case 'number':\n      if (Number.isNaN(value)) {\n        return 'NaN';\n      }\n      if (!Number.isFinite(value)) {\n        return 'Infinity';\n      }\n      if (value !== Math.floor(value)) {\n        return 'float';\n      }\n      return 'number';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      return value.constructor.name;\n    default:\n      return valueType;\n  }\n}\nfunction requiredInteger(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue == null || !Number.isInteger(propValue)) {\n    const propType = getTypeByValue(propValue);\n    return new RangeError(`Invalid ${location} \\`${propName}\\` of type \\`${propType}\\` supplied to \\`${componentName}\\`, expected \\`integer\\`.`);\n  }\n  return null;\n}\nfunction validator(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue === undefined) {\n    return null;\n  }\n  return requiredInteger(props, propName, componentName, location);\n}\nfunction validatorNoop() {\n  return null;\n}\nvalidator.isRequired = requiredInteger;\nvalidatorNoop.isRequired = validatorNoop;\nconst integerPropType = process.env.NODE_ENV === 'production' ? validatorNoop : validator;\nexport default integerPropType;"], "names": [], "mappings": ";;;;AAAO,SAAS,eAAe,KAAK;IAClC,MAAM,YAAY,OAAO;IACzB,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,KAAK,CAAC,QAAQ;gBACvB,OAAO;YACT;YACA,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;gBAC3B,OAAO;YACT;YACA,IAAI,UAAU,KAAK,KAAK,CAAC,QAAQ;gBAC/B,OAAO;YACT;YACA,OAAO;QACT,KAAK;YACH,IAAI,UAAU,MAAM;gBAClB,OAAO;YACT;YACA,OAAO,MAAM,WAAW,CAAC,IAAI;QAC/B;YACE,OAAO;IACX;AACF;AACA,SAAS,gBAAgB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;IAC/D,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,IAAI,aAAa,QAAQ,CAAC,OAAO,SAAS,CAAC,YAAY;QACrD,MAAM,WAAW,eAAe;QAChC,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,SAAS,aAAa,EAAE,SAAS,iBAAiB,EAAE,cAAc,yBAAyB,CAAC;IAC7I;IACA,OAAO;AACT;AACA,SAAS,UAAU,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;IACzD,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,IAAI,cAAc,WAAW;QAC3B,OAAO;IACT;IACA,OAAO,gBAAgB,OAAO,UAAU,eAAe;AACzD;AACA,SAAS;IACP,OAAO;AACT;AACA,UAAU,UAAU,GAAG;AACvB,cAAc,UAAU,GAAG;AAC3B,MAAM,kBAAkB,6EAAwD;uCACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/isMuiElement/isMuiElement.js"], "sourcesContent": ["import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(\n  // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  element.type.muiName ?? element.type?._payload?.value?.muiName) !== -1;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,aAAa,OAAO,EAAE,QAAQ;IACpD,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,YAAY,SAAS,OAAO,CACrE,sFAAsF;IACtF,sIAAsI;IACtI,gDAAgD;IAChD,QAAQ,IAAI,CAAC,OAAO,IAAI,QAAQ,IAAI,EAAE,UAAU,OAAO,aAAa,CAAC;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/utils/esm/requirePropFactory/requirePropFactory.js"], "sourcesContent": ["export default function requirePropFactory(componentNameInError, Component) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => () => null;\n  }\n\n  // eslint-disable-next-line react/forbid-foreign-prop-types\n  const prevPropTypes = Component ? {\n    ...Component.propTypes\n  } : null;\n  const requireProp = requiredProp => (props, propName, componentName, location, propFullName, ...args) => {\n    const propFullNameSafe = propFullName || propName;\n    const defaultTypeChecker = prevPropTypes?.[propFullNameSafe];\n    if (defaultTypeChecker) {\n      const typeCheckerResult = defaultTypeChecker(props, propName, componentName, location, propFullName, ...args);\n      if (typeCheckerResult) {\n        return typeCheckerResult;\n      }\n    }\n    if (typeof props[propName] !== 'undefined' && !props[requiredProp]) {\n      return new Error(`The prop \\`${propFullNameSafe}\\` of ` + `\\`${componentNameInError}\\` can only be used together with the \\`${requiredProp}\\` prop.`);\n    }\n    return null;\n  };\n  return requireProp;\n}"], "names": [], "mappings": ";;;AAAe,SAAS,mBAAmB,oBAAoB,EAAE,SAAS;IACxE,uCAA2C;;IAE3C;IAEA,2DAA2D;IAC3D,MAAM,gBAAgB,YAAY;QAChC,GAAG,UAAU,SAAS;IACxB,IAAI;IACJ,MAAM,cAAc,CAAA,eAAgB,CAAC,OAAO,UAAU,eAAe,UAAU,cAAc,GAAG;YAC9F,MAAM,mBAAmB,gBAAgB;YACzC,MAAM,qBAAqB,eAAe,CAAC,iBAAiB;YAC5D,IAAI,oBAAoB;gBACtB,MAAM,oBAAoB,mBAAmB,OAAO,UAAU,eAAe,UAAU,iBAAiB;gBACxG,IAAI,mBAAmB;oBACrB,OAAO;gBACT;YACF;YACA,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE;gBAClE,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE,iBAAiB,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,qBAAqB,wCAAwC,EAAE,aAAa,QAAQ,CAAC;YACtJ;YACA,OAAO;QACT;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };"], "names": [], "mappings": ";;;AAAA,SAAS,8BAA8B,CAAC,EAAE,CAAC;IACzC,IAAI,QAAQ,GAAG,OAAO,CAAC;IACvB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QACjD,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QACzB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACb;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40babel/runtime/helpers/esm/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };"], "names": [], "mappings": ";;;AAAA,SAAS,uBAAuB,CAAC;IAC/B,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,eAAe;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40babel/runtime/helpers/esm/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,OAAO,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;QAC5F,OAAO,EAAE,SAAS,GAAG,GAAG;IAC1B,GAAG,gBAAgB,GAAG;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40babel/runtime/helpers/esm/inheritsLoose.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,WAAW,GAAG,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAc,AAAD,EAAE,GAAG;AAC3F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/react-transition-group/esm/TransitionGroupContext.js"], "sourcesContent": ["import React from 'react';\nexport default React.createContext(null);"], "names": [], "mappings": ";;;AAAA;;uCACe,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/react-transition-group/esm/utils/ChildMapping.js"], "sourcesContent": ["import { Children, cloneElement, isValidElement } from 'react';\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\n\nexport function getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\n  };\n\n  var result = Object.create(null);\n  if (children) Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nexport function mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n\n  var i;\n  var childMapping = {};\n\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n\n  return childMapping;\n}\n\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\n\nexport function getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return cloneElement(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!isValidElement(child)) return;\n    var hasPrev = (key in prevChildMapping);\n    var hasNext = (key in nextChildMapping);\n    var prevChild = prevChildMapping[key];\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = cloneElement(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}"], "names": [], "mappings": ";;;;;;AAAA;;AAQO,SAAS,gBAAgB,QAAQ,EAAE,KAAK;IAC7C,IAAI,SAAS,SAAS,OAAO,KAAK;QAChC,OAAO,SAAS,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,MAAM,SAAS;IACzD;IAEA,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,UAAU,qMAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,UAAU,SAAU,CAAC;QAC9C,OAAO;IACT,GAAG,OAAO,CAAC,SAAU,KAAK;QACxB,wEAAwE;QACxE,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO;IAC7B;IACA,OAAO;AACT;AAmBO,SAAS,mBAAmB,IAAI,EAAE,IAAI;IAC3C,OAAO,QAAQ,CAAC;IAChB,OAAO,QAAQ,CAAC;IAEhB,SAAS,eAAe,GAAG;QACzB,OAAO,OAAO,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;IAC5C,EAAE,wEAAwE;IAC1E,oBAAoB;IAGpB,IAAI,kBAAkB,OAAO,MAAM,CAAC;IACpC,IAAI,cAAc,EAAE;IAEpB,IAAK,IAAI,WAAW,KAAM;QACxB,IAAI,WAAW,MAAM;YACnB,IAAI,YAAY,MAAM,EAAE;gBACtB,eAAe,CAAC,QAAQ,GAAG;gBAC3B,cAAc,EAAE;YAClB;QACF,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,IAAI;IACJ,IAAI,eAAe,CAAC;IAEpB,IAAK,IAAI,WAAW,KAAM;QACxB,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,IAAK,IAAI,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;gBACpD,IAAI,iBAAiB,eAAe,CAAC,QAAQ,CAAC,EAAE;gBAChD,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,eAAe;YAC7D;QACF;QAEA,YAAY,CAAC,QAAQ,GAAG,eAAe;IACzC,EAAE,qEAAqE;IAGvE,IAAK,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QACvC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,eAAe,WAAW,CAAC,EAAE;IAC9D;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,KAAK,EAAE,IAAI,EAAE,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK;AAC9D;AAEO,SAAS,uBAAuB,KAAK,EAAE,QAAQ;IACpD,OAAO,gBAAgB,MAAM,QAAQ,EAAE,SAAU,KAAK;QACpD,OAAO,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YACzB,UAAU,SAAS,IAAI,CAAC,MAAM;YAC9B,IAAI;YACJ,QAAQ,QAAQ,OAAO,UAAU;YACjC,OAAO,QAAQ,OAAO,SAAS;YAC/B,MAAM,QAAQ,OAAO,QAAQ;QAC/B;IACF;AACF;AACO,SAAS,oBAAoB,SAAS,EAAE,gBAAgB,EAAE,QAAQ;IACvE,IAAI,mBAAmB,gBAAgB,UAAU,QAAQ;IACzD,IAAI,WAAW,mBAAmB,kBAAkB;IACpD,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,SAAU,GAAG;QACzC,IAAI,QAAQ,QAAQ,CAAC,IAAI;QACzB,IAAI,CAAC,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;QAC5B,IAAI,UAAW,OAAO;QACtB,IAAI,UAAW,OAAO;QACtB,IAAI,YAAY,gBAAgB,CAAC,IAAI;QACrC,IAAI,YAAY,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,CAAC,UAAU,KAAK,CAAC,EAAE,EAAE,yBAAyB;QAE3F,IAAI,WAAW,CAAC,CAAC,WAAW,SAAS,GAAG;YACtC,+BAA+B;YAC/B,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAClC,UAAU,SAAS,IAAI,CAAC,MAAM;gBAC9B,IAAI;gBACJ,MAAM,QAAQ,OAAO,QAAQ;gBAC7B,OAAO,QAAQ,OAAO,SAAS;YACjC;QACF,OAAO,IAAI,CAAC,WAAW,WAAW,CAAC,WAAW;YAC5C,wBAAwB;YACxB,8BAA8B;YAC9B,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAClC,IAAI;YACN;QACF,OAAO,IAAI,WAAW,WAAW,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;YAC1D,wCAAwC;YACxC,uCAAuC;YACvC,gCAAgC;YAChC,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAClC,UAAU,SAAS,IAAI,CAAC,MAAM;gBAC9B,IAAI,UAAU,KAAK,CAAC,EAAE;gBACtB,MAAM,QAAQ,OAAO,QAAQ;gBAC7B,OAAO,QAAQ,OAAO,SAAS;YACjC;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/react-transition-group/esm/TransitionGroup.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\n\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\n\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n};\n/**\n * The `<TransitionGroup>` component manages a set of transition components\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n * components, `<TransitionGroup>` is a state machine for managing the mounting\n * and unmounting of components over time.\n *\n * Consider the example below. As items are removed or added to the TodoList the\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\n *\n * Note that `<TransitionGroup>`  does not define any animation behavior!\n * Exactly _how_ a list item animates is up to the individual transition\n * component. This means you can mix and match animations across different list\n * items.\n */\n\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n\n  function TransitionGroup(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\n\n\n    _this.state = {\n      contextValue: {\n        isMounting: true\n      },\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n\n  var _proto = TransitionGroup.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.mounted = true;\n    this.setState({\n      contextValue: {\n        isMounting: false\n      }\n    });\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n        handleExited = _ref.handleExited,\n        firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  } // node is `undefined` when user provided `nodeRef` prop\n  ;\n\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = getChildMapping(this.props.children);\n    if (child.key in currentChildMapping) return;\n\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        Component = _this$props.component,\n        childFactory = _this$props.childFactory,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n\n    var contextValue = this.state.contextValue;\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n\n    if (Component === null) {\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: contextValue\n    }, /*#__PURE__*/React.createElement(Component, props, children));\n  };\n\n  return TransitionGroup;\n}(React.Component);\n\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: PropTypes.any,\n\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: PropTypes.node,\n\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: PropTypes.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nexport default TransitionGroup;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,IAAI,SAAS,OAAO,MAAM,IAAI,SAAU,GAAG;IACzC,OAAO,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,SAAU,CAAC;QACrC,OAAO,GAAG,CAAC,EAAE;IACf;AACF;AAEA,IAAI,eAAe;IACjB,WAAW;IACX,cAAc,SAAS,aAAa,KAAK;QACvC,OAAO;IACT;AACF;AACA;;;;;;;;;;;;;CAaC,GAED,IAAI,kBAAkB,WAAW,GAAE,SAAU,gBAAgB;IAC3D,CAAA,GAAA,qKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB;IAEhC,SAAS,gBAAgB,KAAK,EAAE,OAAO;QACrC,IAAI;QAEJ,QAAQ,iBAAiB,IAAI,CAAC,IAAI,EAAE,OAAO,YAAY,IAAI;QAE3D,IAAI,eAAe,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,UAAsB,AAAD,EAAE,SAAS,+DAA+D;QAG1I,MAAM,KAAK,GAAG;YACZ,cAAc;gBACZ,YAAY;YACd;YACA,cAAc;YACd,aAAa;QACf;QACA,OAAO;IACT;IAEA,IAAI,SAAS,gBAAgB,SAAS;IAEtC,OAAO,iBAAiB,GAAG,SAAS;QAClC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,CAAC;YACZ,cAAc;gBACZ,YAAY;YACd;QACF;IACF;IAEA,OAAO,oBAAoB,GAAG,SAAS;QACrC,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,gBAAgB,wBAAwB,GAAG,SAAS,yBAAyB,SAAS,EAAE,IAAI;QAC1F,IAAI,mBAAmB,KAAK,QAAQ,EAChC,eAAe,KAAK,YAAY,EAChC,cAAc,KAAK,WAAW;QAClC,OAAO;YACL,UAAU,cAAc,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW,gBAAgB,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,kBAAkB;YAC3H,aAAa;QACf;IACF,EAAE,wDAAwD;;IAG1D,OAAO,YAAY,GAAG,SAAS,aAAa,KAAK,EAAE,IAAI;QACrD,IAAI,sBAAsB,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;QAC7D,IAAI,MAAM,GAAG,IAAI,qBAAqB;QAEtC,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;YACxB,MAAM,KAAK,CAAC,QAAQ,CAAC;QACvB;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,QAAQ,CAAC,SAAU,KAAK;gBAC3B,IAAI,WAAW,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,QAAQ;gBAE1C,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC;gBAC1B,OAAO;oBACL,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,cAAc,IAAI,CAAC,KAAK,EACxB,YAAY,YAAY,SAAS,EACjC,eAAe,YAAY,YAAY,EACvC,QAAQ,CAAA,GAAA,oLAAA,CAAA,UAA6B,AAAD,EAAE,aAAa;YAAC;YAAa;SAAe;QAEpF,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,YAAY;QAC1C,IAAI,WAAW,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC;QAC/C,OAAO,MAAM,MAAM;QACnB,OAAO,MAAM,KAAK;QAClB,OAAO,MAAM,IAAI;QAEjB,IAAI,cAAc,MAAM;YACtB,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6KAAA,CAAA,UAAsB,CAAC,QAAQ,EAAE;gBACvE,OAAO;YACT,GAAG;QACL;QAEA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6KAAA,CAAA,UAAsB,CAAC,QAAQ,EAAE;YACvE,OAAO;QACT,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW,OAAO;IACxD;IAEA,OAAO;AACT,EAAE,qMAAA,CAAA,UAAK,CAAC,SAAS;AAEjB,gBAAgB,SAAS,GAAG,uCAAwC;IAClE;;;;;;GAMC,GACD,WAAW,sIAAA,CAAA,UAAS,CAAC,GAAG;IAExB;;;;;;;;;;;;GAYC,GACD,UAAU,sIAAA,CAAA,UAAS,CAAC,IAAI;IAExB;;;;GAIC,GACD,QAAQ,sIAAA,CAAA,UAAS,CAAC,IAAI;IAEtB;;;;GAIC,GACD,OAAO,sIAAA,CAAA,UAAS,CAAC,IAAI;IAErB;;;;GAIC,GACD,MAAM,sIAAA,CAAA,UAAS,CAAC,IAAI;IAEpB;;;;;;;;;GASC,GACD,cAAc,sIAAA,CAAA,UAAS,CAAC,IAAI;AAC9B;AACA,gBAAgB,YAAY,GAAG;uCAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/FitnessCenter.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20.57 14.86 22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29z\"\n}), 'FitnessCenter');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/DirectionsRun.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M13.49 5.48c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m-3.6 13.9 1-4.4 2.1 2v6h2v-7.5l-2.1-2 .6-3c1.3 1.5 3.3 2.5 5.5 2.5v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1-.3 0-.5.1-.8.1l-5.2 2.2v4.7h2v-3.4l1.8-.7-1.6 8.1-4.9-1-.4 2z\"\n}), 'DirectionsRun');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/Assessment.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-7h2zm4 0h-2V7h2zm4 0h-2v-4h2z\"\n}), 'Assessment');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/PlayArrow.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M8 5v14l11-7z\"\n}), 'PlayArrow');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/TrendingUp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z\"\n}), 'TrendingUp');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/Timer.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 1h6v2H9zm10.03 6.39 1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61M13 14h-2V8h2z\"\n}), 'Timer');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/EmojiEvents.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 5h-2V3H7v2H5c-1.1 0-2 .9-2 2v1c0 2.55 1.92 4.63 4.39 4.94.63 1.5 1.98 2.63 3.61 2.96V19H7v2h10v-2h-4v-3.1c1.63-.33 2.98-1.46 3.61-2.96C19.08 12.63 21 10.55 21 8V7c0-1.1-.9-2-2-2M5 8V7h2v3.82C5.84 10.4 5 9.3 5 8m14 0c0 1.3-.84 2.4-2 2.82V7h2z\"\n}), 'EmojiEvents');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40mui/icons-material/esm/Add.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z\"\n}), 'Add');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,kKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/node_modules/%40swc/helpers/cjs/_tagged_template_literal_loose.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _tagged_template_literal_loose(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    strings.raw = raw;\n\n    return strings;\n}\nexports._ = _tagged_template_literal_loose;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,+BAA+B,OAAO,EAAE,GAAG;IAChD,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,QAAQ,GAAG,GAAG;IAEd,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}]}