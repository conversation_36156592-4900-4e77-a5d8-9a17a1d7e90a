import { Metadata } from 'next';
import { generateWorkoutMetadata } from '@/lib/seo/utils';
import { Breadcrumb } from '@/components/ui/breadcrumb';

// This would typically fetch workout data from your API
async function getWorkout(id: string) {
  // Mock workout data - replace with actual API call
  const mockWorkouts: Record<string, any> = {
    'beginner-full-body': {
      title: 'Beginner Full Body Workout',
      description: 'A comprehensive full-body workout designed for beginners. This program focuses on fundamental movements and proper form.',
      duration: 45,
      difficulty: 'Beginner',
      type: 'Strength Training',
      targetMuscles: ['Full Body']
    },
    'intermediate-strength': {
      title: 'Intermediate Strength Training',
      description: 'Build serious strength with this intermediate program focusing on compound movements and progressive overload.',
      duration: 60,
      difficulty: 'Intermediate',
      type: 'Strength Training',
      targetMuscles: ['Upper Body', 'Lower Body']
    },
    'advanced-powerlifting': {
      title: 'Advanced Powerlifting Program',
      description: 'Elite powerlifting program for experienced lifters looking to maximize their squat, bench, and deadlift.',
      duration: 90,
      difficulty: 'Advanced',
      type: 'Powerlifting',
      targetMuscles: ['Full Body']
    }
  };

  return mockWorkouts[id] || {
    title: 'Workout Program',
    description: 'Comprehensive fitness program designed to help you achieve your goals.',
    duration: 60,
    difficulty: 'All Levels',
    type: 'General Fitness',
    targetMuscles: ['Full Body']
  };
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const workout = await getWorkout(params.id);
  return generateWorkoutMetadata(workout);
}

export default function WorkoutDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { id: string };
}) {
  const breadcrumbItems = [
    { name: 'Workouts', href: '/workouts' },
    { name: 'Workout Details', href: `/workouts/${params.id}` }
  ];

  return (
    <div>
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      {children}
    </div>
  );
}
