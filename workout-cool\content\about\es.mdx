import Link from "next/link";

# Acerca de Workout.cool

## ¿Por qué Workout.cool?

Workout.cool nació del deseo de ofrecer una plataforma de entrenamiento confiable, moderna y activamente mantenida, después de que el proyecto original <WorkoutLol variant="muted" /> fuera abandonado.

## La historia

Workout.cool es el resultado de una aventura impulsada por la comunidad.

Fui el **primer colaborador de código abierto** del proyecto <WorkoutLol variant="muted" />.

Esto significa que vi el proyecto *cobrar vida*, *crecer*, luego ser **vendido** y finalmente **abandonado** por su nuevo propietario.

Como muchos usuarios, sentí una **profunda frustración** y una *sensación de abandono* al ver desaparecer una herramienta a la que había contribuido tanto, con solicitudes de funciones sin respuesta y envejeciendo.

---

*<PERSON><PERSON><PERSON> meses*, intenté contactar al nuevo propietario—**sin recibir nunca una sola respuesta** a pesar de muchos intentos (*alrededor de 15*).

Ante este **silencio** y la **angustia de la comunidad**, decidí **tomar el asunto en mis propias manos**:

> En lugar de dejar que todo este trabajo desaparezca, **relancé un proyecto aún más ambicioso, moderno y abierto para todos.**

Este proyecto no está impulsado por el beneficio, sino por la **pasión** y el deseo de servir a la comunidad fitness de código abierto.

**Alguien tenía que salvar a la comunidad—_¡decidí ser ese alguien!_**

## Código abierto y comunidad

Workout.cool es de código abierto, garantizando transparencia, modularidad y escalabilidad.  
¡Todos son bienvenidos a contribuir—código, documentación o ideas!

- [Ver el proyecto en GitHub](https://github.com/Snouzy/workout-cool)
- [Comprar un café para apoyar](https://ko-fi.com/workoutcool)

## ¡Únete a la misión!

¿Quieres contribuir, sugerir una función o simplemente apoyar el proyecto?  
¡Contáctanos o abre un issue en GitHub!

**[<EMAIL>](mailto:<EMAIL>)**