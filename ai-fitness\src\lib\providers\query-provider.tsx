'use client';

/**
 * React Query Provider for AI-fitness application
 * Provides global state management and caching for API requests
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Stale time: how long data is considered fresh
            staleTime: 5 * 60 * 1000, // 5 minutes
            
            // Cache time: how long data stays in cache after component unmounts
            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
            
            // Retry configuration
            retry: (failureCount, error: any) => {
              // Don't retry on authentication errors
              if (error?.status === 401 || error?.status === 403) {
                return false;
              }
              
              // Don't retry on validation errors
              if (error?.status === 422) {
                return false;
              }
              
              // Retry up to 3 times for other errors
              return failureCount < 3;
            },
            
            // Retry delay with exponential backoff
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            
            // Refetch on window focus (useful for real-time data)
            refetchOnWindowFocus: false,
            
            // Refetch on reconnect
            refetchOnReconnect: true,
            
            // Background refetch interval (disabled by default)
            refetchInterval: false,
          },
          mutations: {
            // Retry mutations on network errors
            retry: (failureCount, error: any) => {
              // Don't retry on client errors (4xx)
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              
              // Retry up to 2 times for server errors
              return failureCount < 2;
            },
            
            // Retry delay for mutations
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Show React Query DevTools in development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
}
