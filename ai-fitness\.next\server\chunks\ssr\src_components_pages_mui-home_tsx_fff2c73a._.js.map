{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/pages/mui-home.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Grid,\n  Chip,\n  Fab,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  FitnessCenter,\n  DirectionsRun,\n  Assessment,\n  PlayArrow,\n  TrendingUp,\n  Timer,\n  EmojiEvents,\n  Add,\n} from '@mui/icons-material';\nimport Link from 'next/link';\n\nconst features = [\n  {\n    icon: <FitnessCenter />,\n    title: '智能训练计划',\n    description: 'AI 定制个性化训练方案',\n    color: '#FF6B35',\n    href: '/workouts',\n  },\n  {\n    icon: <DirectionsRun />,\n    title: '运动动作库',\n    description: '千种运动动作详细指导',\n    color: '#4CAF50',\n    href: '/exercises',\n  },\n  {\n    icon: <Assessment />,\n    title: '进度分析',\n    description: '数据驱动的健身追踪',\n    color: '#2196F3',\n    href: '/progress',\n  },\n];\n\nconst quickStats = [\n  { label: '今日训练', value: '45分钟', icon: <Timer />, color: '#FF6B35' },\n  { label: '本周目标', value: '80%', icon: <TrendingUp />, color: '#4CAF50' },\n  { label: '连续天数', value: '12天', icon: <EmojiEvents />, color: '#2196F3' },\n];\n\nexport function MuiHome() {\n  const theme = useTheme();\n\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,\n          py: { xs: 8, md: 12 },\n          position: 'relative',\n          overflow: 'hidden',\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Box textAlign=\"center\" sx={{ position: 'relative', zIndex: 1 }}>\n            <Typography\n              variant=\"h1\"\n              sx={{\n                fontSize: { xs: '2.5rem', md: '4rem' },\n                fontWeight: 700,\n                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                mb: 2,\n              }}\n            >\n              AI-fitness-singles\n            </Typography>\n            <Typography\n              variant=\"h4\"\n              sx={{\n                fontSize: { xs: '1.25rem', md: '1.75rem' },\n                color: 'text.secondary',\n                mb: 4,\n                fontWeight: 400,\n              }}\n            >\n              智能健身平台，专为单身人士打造\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n              <Button\n                variant=\"contained\"\n                size=\"large\"\n                startIcon={<PlayArrow />}\n                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}\n              >\n                开始训练\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                startIcon={<DirectionsRun />}\n                sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}\n                component={Link}\n                href=\"/exercises\"\n              >\n                浏览动作\n              </Button>\n            </Box>\n          </Box>\n        </Container>\n\n        {/* Decorative elements */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '20%',\n            right: '10%',\n            width: 100,\n            height: 100,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.2)}, ${alpha(theme.palette.secondary.main, 0.2)})`,\n            animation: 'float 6s ease-in-out infinite',\n          }}\n        />\n        <Box\n          sx={{\n            position: 'absolute',\n            bottom: '20%',\n            left: '5%',\n            width: 60,\n            height: 60,\n            borderRadius: '50%',\n            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,\n            animation: 'float 4s ease-in-out infinite reverse',\n          }}\n        />\n      </Box>\n\n      {/* Quick Stats */}\n      <Container maxWidth=\"md\" sx={{ mt: -2, position: 'relative', zIndex: 2, py: 4 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          <Grid container spacing={2} sx={{ maxWidth: 500 }}>\n            {quickStats.map((stat, index) => (\n              <Grid size={4} key={index}>\n                <Card\n                  sx={{\n                    textAlign: 'center',\n                    p: 2.5,\n                    minHeight: 160,\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',\n                    backdropFilter: 'blur(15px)',\n                    border: '1px solid rgba(255,255,255,0.3)',\n                    borderRadius: 3,\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateY(-4px)',\n                      boxShadow: `0 8px 20px ${alpha(stat.color, 0.15)}`,\n                      border: `1px solid ${alpha(stat.color, 0.2)}`,\n                    }\n                  }}\n                >\n                  <CardContent sx={{ p: '8px !important' }}>\n                    <Box\n                      sx={{\n                        display: 'inline-flex',\n                        p: 1.5,\n                        borderRadius: '50%',\n                        bgcolor: alpha(stat.color, 0.12),\n                        color: stat.color,\n                        mb: 1.5,\n                        width: 48,\n                        height: 48,\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                      }}\n                    >\n                      {React.cloneElement(stat.icon, { sx: { fontSize: 24 } })}\n                    </Box>\n                    <Typography\n                      variant=\"h5\"\n                      sx={{\n                        fontWeight: 700,\n                        color: stat.color,\n                        mb: 0.5,\n                        fontSize: '1.5rem',\n                        lineHeight: 1.2\n                      }}\n                    >\n                      {stat.value}\n                    </Typography>\n                    <Typography\n                      variant=\"body2\"\n                      color=\"text.secondary\"\n                      sx={{\n                        fontSize: '0.8rem',\n                        fontWeight: 500\n                      }}\n                    >\n                      {stat.label}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </Box>\n      </Container>\n\n      {/* Features Section */}\n      <Container maxWidth=\"lg\" sx={{ py: 8 }}>\n        <Typography\n          variant=\"h2\"\n          textAlign=\"center\"\n          sx={{ mb: 2, fontWeight: 600 }}\n        >\n          核心功能\n        </Typography>\n        <Typography\n          variant=\"h6\"\n          textAlign=\"center\"\n          color=\"text.secondary\"\n          sx={{ mb: 6 }}\n        >\n          一站式健身解决方案\n        </Typography>\n\n        <Grid container spacing={4}>\n          {features.map((feature, index) => (\n            <Grid size={{ xs: 12, md: 4 }} key={index}>\n              <Card\n                component={Link}\n                href={feature.href}\n                sx={{\n                  height: '100%',\n                  textDecoration: 'none',\n                  cursor: 'pointer',\n                  '&:hover': {\n                    transform: 'translateY(-8px)',\n                  },\n                }}\n              >\n                <CardContent sx={{ textAlign: 'center', p: 4 }}>\n                  <Box\n                    sx={{\n                      display: 'inline-flex',\n                      p: 3,\n                      borderRadius: '50%',\n                      bgcolor: alpha(feature.color, 0.1),\n                      color: feature.color,\n                      mb: 3,\n                      fontSize: '2rem',\n                    }}\n                  >\n                    {feature.icon}\n                  </Box>\n                  <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 2 }}>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"text.secondary\">\n                    {feature.description}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n\n      {/* Quick Actions */}\n      <Box sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}>\n        <Fab\n          color=\"primary\"\n          aria-label=\"add workout\"\n          sx={{\n            width: 64,\n            height: 64,\n            '&:hover': {\n              transform: 'scale(1.1)',\n            },\n          }}\n        >\n          <Add sx={{ fontSize: '2rem' }} />\n        </Fab>\n      </Box>\n\n      {/* CSS for animations */}\n      <style jsx global>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-20px); }\n        }\n      `}</style>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AA1BA;;;;;;;;;;;;;;AA4BA,MAAM,WAAW;IACf;QACE,oBAAM,8OAAC,kKAAA,CAAA,UAAa;;;;;QACpB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,kKAAA,CAAA,UAAa;;;;;QACpB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;QACjB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAQ,OAAO;QAAQ,oBAAM,8OAAC,0JAAA,CAAA,UAAK;;;;;QAAK,OAAO;IAAU;IAClE;QAAE,OAAO;QAAQ,OAAO;QAAO,oBAAM,8OAAC,+JAAA,CAAA,UAAU;;;;;QAAK,OAAO;IAAU;IACtE;QAAE,OAAO;QAAQ,OAAO;QAAO,oBAAM,8OAAC,gKAAA,CAAA,UAAW;;;;;QAAK,OAAO;IAAU;CACxE;AAEM,SAAS;IACd,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAErB,qBACE,8OAAC,wLAAA,CAAA,MAAG;QAAC,IAAI;YAAE,WAAW;YAAS,SAAS;QAAqB;;0BAE3D,8OAAC,wLAAA,CAAA,MAAG;gBACF,IAAI;oBACF,YAAY,CAAC,wBAAwB,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;oBACrI,IAAI;wBAAE,IAAI;wBAAG,IAAI;oBAAG;oBACpB,UAAU;oBACV,UAAU;gBACZ;;kCAEA,8OAAC,0MAAA,CAAA,YAAS;wBAAC,UAAS;kCAClB,cAAA,8OAAC,wLAAA,CAAA,MAAG;4BAAC,WAAU;4BAAS,IAAI;gCAAE,UAAU;gCAAY,QAAQ;4BAAE;;8CAC5D,8OAAC,6MAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,UAAU;4CAAE,IAAI;4CAAU,IAAI;wCAAO;wCACrC,YAAY;wCACZ,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;wCACpG,gBAAgB;wCAChB,sBAAsB;wCACtB,qBAAqB;wCACrB,IAAI;oCACN;8CACD;;;;;;8CAGD,8OAAC,6MAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,UAAU;4CAAE,IAAI;4CAAW,IAAI;wCAAU;wCACzC,OAAO;wCACP,IAAI;wCACJ,YAAY;oCACd;8CACD;;;;;;8CAGD,8OAAC,wLAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,SAAS;wCAAQ,KAAK;wCAAG,gBAAgB;wCAAU,UAAU;oCAAO;;sDAC7E,8OAAC,iMAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,yBAAW,8OAAC,8JAAA,CAAA,UAAS;;;;;4CACrB,IAAI;gDAAE,IAAI;gDAAG,IAAI;gDAAK,UAAU;4CAAS;sDAC1C;;;;;;sDAGD,8OAAC,iMAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,yBAAW,8OAAC,kKAAA,CAAA,UAAa;;;;;4CACzB,IAAI;gDAAE,IAAI;gDAAG,IAAI;gDAAK,UAAU;4CAAS;4CACzC,WAAW,4JAAA,CAAA,UAAI;4CACf,MAAK;sDACN;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC,wLAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;kCAEF,8OAAC,wLAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,YAAY,CAAC,uBAAuB,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BAC5H,WAAW;wBACb;;;;;;;;;;;;0BAKJ,8OAAC,0MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI,CAAC;oBAAG,UAAU;oBAAY,QAAQ;oBAAG,IAAI;gBAAE;0BAC5E,cAAA,8OAAC,wLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,gBAAgB;oBAAS;8BACnD,cAAA,8OAAC,2LAAA,CAAA,OAAI;wBAAC,SAAS;wBAAC,SAAS;wBAAG,IAAI;4BAAE,UAAU;wBAAI;kCAC7C,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,2LAAA,CAAA,OAAI;gCAAC,MAAM;0CACV,cAAA,8OAAC,2LAAA,CAAA,OAAI;oCACH,IAAI;wCACF,WAAW;wCACX,GAAG;wCACH,WAAW;wCACX,YAAY;wCACZ,gBAAgB;wCAChB,QAAQ;wCACR,cAAc;wCACd,YAAY;wCACZ,WAAW;4CACT,WAAW;4CACX,WAAW,CAAC,WAAW,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,OAAO;4CAClD,QAAQ,CAAC,UAAU,EAAE,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,MAAM;wCAC/C;oCACF;8CAEA,cAAA,8OAAC,gNAAA,CAAA,cAAW;wCAAC,IAAI;4CAAE,GAAG;wCAAiB;;0DACrC,8OAAC,wLAAA,CAAA,MAAG;gDACF,IAAI;oDACF,SAAS;oDACT,GAAG;oDACH,cAAc;oDACd,SAAS,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE;oDAC3B,OAAO,KAAK,KAAK;oDACjB,IAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,YAAY;oDACZ,gBAAgB;gDAClB;0DAEC,cAAA,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;oDAAE,IAAI;wDAAE,UAAU;oDAAG;gDAAE;;;;;;0DAExD,8OAAC,6MAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,IAAI;oDACF,YAAY;oDACZ,OAAO,KAAK,KAAK;oDACjB,IAAI;oDACJ,UAAU;oDACV,YAAY;gDACd;0DAEC,KAAK,KAAK;;;;;;0DAEb,8OAAC,6MAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,OAAM;gDACN,IAAI;oDACF,UAAU;oDACV,YAAY;gDACd;0DAEC,KAAK,KAAK;;;;;;;;;;;;;;;;;+BAvDC;;;;;;;;;;;;;;;;;;;;0BAkE5B,8OAAC,0MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;;kCACnC,8OAAC,6MAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,WAAU;wBACV,IAAI;4BAAE,IAAI;4BAAG,YAAY;wBAAI;kCAC9B;;;;;;kCAGD,8OAAC,6MAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,WAAU;wBACV,OAAM;wBACN,IAAI;4BAAE,IAAI;wBAAE;kCACb;;;;;;kCAID,8OAAC,2LAAA,CAAA,OAAI;wBAAC,SAAS;wBAAC,SAAS;kCACtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,2LAAA,CAAA,OAAI;gCAAC,MAAM;oCAAE,IAAI;oCAAI,IAAI;gCAAE;0CAC1B,cAAA,8OAAC,2LAAA,CAAA,OAAI;oCACH,WAAW,4JAAA,CAAA,UAAI;oCACf,MAAM,QAAQ,IAAI;oCAClB,IAAI;wCACF,QAAQ;wCACR,gBAAgB;wCAChB,QAAQ;wCACR,WAAW;4CACT,WAAW;wCACb;oCACF;8CAEA,cAAA,8OAAC,gNAAA,CAAA,cAAW;wCAAC,IAAI;4CAAE,WAAW;4CAAU,GAAG;wCAAE;;0DAC3C,8OAAC,wLAAA,CAAA,MAAG;gDACF,IAAI;oDACF,SAAS;oDACT,GAAG;oDACH,cAAc;oDACd,SAAS,CAAA,GAAA,8KAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK,EAAE;oDAC9B,OAAO,QAAQ,KAAK;oDACpB,IAAI;oDACJ,UAAU;gDACZ;0DAEC,QAAQ,IAAI;;;;;;0DAEf,8OAAC,6MAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,IAAI;oDAAE,YAAY;oDAAK,IAAI;gDAAE;0DACnD,QAAQ,KAAK;;;;;;0DAEhB,8OAAC,6MAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAQ,OAAM;0DAC/B,QAAQ,WAAW;;;;;;;;;;;;;;;;;+BA/BQ;;;;;;;;;;;;;;;;0BAyC1C,8OAAC,wLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,UAAU;oBAAS,QAAQ;oBAAI,OAAO;oBAAI,QAAQ;gBAAK;0BAChE,cAAA,8OAAC,wLAAA,CAAA,MAAG;oBACF,OAAM;oBACN,cAAW;oBACX,IAAI;wBACF,OAAO;wBACP,QAAQ;wBACR,WAAW;4BACT,WAAW;wBACb;oBACF;8BAEA,cAAA,8OAAC,wJAAA,CAAA,UAAG;wBAAC,IAAI;4BAAE,UAAU;wBAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AAatC", "debugId": null}}]}