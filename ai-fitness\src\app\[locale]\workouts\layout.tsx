import { Metadata } from 'next';
import { generatePageMetadata } from '@/lib/seo/utils';
import { Breadcrumb } from '@/components/ui/breadcrumb';

export const metadata: Metadata = generatePageMetadata({
  title: "Workout Programs - AI-Powered Fitness Plans",
  description: "Discover AI-generated workout programs tailored to your fitness level and goals. From beginner-friendly routines to advanced training plans, find the perfect workout for you.",
  path: "/workouts",
});

export default function WorkoutsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Workouts', href: '/workouts' }
  ];

  return (
    <div>
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      {children}
    </div>
  );
}
