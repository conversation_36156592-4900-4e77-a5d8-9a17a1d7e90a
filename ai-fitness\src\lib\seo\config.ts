import { Metadata } from 'next';

// Base SEO configuration
export const siteConfig = {
  name: 'AI-fitness-singles',
  title: 'AI-fitness-singles - Smart Fitness Platform for Singles',
  description: 'AI-powered fitness platform designed for singles. Create personalized workout plans, access comprehensive exercise database, track progress with detailed analytics, and achieve your fitness goals.',
  url: process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app',
  ogImage: '/images/og-image.jpg',
  creator: 'AI-fitness-singles Team',
  keywords: [
    'fitness',
    'workout',
    'exercise',
    'AI fitness',
    'personal trainer',
    'fitness tracking',
    'workout plans',
    'fitness for singles',
    'health',
    'wellness',
    'strength training',
    'cardio',
    'fitness analytics',
    'progress tracking'
  ],
  authors: [
    {
      name: 'AI-fitness-singles',
      url: process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app',
    }
  ],
  social: {
    twitter: '@aifitnesssingle',
    facebook: 'aifitnessingles',
    instagram: 'aifitnessingles',
    youtube: '@aifitnessingles'
  }
};

// Default metadata for the application
export const defaultMetadata: Metadata = {
  title: {
    default: siteConfig.title,
    template: `%s | ${siteConfig.name}`
  },
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  authors: siteConfig.authors,
  creator: siteConfig.creator,
  metadataBase: new URL(siteConfig.url),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.title,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.title,
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.title,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: siteConfig.social.twitter,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_VERIFICATION,
  },
  category: 'fitness',
};

// Generate metadata for specific pages
export function generatePageMetadata({
  title,
  description,
  path = '',
  image,
  noIndex = false,
}: {
  title: string;
  description: string;
  path?: string;
  image?: string;
  noIndex?: boolean;
}): Metadata {
  const url = `${siteConfig.url}${path}`;
  const ogImage = image || siteConfig.ogImage;

  return {
    title,
    description,
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
    },
    twitter: {
      title,
      description,
      images: [ogImage],
    },
    robots: noIndex ? {
      index: false,
      follow: false,
    } : undefined,
  };
}

// Structured data schemas
export const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: siteConfig.name,
  url: siteConfig.url,
  logo: `${siteConfig.url}/images/logo.png`,
  description: siteConfig.description,
  sameAs: [
    `https://twitter.com/${siteConfig.social.twitter.replace('@', '')}`,
    `https://facebook.com/${siteConfig.social.facebook}`,
    `https://instagram.com/${siteConfig.social.instagram}`,
    `https://youtube.com/${siteConfig.social.youtube.replace('@', '')}`
  ],
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'customer service',
    availableLanguage: 'English'
  }
};

export const websiteSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: siteConfig.name,
  url: siteConfig.url,
  description: siteConfig.description,
  potentialAction: {
    '@type': 'SearchAction',
    target: `${siteConfig.url}/search?q={search_term_string}`,
    'query-input': 'required name=search_term_string'
  }
};

export const webApplicationSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebApplication',
  name: siteConfig.name,
  url: siteConfig.url,
  description: siteConfig.description,
  applicationCategory: 'HealthApplication',
  operatingSystem: 'Web Browser',
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'USD'
  },
  featureList: [
    'AI-powered workout planning',
    'Exercise database',
    'Progress tracking',
    'Fitness analytics',
    'Personalized recommendations'
  ]
};
