(()=>{var e={};e.id=347,e.ids=[347],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22266:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=a(65239),i=a(48088),r=a(88170),l=a.n(r),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["workouts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,43523)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/workouts/page",pathname:"/workouts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43523:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\fitness-singles\\\\ai-fitness\\\\src\\\\app\\\\workouts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\page.tsx","default")},61855:(e,s,a)=>{Promise.resolve().then(a.bind(a,43523))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67997:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(60687),i=a(43210),r=a(86246),l=a(44493),n=a(29523),c=a(96834),d=a(89667),o=a(52027),x=a(70293),m=a(35032),h=a(82080),j=a(40228),p=a(48730),u=a(99270),g=a(80462),f=a(11860),N=a(67760),v=a(28947),y=a(41312),w=a(97840),b=a(96474),A=a(95698),k=a(29908),C=a(85814),P=a.n(C);function $(){let[e,s]=(0,i.useState)("programs"),[a,C]=(0,i.useState)(""),[$,z]=(0,i.useState)(!1),[S,D]=(0,i.useState)({category:[],difficulty:[],duration:[]}),{isAuthenticated:W}=(0,k.As)(),{data:_,isLoading:M,error:E}=(0,A.Mm)({limit:20,category:S.category.length>0?S.category[0]:void 0,difficulty:S.difficulty.length>0?S.difficulty[0]:void 0,duration:S.duration.length>0?S.duration[0]:void 0}),{data:L,isLoading:R}=(0,A.My)({limit:20}),{data:T,isLoading:Z}=(0,A.mS)(6),{data:q,isLoading:B}=(0,A.Ox)(6),{data:F,isLoading:G}=(0,A.sK)(),O=(0,A.t2)(),I=(0,A.bR)(),U=(e,s)=>{D(a=>{let t=a[e],i=t.includes(s)?t.filter(e=>e!==s):[...t,s];return{...a,[e]:i}})},K=Object.values(S).some(e=>e.length>0),V=async e=>{try{await O.mutateAsync(e)}catch(e){console.error("Failed to join program:",e)}},Y=async e=>{try{await I.mutateAsync({exercises:[],notes:`Quick workout session - ${new Date().toLocaleDateString()}`})}catch(e){console.error("Failed to start workout:",e)}},H=_?.data?.filter(e=>""===a||e.title.toLowerCase().includes(a.toLowerCase())||e.description?.toLowerCase().includes(a.toLowerCase()))||[];return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(r.V,{}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Workout Programs"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover personalized workout programs designed to help you reach your fitness goals"})]}),W&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Active Programs"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:F?.length||0})]}),(0,t.jsx)(h.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"This Week"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?.data?.filter(e=>"COMPLETED"===e.status).length||0})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Total Minutes"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:L?.data?.reduce((e,s)=>e+(s.duration||0),0)||0})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsxs)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm",children:[(0,t.jsx)("button",{onClick:()=>s("programs"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"programs"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:"All Programs"}),W&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{onClick:()=>s("my-programs"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"my-programs"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:"My Programs"}),(0,t.jsx)("button",{onClick:()=>s("sessions"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"sessions"===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:"My Sessions"})]})]}),"programs"===e&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-8",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,t.jsx)(d.p,{type:"text",placeholder:"Search workout programs...",value:a,onChange:e=>C(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>z(!$),className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"Filters",K&&(0,t.jsx)(c.E,{variant:"secondary",className:"ml-1",children:Object.values(S).reduce((e,s)=>e+s.length,0)})]})]}),$&&(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Category"}),(0,t.jsx)("div",{className:"space-y-2",children:["Strength","Cardio","Flexibility","HIIT","Yoga"].map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:S.category.includes(e),onChange:()=>U("category",e),className:"rounded border-gray-300"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Difficulty"}),(0,t.jsx)("div",{className:"space-y-2",children:["Beginner","Intermediate","Advanced"].map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:S.difficulty.includes(e),onChange:()=>U("difficulty",e),className:"rounded border-gray-300"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Duration"}),(0,t.jsx)("div",{className:"space-y-2",children:["15-30 min","30-45 min","45-60 min","60+ min"].map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:S.duration.includes(e),onChange:()=>U("duration",e),className:"rounded border-gray-300"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e})]},e))})]})]}),K&&(0,t.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>{D({category:[],difficulty:[],duration:[]})},className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Clear Filters"]})})]})]}),"programs"===e&&(0,t.jsxs)(t.Fragment,{children:[!a&&!K&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Popular Programs"}),Z?(0,t.jsx)(o.z0,{}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:T?.slice(0,6).map(e=>(0,t.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(l.ZB,{className:"text-lg",children:e.title}),(0,t.jsx)(l.BT,{children:e.description})]}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.duration||"N/A"," min"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.difficulty||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.participantCount||0})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{size:"sm",className:"flex-1",onClick:()=>Y(e.id),disabled:I.isPending,children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(e.id),disabled:O.isPending,children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})]})]})})]},e.id))})]}),W&&(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Recommended for You"}),B?(0,t.jsx)(o.z0,{}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:q?.map(e=>(0,t.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(l.ZB,{className:"text-lg",children:e.title}),(0,t.jsx)(l.BT,{children:e.description})]}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.duration||"N/A"," min"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.difficulty||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.participantCount||0})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{size:"sm",className:"flex-1",onClick:()=>Y(e.id),disabled:I.isPending,children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(e.id),disabled:O.isPending,children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})]})]})})]},e.id))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:a?`Search Results for "${a}"`:"All Programs"}),M?(0,t.jsx)(o.z0,{}):E?(0,t.jsx)(x.Kw,{title:"Failed to load programs",message:"Please try again later",onRetry:()=>window.location.reload()}):0===H.length?a?(0,t.jsx)(m.wT,{searchTerm:a,onClearSearch:()=>C("")}):(0,t.jsx)(m.sM,{}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:H.map(e=>(0,t.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(l.ZB,{className:"text-lg",children:e.title}),(0,t.jsx)(l.BT,{children:e.description})]}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.duration||"N/A"," min"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.difficulty||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.participantCount||0})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(P(),{href:`/workouts/${e.id}`,className:"flex-1",children:(0,t.jsxs)(n.$,{size:"sm",className:"w-full",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"View Details"]})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(e.id),disabled:O.isPending,children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})]})]})})]},e.id))})]})]}),"my-programs"===e&&W&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"My Programs"}),G?(0,t.jsx)(o.z0,{}):F&&0!==F.length?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:F.map(e=>(0,t.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(l.ZB,{className:"text-lg",children:e.title}),(0,t.jsx)(l.BT,{children:e.description})]}),(0,t.jsx)(c.E,{variant:"secondary",children:"Joined"})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.duration||"N/A"," min"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.difficulty||"N/A"})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(n.$,{size:"sm",className:"flex-1",onClick:()=>Y(e.id),disabled:I.isPending,children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Start Workout"]}),(0,t.jsx)(P(),{href:`/workouts/${e.id}`,children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})})]})]})})]},e.id))}):(0,t.jsx)(m.sM,{})]}),"sessions"===e&&W&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"My Workout Sessions"}),R?(0,t.jsx)(o.z0,{}):L?.data&&0!==L.data.length?(0,t.jsx)("div",{className:"space-y-4",children:L.data.map(e=>(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.notes||"Workout Session"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Workout Session"}),(0,t.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-sm text-gray-500",children:[(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),new Date().toLocaleDateString()]}),e.duration&&(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),e.duration," min"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.E,{variant:"COMPLETED"===e.status?"default":"IN_PROGRESS"===e.status?"secondary":"outline",children:e.status}),(0,t.jsx)(P(),{href:`/workouts/sessions/${e.id}`,children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:"View Details"})})]})]})})},e.id))}):(0,t.jsx)(m.sM,{})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},98807:(e,s,a)=>{Promise.resolve().then(a.bind(a,67997))}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[96,76],()=>a(22266));module.exports=t})();