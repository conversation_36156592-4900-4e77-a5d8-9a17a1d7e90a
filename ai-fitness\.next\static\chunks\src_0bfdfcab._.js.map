{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/providers/query-provider.tsx"], "sourcesContent": ["'use client';\n\n/**\n * React Query Provider for AI-fitness application\n * Provides global state management and caching for API requests\n */\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { useState } from 'react';\n\ninterface QueryProviderProps {\n  children: React.ReactNode;\n}\n\nexport function QueryProvider({ children }: QueryProviderProps) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            // Stale time: how long data is considered fresh\n            staleTime: 5 * 60 * 1000, // 5 minutes\n            \n            // Cache time: how long data stays in cache after component unmounts\n            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n            \n            // Retry configuration\n            retry: (failureCount, error: any) => {\n              // Don't retry on authentication errors\n              if (error?.status === 401 || error?.status === 403) {\n                return false;\n              }\n              \n              // Don't retry on validation errors\n              if (error?.status === 422) {\n                return false;\n              }\n              \n              // Retry up to 3 times for other errors\n              return failureCount < 3;\n            },\n            \n            // Retry delay with exponential backoff\n            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n            \n            // Refetch on window focus (useful for real-time data)\n            refetchOnWindowFocus: false,\n            \n            // Refetch on reconnect\n            refetchOnReconnect: true,\n            \n            // Background refetch interval (disabled by default)\n            refetchInterval: false,\n          },\n          mutations: {\n            // Retry mutations on network errors\n            retry: (failureCount, error: any) => {\n              // Don't retry on client errors (4xx)\n              if (error?.status >= 400 && error?.status < 500) {\n                return false;\n              }\n              \n              // Retry up to 2 times for server errors\n              return failureCount < 2;\n            },\n            \n            // Retry delay for mutations\n            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      {/* Show React Query DevTools in development */}\n      {process.env.NODE_ENV === 'development' && (\n        <ReactQueryDevtools\n          initialIsOpen={false}\n          buttonPosition=\"bottom-right\"\n        />\n      )}\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;AA8EO;;AA5EP;;;CAGC,GAED;AAAA;AACA;AACA;;;AATA;;;;AAeO,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,gDAAgD;wBAChD,WAAW,IAAI,KAAK;wBAEpB,oEAAoE;wBACpE,QAAQ,KAAK,KAAK;wBAElB,sBAAsB;wBACtB,KAAK;sDAAE,CAAC,cAAc;gCACpB,uCAAuC;gCACvC,IAAI,OAAO,WAAW,OAAO,OAAO,WAAW,KAAK;oCAClD,OAAO;gCACT;gCAEA,mCAAmC;gCACnC,IAAI,OAAO,WAAW,KAAK;oCACzB,OAAO;gCACT;gCAEA,uCAAuC;gCACvC,OAAO,eAAe;4BACxB;;wBAEA,uCAAuC;wBACvC,UAAU;sDAAE,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;;wBAEjE,sDAAsD;wBACtD,sBAAsB;wBAEtB,uBAAuB;wBACvB,oBAAoB;wBAEpB,oDAAoD;wBACpD,iBAAiB;oBACnB;oBACA,WAAW;wBACT,oCAAoC;wBACpC,KAAK;sDAAE,CAAC,cAAc;gCACpB,qCAAqC;gCACrC,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;oCAC/C,OAAO;gCACT;gCAEA,wCAAwC;gCACxC,OAAO,eAAe;4BACxB;;wBAEA,4BAA4B;wBAC5B,UAAU;sDAAE,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;;oBACnE;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;YAEA,oDAAyB,+BACxB,6LAAC,uLAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,gBAAe;;;;;;;;;;;;AAKzB;GAvEgB;KAAA", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/config.ts"], "sourcesContent": ["/**\n * API Configuration for AI-fitness application\n * Connects to workout-cool backend API\n */\n\nexport const API_CONFIG = {\n  // Base URL for the workout-cool backend API\n  BASE_URL: process.env.NEXT_PUBLIC_WORKOUT_COOL_API_URL || 'http://localhost:3000',\n\n  // API endpoints - Updated to match workout-cool API structure\n  ENDPOINTS: {\n    // Authentication - Better Auth endpoints\n    AUTH: {\n      SIGNIN: '/api/auth/sign-in',\n      SIGNUP: '/api/auth/sign-up',\n      SIGNOUT: '/api/auth/sign-out',\n      SESSION: '/api/auth/session',\n      RESET_PASSWORD: '/api/auth/reset-password',\n      VERIFY_EMAIL: '/api/auth/verify-email',\n    },\n\n    // Public Programs - workout-cool public API\n    PROGRAMS: {\n      PUBLIC_LIST: '/api/programs/public',\n      DETAILS: (slug: string) => `/api/programs/${slug}`,\n      ENROLL: (id: string) => `/api/programs/${id}/enroll`,\n      PROGRESS: (id: string) => `/api/programs/${id}/progress`,\n      SESSIONS: (programId: string) => `/api/programs/${programId}/sessions`,\n      SESSION_DETAIL: (programId: string, sessionId: string) => `/api/programs/${programId}/sessions/${sessionId}`,\n    },\n\n    // Exercises - workout-cool exercise API\n    EXERCISES: {\n      PUBLIC_LIST: '/api/exercises/public',\n      SEARCH: '/api/exercises/search',\n      DETAILS: (id: string) => `/api/exercises/${id}`,\n      ATTRIBUTES: '/api/exercises/attributes',\n      BY_MUSCLE_GROUP: (muscleGroup: string) => `/api/exercises/muscle-groups/${muscleGroup}`,\n    },\n\n    // Workout Sessions - workout-cool session management\n    WORKOUTS: {\n      LIST: '/api/workout-sessions',\n      CREATE: '/api/workout-sessions',\n      DETAILS: (id: string) => `/api/workout-sessions/${id}`,\n      UPDATE: (id: string) => `/api/workout-sessions/${id}`,\n      DELETE: (id: string) => `/api/workout-sessions/${id}`,\n      COMPLETE: (id: string) => `/api/workout-sessions/${id}/complete`,\n      SYNC: '/api/workout-sessions/sync',\n      USER_SESSIONS: (userId: string) => `/api/users/${userId}/workout-sessions`,\n    },\n\n    // User Progress - workout-cool progress tracking\n    PROGRESS: {\n      USER_STATS: (userId: string) => `/api/users/${userId}/stats`,\n      PROGRAM_PROGRESS: (userId: string, programId: string) => `/api/users/${userId}/programs/${programId}/progress`,\n      WORKOUT_HISTORY: (userId: string) => `/api/users/${userId}/workout-history`,\n      BODY_MEASUREMENTS: (userId: string) => `/api/users/${userId}/body-measurements`,\n      GOALS: (userId: string) => `/api/users/${userId}/goals`,\n    },\n\n    // Premium - workout-cool subscription system\n    PREMIUM: {\n      PLANS: '/api/premium/plans',\n      STATUS: '/api/premium/status',\n      SUBSCRIPTION: '/api/premium/subscription',\n      CHECKOUT: '/api/premium/checkout',\n      BILLING_PORTAL: '/api/premium/billing-portal',\n    },\n\n    // User Management - workout-cool user API\n    USERS: {\n      PROFILE: '/api/users/profile',\n      UPDATE_PROFILE: '/api/users/profile',\n      PREFERENCES: '/api/users/preferences',\n      ENROLLMENTS: (userId: string) => `/api/users/${userId}/program-enrollments`,\n    },\n\n    // Health Check\n    HEALTH: '/api/health',\n  },\n  \n  // Request timeouts\n  TIMEOUT: {\n    DEFAULT: 10000, // 10 seconds\n    UPLOAD: 30000,  // 30 seconds\n    DOWNLOAD: 60000, // 60 seconds\n  },\n  \n  // Retry configuration\n  RETRY: {\n    ATTEMPTS: 3,\n    DELAY: 1000, // 1 second\n    BACKOFF_FACTOR: 2,\n  },\n} as const;\n\nexport type ApiEndpoints = typeof API_CONFIG.ENDPOINTS;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAIW;AAFL,MAAM,aAAa;IACxB,4CAA4C;IAC5C,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI;IAE1D,8DAA8D;IAC9D,WAAW;QACT,yCAAyC;QACzC,MAAM;YACJ,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,SAAS;YACT,gBAAgB;YAChB,cAAc;QAChB;QAEA,4CAA4C;QAC5C,UAAU;YACR,aAAa;YACb,SAAS,CAAC,OAAiB,CAAC,cAAc,EAAE,MAAM;YAClD,QAAQ,CAAC,KAAe,CAAC,cAAc,EAAE,GAAG,OAAO,CAAC;YACpD,UAAU,CAAC,KAAe,CAAC,cAAc,EAAE,GAAG,SAAS,CAAC;YACxD,UAAU,CAAC,YAAsB,CAAC,cAAc,EAAE,UAAU,SAAS,CAAC;YACtE,gBAAgB,CAAC,WAAmB,YAAsB,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,WAAW;QAC9G;QAEA,wCAAwC;QACxC,WAAW;YACT,aAAa;YACb,QAAQ;YACR,SAAS,CAAC,KAAe,CAAC,eAAe,EAAE,IAAI;YAC/C,YAAY;YACZ,iBAAiB,CAAC,cAAwB,CAAC,6BAA6B,EAAE,aAAa;QACzF;QAEA,qDAAqD;QACrD,UAAU;YACR,MAAM;YACN,QAAQ;YACR,SAAS,CAAC,KAAe,CAAC,sBAAsB,EAAE,IAAI;YACtD,QAAQ,CAAC,KAAe,CAAC,sBAAsB,EAAE,IAAI;YACrD,QAAQ,CAAC,KAAe,CAAC,sBAAsB,EAAE,IAAI;YACrD,UAAU,CAAC,KAAe,CAAC,sBAAsB,EAAE,GAAG,SAAS,CAAC;YAChE,MAAM;YACN,eAAe,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,iBAAiB,CAAC;QAC5E;QAEA,iDAAiD;QACjD,UAAU;YACR,YAAY,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;YAC5D,kBAAkB,CAAC,QAAgB,YAAsB,CAAC,WAAW,EAAE,OAAO,UAAU,EAAE,UAAU,SAAS,CAAC;YAC9G,iBAAiB,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,gBAAgB,CAAC;YAC3E,mBAAmB,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,kBAAkB,CAAC;YAC/E,OAAO,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;QACzD;QAEA,6CAA6C;QAC7C,SAAS;YACP,OAAO;YACP,QAAQ;YACR,cAAc;YACd,UAAU;YACV,gBAAgB;QAClB;QAEA,0CAA0C;QAC1C,OAAO;YACL,SAAS;YACT,gBAAgB;YAChB,aAAa;YACb,aAAa,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,oBAAoB,CAAC;QAC7E;QAEA,eAAe;QACf,QAAQ;IACV;IAEA,mBAAmB;IACnB,SAAS;QACP,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IAEA,sBAAsB;IACtB,OAAO;QACL,UAAU;QACV,OAAO;QACP,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/errors.ts"], "sourcesContent": ["/**\n * API Error Classes for AI-fitness application\n * Provides structured error handling for different types of API errors\n */\n\nexport class ApiError extends Error {\n  public readonly status: number;\n  public readonly code?: string;\n  public readonly details?: any;\n\n  constructor(message: string, status: number = 500, code?: string, details?: any) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.code = code;\n    this.details = details;\n\n    // Maintains proper stack trace for where our error was thrown (only available on V8)\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ApiError);\n    }\n  }\n\n  /**\n   * Check if error is a client error (4xx)\n   */\n  get isClientError(): boolean {\n    return this.status >= 400 && this.status < 500;\n  }\n\n  /**\n   * Check if error is a server error (5xx)\n   */\n  get isServerError(): boolean {\n    return this.status >= 500;\n  }\n\n  /**\n   * Convert error to JSON for logging\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      code: this.code,\n      details: this.details,\n      stack: this.stack,\n    };\n  }\n}\n\nexport class NetworkError extends ApiError {\n  constructor(message: string = 'Network error occurred') {\n    super(message, 0, 'NETWORK_ERROR');\n    this.name = 'NetworkError';\n  }\n}\n\nexport class AuthenticationError extends ApiError {\n  constructor(message: string = 'Authentication required') {\n    super(message, 401, 'AUTHENTICATION_ERROR');\n    this.name = 'AuthenticationError';\n  }\n}\n\nexport class AuthorizationError extends ApiError {\n  constructor(message: string = 'Access denied') {\n    super(message, 403, 'AUTHORIZATION_ERROR');\n    this.name = 'AuthorizationError';\n  }\n}\n\nexport class ValidationError extends ApiError {\n  public readonly fieldErrors: Record<string, string[]>;\n\n  constructor(message: string = 'Validation failed', fieldErrors: Record<string, string[]> = {}) {\n    super(message, 422, 'VALIDATION_ERROR', fieldErrors);\n    this.name = 'ValidationError';\n    this.fieldErrors = fieldErrors;\n  }\n\n  /**\n   * Get error message for a specific field\n   */\n  getFieldError(field: string): string | null {\n    const errors = this.fieldErrors[field];\n    return errors && errors.length > 0 ? errors[0] : null;\n  }\n\n  /**\n   * Check if a specific field has errors\n   */\n  hasFieldError(field: string): boolean {\n    return Boolean(this.fieldErrors[field]?.length);\n  }\n\n  /**\n   * Get all field error messages as a flat array\n   */\n  getAllFieldErrors(): string[] {\n    return Object.values(this.fieldErrors).flat();\n  }\n}\n\nexport class NotFoundError extends ApiError {\n  constructor(message: string = 'Resource not found') {\n    super(message, 404, 'NOT_FOUND_ERROR');\n    this.name = 'NotFoundError';\n  }\n}\n\nexport class ConflictError extends ApiError {\n  constructor(message: string = 'Resource conflict') {\n    super(message, 409, 'CONFLICT_ERROR');\n    this.name = 'ConflictError';\n  }\n}\n\nexport class RateLimitError extends ApiError {\n  public readonly retryAfter?: number;\n\n  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {\n    super(message, 429, 'RATE_LIMIT_ERROR', { retryAfter });\n    this.name = 'RateLimitError';\n    this.retryAfter = retryAfter;\n  }\n}\n\n/**\n * Type guard to check if an error is an ApiError\n */\nexport function isApiError(error: any): error is ApiError {\n  return error instanceof ApiError;\n}\n\n/**\n * Type guard to check if an error is a NetworkError\n */\nexport function isNetworkError(error: any): error is NetworkError {\n  return error instanceof NetworkError;\n}\n\n/**\n * Type guard to check if an error is an AuthenticationError\n */\nexport function isAuthenticationError(error: any): error is AuthenticationError {\n  return error instanceof AuthenticationError;\n}\n\n/**\n * Type guard to check if an error is a ValidationError\n */\nexport function isValidationError(error: any): error is ValidationError {\n  return error instanceof ValidationError;\n}\n\n/**\n * Get user-friendly error message from any error\n */\nexport function getErrorMessage(error: any): string {\n  if (isApiError(error)) {\n    return error.message;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  return 'An unexpected error occurred';\n}\n\n/**\n * Get error code from any error\n */\nexport function getErrorCode(error: any): string | null {\n  if (isApiError(error)) {\n    return error.code || null;\n  }\n\n  return null;\n}\n\n/**\n * Check if error should trigger a retry\n */\nexport function shouldRetry(error: any): boolean {\n  if (isNetworkError(error)) {\n    return true;\n  }\n\n  if (isApiError(error)) {\n    // Retry on server errors but not client errors\n    return error.isServerError;\n  }\n\n  return false;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;AAEM,MAAM,iBAAiB;IACZ,OAAe;IACf,KAAc;IACd,QAAc;IAE9B,YAAY,OAAe,EAAE,SAAiB,GAAG,EAAE,IAAa,EAAE,OAAa,CAAE;QAC/E,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QAEf,qFAAqF;QACrF,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE;QAChC;IACF;IAEA;;GAEC,GACD,IAAI,gBAAyB;QAC3B,OAAO,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG;IAC7C;IAEA;;GAEC,GACD,IAAI,gBAAyB;QAC3B,OAAO,IAAI,CAAC,MAAM,IAAI;IACxB;IAEA;;GAEC,GACD,SAAS;QACP,OAAO;YACL,MAAM,IAAI,CAAC,IAAI;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,IAAI,CAAC,MAAM;YACnB,MAAM,IAAI,CAAC,IAAI;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,OAAO,IAAI,CAAC,KAAK;QACnB;IACF;AACF;AAEO,MAAM,qBAAqB;IAChC,YAAY,UAAkB,wBAAwB,CAAE;QACtD,KAAK,CAAC,SAAS,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,4BAA4B;IACvC,YAAY,UAAkB,yBAAyB,CAAE;QACvD,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,2BAA2B;IACtC,YAAY,UAAkB,eAAe,CAAE;QAC7C,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,wBAAwB;IACnB,YAAsC;IAEtD,YAAY,UAAkB,mBAAmB,EAAE,cAAwC,CAAC,CAAC,CAAE;QAC7F,KAAK,CAAC,SAAS,KAAK,oBAAoB;QACxC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;;GAEC,GACD,cAAc,KAAa,EAAiB;QAC1C,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM;QACtC,OAAO,UAAU,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG;IACnD;IAEA;;GAEC,GACD,cAAc,KAAa,EAAW;QACpC,OAAO,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;IAC1C;IAEA;;GAEC,GACD,oBAA8B;QAC5B,OAAO,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI;IAC7C;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,UAAkB,oBAAoB,CAAE;QAClD,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,sBAAsB;IACjC,YAAY,UAAkB,mBAAmB,CAAE;QACjD,KAAK,CAAC,SAAS,KAAK;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,uBAAuB;IAClB,WAAoB;IAEpC,YAAY,UAAkB,qBAAqB,EAAE,UAAmB,CAAE;QACxE,KAAK,CAAC,SAAS,KAAK,oBAAoB;YAAE;QAAW;QACrD,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG;IACpB;AACF;AAKO,SAAS,WAAW,KAAU;IACnC,OAAO,iBAAiB;AAC1B;AAKO,SAAS,eAAe,KAAU;IACvC,OAAO,iBAAiB;AAC1B;AAKO,SAAS,sBAAsB,KAAU;IAC9C,OAAO,iBAAiB;AAC1B;AAKO,SAAS,kBAAkB,KAAU;IAC1C,OAAO,iBAAiB;AAC1B;AAKO,SAAS,gBAAgB,KAAU;IACxC,IAAI,WAAW,QAAQ;QACrB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,aAAa,KAAU;IACrC,IAAI,WAAW,QAAQ;QACrB,OAAO,MAAM,IAAI,IAAI;IACvB;IAEA,OAAO;AACT;AAKO,SAAS,YAAY,KAAU;IACpC,IAAI,eAAe,QAAQ;QACzB,OAAO;IACT;IAEA,IAAI,WAAW,QAAQ;QACrB,+CAA+C;QAC/C,OAAO,MAAM,aAAa;IAC5B;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/client.ts"], "sourcesContent": ["/**\n * API Client for AI-fitness application\n * Handles HTTP requests, authentication, and error handling\n */\n\nimport { API_CONFIG } from './config';\nimport { ApiError, NetworkError, AuthenticationError, ValidationError } from './errors';\n\nexport interface ApiRequestOptions extends RequestInit {\n  timeout?: number;\n  retries?: number;\n  requireAuth?: boolean;\n}\n\nexport interface ApiResponse<T = any> {\n  data: T;\n  success: boolean;\n  message?: string;\n  errors?: Record<string, string[]>;\n}\n\nclass ApiClient {\n  private baseUrl: string;\n  private defaultHeaders: HeadersInit;\n\n  constructor() {\n    this.baseUrl = API_CONFIG.BASE_URL;\n    this.defaultHeaders = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n    };\n  }\n\n  /**\n   * Get authentication token from session storage or cookies\n   */\n  private getAuthToken(): string | null {\n    if (typeof window !== 'undefined') {\n      // Try to get token from localStorage first\n      const token = localStorage.getItem('auth-token');\n      if (token) {\n        return token;\n      }\n\n      // Try to get user data and extract token\n      const userData = localStorage.getItem('auth-user');\n      if (userData) {\n        try {\n          const user = JSON.parse(userData);\n          return user.token || null;\n        } catch {\n          return null;\n        }\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Build request headers with authentication if available\n   */\n  private buildHeaders(options: ApiRequestOptions = {}): HeadersInit {\n    const headers = { ...this.defaultHeaders };\n\n    // Add authentication header if required and available\n    if (options.requireAuth !== false) {\n      const token = this.getAuthToken();\n      if (token) {\n        (headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;\n      }\n    }\n\n    // Merge with custom headers\n    if (options.headers) {\n      Object.assign(headers, options.headers);\n    }\n\n    return headers;\n  }\n\n  /**\n   * Handle API response and errors\n   */\n  private async handleResponse<T>(response: Response): Promise<T> {\n    const contentType = response.headers.get('content-type');\n    const isJson = contentType?.includes('application/json');\n\n    let data: any;\n    try {\n      data = isJson ? await response.json() : await response.text();\n    } catch (error) {\n      throw new ApiError('Failed to parse response', response.status);\n    }\n\n    if (!response.ok) {\n      switch (response.status) {\n        case 401:\n          throw new AuthenticationError(data.message || 'Authentication required');\n        case 422:\n          throw new ValidationError(data.message || 'Validation failed', data.errors);\n        case 404:\n          throw new ApiError(data.message || 'Resource not found', 404);\n        case 500:\n          throw new ApiError(data.message || 'Internal server error', 500);\n        default:\n          throw new ApiError(data.message || 'Request failed', response.status);\n      }\n    }\n\n    return data;\n  }\n\n  /**\n   * Make HTTP request with retry logic\n   */\n  private async makeRequest<T>(\n    url: string,\n    options: ApiRequestOptions = {}\n  ): Promise<T> {\n    const {\n      timeout = API_CONFIG.TIMEOUT.DEFAULT,\n      retries = API_CONFIG.RETRY.ATTEMPTS,\n      ...requestOptions\n    } = options;\n\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n    const requestConfig: RequestInit = {\n      ...requestOptions,\n      headers: this.buildHeaders(options),\n      signal: controller.signal,\n    };\n\n    let lastError: Error | null = null;\n\n    for (let attempt = 0; attempt <= retries; attempt++) {\n      try {\n        const response = await fetch(`${this.baseUrl}${url}`, requestConfig);\n        clearTimeout(timeoutId);\n        return await this.handleResponse<T>(response);\n      } catch (error) {\n        lastError = error as Error;\n\n        // Don't retry on authentication or validation errors\n        if (error instanceof AuthenticationError || error instanceof ValidationError) {\n          throw error;\n        }\n\n        // Don't retry on the last attempt\n        if (attempt === retries) {\n          break;\n        }\n\n        // Wait before retrying\n        const delay = API_CONFIG.RETRY.DELAY * Math.pow(API_CONFIG.RETRY.BACKOFF_FACTOR, attempt);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n\n    clearTimeout(timeoutId);\n\n    // Handle network errors\n    if (lastError) {\n      if (lastError.name === 'AbortError') {\n        throw new NetworkError('Request timeout');\n      }\n\n      if (lastError.name === 'TypeError') {\n        throw new NetworkError('Network error - please check your connection');\n      }\n\n      throw lastError;\n    }\n\n    throw new Error('Request failed after all retries');\n  }\n\n  /**\n   * GET request\n   */\n  async get<T>(url: string, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, { ...options, method: 'GET' });\n  }\n\n  /**\n   * POST request\n   */\n  async post<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, {\n      ...options,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  /**\n   * PUT request\n   */\n  async put<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, {\n      ...options,\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  /**\n   * PATCH request\n   */\n  async patch<T>(url: string, data?: any, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, {\n      ...options,\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  /**\n   * DELETE request\n   */\n  async delete<T>(url: string, options: ApiRequestOptions = {}): Promise<T> {\n    return this.makeRequest<T>(url, { ...options, method: 'DELETE' });\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAeA,MAAM;IACI,QAAgB;IAChB,eAA4B;IAEpC,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,8HAAA,CAAA,aAAU,CAAC,QAAQ;QAClC,IAAI,CAAC,cAAc,GAAG;YACpB,gBAAgB;YAChB,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,AAAQ,eAA8B;QACpC,wCAAmC;YACjC,2CAA2C;YAC3C,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,OAAO;YACT;YAEA,yCAAyC;YACzC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACZ,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,OAAO,KAAK,KAAK,IAAI;gBACvB,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,aAAa,UAA6B,CAAC,CAAC,EAAe;QACjE,MAAM,UAAU;YAAE,GAAG,IAAI,CAAC,cAAc;QAAC;QAEzC,sDAAsD;QACtD,IAAI,QAAQ,WAAW,KAAK,OAAO;YACjC,MAAM,QAAQ,IAAI,CAAC,YAAY;YAC/B,IAAI,OAAO;gBACR,OAAkC,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;YAC1E;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,OAAO,EAAE;YACnB,OAAO,MAAM,CAAC,SAAS,QAAQ,OAAO;QACxC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,eAAkB,QAAkB,EAAc;QAC9D,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,aAAa,SAAS;QAErC,IAAI;QACJ,IAAI;YACF,OAAO,SAAS,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI;QAC7D,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,8HAAA,CAAA,WAAQ,CAAC,4BAA4B,SAAS,MAAM;QAChE;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAQ,SAAS,MAAM;gBACrB,KAAK;oBACH,MAAM,IAAI,8HAAA,CAAA,sBAAmB,CAAC,KAAK,OAAO,IAAI;gBAChD,KAAK;oBACH,MAAM,IAAI,8HAAA,CAAA,kBAAe,CAAC,KAAK,OAAO,IAAI,qBAAqB,KAAK,MAAM;gBAC5E,KAAK;oBACH,MAAM,IAAI,8HAAA,CAAA,WAAQ,CAAC,KAAK,OAAO,IAAI,sBAAsB;gBAC3D,KAAK;oBACH,MAAM,IAAI,8HAAA,CAAA,WAAQ,CAAC,KAAK,OAAO,IAAI,yBAAyB;gBAC9D;oBACE,MAAM,IAAI,8HAAA,CAAA,WAAQ,CAAC,KAAK,OAAO,IAAI,kBAAkB,SAAS,MAAM;YACxE;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,YACZ,GAAW,EACX,UAA6B,CAAC,CAAC,EACnB;QACZ,MAAM,EACJ,UAAU,8HAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,EACpC,UAAU,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,QAAQ,EACnC,GAAG,gBACJ,GAAG;QAEJ,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;QAEvD,MAAM,gBAA6B;YACjC,GAAG,cAAc;YACjB,SAAS,IAAI,CAAC,YAAY,CAAC;YAC3B,QAAQ,WAAW,MAAM;QAC3B;QAEA,IAAI,YAA0B;QAE9B,IAAK,IAAI,UAAU,GAAG,WAAW,SAAS,UAAW;YACnD,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE;gBACtD,aAAa;gBACb,OAAO,MAAM,IAAI,CAAC,cAAc,CAAI;YACtC,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,qDAAqD;gBACrD,IAAI,iBAAiB,8HAAA,CAAA,sBAAmB,IAAI,iBAAiB,8HAAA,CAAA,kBAAe,EAAE;oBAC5E,MAAM;gBACR;gBAEA,kCAAkC;gBAClC,IAAI,YAAY,SAAS;oBACvB;gBACF;gBAEA,uBAAuB;gBACvB,MAAM,QAAQ,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,8HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,cAAc,EAAE;gBACjF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,aAAa;QAEb,wBAAwB;QACxB,IAAI,WAAW;YACb,IAAI,UAAU,IAAI,KAAK,cAAc;gBACnC,MAAM,IAAI,8HAAA,CAAA,eAAY,CAAC;YACzB;YAEA,IAAI,UAAU,IAAI,KAAK,aAAa;gBAClC,MAAM,IAAI,8HAAA,CAAA,eAAY,CAAC;YACzB;YAEA,MAAM;QACR;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA;;GAEC,GACD,MAAM,IAAO,GAAW,EAAE,UAA6B,CAAC,CAAC,EAAc;QACrE,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAC9D;IAEA;;GAEC,GACD,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAE,UAA6B,CAAC,CAAC,EAAc;QAClF,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAC9B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,IAAO,GAAW,EAAE,IAAU,EAAE,UAA6B,CAAC,CAAC,EAAc;QACjF,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAC9B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,MAAS,GAAW,EAAE,IAAU,EAAE,UAA6B,CAAC,CAAC,EAAc;QACnF,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAC9B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA;;GAEC,GACD,MAAM,OAAU,GAAW,EAAE,UAA6B,CAAC,CAAC,EAAc;QACxE,OAAO,IAAI,CAAC,WAAW,CAAI,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IACjE;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/adapters/workout-cool.adapter.ts"], "sourcesContent": ["/**\n * Adapter for workout-cool API data structures\n * Converts workout-cool data models to our frontend types\n */\n\nimport type { \n  Program, \n  Exercise, \n  WorkoutSession, \n  User,\n  ProgramSession,\n  ProgressStats \n} from '../types';\n\n// workout-cool API response types\nexport interface WorkoutCoolProgram {\n  id: string;\n  title: string;\n  titleEn?: string;\n  description: string;\n  descriptionEn?: string;\n  slug: string;\n  slugEn?: string;\n  category: string;\n  image: string;\n  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';\n  type: string;\n  durationWeeks: number;\n  sessionsPerWeek: number;\n  sessionDurationMin: number;\n  equipment: string[];\n  isPremium: boolean;\n  visibility: 'PUBLISHED' | 'DRAFT' | 'ARCHIVED';\n  isActive: boolean;\n  participantCount: number;\n  createdAt: string;\n  updatedAt: string;\n  coaches?: Array<{\n    id: string;\n    name: string;\n    image: string;\n    order: number;\n  }>;\n  weeks?: Array<{\n    id: string;\n    weekNumber: number;\n    sessions: WorkoutCoolProgramSession[];\n  }>;\n  enrollments?: Array<{ id: string }>;\n}\n\nexport interface WorkoutCoolProgramSession {\n  id: string;\n  sessionNumber: number;\n  title: string;\n  titleEn?: string;\n  description: string;\n  descriptionEn?: string;\n  estimatedMinutes: number;\n  equipment: string[];\n  isPremium: boolean;\n  exercises: Array<{\n    id: string;\n    order: number;\n    exercise: WorkoutCoolExercise;\n    suggestedSets: Array<{\n      setIndex: number;\n      reps?: number;\n      weight?: number;\n      duration?: number;\n      restTime?: number;\n    }>;\n  }>;\n}\n\nexport interface WorkoutCoolExercise {\n  id: string;\n  name: string;\n  nameEn?: string;\n  description: string;\n  descriptionEn?: string;\n  instructions: string;\n  instructionsEn?: string;\n  tips?: string;\n  tipsEn?: string;\n  imageUrl?: string;\n  videoUrl?: string;\n  attributes: Array<{\n    id: string;\n    attributeName: {\n      id: string;\n      name: string;\n      nameEn?: string;\n    };\n    attributeValue: {\n      id: string;\n      value: string;\n      valueEn?: string;\n    };\n  }>;\n}\n\nexport interface WorkoutCoolUser {\n  id: string;\n  email: string;\n  name: string;\n  firstName: string;\n  lastName: string;\n  image?: string;\n  locale?: string;\n  role: 'user' | 'admin';\n  isPremium?: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface WorkoutCoolWorkoutSession {\n  id: string;\n  userId: string;\n  programId?: string;\n  sessionId?: string;\n  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';\n  startedAt?: string;\n  completedAt?: string;\n  duration?: number;\n  notes?: string;\n  exercises: Array<{\n    id: string;\n    exerciseId: string;\n    order: number;\n    exercise: WorkoutCoolExercise;\n    sets: Array<{\n      id: string;\n      setIndex: number;\n      reps?: number;\n      weight?: number;\n      duration?: number;\n      restTime?: number;\n      completed: boolean;\n    }>;\n  }>;\n}\n\n/**\n * Adapter class for converting workout-cool data to frontend types\n */\nexport class WorkoutCoolAdapter {\n  /**\n   * Convert workout-cool program to frontend Program type\n   */\n  static adaptProgram(wcProgram: WorkoutCoolProgram): Program {\n    return {\n      id: wcProgram.id,\n      title: wcProgram.titleEn || wcProgram.title,\n      description: wcProgram.descriptionEn || wcProgram.description,\n      slug: wcProgram.slugEn || wcProgram.slug,\n      category: wcProgram.category,\n      image: wcProgram.image,\n      difficulty: wcProgram.level.toLowerCase() as 'beginner' | 'intermediate' | 'advanced',\n      duration: wcProgram.sessionDurationMin,\n      durationWeeks: wcProgram.durationWeeks,\n      sessionsPerWeek: wcProgram.sessionsPerWeek,\n      equipment: wcProgram.equipment,\n      isPremium: wcProgram.isPremium,\n      isActive: wcProgram.isActive,\n      participantCount: wcProgram.participantCount,\n      createdAt: new Date(wcProgram.createdAt),\n      updatedAt: new Date(wcProgram.updatedAt),\n      coaches: wcProgram.coaches?.map(coach => ({\n        id: coach.id,\n        name: coach.name,\n        image: coach.image,\n        order: coach.order,\n      })) || [],\n      weeks: wcProgram.weeks?.map(week => ({\n        id: week.id,\n        weekNumber: week.weekNumber,\n        sessions: week.sessions.map(session => this.adaptProgramSession(session)),\n      })) || [],\n    };\n  }\n\n  /**\n   * Convert workout-cool program session to frontend ProgramSession type\n   */\n  static adaptProgramSession(wcSession: WorkoutCoolProgramSession): ProgramSession {\n    return {\n      id: wcSession.id,\n      sessionNumber: wcSession.sessionNumber,\n      title: wcSession.titleEn || wcSession.title,\n      description: wcSession.descriptionEn || wcSession.description,\n      estimatedDuration: wcSession.estimatedMinutes,\n      equipment: wcSession.equipment,\n      isPremium: wcSession.isPremium,\n      exercises: wcSession.exercises.map(ex => ({\n        id: ex.id,\n        order: ex.order,\n        exerciseId: ex.exercise.id,\n        exercise: this.adaptExercise(ex.exercise),\n        sets: ex.suggestedSets.map(set => ({\n          setIndex: set.setIndex,\n          reps: set.reps,\n          weight: set.weight,\n          duration: set.duration,\n          restTime: set.restTime,\n        })),\n      })),\n    };\n  }\n\n  /**\n   * Convert workout-cool exercise to frontend Exercise type\n   */\n  static adaptExercise(wcExercise: WorkoutCoolExercise): Exercise {\n    // Extract muscle groups and equipment from attributes\n    const muscleGroups: string[] = [];\n    const equipment: string[] = [];\n    let category: 'strength' | 'cardio' | 'flexibility' | 'balance' = 'strength';\n    let difficulty: 'beginner' | 'intermediate' | 'advanced' = 'beginner';\n\n    wcExercise.attributes.forEach(attr => {\n      const attrName = attr.attributeName.nameEn || attr.attributeName.name;\n      const attrValue = attr.attributeValue.valueEn || attr.attributeValue.value;\n\n      if (attrName.toLowerCase().includes('muscle') || attrName.toLowerCase().includes('target')) {\n        muscleGroups.push(attrValue);\n      } else if (attrName.toLowerCase().includes('equipment')) {\n        equipment.push(attrValue);\n      } else if (attrName.toLowerCase().includes('category') || attrName.toLowerCase().includes('type')) {\n        if (attrValue.toLowerCase().includes('cardio')) category = 'cardio';\n        else if (attrValue.toLowerCase().includes('flexibility') || attrValue.toLowerCase().includes('stretch')) category = 'flexibility';\n        else if (attrValue.toLowerCase().includes('balance')) category = 'balance';\n      } else if (attrName.toLowerCase().includes('difficulty') || attrName.toLowerCase().includes('level')) {\n        if (attrValue.toLowerCase().includes('intermediate')) difficulty = 'intermediate';\n        else if (attrValue.toLowerCase().includes('advanced')) difficulty = 'advanced';\n      }\n    });\n\n    return {\n      id: wcExercise.id,\n      name: wcExercise.nameEn || wcExercise.name,\n      description: wcExercise.descriptionEn || wcExercise.description,\n      category,\n      muscleGroups,\n      equipment,\n      difficulty,\n      instructions: (wcExercise.instructionsEn || wcExercise.instructions)?.split('\\n') || [],\n      tips: (wcExercise.tipsEn || wcExercise.tips)?.split('\\n') || [],\n      imageUrl: wcExercise.imageUrl,\n      videoUrl: wcExercise.videoUrl,\n    };\n  }\n\n  /**\n   * Convert workout-cool user to frontend User type\n   */\n  static adaptUser(wcUser: WorkoutCoolUser): User {\n    return {\n      id: wcUser.id,\n      email: wcUser.email,\n      name: wcUser.name,\n      firstName: wcUser.firstName,\n      lastName: wcUser.lastName,\n      avatar: wcUser.image,\n      role: wcUser.role,\n      isPremium: wcUser.isPremium || false,\n      preferences: {\n        language: wcUser.locale || 'en',\n        timezone: 'UTC',\n        units: 'metric',\n        notifications: {\n          email: true,\n          push: true,\n          workout: true,\n          progress: true,\n        },\n      },\n      createdAt: new Date(wcUser.createdAt),\n      updatedAt: new Date(wcUser.updatedAt),\n    };\n  }\n\n  /**\n   * Convert workout-cool workout session to frontend WorkoutSession type\n   */\n  static adaptWorkoutSession(wcSession: WorkoutCoolWorkoutSession): WorkoutSession {\n    return {\n      id: wcSession.id,\n      userId: wcSession.userId,\n      programId: wcSession.programId,\n      sessionId: wcSession.sessionId,\n      status: wcSession.status.toLowerCase() as 'planned' | 'in_progress' | 'completed' | 'cancelled',\n      startedAt: wcSession.startedAt ? new Date(wcSession.startedAt) : undefined,\n      completedAt: wcSession.completedAt ? new Date(wcSession.completedAt) : undefined,\n      duration: wcSession.duration,\n      notes: wcSession.notes,\n      exercises: wcSession.exercises.map(ex => ({\n        id: ex.id,\n        exerciseId: ex.exerciseId,\n        order: ex.order,\n        exercise: this.adaptExercise(ex.exercise),\n        sets: ex.sets.map(set => ({\n          id: set.id,\n          setIndex: set.setIndex,\n          reps: set.reps,\n          weight: set.weight,\n          duration: set.duration,\n          restTime: set.restTime,\n          completed: set.completed,\n        })),\n      })),\n    };\n  }\n\n  /**\n   * Convert frontend data to workout-cool format for API requests\n   */\n  static toWorkoutCoolFormat = {\n    /**\n     * Convert frontend workout session to workout-cool format\n     */\n    workoutSession: (session: Partial<WorkoutSession>) => ({\n      userId: session.userId,\n      programId: session.programId,\n      sessionId: session.sessionId,\n      status: session.status?.toUpperCase(),\n      startedAt: session.startedAt?.toISOString(),\n      completedAt: session.completedAt?.toISOString(),\n      duration: session.duration,\n      notes: session.notes,\n      exercises: session.exercises?.map(ex => ({\n        exerciseId: ex.exerciseId,\n        order: ex.order,\n        sets: ex.sets.map(set => ({\n          setIndex: set.setIndex,\n          reps: set.reps,\n          weight: set.weight,\n          duration: set.duration,\n          restTime: set.restTime,\n          completed: set.completed,\n        })),\n      })),\n    }),\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AA+IM,MAAM;IACX;;GAEC,GACD,OAAO,aAAa,SAA6B,EAAW;QAC1D,OAAO;YACL,IAAI,UAAU,EAAE;YAChB,OAAO,UAAU,OAAO,IAAI,UAAU,KAAK;YAC3C,aAAa,UAAU,aAAa,IAAI,UAAU,WAAW;YAC7D,MAAM,UAAU,MAAM,IAAI,UAAU,IAAI;YACxC,UAAU,UAAU,QAAQ;YAC5B,OAAO,UAAU,KAAK;YACtB,YAAY,UAAU,KAAK,CAAC,WAAW;YACvC,UAAU,UAAU,kBAAkB;YACtC,eAAe,UAAU,aAAa;YACtC,iBAAiB,UAAU,eAAe;YAC1C,WAAW,UAAU,SAAS;YAC9B,WAAW,UAAU,SAAS;YAC9B,UAAU,UAAU,QAAQ;YAC5B,kBAAkB,UAAU,gBAAgB;YAC5C,WAAW,IAAI,KAAK,UAAU,SAAS;YACvC,WAAW,IAAI,KAAK,UAAU,SAAS;YACvC,SAAS,UAAU,OAAO,EAAE,IAAI,CAAA,QAAS,CAAC;oBACxC,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;oBAChB,OAAO,MAAM,KAAK;oBAClB,OAAO,MAAM,KAAK;gBACpB,CAAC,MAAM,EAAE;YACT,OAAO,UAAU,KAAK,EAAE,IAAI,CAAA,OAAQ,CAAC;oBACnC,IAAI,KAAK,EAAE;oBACX,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,IAAI,CAAC,mBAAmB,CAAC;gBAClE,CAAC,MAAM,EAAE;QACX;IACF;IAEA;;GAEC,GACD,OAAO,oBAAoB,SAAoC,EAAkB;QAC/E,OAAO;YACL,IAAI,UAAU,EAAE;YAChB,eAAe,UAAU,aAAa;YACtC,OAAO,UAAU,OAAO,IAAI,UAAU,KAAK;YAC3C,aAAa,UAAU,aAAa,IAAI,UAAU,WAAW;YAC7D,mBAAmB,UAAU,gBAAgB;YAC7C,WAAW,UAAU,SAAS;YAC9B,WAAW,UAAU,SAAS;YAC9B,WAAW,UAAU,SAAS,CAAC,GAAG,CAAC,CAAA,KAAM,CAAC;oBACxC,IAAI,GAAG,EAAE;oBACT,OAAO,GAAG,KAAK;oBACf,YAAY,GAAG,QAAQ,CAAC,EAAE;oBAC1B,UAAU,IAAI,CAAC,aAAa,CAAC,GAAG,QAAQ;oBACxC,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;4BACjC,UAAU,IAAI,QAAQ;4BACtB,MAAM,IAAI,IAAI;4BACd,QAAQ,IAAI,MAAM;4BAClB,UAAU,IAAI,QAAQ;4BACtB,UAAU,IAAI,QAAQ;wBACxB,CAAC;gBACH,CAAC;QACH;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,UAA+B,EAAY;QAC9D,sDAAsD;QACtD,MAAM,eAAyB,EAAE;QACjC,MAAM,YAAsB,EAAE;QAC9B,IAAI,WAA8D;QAClE,IAAI,aAAuD;QAE3D,WAAW,UAAU,CAAC,OAAO,CAAC,CAAA;YAC5B,MAAM,WAAW,KAAK,aAAa,CAAC,MAAM,IAAI,KAAK,aAAa,CAAC,IAAI;YACrE,MAAM,YAAY,KAAK,cAAc,CAAC,OAAO,IAAI,KAAK,cAAc,CAAC,KAAK;YAE1E,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,aAAa,SAAS,WAAW,GAAG,QAAQ,CAAC,WAAW;gBAC1F,aAAa,IAAI,CAAC;YACpB,OAAO,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,cAAc;gBACvD,UAAU,IAAI,CAAC;YACjB,OAAO,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,eAAe,SAAS,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACjG,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;qBACtD,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,kBAAkB,UAAU,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;qBAC/G,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YACnE,OAAO,IAAI,SAAS,WAAW,GAAG,QAAQ,CAAC,iBAAiB,SAAS,WAAW,GAAG,QAAQ,CAAC,UAAU;gBACpG,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,iBAAiB,aAAa;qBAC9D,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,aAAa,aAAa;YACtE;QACF;QAEA,OAAO;YACL,IAAI,WAAW,EAAE;YACjB,MAAM,WAAW,MAAM,IAAI,WAAW,IAAI;YAC1C,aAAa,WAAW,aAAa,IAAI,WAAW,WAAW;YAC/D;YACA;YACA;YACA;YACA,cAAc,CAAC,WAAW,cAAc,IAAI,WAAW,YAAY,GAAG,MAAM,SAAS,EAAE;YACvF,MAAM,CAAC,WAAW,MAAM,IAAI,WAAW,IAAI,GAAG,MAAM,SAAS,EAAE;YAC/D,UAAU,WAAW,QAAQ;YAC7B,UAAU,WAAW,QAAQ;QAC/B;IACF;IAEA;;GAEC,GACD,OAAO,UAAU,MAAuB,EAAQ;QAC9C,OAAO;YACL,IAAI,OAAO,EAAE;YACb,OAAO,OAAO,KAAK;YACnB,MAAM,OAAO,IAAI;YACjB,WAAW,OAAO,SAAS;YAC3B,UAAU,OAAO,QAAQ;YACzB,QAAQ,OAAO,KAAK;YACpB,MAAM,OAAO,IAAI;YACjB,WAAW,OAAO,SAAS,IAAI;YAC/B,aAAa;gBACX,UAAU,OAAO,MAAM,IAAI;gBAC3B,UAAU;gBACV,OAAO;gBACP,eAAe;oBACb,OAAO;oBACP,MAAM;oBACN,SAAS;oBACT,UAAU;gBACZ;YACF;YACA,WAAW,IAAI,KAAK,OAAO,SAAS;YACpC,WAAW,IAAI,KAAK,OAAO,SAAS;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,oBAAoB,SAAoC,EAAkB;QAC/E,OAAO;YACL,IAAI,UAAU,EAAE;YAChB,QAAQ,UAAU,MAAM;YACxB,WAAW,UAAU,SAAS;YAC9B,WAAW,UAAU,SAAS;YAC9B,QAAQ,UAAU,MAAM,CAAC,WAAW;YACpC,WAAW,UAAU,SAAS,GAAG,IAAI,KAAK,UAAU,SAAS,IAAI;YACjE,aAAa,UAAU,WAAW,GAAG,IAAI,KAAK,UAAU,WAAW,IAAI;YACvE,UAAU,UAAU,QAAQ;YAC5B,OAAO,UAAU,KAAK;YACtB,WAAW,UAAU,SAAS,CAAC,GAAG,CAAC,CAAA,KAAM,CAAC;oBACxC,IAAI,GAAG,EAAE;oBACT,YAAY,GAAG,UAAU;oBACzB,OAAO,GAAG,KAAK;oBACf,UAAU,IAAI,CAAC,aAAa,CAAC,GAAG,QAAQ;oBACxC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;4BACxB,IAAI,IAAI,EAAE;4BACV,UAAU,IAAI,QAAQ;4BACtB,MAAM,IAAI,IAAI;4BACd,QAAQ,IAAI,MAAM;4BAClB,UAAU,IAAI,QAAQ;4BACtB,UAAU,IAAI,QAAQ;4BACtB,WAAW,IAAI,SAAS;wBAC1B,CAAC;gBACH,CAAC;QACH;IACF;IAEA;;GAEC,GACD,OAAO,sBAAsB;QAC3B;;KAEC,GACD,gBAAgB,CAAC,UAAqC,CAAC;gBACrD,QAAQ,QAAQ,MAAM;gBACtB,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;gBAC5B,QAAQ,QAAQ,MAAM,EAAE;gBACxB,WAAW,QAAQ,SAAS,EAAE;gBAC9B,aAAa,QAAQ,WAAW,EAAE;gBAClC,UAAU,QAAQ,QAAQ;gBAC1B,OAAO,QAAQ,KAAK;gBACpB,WAAW,QAAQ,SAAS,EAAE,IAAI,CAAA,KAAM,CAAC;wBACvC,YAAY,GAAG,UAAU;wBACzB,OAAO,GAAG,KAAK;wBACf,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gCACxB,UAAU,IAAI,QAAQ;gCACtB,MAAM,IAAI,IAAI;gCACd,QAAQ,IAAI,MAAM;gCAClB,UAAU,IAAI,QAAQ;gCACtB,UAAU,IAAI,QAAQ;gCACtB,WAAW,IAAI,SAAS;4BAC1B,CAAC;oBACH,CAAC;YACH,CAAC;IACH,EAAE;AACJ", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/auth.ts"], "sourcesContent": ["/**\n * Authentication API Service\n * Handles authentication-related API calls to workout-cool backend\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport { WorkoutCoolAdapter, type WorkoutCoolUser } from '../adapters/workout-cool.adapter';\nimport type { User, Session, AuthCredentials, SignUpData } from '../types';\n\nexport class AuthService {\n  /**\n   * Sign in with email and password - adapted for Better Auth\n   */\n  static async signIn(credentials: AuthCredentials): Promise<Session> {\n    const response = await apiClient.post<{\n      user: WorkoutCoolUser;\n      session: { id: string; userId: string; expiresAt: string };\n    }>(API_CONFIG.ENDPOINTS.AUTH.SIGNIN, {\n      email: credentials.email,\n      password: credentials.password,\n    }, {\n      requireAuth: false,\n    });\n\n    return {\n      user: WorkoutCoolAdapter.adaptUser(response.user),\n      sessionId: response.session.id,\n      expiresAt: new Date(response.session.expiresAt),\n    };\n  }\n\n  /**\n   * Sign up with user data - adapted for Better Auth\n   */\n  static async signUp(userData: SignUpData): Promise<Session> {\n    const response = await apiClient.post<{\n      user: WorkoutCoolUser;\n      session: { id: string; userId: string; expiresAt: string };\n    }>(API_CONFIG.ENDPOINTS.AUTH.SIGNUP, {\n      email: userData.email,\n      password: userData.password,\n      firstName: userData.firstName,\n      lastName: userData.lastName,\n      name: `${userData.firstName} ${userData.lastName}`,\n    }, {\n      requireAuth: false,\n    });\n\n    return {\n      user: WorkoutCoolAdapter.adaptUser(response.user),\n      sessionId: response.session.id,\n      expiresAt: new Date(response.session.expiresAt),\n    };\n  }\n\n  /**\n   * Sign out current user - adapted for Better Auth\n   */\n  static async signOut(): Promise<void> {\n    return apiClient.post<void>(API_CONFIG.ENDPOINTS.AUTH.SIGNOUT);\n  }\n\n  /**\n   * Get current session\n   */\n  static async getSession(): Promise<Session | null> {\n    try {\n      return await apiClient.get<Session>(API_CONFIG.ENDPOINTS.AUTH.SESSION);\n    } catch (error: any) {\n      // Return null if not authenticated\n      if (error.status === 401) {\n        return null;\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * Request password reset\n   */\n  static async requestPasswordReset(email: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(\n      API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD,\n      { email },\n      { requireAuth: false }\n    );\n  }\n\n  /**\n   * Reset password with token\n   */\n  static async resetPassword(\n    token: string,\n    newPassword: string\n  ): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(\n      API_CONFIG.ENDPOINTS.AUTH.RESET_PASSWORD,\n      { token, password: newPassword },\n      { requireAuth: false }\n    );\n  }\n\n  /**\n   * Verify email with token\n   */\n  static async verifyEmail(token: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(\n      '/api/auth/verify-email',\n      { token },\n      { requireAuth: false }\n    );\n  }\n\n  /**\n   * Refresh authentication token\n   */\n  static async refreshToken(): Promise<Session> {\n    return apiClient.post<Session>('/api/auth/refresh');\n  }\n\n  /**\n   * Update user profile\n   */\n  static async updateProfile(updates: Partial<User>): Promise<User> {\n    return apiClient.patch<User>('/api/auth/profile', updates);\n  }\n\n  /**\n   * Change password\n   */\n  static async changePassword(\n    currentPassword: string,\n    newPassword: string\n  ): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>('/api/auth/change-password', {\n      currentPassword,\n      newPassword,\n    });\n  }\n\n  /**\n   * Delete account\n   */\n  static async deleteAccount(password: string): Promise<{ message: string }> {\n    return apiClient.delete<{ message: string }>('/api/auth/account', {\n      body: JSON.stringify({ password }),\n    });\n  }\n\n  /**\n   * Get user sessions\n   */\n  static async getUserSessions(): Promise<Array<{\n    id: string;\n    userAgent: string;\n    ip: string;\n    createdAt: string;\n    lastUsed: string;\n    isCurrent: boolean;\n  }>> {\n    return apiClient.get<Array<{\n      id: string;\n      userAgent: string;\n      ip: string;\n      createdAt: string;\n      lastUsed: string;\n      isCurrent: boolean;\n    }>>('/api/auth/sessions');\n  }\n\n  /**\n   * Revoke a specific session\n   */\n  static async revokeSession(sessionId: string): Promise<{ message: string }> {\n    return apiClient.delete<{ message: string }>(`/api/auth/sessions/${sessionId}`);\n  }\n\n  /**\n   * Revoke all other sessions\n   */\n  static async revokeAllOtherSessions(): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>('/api/auth/sessions/revoke-all');\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AAGO,MAAM;IACX;;GAEC,GACD,aAAa,OAAO,WAA4B,EAAoB;QAClE,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,IAAI,CAGlC,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;YACnC,OAAO,YAAY,KAAK;YACxB,UAAU,YAAY,QAAQ;QAChC,GAAG;YACD,aAAa;QACf;QAEA,OAAO;YACL,MAAM,8JAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,SAAS,IAAI;YAChD,WAAW,SAAS,OAAO,CAAC,EAAE;YAC9B,WAAW,IAAI,KAAK,SAAS,OAAO,CAAC,SAAS;QAChD;IACF;IAEA;;GAEC,GACD,aAAa,OAAO,QAAoB,EAAoB;QAC1D,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,IAAI,CAGlC,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;YACnC,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,MAAM,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;QACpD,GAAG;YACD,aAAa;QACf;QAEA,OAAO;YACL,MAAM,8JAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,SAAS,IAAI;YAChD,WAAW,SAAS,OAAO,CAAC,EAAE;YAC9B,WAAW,IAAI,KAAK,SAAS,OAAO,CAAC,SAAS;QAChD;IACF;IAEA;;GAEC,GACD,aAAa,UAAyB;QACpC,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAO,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;IAC/D;IAEA;;GAEC,GACD,aAAa,aAAsC;QACjD,IAAI;YACF,OAAO,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAU,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;QACvE,EAAE,OAAO,OAAY;YACnB,mCAAmC;YACnC,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,qBAAqB,KAAa,EAAgC;QAC7E,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EACxC;YAAE;QAAM,GACR;YAAE,aAAa;QAAM;IAEzB;IAEA;;GAEC,GACD,aAAa,cACX,KAAa,EACb,WAAmB,EACW;QAC9B,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EACxC;YAAE;YAAO,UAAU;QAAY,GAC/B;YAAE,aAAa;QAAM;IAEzB;IAEA;;GAEC,GACD,aAAa,YAAY,KAAa,EAAgC;QACpE,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CACnB,0BACA;YAAE;QAAM,GACR;YAAE,aAAa;QAAM;IAEzB;IAEA;;GAEC,GACD,aAAa,eAAiC;QAC5C,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAU;IACjC;IAEA;;GAEC,GACD,aAAa,cAAc,OAAsB,EAAiB;QAChE,OAAO,8HAAA,CAAA,YAAS,CAAC,KAAK,CAAO,qBAAqB;IACpD;IAEA;;GAEC,GACD,aAAa,eACX,eAAuB,EACvB,WAAmB,EACW;QAC9B,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB,6BAA6B;YACtE;YACA;QACF;IACF;IAEA;;GAEC,GACD,aAAa,cAAc,QAAgB,EAAgC;QACzE,OAAO,8HAAA,CAAA,YAAS,CAAC,MAAM,CAAsB,qBAAqB;YAChE,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;IACF;IAEA;;GAEC,GACD,aAAa,kBAOT;QACF,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAOhB;IACN;IAEA;;GAEC,GACD,aAAa,cAAc,SAAiB,EAAgC;QAC1E,OAAO,8HAAA,CAAA,YAAS,CAAC,MAAM,CAAsB,CAAC,mBAAmB,EAAE,WAAW;IAChF;IAEA;;GAEC,GACD,aAAa,yBAAuD;QAClE,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/hooks/use-auth.ts"], "sourcesContent": ["/**\n * Authentication React Query hooks\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { AuthService } from '../api/services/auth';\nimport type { AuthCredentials, SignUpData } from '../api/types';\n\n// Query keys for authentication\nexport const authKeys = {\n  all: ['auth'] as const,\n  session: () => [...authKeys.all, 'session'] as const,\n  sessions: () => [...authKeys.all, 'sessions'] as const,\n};\n\n/**\n * Hook to get current session\n */\nexport function useSession() {\n  return useQuery({\n    queryKey: authKeys.session(),\n    queryFn: () => AuthService.getSession(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    retry: false, // Don't retry on auth failures\n  });\n}\n\n/**\n * Hook to get authentication state\n */\nexport function useAuth() {\n  const { data: session, isLoading, error } = useSession();\n  \n  return {\n    user: session?.user || null,\n    isAuthenticated: !!session?.user,\n    isLoading,\n    error,\n    session,\n  };\n}\n\n/**\n * Hook to check if user is admin\n */\nexport function useIsAdmin() {\n  const { user, isAuthenticated } = useAuth();\n  return isAuthenticated && user?.role === 'admin';\n}\n\n/**\n * Hook to check if user is premium\n */\nexport function useIsPremium() {\n  const { user, isAuthenticated } = useAuth();\n  return isAuthenticated && user?.isPremium === true;\n}\n\n/**\n * Hook for sign in mutation\n */\nexport function useSignIn() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (credentials: AuthCredentials) => AuthService.signIn(credentials),\n    onSuccess: (session) => {\n      // Update session cache\n      queryClient.setQueryData(authKeys.session(), session);\n      \n      // Invalidate all queries to refetch with new auth state\n      queryClient.invalidateQueries();\n    },\n    onError: (error) => {\n      console.error('Sign in failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for sign up mutation\n */\nexport function useSignUp() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (userData: SignUpData) => AuthService.signUp(userData),\n    onSuccess: (session) => {\n      // Update session cache\n      queryClient.setQueryData(authKeys.session(), session);\n      \n      // Invalidate all queries to refetch with new auth state\n      queryClient.invalidateQueries();\n    },\n    onError: (error) => {\n      console.error('Sign up failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for sign out mutation\n */\nexport function useSignOut() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: () => AuthService.signOut(),\n    onSuccess: () => {\n      // Clear session cache\n      queryClient.setQueryData(authKeys.session(), null);\n      \n      // Clear all cached data\n      queryClient.clear();\n    },\n    onError: (error) => {\n      console.error('Sign out failed:', error);\n      \n      // Even if sign out fails on server, clear local cache\n      queryClient.setQueryData(authKeys.session(), null);\n      queryClient.clear();\n    },\n  });\n}\n\n/**\n * Hook for password reset request\n */\nexport function useRequestPasswordReset() {\n  return useMutation({\n    mutationFn: (email: string) => AuthService.requestPasswordReset(email),\n    onError: (error) => {\n      console.error('Password reset request failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for password reset\n */\nexport function useResetPassword() {\n  return useMutation({\n    mutationFn: ({ token, newPassword }: { token: string; newPassword: string }) =>\n      AuthService.resetPassword(token, newPassword),\n    onError: (error) => {\n      console.error('Password reset failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for email verification\n */\nexport function useVerifyEmail() {\n  return useMutation({\n    mutationFn: (token: string) => AuthService.verifyEmail(token),\n    onError: (error) => {\n      console.error('Email verification failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for profile update\n */\nexport function useUpdateProfile() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (updates: Parameters<typeof AuthService.updateProfile>[0]) =>\n      AuthService.updateProfile(updates),\n    onSuccess: (updatedUser) => {\n      // Update session cache with new user data\n      const currentSession = queryClient.getQueryData(authKeys.session());\n      if (currentSession) {\n        queryClient.setQueryData(authKeys.session(), {\n          ...currentSession,\n          user: updatedUser,\n        });\n      }\n    },\n    onError: (error) => {\n      console.error('Profile update failed:', error);\n    },\n  });\n}\n\n/**\n * Hook for password change\n */\nexport function useChangePassword() {\n  return useMutation({\n    mutationFn: ({ currentPassword, newPassword }: {\n      currentPassword: string;\n      newPassword: string;\n    }) => AuthService.changePassword(currentPassword, newPassword),\n    onError: (error) => {\n      console.error('Password change failed:', error);\n    },\n  });\n}\n\n/**\n * Hook to get user sessions\n */\nexport function useUserSessions() {\n  return useQuery({\n    queryKey: authKeys.sessions(),\n    queryFn: () => AuthService.getUserSessions(),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n/**\n * Hook for session revocation\n */\nexport function useRevokeSession() {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (sessionId: string) => AuthService.revokeSession(sessionId),\n    onSuccess: () => {\n      // Refetch sessions list\n      queryClient.invalidateQueries({ queryKey: authKeys.sessions() });\n    },\n    onError: (error) => {\n      console.error('Session revocation failed:', error);\n    },\n  });\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;AAED;AAAA;AAAA;AACA;;;;AAIO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAO;IACb,SAAS,IAAM;eAAI,SAAS,GAAG;YAAE;SAAU;IAC3C,UAAU,IAAM;eAAI,SAAS,GAAG;YAAE;SAAW;AAC/C;AAKO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,OAAO;QAC1B,OAAO;mCAAE,IAAM,wIAAA,CAAA,cAAW,CAAC,UAAU;;QACrC,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;AACF;GAPgB;;QACP,8KAAA,CAAA,WAAQ;;;AAWV,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAE5C,OAAO;QACL,MAAM,SAAS,QAAQ;QACvB,iBAAiB,CAAC,CAAC,SAAS;QAC5B;QACA;QACA;IACF;AACF;IAVgB;;QAC8B;;;AAcvC,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;IAClC,OAAO,mBAAmB,MAAM,SAAS;AAC3C;IAHgB;;QACoB;;;AAO7B,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;IAClC,OAAO,mBAAmB,MAAM,cAAc;AAChD;IAHgB;;QACoB;;;AAO7B,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qCAAE,CAAC,cAAiC,wIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;;QACjE,SAAS;qCAAE,CAAC;gBACV,uBAAuB;gBACvB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;gBAE7C,wDAAwD;gBACxD,YAAY,iBAAiB;YAC/B;;QACA,OAAO;qCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,mBAAmB;YACnC;;IACF;AACF;IAhBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qCAAE,CAAC,WAAyB,wIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;;QACzD,SAAS;qCAAE,CAAC;gBACV,uBAAuB;gBACvB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;gBAE7C,wDAAwD;gBACxD,YAAY,iBAAiB;YAC/B;;QACA,OAAO;qCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,mBAAmB;YACnC;;IACF;AACF;IAhBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;sCAAE,IAAM,wIAAA,CAAA,cAAW,CAAC,OAAO;;QACrC,SAAS;sCAAE;gBACT,sBAAsB;gBACtB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;gBAE7C,wBAAwB;gBACxB,YAAY,KAAK;YACnB;;QACA,OAAO;sCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,oBAAoB;gBAElC,sDAAsD;gBACtD,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;gBAC7C,YAAY,KAAK;YACnB;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAsBb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;mDAAE,CAAC,QAAkB,wIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC;;QAChE,OAAO;mDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;YAClD;;IACF;AACF;IAPgB;;QACP,iLAAA,CAAA,cAAW;;;AAWb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,EAAE,KAAK,EAAE,WAAW,EAA0C,GACzE,wIAAA,CAAA,cAAW,CAAC,aAAa,CAAC,OAAO;;QACnC,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IARgB;;QACP,iLAAA,CAAA,cAAW;;;AAYb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;0CAAE,CAAC,QAAkB,wIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;;QACvD,OAAO;0CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;;IACF;AACF;IAPgB;;QACP,iLAAA,CAAA,cAAW;;;AAWb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,UACX,wIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;;QAC5B,SAAS;4CAAE,CAAC;gBACV,0CAA0C;gBAC1C,MAAM,iBAAiB,YAAY,YAAY,CAAC,SAAS,OAAO;gBAChE,IAAI,gBAAgB;oBAClB,YAAY,YAAY,CAAC,SAAS,OAAO,IAAI;wBAC3C,GAAG,cAAc;wBACjB,MAAM;oBACR;gBACF;YACF;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;KApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAsBb,SAAS;;IACd,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,EAAE,eAAe,EAAE,WAAW,EAG1C,GAAK,wIAAA,CAAA,cAAW,CAAC,cAAc,CAAC,iBAAiB;;QAClD,OAAO;6CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AACF;KAVgB;;QACP,iLAAA,CAAA,cAAW;;;AAcb,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,QAAQ;QAC3B,OAAO;wCAAE,IAAM,wIAAA,CAAA,cAAW,CAAC,eAAe;;QAC1C,WAAW,IAAI,KAAK;IACtB;AACF;KANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,YAAsB,wIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;;QAC7D,SAAS;4CAAE;gBACT,wBAAwB;gBACxB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,QAAQ;gBAAG;YAChE;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;;IACF;AACF;KAbgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/providers/auth-provider.tsx"], "sourcesContent": ["'use client';\n\n/**\n * Authentication Provider for AI-fitness application\n * Provides authentication context and session management\n */\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { useAuth as useAuthQuery } from '../hooks/use-auth';\nimport type { User } from '../api/types';\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: Error | null;\n  refreshAuth: () => void;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const { user, isAuthenticated, isLoading, error } = useAuthQuery();\n  const [refreshKey, setRefreshKey] = useState(0);\n\n  const refreshAuth = () => {\n    setRefreshKey(prev => prev + 1);\n  };\n\n  // Update API client with auth token when user changes\n  useEffect(() => {\n    if (user && typeof window !== 'undefined') {\n      // Store auth state in localStorage for API client\n      localStorage.setItem('auth-user', JSON.stringify(user));\n    } else if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth-user');\n    }\n  }, [user]);\n\n  const contextValue: AuthContextType = {\n    user,\n    isAuthenticated,\n    isLoading,\n    error,\n    refreshAuth,\n  };\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\n/**\n * Hook to use authentication context\n */\nexport function useAuthContext() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuthContext must be used within an AuthProvider');\n  }\n  return context;\n}\n\n/**\n * Higher-order component to protect routes that require authentication\n */\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>\n): React.ComponentType<P> {\n  return function AuthenticatedComponent(props: P) {\n    const { isAuthenticated, isLoading } = useAuthContext();\n\n    if (isLoading) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n      );\n    }\n\n    if (!isAuthenticated) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-2xl font-bold text-center text-gray-900 mb-4\">\n              Authentication Required\n            </h2>\n            <p className=\"text-gray-600 text-center mb-6\">\n              Please sign in to access this page.\n            </p>\n            <div className=\"space-y-3\">\n              <button\n                onClick={() => window.location.href = '/auth/signin'}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Sign In\n              </button>\n              <button\n                onClick={() => window.location.href = '/auth/signup'}\n                className=\"w-full bg-gray-200 text-gray-900 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors\"\n              >\n                Sign Up\n              </button>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return <Component {...props} />;\n  };\n}\n\n/**\n * Higher-order component to protect routes that require admin access\n */\nexport function withAdminAuth<P extends object>(\n  Component: React.ComponentType<P>\n): React.ComponentType<P> {\n  return function AdminAuthenticatedComponent(props: P) {\n    const { user, isAuthenticated, isLoading } = useAuthContext();\n\n    if (isLoading) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n        </div>\n      );\n    }\n\n    if (!isAuthenticated || user?.role !== 'admin') {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-2xl font-bold text-center text-gray-900 mb-4\">\n              Admin Access Required\n            </h2>\n            <p className=\"text-gray-600 text-center mb-6\">\n              You need administrator privileges to access this page.\n            </p>\n            <button\n              onClick={() => window.location.href = '/'}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              Go Home\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return <Component {...props} />;\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;CAGC,GAED;AACA;;;AARA;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAY,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,cAAc;QAClB,cAAc,CAAA,OAAQ,OAAO;IAC/B;IAEA,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,QAAQ,aAAkB,aAAa;gBACzC,kDAAkD;gBAClD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACnD,OAAO,wCAAmC;gBACxC,aAAa,UAAU,CAAC;YAC1B;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,eAAgC;QACpC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA/BgB;;QACsC,qIAAA,CAAA,UAAY;;;KADlD;AAoCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAWT,SAAS,SACd,SAAiC;;IAEjC,UAAO,SAAS,uBAAuB,KAAQ;;QAC7C,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;QAEvC,IAAI,WAAW;YACb,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,CAAC,iBAAiB;YACpB,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;QAOX;QAEA,qBAAO,6LAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;;YAxCyC;;;AAyC3C;AAKO,SAAS,cACd,SAAiC;;IAEjC,UAAO,SAAS,4BAA4B,KAAQ;;QAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;QAE7C,IAAI,WAAW;YACb,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,CAAC,mBAAmB,MAAM,SAAS,SAAS;YAC9C,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAG9C,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,qBAAO,6LAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;;YAhC+C;;;AAiCjD", "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/store/app-store.ts"], "sourcesContent": ["/**\n * Global Application State Management with Zustand\n * Handles app-wide state that needs to persist across components\n */\n\nimport { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport { immer } from 'zustand/middleware/immer';\nimport type { User } from '../api/types';\n\n// ============================================================================\n// APP STATE INTERFACES\n// ============================================================================\n\ninterface AppSettings {\n  theme: 'light' | 'dark' | 'system';\n  language: 'en' | 'zh';\n  units: 'metric' | 'imperial';\n  notifications: {\n    workoutReminders: boolean;\n    progressUpdates: boolean;\n    achievements: boolean;\n    marketing: boolean;\n  };\n  privacy: {\n    shareProgress: boolean;\n    showInLeaderboards: boolean;\n    allowDataCollection: boolean;\n  };\n}\n\ninterface WorkoutPreferences {\n  defaultDuration: number; // minutes\n  preferredDifficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';\n  favoriteCategories: string[];\n  excludedEquipment: string[];\n  restTimeBetweenSets: number; // seconds\n  autoStartNextExercise: boolean;\n  playWorkoutMusic: boolean;\n  voiceInstructions: boolean;\n}\n\ninterface UIState {\n  sidebarCollapsed: boolean;\n  activeWorkoutSession: string | null;\n  currentPage: string;\n  breadcrumbs: Array<{ label: string; href: string }>;\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    title: string;\n    message: string;\n    timestamp: number;\n    read: boolean;\n  }>;\n  modals: {\n    workoutComplete: boolean;\n    goalAchieved: boolean;\n    subscriptionPrompt: boolean;\n  };\n}\n\ninterface OfflineState {\n  isOnline: boolean;\n  pendingSync: Array<{\n    id: string;\n    type: 'workout' | 'progress' | 'goal';\n    action: 'create' | 'update' | 'delete';\n    data: any;\n    timestamp: number;\n  }>;\n  lastSyncTime: number | null;\n}\n\ninterface AppState {\n  // User & Auth\n  user: User | null;\n  isAuthenticated: boolean;\n  \n  // App Settings\n  settings: AppSettings;\n  workoutPreferences: WorkoutPreferences;\n  \n  // UI State\n  ui: UIState;\n  \n  // Offline Support\n  offline: OfflineState;\n  \n  // Actions\n  setUser: (user: User | null) => void;\n  setAuthenticated: (authenticated: boolean) => void;\n  updateSettings: (settings: Partial<AppSettings>) => void;\n  updateWorkoutPreferences: (preferences: Partial<WorkoutPreferences>) => void;\n  updateUIState: (ui: Partial<UIState>) => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n  setOnlineStatus: (isOnline: boolean) => void;\n  addPendingSync: (item: Omit<OfflineState['pendingSync'][0], 'id' | 'timestamp'>) => void;\n  removePendingSync: (id: string) => void;\n  clearPendingSync: () => void;\n  updateLastSyncTime: () => void;\n  reset: () => void;\n}\n\n// ============================================================================\n// DEFAULT VALUES\n// ============================================================================\n\nconst defaultSettings: AppSettings = {\n  theme: 'system',\n  language: 'en',\n  units: 'metric',\n  notifications: {\n    workoutReminders: true,\n    progressUpdates: true,\n    achievements: true,\n    marketing: false,\n  },\n  privacy: {\n    shareProgress: false,\n    showInLeaderboards: true,\n    allowDataCollection: true,\n  },\n};\n\nconst defaultWorkoutPreferences: WorkoutPreferences = {\n  defaultDuration: 45,\n  preferredDifficulty: 'INTERMEDIATE',\n  favoriteCategories: [],\n  excludedEquipment: [],\n  restTimeBetweenSets: 60,\n  autoStartNextExercise: false,\n  playWorkoutMusic: true,\n  voiceInstructions: false,\n};\n\nconst defaultUIState: UIState = {\n  sidebarCollapsed: false,\n  activeWorkoutSession: null,\n  currentPage: '/',\n  breadcrumbs: [],\n  notifications: [],\n  modals: {\n    workoutComplete: false,\n    goalAchieved: false,\n    subscriptionPrompt: false,\n  },\n};\n\nconst defaultOfflineState: OfflineState = {\n  isOnline: true,\n  pendingSync: [],\n  lastSyncTime: null,\n};\n\n// ============================================================================\n// ZUSTAND STORE\n// ============================================================================\n\nexport const useAppStore = create<AppState>()(\n  persist(\n    immer((set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      settings: defaultSettings,\n      workoutPreferences: defaultWorkoutPreferences,\n      ui: defaultUIState,\n      offline: defaultOfflineState,\n\n      // Actions\n      setUser: (user) => set((state) => {\n        state.user = user;\n      }),\n\n      setAuthenticated: (authenticated) => set((state) => {\n        state.isAuthenticated = authenticated;\n        if (!authenticated) {\n          state.user = null;\n        }\n      }),\n\n      updateSettings: (newSettings) => set((state) => {\n        Object.assign(state.settings, newSettings);\n      }),\n\n      updateWorkoutPreferences: (newPreferences) => set((state) => {\n        Object.assign(state.workoutPreferences, newPreferences);\n      }),\n\n      updateUIState: (newUIState) => set((state) => {\n        Object.assign(state.ui, newUIState);\n      }),\n\n      addNotification: (notification) => set((state) => {\n        // Ensure notifications array exists\n        if (!state.ui.notifications) {\n          state.ui.notifications = [];\n        }\n\n        const newNotification = {\n          ...notification,\n          id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          timestamp: Date.now(),\n          read: false,\n        };\n        state.ui.notifications.unshift(newNotification);\n\n        // Keep only last 50 notifications\n        if (state.ui.notifications.length > 50) {\n          state.ui.notifications = state.ui.notifications.slice(0, 50);\n        }\n      }),\n\n      markNotificationRead: (id) => set((state) => {\n        // Ensure notifications array exists\n        if (!state.ui.notifications) {\n          state.ui.notifications = [];\n          return;\n        }\n\n        const notification = state.ui.notifications.find(n => n.id === id);\n        if (notification) {\n          notification.read = true;\n        }\n      }),\n\n      clearNotifications: () => set((state) => {\n        state.ui.notifications = [];\n      }),\n\n      setOnlineStatus: (isOnline) => set((state) => {\n        state.offline.isOnline = isOnline;\n      }),\n\n      addPendingSync: (item) => set((state) => {\n        const newItem = {\n          ...item,\n          id: `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          timestamp: Date.now(),\n        };\n        state.offline.pendingSync.push(newItem);\n      }),\n\n      removePendingSync: (id) => set((state) => {\n        state.offline.pendingSync = state.offline.pendingSync.filter(item => item.id !== id);\n      }),\n\n      clearPendingSync: () => set((state) => {\n        state.offline.pendingSync = [];\n      }),\n\n      updateLastSyncTime: () => set((state) => {\n        state.offline.lastSyncTime = Date.now();\n      }),\n\n      reset: () => set(() => ({\n        user: null,\n        isAuthenticated: false,\n        settings: defaultSettings,\n        workoutPreferences: defaultWorkoutPreferences,\n        ui: defaultUIState,\n        offline: defaultOfflineState,\n      })),\n    })),\n    {\n      name: 'ai-fitness-app-store',\n      storage: createJSONStorage(() => localStorage),\n      partialize: (state) => ({\n        // Only persist certain parts of the state\n        settings: state.settings,\n        workoutPreferences: state.workoutPreferences,\n        ui: {\n          sidebarCollapsed: state.ui.sidebarCollapsed,\n          // Don't persist notifications and modals\n          notifications: [], // Always initialize as empty array\n        },\n        offline: {\n          pendingSync: state.offline.pendingSync,\n          lastSyncTime: state.offline.lastSyncTime,\n          // Don't persist online status\n        },\n      }),\n    }\n  )\n);\n\n// ============================================================================\n// SELECTORS\n// ============================================================================\n\n// Convenience selectors for commonly used state\nexport const useUser = () => useAppStore((state) => state.user);\nexport const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);\nexport const useSettings = () => useAppStore((state) => state.settings);\nexport const useWorkoutPreferences = () => useAppStore((state) => state.workoutPreferences);\nexport const useUIState = () => useAppStore((state) => state.ui);\nexport const useNotifications = () => useAppStore((state) => state.ui.notifications || []);\nexport const useOfflineState = () => useAppStore((state) => state.offline);\n\n// Computed selectors\nexport const useUnreadNotificationCount = () =>\n  useAppStore((state) => (state.ui.notifications || []).filter(n => !n.read).length);\n\nexport const useHasPendingSync = () => \n  useAppStore((state) => state.offline.pendingSync.length > 0);\n\nexport const useIsOffline = () => \n  useAppStore((state) => !state.offline.isOnline);\n\n// ============================================================================\n// STORE ACTIONS\n// ============================================================================\n\n// Export actions for use outside of components\nexport const appActions = {\n  setUser: (user: User | null) => useAppStore.getState().setUser(user),\n  setAuthenticated: (authenticated: boolean) => useAppStore.getState().setAuthenticated(authenticated),\n  updateSettings: (settings: Partial<AppSettings>) => useAppStore.getState().updateSettings(settings),\n  updateWorkoutPreferences: (preferences: Partial<WorkoutPreferences>) => useAppStore.getState().updateWorkoutPreferences(preferences),\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>) => useAppStore.getState().addNotification(notification),\n  setOnlineStatus: (isOnline: boolean) => useAppStore.getState().setOnlineStatus(isOnline),\n  addPendingSync: (item: Omit<OfflineState['pendingSync'][0], 'id' | 'timestamp'>) => useAppStore.getState().addPendingSync(item),\n  removePendingSync: (id: string) => useAppStore.getState().removePendingSync(id),\n  clearPendingSync: () => useAppStore.getState().clearPendingSync(),\n  updateLastSyncTime: () => useAppStore.getState().updateLastSyncTime(),\n  reset: () => useAppStore.getState().reset(),\n};\n\n// ============================================================================\n// TYPES EXPORT\n// ============================================================================\n\nexport type { AppSettings, WorkoutPreferences, UIState, OfflineState };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAED;AACA;AACA;;;;;AAmGA,+EAA+E;AAC/E,iBAAiB;AACjB,+EAA+E;AAE/E,MAAM,kBAA+B;IACnC,OAAO;IACP,UAAU;IACV,OAAO;IACP,eAAe;QACb,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,WAAW;IACb;IACA,SAAS;QACP,eAAe;QACf,oBAAoB;QACpB,qBAAqB;IACvB;AACF;AAEA,MAAM,4BAAgD;IACpD,iBAAiB;IACjB,qBAAqB;IACrB,oBAAoB,EAAE;IACtB,mBAAmB,EAAE;IACrB,qBAAqB;IACrB,uBAAuB;IACvB,kBAAkB;IAClB,mBAAmB;AACrB;AAEA,MAAM,iBAA0B;IAC9B,kBAAkB;IAClB,sBAAsB;IACtB,aAAa;IACb,aAAa,EAAE;IACf,eAAe,EAAE;IACjB,QAAQ;QACN,iBAAiB;QACjB,cAAc;QACd,oBAAoB;IACtB;AACF;AAEA,MAAM,sBAAoC;IACxC,UAAU;IACV,aAAa,EAAE;IACf,cAAc;AAChB;AAMO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnB,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,UAAU;QACV,oBAAoB;QACpB,IAAI;QACJ,SAAS;QAET,UAAU;QACV,SAAS,CAAC,OAAS,IAAI,CAAC;gBACtB,MAAM,IAAI,GAAG;YACf;QAEA,kBAAkB,CAAC,gBAAkB,IAAI,CAAC;gBACxC,MAAM,eAAe,GAAG;gBACxB,IAAI,CAAC,eAAe;oBAClB,MAAM,IAAI,GAAG;gBACf;YACF;QAEA,gBAAgB,CAAC,cAAgB,IAAI,CAAC;gBACpC,OAAO,MAAM,CAAC,MAAM,QAAQ,EAAE;YAChC;QAEA,0BAA0B,CAAC,iBAAmB,IAAI,CAAC;gBACjD,OAAO,MAAM,CAAC,MAAM,kBAAkB,EAAE;YAC1C;QAEA,eAAe,CAAC,aAAe,IAAI,CAAC;gBAClC,OAAO,MAAM,CAAC,MAAM,EAAE,EAAE;YAC1B;QAEA,iBAAiB,CAAC,eAAiB,IAAI,CAAC;gBACtC,oCAAoC;gBACpC,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE;oBAC3B,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE;gBAC7B;gBAEA,MAAM,kBAAkB;oBACtB,GAAG,YAAY;oBACf,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBAC3E,WAAW,KAAK,GAAG;oBACnB,MAAM;gBACR;gBACA,MAAM,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;gBAE/B,kCAAkC;gBAClC,IAAI,MAAM,EAAE,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI;oBACtC,MAAM,EAAE,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG;gBAC3D;YACF;QAEA,sBAAsB,CAAC,KAAO,IAAI,CAAC;gBACjC,oCAAoC;gBACpC,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE;oBAC3B,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE;oBAC3B;gBACF;gBAEA,MAAM,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC/D,IAAI,cAAc;oBAChB,aAAa,IAAI,GAAG;gBACtB;YACF;QAEA,oBAAoB,IAAM,IAAI,CAAC;gBAC7B,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE;YAC7B;QAEA,iBAAiB,CAAC,WAAa,IAAI,CAAC;gBAClC,MAAM,OAAO,CAAC,QAAQ,GAAG;YAC3B;QAEA,gBAAgB,CAAC,OAAS,IAAI,CAAC;gBAC7B,MAAM,UAAU;oBACd,GAAG,IAAI;oBACP,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACnE,WAAW,KAAK,GAAG;gBACrB;gBACA,MAAM,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;YACjC;QAEA,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,OAAO,CAAC,WAAW,GAAG,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACnF;QAEA,kBAAkB,IAAM,IAAI,CAAC;gBAC3B,MAAM,OAAO,CAAC,WAAW,GAAG,EAAE;YAChC;QAEA,oBAAoB,IAAM,IAAI,CAAC;gBAC7B,MAAM,OAAO,CAAC,YAAY,GAAG,KAAK,GAAG;YACvC;QAEA,OAAO,IAAM,IAAI,IAAM,CAAC;oBACtB,MAAM;oBACN,iBAAiB;oBACjB,UAAU;oBACV,oBAAoB;oBACpB,IAAI;oBACJ,SAAS;gBACX,CAAC;IACH,CAAC,IACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,0CAA0C;YAC1C,UAAU,MAAM,QAAQ;YACxB,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;gBAC3C,yCAAyC;gBACzC,eAAe,EAAE;YACnB;YACA,SAAS;gBACP,aAAa,MAAM,OAAO,CAAC,WAAW;gBACtC,cAAc,MAAM,OAAO,CAAC,YAAY;YAE1C;QACF,CAAC;AACH;AASG,MAAM,UAAU;;IAAM,OAAA;+BAAY,CAAC,QAAU,MAAM,IAAI;;AAAA;GAAjD;;QAAgB;;;AACtB,MAAM,qBAAqB;;IAAM,OAAA;0CAAY,CAAC,QAAU,MAAM,eAAe;;AAAA;IAAvE;;QAA2B;;;AACjC,MAAM,cAAc;;IAAM,OAAA;mCAAY,CAAC,QAAU,MAAM,QAAQ;;AAAA;IAAzD;;QAAoB;;;AAC1B,MAAM,wBAAwB;;IAAM,OAAA;6CAAY,CAAC,QAAU,MAAM,kBAAkB;;AAAA;IAA7E;;QAA8B;;;AACpC,MAAM,aAAa;;IAAM,OAAA;kCAAY,CAAC,QAAU,MAAM,EAAE;;AAAA;IAAlD;;QAAmB;;;AACzB,MAAM,mBAAmB;;IAAM,OAAA;wCAAY,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa,IAAI,EAAE;;AAAA;IAA5E;;QAAyB;;;AAC/B,MAAM,kBAAkB;;IAAM,OAAA;uCAAY,CAAC,QAAU,MAAM,OAAO;;AAAA;IAA5D;;QAAwB;;;AAG9B,MAAM,6BAA6B;;IACxC,OAAA;kDAAY,CAAC,QAAU,CAAC,MAAM,EAAE,CAAC,aAAa,IAAI,EAAE,EAAE,MAAM;0DAAC,CAAA,IAAK,CAAC,EAAE,IAAI;yDAAE,MAAM;;AAAA;IADtE;;QACX;;;AAEK,MAAM,oBAAoB;;IAC/B,OAAA;yCAAY,CAAC,QAAU,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG;;AAAC;IADhD;;QACX;;;AAEK,MAAM,eAAe;;IAC1B,OAAA;oCAAY,CAAC,QAAU,CAAC,MAAM,OAAO,CAAC,QAAQ;;AAAA;IADnC;;QACX;;;AAOK,MAAM,aAAa;IACxB,SAAS,CAAC,OAAsB,YAAY,QAAQ,GAAG,OAAO,CAAC;IAC/D,kBAAkB,CAAC,gBAA2B,YAAY,QAAQ,GAAG,gBAAgB,CAAC;IACtF,gBAAgB,CAAC,WAAmC,YAAY,QAAQ,GAAG,cAAc,CAAC;IAC1F,0BAA0B,CAAC,cAA6C,YAAY,QAAQ,GAAG,wBAAwB,CAAC;IACxH,iBAAiB,CAAC,eAAiF,YAAY,QAAQ,GAAG,eAAe,CAAC;IAC1I,iBAAiB,CAAC,WAAsB,YAAY,QAAQ,GAAG,eAAe,CAAC;IAC/E,gBAAgB,CAAC,OAAmE,YAAY,QAAQ,GAAG,cAAc,CAAC;IAC1H,mBAAmB,CAAC,KAAe,YAAY,QAAQ,GAAG,iBAAiB,CAAC;IAC5E,kBAAkB,IAAM,YAAY,QAAQ,GAAG,gBAAgB;IAC/D,oBAAoB,IAAM,YAAY,QAAQ,GAAG,kBAAkB;IACnE,OAAO,IAAM,YAAY,QAAQ,GAAG,KAAK;AAC3C", "debugId": null}}, {"offset": {"line": 1805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/workouts.ts"], "sourcesContent": ["/**\n * Workout API Service\n * Handles all workout-related API calls\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport { WorkoutCoolAdapter, type WorkoutCoolProgram, type WorkoutCoolWorkoutSession } from '../adapters/workout-cool.adapter';\nimport type {\n  WorkoutSession,\n  Program,\n  CreateWorkoutSessionData,\n  PaginatedResponse\n} from '../types';\n\nexport class WorkoutService {\n  /**\n   * Get user's workout sessions with optional filtering - adapted for workout-cool API\n   */\n  static async getWorkoutSessions(params: {\n    limit?: number;\n    offset?: number;\n    status?: string;\n    programId?: string;\n    startDate?: string;\n    endDate?: string;\n    userId?: string;\n  } = {}): Promise<PaginatedResponse<WorkoutSession>> {\n    const searchParams = new URLSearchParams();\n\n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.status) searchParams.append('status', params.status.toUpperCase());\n    if (params.programId) searchParams.append('programId', params.programId);\n    if (params.startDate) searchParams.append('startDate', params.startDate);\n    if (params.endDate) searchParams.append('endDate', params.endDate);\n\n    const queryString = searchParams.toString();\n    const url = queryString\n      ? `${API_CONFIG.ENDPOINTS.WORKOUTS.LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.WORKOUTS.LIST;\n\n    const response = await apiClient.get<{ sessions: WorkoutCoolWorkoutSession[] }>(url);\n\n    // Adapt workout-cool sessions to our format\n    const adaptedSessions = response.sessions?.map(session =>\n      WorkoutCoolAdapter.adaptWorkoutSession(session)\n    ) || [];\n\n    return {\n      data: adaptedSessions,\n      pagination: {\n        page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,\n        limit: params.limit || 10,\n        total: adaptedSessions.length,\n        totalPages: Math.ceil(adaptedSessions.length / (params.limit || 10)),\n      },\n    };\n  }\n\n  /**\n   * Get workout session by ID - adapted for workout-cool API\n   */\n  static async getWorkoutSession(id: string): Promise<WorkoutSession> {\n    const response = await apiClient.get<WorkoutCoolWorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id));\n    return WorkoutCoolAdapter.adaptWorkoutSession(response);\n  }\n\n  /**\n   * Create a new workout session - adapted for workout-cool API\n   */\n  static async createWorkoutSession(data: CreateWorkoutSessionData): Promise<WorkoutSession> {\n    const workoutCoolData = WorkoutCoolAdapter.toWorkoutCoolFormat.workoutSession(data);\n    const response = await apiClient.post<WorkoutCoolWorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.CREATE, workoutCoolData);\n    return WorkoutCoolAdapter.adaptWorkoutSession(response);\n  }\n\n  /**\n   * Update workout session - adapted for workout-cool API\n   */\n  static async updateWorkoutSession(\n    id: string,\n    data: Partial<CreateWorkoutSessionData>\n  ): Promise<WorkoutSession> {\n    const workoutCoolData = WorkoutCoolAdapter.toWorkoutCoolFormat.workoutSession(data);\n    const response = await apiClient.patch<WorkoutCoolWorkoutSession>(API_CONFIG.ENDPOINTS.WORKOUTS.UPDATE(id), workoutCoolData);\n    return WorkoutCoolAdapter.adaptWorkoutSession(response);\n  }\n\n  /**\n   * Delete workout session - adapted for workout-cool API\n   */\n  static async deleteWorkoutSession(id: string): Promise<void> {\n    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.WORKOUTS.DELETE(id));\n  }\n\n  /**\n   * Start a workout session - adapted for workout-cool API\n   */\n  static async startWorkoutSession(id: string): Promise<WorkoutSession> {\n    const response = await apiClient.post<WorkoutCoolWorkoutSession>(`${API_CONFIG.ENDPOINTS.WORKOUTS.DETAILS(id)}/start`);\n    return WorkoutCoolAdapter.adaptWorkoutSession(response);\n  }\n\n  /**\n   * Complete a workout session - adapted for workout-cool API\n   */\n  static async completeWorkoutSession(\n    id: string,\n    data: { duration: number; notes?: string }\n  ): Promise<WorkoutSession> {\n    const response = await apiClient.post<WorkoutCoolWorkoutSession>(\n      API_CONFIG.ENDPOINTS.WORKOUTS.COMPLETE(id),\n      data\n    );\n    return WorkoutCoolAdapter.adaptWorkoutSession(response);\n  }\n\n  /**\n   * Get workout programs - adapted for workout-cool public API\n   */\n  static async getWorkoutPrograms(params: {\n    limit?: number;\n    offset?: number;\n    category?: string;\n    difficulty?: string;\n    duration?: string;\n  } = {}): Promise<PaginatedResponse<Program>> {\n    const searchParams = new URLSearchParams();\n\n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.category) searchParams.append('category', params.category);\n    if (params.difficulty) searchParams.append('level', params.difficulty.toUpperCase());\n    if (params.duration) searchParams.append('duration', params.duration);\n\n    const queryString = searchParams.toString();\n    const url = queryString\n      ? `${API_CONFIG.ENDPOINTS.PROGRAMS.PUBLIC_LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.PROGRAMS.PUBLIC_LIST;\n\n    const response = await apiClient.get<WorkoutCoolProgram[]>(url);\n\n    // Adapt workout-cool programs to our format\n    const adaptedPrograms = response.map(program =>\n      WorkoutCoolAdapter.adaptProgram(program)\n    );\n\n    return {\n      data: adaptedPrograms,\n      pagination: {\n        page: Math.floor((params.offset || 0) / (params.limit || 10)) + 1,\n        limit: params.limit || 10,\n        total: adaptedPrograms.length,\n        totalPages: Math.ceil(adaptedPrograms.length / (params.limit || 10)),\n      },\n    };\n  }\n\n  /**\n   * Get workout program by ID\n   */\n  static async getWorkoutProgram(id: string): Promise<Program> {\n    return apiClient.get<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id));\n  }\n\n  /**\n   * Create a new workout program\n   */\n  static async createWorkoutProgram(data: any): Promise<Program> {\n    return apiClient.post<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.CREATE, data);\n  }\n\n  /**\n   * Update workout program\n   */\n  static async updateWorkoutProgram(\n    id: string,\n    data: any\n  ): Promise<Program> {\n    return apiClient.patch<Program>(API_CONFIG.ENDPOINTS.PROGRAMS.UPDATE(id), data);\n  }\n\n  /**\n   * Delete workout program\n   */\n  static async deleteWorkoutProgram(id: string): Promise<void> {\n    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRAMS.DELETE(id));\n  }\n\n  /**\n   * Join a workout program\n   */\n  static async joinWorkoutProgram(id: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(`${API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id)}/join`);\n  }\n\n  /**\n   * Leave a workout program\n   */\n  static async leaveWorkoutProgram(id: string): Promise<{ message: string }> {\n    return apiClient.post<{ message: string }>(`${API_CONFIG.ENDPOINTS.PROGRAMS.DETAILS(id)}/leave`);\n  }\n\n  /**\n   * Get user's joined programs\n   */\n  static async getUserPrograms(): Promise<Program[]> {\n    const response = await apiClient.get<{ programs: Program[] }>('/api/user/programs');\n    return response.programs || [];\n  }\n\n  /**\n   * Get popular workout programs\n   */\n  static async getPopularPrograms(limit = 10): Promise<Program[]> {\n    const searchParams = new URLSearchParams({\n      sort: 'popular',\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;\n    const response = await apiClient.get<PaginatedResponse<Program>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Get recommended workout programs for user\n   */\n  static async getRecommendedPrograms(limit = 6): Promise<Program[]> {\n    const searchParams = new URLSearchParams({\n      recommended: 'true',\n      limit: limit.toString(),\n    });\n\n    const url = `${API_CONFIG.ENDPOINTS.PROGRAMS.LIST}?${searchParams.toString()}`;\n    const response = await apiClient.get<PaginatedResponse<Program>>(url);\n    return response.data || [];\n  }\n\n  /**\n   * Generate AI workout plan\n   */\n  static async generateAIWorkout(preferences: {\n    goals: string[];\n    equipment: string[];\n    duration: number;\n    difficulty: string;\n    muscleGroups: string[];\n  }): Promise<WorkoutSession> {\n    return apiClient.post<WorkoutSession>('/api/workouts/generate', preferences);\n  }\n\n  /**\n   * Get workout statistics\n   */\n  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    totalWorkouts: number;\n    totalDuration: number;\n    averageDuration: number;\n    caloriesBurned: number;\n    streakDays: number;\n    completionRate: number;\n    favoriteExercises: Array<{ name: string; count: number }>;\n    weeklyProgress: Array<{ date: string; workouts: number; duration: number }>;\n  }> {\n    return apiClient.get(`/api/workouts/stats?period=${period}`);\n  }\n\n  /**\n   * Get workout history\n   */\n  static async getWorkoutHistory(params: {\n    limit?: number;\n    offset?: number;\n    startDate?: string;\n    endDate?: string;\n  } = {}): Promise<PaginatedResponse<WorkoutSession>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.startDate) searchParams.append('startDate', params.startDate);\n    if (params.endDate) searchParams.append('endDate', params.endDate);\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `/api/workouts/history?${queryString}`\n      : '/api/workouts/history';\n\n    return apiClient.get<PaginatedResponse<WorkoutSession>>(url);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;;;;AAQO,MAAM;IACX;;GAEC,GACD,aAAa,mBAAmB,SAQ5B,CAAC,CAAC,EAA8C;QAClD,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,WAAW;QAC1E,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAEjE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,GACtD,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI;QAEtC,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA4C;QAEhF,4CAA4C;QAC5C,MAAM,kBAAkB,SAAS,QAAQ,EAAE,IAAI,CAAA,UAC7C,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC,aACpC,EAAE;QAEP,OAAO;YACL,MAAM;YACN,YAAY;gBACV,MAAM,KAAK,KAAK,CAAC,CAAC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,KAAK;gBAChE,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,gBAAgB,MAAM;gBAC7B,YAAY,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG,CAAC,OAAO,KAAK,IAAI,EAAE;YACpE;QACF;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,EAAU,EAA2B;QAClE,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA4B,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;QACtG,OAAO,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;IAChD;IAEA;;GAEC,GACD,aAAa,qBAAqB,IAA8B,EAA2B;QACzF,MAAM,kBAAkB,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC,cAAc,CAAC;QAC9E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,IAAI,CAA4B,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;QACvG,OAAO,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;IAChD;IAEA;;GAEC,GACD,aAAa,qBACX,EAAU,EACV,IAAuC,EACd;QACzB,MAAM,kBAAkB,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC,cAAc,CAAC;QAC9E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,KAAK,CAA4B,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK;QAC5G,OAAO,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;IAChD;IAEA;;GAEC,GACD,aAAa,qBAAqB,EAAU,EAAiB;QAC3D,OAAO,8HAAA,CAAA,YAAS,CAAC,MAAM,CAAO,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,aAAa,oBAAoB,EAAU,EAA2B;QACpE,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,IAAI,CAA4B,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC;QACrH,OAAO,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;IAChD;IAEA;;GAEC,GACD,aAAa,uBACX,EAAU,EACV,IAA0C,EACjB;QACzB,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,IAAI,CACnC,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,KACvC;QAEF,OAAO,8JAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;IAChD;IAEA;;GAEC,GACD,aAAa,mBAAmB,SAM5B,CAAC,CAAC,EAAuC;QAC3C,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,QAAQ,EAAE,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACpE,IAAI,OAAO,UAAU,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,UAAU,CAAC,WAAW;QACjF,IAAI,OAAO,QAAQ,EAAE,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QAEpE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,aAAa,GAC7D,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW;QAE7C,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAuB;QAE3D,4CAA4C;QAC5C,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA,UACnC,8JAAA,CAAA,qBAAkB,CAAC,YAAY,CAAC;QAGlC,OAAO;YACL,MAAM;YACN,YAAY;gBACV,MAAM,KAAK,KAAK,CAAC,CAAC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,KAAK;gBAChE,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,gBAAgB,MAAM;gBAC7B,YAAY,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG,CAAC,OAAO,KAAK,IAAI,EAAE;YACpE;QACF;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,EAAU,EAAoB;QAC3D,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAU,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;IACtE;IAEA;;GAEC,GACD,aAAa,qBAAqB,IAAS,EAAoB;QAC7D,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAU,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;IACvE;IAEA;;GAEC,GACD,aAAa,qBACX,EAAU,EACV,IAAS,EACS;QAClB,OAAO,8HAAA,CAAA,YAAS,CAAC,KAAK,CAAU,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK;IAC5E;IAEA;;GAEC,GACD,aAAa,qBAAqB,EAAU,EAAiB;QAC3D,OAAO,8HAAA,CAAA,YAAS,CAAC,MAAM,CAAO,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,aAAa,mBAAmB,EAAU,EAAgC;QACxE,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;IAChG;IAEA;;GAEC,GACD,aAAa,oBAAoB,EAAU,EAAgC;QACzE,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAsB,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC;IACjG;IAEA;;GAEC,GACD,aAAa,kBAAsC;QACjD,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA0B;QAC9D,OAAO,SAAS,QAAQ,IAAI,EAAE;IAChC;IAEA;;GAEC,GACD,aAAa,mBAAmB,QAAQ,EAAE,EAAsB;QAC9D,MAAM,eAAe,IAAI,gBAAgB;YACvC,MAAM;YACN,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAC9E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA6B;QACjE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,uBAAuB,QAAQ,CAAC,EAAsB;QACjE,MAAM,eAAe,IAAI,gBAAgB;YACvC,aAAa;YACb,OAAO,MAAM,QAAQ;QACvB;QAEA,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;QAC9E,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,GAAG,CAA6B;QACjE,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA;;GAEC,GACD,aAAa,kBAAkB,WAM9B,EAA2B;QAC1B,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAiB,0BAA0B;IAClE;IAEA;;GAEC,GACD,aAAa,gBAAgB,SAAoC,OAAO,EASrE;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ;IAC7D;IAEA;;GAEC,GACD,aAAa,kBAAkB,SAK3B,CAAC,CAAC,EAA8C;QAClD,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAEjE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,CAAC,sBAAsB,EAAE,aAAa,GACtC;QAEJ,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAoC;IAC1D;AACF", "debugId": null}}, {"offset": {"line": 1994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/services/progress.ts"], "sourcesContent": ["/**\n * Progress Tracking API Service\n * Handles all progress and analytics related API calls\n */\n\nimport { apiClient } from '../client';\nimport { API_CONFIG } from '../config';\nimport type { \n  ProgressRecord, \n  ProgressStats,\n  CreateProgressRecordData,\n  UpdateProgressRecordData,\n  PaginatedResponse \n} from '../types';\n\nexport class ProgressService {\n  /**\n   * Get user's progress records with optional filtering\n   */\n  static async getProgressRecords(params: {\n    limit?: number;\n    offset?: number;\n    type?: string;\n    startDate?: string;\n    endDate?: string;\n    exerciseId?: string;\n    workoutId?: string;\n  } = {}): Promise<PaginatedResponse<ProgressRecord>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params.limit) searchParams.append('limit', params.limit.toString());\n    if (params.offset) searchParams.append('offset', params.offset.toString());\n    if (params.type) searchParams.append('type', params.type);\n    if (params.startDate) searchParams.append('startDate', params.startDate);\n    if (params.endDate) searchParams.append('endDate', params.endDate);\n    if (params.exerciseId) searchParams.append('exerciseId', params.exerciseId);\n    if (params.workoutId) searchParams.append('workoutId', params.workoutId);\n\n    const queryString = searchParams.toString();\n    const url = queryString \n      ? `${API_CONFIG.ENDPOINTS.PROGRESS.LIST}?${queryString}`\n      : API_CONFIG.ENDPOINTS.PROGRESS.LIST;\n\n    return apiClient.get<PaginatedResponse<ProgressRecord>>(url);\n  }\n\n  /**\n   * Get progress record by ID\n   */\n  static async getProgressRecord(id: string): Promise<ProgressRecord> {\n    return apiClient.get<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.DETAILS(id));\n  }\n\n  /**\n   * Create a new progress record\n   */\n  static async createProgressRecord(data: CreateProgressRecordData): Promise<ProgressRecord> {\n    return apiClient.post<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.CREATE, data);\n  }\n\n  /**\n   * Update progress record\n   */\n  static async updateProgressRecord(\n    id: string, \n    data: UpdateProgressRecordData\n  ): Promise<ProgressRecord> {\n    return apiClient.patch<ProgressRecord>(API_CONFIG.ENDPOINTS.PROGRESS.UPDATE(id), data);\n  }\n\n  /**\n   * Delete progress record\n   */\n  static async deleteProgressRecord(id: string): Promise<void> {\n    return apiClient.delete<void>(API_CONFIG.ENDPOINTS.PROGRESS.DELETE(id));\n  }\n\n  /**\n   * Get comprehensive progress statistics\n   */\n  static async getProgressStats(period: 'week' | 'month' | 'year' | 'all' = 'month'): Promise<ProgressStats> {\n    return apiClient.get<ProgressStats>(`${API_CONFIG.ENDPOINTS.PROGRESS.STATS}?period=${period}`);\n  }\n\n  /**\n   * Get workout completion statistics\n   */\n  static async getWorkoutStats(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    totalWorkouts: number;\n    totalDuration: number;\n    averageDuration: number;\n    caloriesBurned: number;\n    streakDays: number;\n    completionRate: number;\n    favoriteExercises: Array<{ name: string; count: number }>;\n    weeklyProgress: Array<{ date: string; workouts: number; duration: number; calories: number }>;\n    monthlyProgress: Array<{ month: string; workouts: number; duration: number; calories: number }>;\n  }> {\n    return apiClient.get(`/api/progress/workout-stats?period=${period}`);\n  }\n\n  /**\n   * Get exercise performance data\n   */\n  static async getExerciseProgress(exerciseId: string, period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    exerciseId: string;\n    exerciseName: string;\n    totalSessions: number;\n    bestPerformance: {\n      weight?: number;\n      reps?: number;\n      duration?: number;\n      distance?: number;\n      date: string;\n    };\n    averagePerformance: {\n      weight?: number;\n      reps?: number;\n      duration?: number;\n      distance?: number;\n    };\n    progressData: Array<{\n      date: string;\n      weight?: number;\n      reps?: number;\n      duration?: number;\n      distance?: number;\n      volume?: number;\n    }>;\n    improvements: {\n      weightIncrease?: number;\n      repsIncrease?: number;\n      durationIncrease?: number;\n      distanceIncrease?: number;\n    };\n  }> {\n    return apiClient.get(`/api/progress/exercise/${exerciseId}?period=${period}`);\n  }\n\n  /**\n   * Get body measurements progress\n   */\n  static async getBodyMeasurements(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    measurements: Array<{\n      date: string;\n      weight?: number;\n      bodyFat?: number;\n      muscleMass?: number;\n      chest?: number;\n      waist?: number;\n      hips?: number;\n      arms?: number;\n      thighs?: number;\n    }>;\n    trends: {\n      weight?: { change: number; percentage: number };\n      bodyFat?: { change: number; percentage: number };\n      muscleMass?: { change: number; percentage: number };\n    };\n  }> {\n    return apiClient.get(`/api/progress/body-measurements?period=${period}`);\n  }\n\n  /**\n   * Add body measurement record\n   */\n  static async addBodyMeasurement(data: {\n    date: string;\n    weight?: number;\n    bodyFat?: number;\n    muscleMass?: number;\n    chest?: number;\n    waist?: number;\n    hips?: number;\n    arms?: number;\n    thighs?: number;\n    notes?: string;\n  }): Promise<{ message: string }> {\n    return apiClient.post('/api/progress/body-measurements', data);\n  }\n\n  /**\n   * Get fitness goals and progress\n   */\n  static async getFitnessGoals(): Promise<{\n    goals: Array<{\n      id: string;\n      title: string;\n      description: string;\n      targetValue: number;\n      currentValue: number;\n      unit: string;\n      deadline: string;\n      category: string;\n      progress: number;\n      status: 'active' | 'completed' | 'paused';\n    }>;\n  }> {\n    return apiClient.get('/api/progress/goals');\n  }\n\n  /**\n   * Create a new fitness goal\n   */\n  static async createFitnessGoal(data: {\n    title: string;\n    description: string;\n    targetValue: number;\n    unit: string;\n    deadline: string;\n    category: string;\n  }): Promise<{ message: string; goalId: string }> {\n    return apiClient.post('/api/progress/goals', data);\n  }\n\n  /**\n   * Update fitness goal progress\n   */\n  static async updateGoalProgress(goalId: string, currentValue: number): Promise<{ message: string }> {\n    return apiClient.patch(`/api/progress/goals/${goalId}`, { currentValue });\n  }\n\n  /**\n   * Get achievement badges and milestones\n   */\n  static async getAchievements(): Promise<{\n    badges: Array<{\n      id: string;\n      name: string;\n      description: string;\n      icon: string;\n      category: string;\n      earnedDate?: string;\n      progress?: number;\n      requirement: number;\n    }>;\n    milestones: Array<{\n      id: string;\n      title: string;\n      description: string;\n      achievedDate: string;\n      category: string;\n    }>;\n  }> {\n    return apiClient.get('/api/progress/achievements');\n  }\n\n  /**\n   * Get workout calendar data\n   */\n  static async getWorkoutCalendar(year: number, month: number): Promise<{\n    calendar: Array<{\n      date: string;\n      workouts: number;\n      duration: number;\n      calories: number;\n      hasWorkout: boolean;\n    }>;\n    monthStats: {\n      totalWorkouts: number;\n      totalDuration: number;\n      totalCalories: number;\n      activeDays: number;\n    };\n  }> {\n    return apiClient.get(`/api/progress/calendar?year=${year}&month=${month}`);\n  }\n\n  /**\n   * Get personal records (PRs)\n   */\n  static async getPersonalRecords(): Promise<{\n    records: Array<{\n      exerciseId: string;\n      exerciseName: string;\n      recordType: 'weight' | 'reps' | 'duration' | 'distance';\n      value: number;\n      unit: string;\n      achievedDate: string;\n      workoutId?: string;\n    }>;\n    recentPRs: Array<{\n      exerciseId: string;\n      exerciseName: string;\n      recordType: string;\n      value: number;\n      unit: string;\n      achievedDate: string;\n      improvement: number;\n    }>;\n  }> {\n    return apiClient.get('/api/progress/personal-records');\n  }\n\n  /**\n   * Get strength progression data\n   */\n  static async getStrengthProgression(exerciseIds?: string[]): Promise<{\n    exercises: Array<{\n      exerciseId: string;\n      exerciseName: string;\n      progressData: Array<{\n        date: string;\n        maxWeight: number;\n        totalVolume: number;\n        oneRepMax: number;\n      }>;\n      trends: {\n        weightTrend: number;\n        volumeTrend: number;\n        oneRepMaxTrend: number;\n      };\n    }>;\n  }> {\n    const params = exerciseIds ? `?exercises=${exerciseIds.join(',')}` : '';\n    return apiClient.get(`/api/progress/strength-progression${params}`);\n  }\n\n  /**\n   * Export progress data\n   */\n  static async exportProgressData(format: 'csv' | 'json' = 'csv', period: 'month' | 'year' | 'all' = 'all'): Promise<Blob> {\n    const response = await fetch(`${API_CONFIG.BASE_URL}/api/progress/export?format=${format}&period=${period}`, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error('Failed to export data');\n    }\n\n    return response.blob();\n  }\n\n  /**\n   * Get workout intensity analysis\n   */\n  static async getWorkoutIntensity(period: 'week' | 'month' | 'year' = 'month'): Promise<{\n    averageIntensity: number;\n    intensityDistribution: Array<{\n      level: 'low' | 'moderate' | 'high' | 'very_high';\n      count: number;\n      percentage: number;\n    }>;\n    weeklyIntensity: Array<{\n      week: string;\n      averageIntensity: number;\n      workoutCount: number;\n    }>;\n  }> {\n    return apiClient.get(`/api/progress/workout-intensity?period=${period}`);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AASO,MAAM;IACX;;GAEC,GACD,aAAa,mBAAmB,SAQ5B,CAAC,CAAC,EAA8C;QAClD,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACvE,IAAI,OAAO,IAAI,EAAE,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI;QACxD,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QACjE,IAAI,OAAO,UAAU,EAAE,aAAa,MAAM,CAAC,cAAc,OAAO,UAAU;QAC1E,IAAI,OAAO,SAAS,EAAE,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,cACR,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,GACtD,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI;QAEtC,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAoC;IAC1D;IAEA;;GAEC,GACD,aAAa,kBAAkB,EAAU,EAA2B;QAClE,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAiB,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC7E;IAEA;;GAEC,GACD,aAAa,qBAAqB,IAA8B,EAA2B;QACzF,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAiB,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;IAC9E;IAEA;;GAEC,GACD,aAAa,qBACX,EAAU,EACV,IAA8B,EACL;QACzB,OAAO,8HAAA,CAAA,YAAS,CAAC,KAAK,CAAiB,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK;IACnF;IAEA;;GAEC,GACD,aAAa,qBAAqB,EAAU,EAAiB;QAC3D,OAAO,8HAAA,CAAA,YAAS,CAAC,MAAM,CAAO,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrE;IAEA;;GAEC,GACD,aAAa,iBAAiB,SAA4C,OAAO,EAA0B;QACzG,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAgB,GAAG,8HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ;IAC/F;IAEA;;GAEC,GACD,aAAa,gBAAgB,SAAoC,OAAO,EAUrE;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ;IACrE;IAEA;;GAEC,GACD,aAAa,oBAAoB,UAAkB,EAAE,SAAoC,OAAO,EA+B7F;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,QAAQ,EAAE,QAAQ;IAC9E;IAEA;;GAEC,GACD,aAAa,oBAAoB,SAAoC,OAAO,EAiBzE;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,QAAQ;IACzE;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAW/B,EAAgC;QAC/B,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,mCAAmC;IAC3D;IAEA;;GAEC,GACD,aAAa,kBAaV;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;IACvB;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAO9B,EAAgD;QAC/C,OAAO,8HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;IAC/C;IAEA;;GAEC,GACD,aAAa,mBAAmB,MAAc,EAAE,YAAoB,EAAgC;QAClG,OAAO,8HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAAE;QAAa;IACzE;IAEA;;GAEC,GACD,aAAa,kBAkBV;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;IACvB;IAEA;;GAEC,GACD,aAAa,mBAAmB,IAAY,EAAE,KAAa,EAcxD;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC3E;IAEA;;GAEC,GACD,aAAa,qBAmBV;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;IACvB;IAEA;;GAEC,GACD,aAAa,uBAAuB,WAAsB,EAgBvD;QACD,MAAM,SAAS,cAAc,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,MAAM,GAAG;QACrE,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,kCAAkC,EAAE,QAAQ;IACpE;IAEA;;GAEC,GACD,aAAa,mBAAmB,SAAyB,KAAK,EAAE,SAAmC,KAAK,EAAiB;QACvH,MAAM,WAAW,MAAM,MAAM,GAAG,8HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,QAAQ,EAAE,QAAQ,EAAE;YAC3G,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;YACjE;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,oBAAoB,SAAoC,OAAO,EAYzE;QACD,OAAO,8HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,QAAQ;IACzE;AACF", "debugId": null}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/query/config.ts"], "sourcesContent": ["/**\n * React Query Configuration and Cache Management\n * Centralized configuration for data fetching, caching, and synchronization\n */\n\nimport { QueryClient, DefaultOptions } from '@tanstack/react-query';\nimport { persistQueryClient } from '@tanstack/react-query-persist-client';\nimport { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';\nimport { appActions } from '../store/app-store';\n\n// ============================================================================\n// QUERY CLIENT CONFIGURATION\n// ============================================================================\n\nconst queryConfig: DefaultOptions = {\n  queries: {\n    // Global defaults for all queries\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n    retry: (failureCount, error: any) => {\n      // Don't retry on 4xx errors (client errors)\n      if (error?.status >= 400 && error?.status < 500) {\n        return false;\n      }\n      // Retry up to 3 times for other errors\n      return failureCount < 3;\n    },\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n    refetchOnWindowFocus: false,\n    refetchOnReconnect: true,\n    refetchOnMount: true,\n  },\n  mutations: {\n    // Global defaults for all mutations\n    retry: (failureCount, error: any) => {\n      // Don't retry mutations on client errors\n      if (error?.status >= 400 && error?.status < 500) {\n        return false;\n      }\n      // Retry once for server errors\n      return failureCount < 1;\n    },\n    onError: (error: any) => {\n      // Global error handling for mutations\n      console.error('Mutation error:', error);\n      \n      // Add error notification\n      appActions.addNotification({\n        type: 'error',\n        title: 'Operation Failed',\n        message: error?.message || 'An unexpected error occurred',\n      });\n    },\n  },\n};\n\n// Create the query client\nexport const queryClient = new QueryClient({\n  defaultOptions: queryConfig,\n});\n\n// ============================================================================\n// PERSISTENCE CONFIGURATION\n// ============================================================================\n\nconst persister = createSyncStoragePersister({\n  storage: typeof window !== 'undefined' ? window.localStorage : undefined,\n  key: 'ai-fitness-query-cache',\n  serialize: JSON.stringify,\n  deserialize: JSON.parse,\n});\n\n// Persist query client (only in browser)\nif (typeof window !== 'undefined') {\n  persistQueryClient({\n    queryClient,\n    persister,\n    maxAge: 24 * 60 * 60 * 1000, // 24 hours\n    buster: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n  });\n}\n\n// ============================================================================\n// CACHE MANAGEMENT UTILITIES\n// ============================================================================\n\nexport const cacheUtils = {\n  /**\n   * Invalidate all queries for a specific entity type\n   */\n  invalidateEntity: (entityType: string) => {\n    queryClient.invalidateQueries({ queryKey: [entityType] });\n  },\n\n  /**\n   * Remove all cached data for a specific entity\n   */\n  removeEntity: (entityType: string, id?: string) => {\n    if (id) {\n      queryClient.removeQueries({ queryKey: [entityType, id] });\n    } else {\n      queryClient.removeQueries({ queryKey: [entityType] });\n    }\n  },\n\n  /**\n   * Prefetch data for better UX\n   */\n  prefetch: async (queryKey: any[], queryFn: () => Promise<any>) => {\n    await queryClient.prefetchQuery({\n      queryKey,\n      queryFn,\n      staleTime: 10 * 60 * 1000, // 10 minutes\n    });\n  },\n\n  /**\n   * Set query data manually (for optimistic updates)\n   */\n  setQueryData: <T>(queryKey: any[], data: T) => {\n    queryClient.setQueryData(queryKey, data);\n  },\n\n  /**\n   * Get cached query data\n   */\n  getQueryData: <T>(queryKey: any[]): T | undefined => {\n    return queryClient.getQueryData(queryKey);\n  },\n\n  /**\n   * Clear all cached data\n   */\n  clearAll: () => {\n    queryClient.clear();\n  },\n\n  /**\n   * Reset queries to refetch fresh data\n   */\n  resetQueries: (queryKey?: any[]) => {\n    if (queryKey) {\n      queryClient.resetQueries({ queryKey });\n    } else {\n      queryClient.resetQueries();\n    }\n  },\n\n  /**\n   * Cancel ongoing queries\n   */\n  cancelQueries: (queryKey?: any[]) => {\n    if (queryKey) {\n      queryClient.cancelQueries({ queryKey });\n    } else {\n      queryClient.cancelQueries();\n    }\n  },\n};\n\n// ============================================================================\n// QUERY KEY FACTORIES\n// ============================================================================\n\n/**\n * Centralized query key management for consistency\n */\nexport const queryKeys = {\n  // Auth\n  auth: {\n    all: ['auth'] as const,\n    session: () => [...queryKeys.auth.all, 'session'] as const,\n    user: () => [...queryKeys.auth.all, 'user'] as const,\n  },\n\n  // Exercises\n  exercises: {\n    all: ['exercises'] as const,\n    lists: () => [...queryKeys.exercises.all, 'list'] as const,\n    list: (filters: any) => [...queryKeys.exercises.lists(), filters] as const,\n    details: () => [...queryKeys.exercises.all, 'detail'] as const,\n    detail: (id: string) => [...queryKeys.exercises.details(), id] as const,\n    search: (query: string) => [...queryKeys.exercises.all, 'search', query] as const,\n    attributes: () => [...queryKeys.exercises.all, 'attributes'] as const,\n  },\n\n  // Workouts\n  workouts: {\n    all: ['workouts'] as const,\n    sessions: () => [...queryKeys.workouts.all, 'sessions'] as const,\n    session: (id: string) => [...queryKeys.workouts.sessions(), id] as const,\n    programs: () => [...queryKeys.workouts.all, 'programs'] as const,\n    program: (id: string) => [...queryKeys.workouts.programs(), id] as const,\n    history: (filters: any) => [...queryKeys.workouts.all, 'history', filters] as const,\n    stats: (period: string) => [...queryKeys.workouts.all, 'stats', period] as const,\n  },\n\n  // Progress\n  progress: {\n    all: ['progress'] as const,\n    records: () => [...queryKeys.progress.all, 'records'] as const,\n    record: (id: string) => [...queryKeys.progress.records(), id] as const,\n    stats: (period: string) => [...queryKeys.progress.all, 'stats', period] as const,\n    goals: () => [...queryKeys.progress.all, 'goals'] as const,\n    achievements: () => [...queryKeys.progress.all, 'achievements'] as const,\n    calendar: (year: number, month: number) => [...queryKeys.progress.all, 'calendar', year, month] as const,\n  },\n\n  // User\n  user: {\n    all: ['user'] as const,\n    profile: () => [...queryKeys.user.all, 'profile'] as const,\n    preferences: () => [...queryKeys.user.all, 'preferences'] as const,\n    subscription: () => [...queryKeys.user.all, 'subscription'] as const,\n  },\n};\n\n// ============================================================================\n// OFFLINE SUPPORT\n// ============================================================================\n\nexport const offlineUtils = {\n  /**\n   * Check if we're online\n   */\n  isOnline: () => {\n    return typeof navigator !== 'undefined' ? navigator.onLine : true;\n  },\n\n  /**\n   * Setup online/offline event listeners\n   */\n  setupNetworkListeners: () => {\n    if (typeof window === 'undefined') return;\n\n    const handleOnline = () => {\n      appActions.setOnlineStatus(true);\n      // Refetch all queries when coming back online\n      queryClient.refetchQueries();\n    };\n\n    const handleOffline = () => {\n      appActions.setOnlineStatus(false);\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    // Set initial status\n    appActions.setOnlineStatus(navigator.onLine);\n\n    // Return cleanup function\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  },\n\n  /**\n   * Queue mutation for offline sync\n   */\n  queueOfflineMutation: (type: string, action: string, data: any) => {\n    appActions.addPendingSync({\n      type: type as any,\n      action: action as any,\n      data,\n    });\n  },\n};\n\n// ============================================================================\n// QUERY CLIENT EVENTS\n// ============================================================================\n\n// Setup global query client event listeners\nqueryClient.getQueryCache().subscribe((event) => {\n  // Log query events in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('Query event:', event);\n  }\n\n  // Handle specific events\n  switch (event.type) {\n    case 'added':\n      // Query was added to cache\n      break;\n    case 'removed':\n      // Query was removed from cache\n      break;\n    case 'updated':\n      // Query data was updated\n      break;\n  }\n});\n\nqueryClient.getMutationCache().subscribe((event) => {\n  // Log mutation events in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('Mutation event:', event);\n  }\n\n  // Handle mutation success/error globally\n  if (event.type === 'updated') {\n    const mutation = event.mutation;\n    \n    if (mutation.state.status === 'success') {\n      // Global success handling\n      appActions.addNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Operation completed successfully',\n      });\n    }\n  }\n});\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport { queryClient as default };\nexport type { QueryClient };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AA2EW;AAzEZ;AACA;AACA;AACA;;;;;AAEA,+EAA+E;AAC/E,6BAA6B;AAC7B,+EAA+E;AAE/E,MAAM,cAA8B;IAClC,SAAS;QACP,kCAAkC;QAClC,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO,CAAC,cAAc;YACpB,4CAA4C;YAC5C,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;gBAC/C,OAAO;YACT;YACA,uCAAuC;YACvC,OAAO,eAAe;QACxB;QACA,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QACjE,sBAAsB;QACtB,oBAAoB;QACpB,gBAAgB;IAClB;IACA,WAAW;QACT,oCAAoC;QACpC,OAAO,CAAC,cAAc;YACpB,yCAAyC;YACzC,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;gBAC/C,OAAO;YACT;YACA,+BAA+B;YAC/B,OAAO,eAAe;QACxB;QACA,SAAS,CAAC;YACR,sCAAsC;YACtC,QAAQ,KAAK,CAAC,mBAAmB;YAEjC,yBAAyB;YACzB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS,OAAO,WAAW;YAC7B;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IACzC,gBAAgB;AAClB;AAEA,+EAA+E;AAC/E,4BAA4B;AAC5B,+EAA+E;AAE/E,MAAM,YAAY,CAAA,GAAA,kMAAA,CAAA,6BAA0B,AAAD,EAAE;IAC3C,SAAS,uCAAgC,OAAO,YAAY;IAC5D,KAAK;IACL,WAAW,KAAK,SAAS;IACzB,aAAa,KAAK,KAAK;AACzB;AAEA,yCAAyC;AACzC,wCAAmC;IACjC,CAAA,GAAA,iMAAA,CAAA,qBAAkB,AAAD,EAAE;QACjB;QACA;QACA,QAAQ,KAAK,KAAK,KAAK;QACvB,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI;IACjD;AACF;AAMO,MAAM,aAAa;IACxB;;GAEC,GACD,kBAAkB,CAAC;QACjB,YAAY,iBAAiB,CAAC;YAAE,UAAU;gBAAC;aAAW;QAAC;IACzD;IAEA;;GAEC,GACD,cAAc,CAAC,YAAoB;QACjC,IAAI,IAAI;YACN,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;QACzD,OAAO;YACL,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACrD;IACF;IAEA;;GAEC,GACD,UAAU,OAAO,UAAiB;QAChC,MAAM,YAAY,aAAa,CAAC;YAC9B;YACA;YACA,WAAW,KAAK,KAAK;QACvB;IACF;IAEA;;GAEC,GACD,cAAc,CAAI,UAAiB;QACjC,YAAY,YAAY,CAAC,UAAU;IACrC;IAEA;;GAEC,GACD,cAAc,CAAI;QAChB,OAAO,YAAY,YAAY,CAAC;IAClC;IAEA;;GAEC,GACD,UAAU;QACR,YAAY,KAAK;IACnB;IAEA;;GAEC,GACD,cAAc,CAAC;QACb,IAAI,UAAU;YACZ,YAAY,YAAY,CAAC;gBAAE;YAAS;QACtC,OAAO;YACL,YAAY,YAAY;QAC1B;IACF;IAEA;;GAEC,GACD,eAAe,CAAC;QACd,IAAI,UAAU;YACZ,YAAY,aAAa,CAAC;gBAAE;YAAS;QACvC,OAAO;YACL,YAAY,aAAa;QAC3B;IACF;AACF;AASO,MAAM,YAAY;IACvB,OAAO;IACP,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,SAAS,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAU;QACjD,MAAM,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAO;IAC7C;IAEA,YAAY;IACZ,WAAW;QACT,KAAK;YAAC;SAAY;QAClB,OAAO,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAO;QACjD,MAAM,CAAC,UAAiB;mBAAI,UAAU,SAAS,CAAC,KAAK;gBAAI;aAAQ;QACjE,SAAS,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAS;QACrD,QAAQ,CAAC,KAAe;mBAAI,UAAU,SAAS,CAAC,OAAO;gBAAI;aAAG;QAC9D,QAAQ,CAAC,QAAkB;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;gBAAU;aAAM;QACxE,YAAY,IAAM;mBAAI,UAAU,SAAS,CAAC,GAAG;gBAAE;aAAa;IAC9D;IAEA,WAAW;IACX,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,SAAS,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,QAAQ;gBAAI;aAAG;QAC/D,UAAU,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAW;QACvD,SAAS,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,QAAQ;gBAAI;aAAG;QAC/D,SAAS,CAAC,UAAiB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAW;aAAQ;QAC1E,OAAO,CAAC,SAAmB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAS;aAAO;IACzE;IAEA,WAAW;IACX,UAAU;QACR,KAAK;YAAC;SAAW;QACjB,SAAS,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAU;QACrD,QAAQ,CAAC,KAAe;mBAAI,UAAU,QAAQ,CAAC,OAAO;gBAAI;aAAG;QAC7D,OAAO,CAAC,SAAmB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAS;aAAO;QACvE,OAAO,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAQ;QACjD,cAAc,IAAM;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;aAAe;QAC/D,UAAU,CAAC,MAAc,QAAkB;mBAAI,UAAU,QAAQ,CAAC,GAAG;gBAAE;gBAAY;gBAAM;aAAM;IACjG;IAEA,OAAO;IACP,MAAM;QACJ,KAAK;YAAC;SAAO;QACb,SAAS,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAU;QACjD,aAAa,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAc;QACzD,cAAc,IAAM;mBAAI,UAAU,IAAI,CAAC,GAAG;gBAAE;aAAe;IAC7D;AACF;AAMO,MAAM,eAAe;IAC1B;;GAEC,GACD,UAAU;QACR,OAAO,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;IAC/D;IAEA;;GAEC,GACD,uBAAuB;QACrB,uCAAmC;;QAAM;QAEzC,MAAM,eAAe;YACnB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;YAC3B,8CAA8C;YAC9C,YAAY,cAAc;QAC5B;QAEA,MAAM,gBAAgB;YACpB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;QAC7B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,qBAAqB;QACrB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC,UAAU,MAAM;QAE3C,0BAA0B;QAC1B,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF;IAEA;;GAEC,GACD,sBAAsB,CAAC,MAAc,QAAgB;QACnD,sIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;YACxB,MAAM;YACN,QAAQ;YACR;QACF;IACF;AACF;AAEA,+EAA+E;AAC/E,sBAAsB;AACtB,+EAA+E;AAE/E,4CAA4C;AAC5C,YAAY,aAAa,GAAG,SAAS,CAAC,CAAC;IACrC,kCAAkC;IAClC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,yBAAyB;IACzB,OAAQ,MAAM,IAAI;QAChB,KAAK;YAEH;QACF,KAAK;YAEH;QACF,KAAK;YAEH;IACJ;AACF;AAEA,YAAY,gBAAgB,GAAG,SAAS,CAAC,CAAC;IACxC,qCAAqC;IACrC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,yCAAyC;IACzC,IAAI,MAAM,IAAI,KAAK,WAAW;QAC5B,MAAM,WAAW,MAAM,QAAQ;QAE/B,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,WAAW;YACvC,0BAA0B;YAC1B,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/sync/sync-manager.ts"], "sourcesContent": ["/**\n * Sync Manager for Offline Data Synchronization\n * Handles syncing offline data when connection is restored\n */\n\nimport { useAppStore, appActions } from '../store/app-store';\nimport { WorkoutService } from '../api/services/workouts';\nimport { ProgressService } from '../api/services/progress';\nimport { queryClient, cacheUtils } from '../query/config';\n\n// ============================================================================\n// SYNC MANAGER CLASS\n// ============================================================================\n\nexport class SyncManager {\n  private static instance: SyncManager;\n  private syncInProgress = false;\n  private syncQueue: Array<any> = [];\n\n  private constructor() {\n    this.setupNetworkListeners();\n  }\n\n  static getInstance(): SyncManager {\n    if (!SyncManager.instance) {\n      SyncManager.instance = new SyncManager();\n    }\n    return SyncManager.instance;\n  }\n\n  /**\n   * Setup network event listeners\n   */\n  private setupNetworkListeners() {\n    if (typeof window === 'undefined') return;\n\n    const handleOnline = () => {\n      appActions.setOnlineStatus(true);\n      this.syncPendingData();\n    };\n\n    const handleOffline = () => {\n      appActions.setOnlineStatus(false);\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    // Set initial status\n    appActions.setOnlineStatus(navigator.onLine);\n  }\n\n  /**\n   * Add data to sync queue for offline processing\n   */\n  addToSyncQueue(item: {\n    type: 'workout' | 'progress' | 'goal';\n    action: 'create' | 'update' | 'delete';\n    data: any;\n    localId?: string;\n  }) {\n    appActions.addPendingSync(item);\n    \n    // Try to sync immediately if online\n    if (navigator.onLine) {\n      this.syncPendingData();\n    }\n  }\n\n  /**\n   * Sync all pending data\n   */\n  async syncPendingData() {\n    if (this.syncInProgress || !navigator.onLine) {\n      return;\n    }\n\n    const state = useAppStore.getState();\n    const pendingItems = state.offline.pendingSync;\n\n    if (pendingItems.length === 0) {\n      return;\n    }\n\n    this.syncInProgress = true;\n\n    try {\n      appActions.addNotification({\n        type: 'info',\n        title: 'Syncing Data',\n        message: `Syncing ${pendingItems.length} pending items...`,\n      });\n\n      const syncResults = await Promise.allSettled(\n        pendingItems.map(item => this.syncSingleItem(item))\n      );\n\n      // Process results\n      let successCount = 0;\n      let failureCount = 0;\n\n      syncResults.forEach((result, index) => {\n        const item = pendingItems[index];\n        \n        if (result.status === 'fulfilled') {\n          successCount++;\n          appActions.removePendingSync(item.id);\n        } else {\n          failureCount++;\n          console.error(`Failed to sync item ${item.id}:`, result.reason);\n        }\n      });\n\n      // Update last sync time\n      appActions.updateLastSyncTime();\n\n      // Show result notification\n      if (failureCount === 0) {\n        appActions.addNotification({\n          type: 'success',\n          title: 'Sync Complete',\n          message: `Successfully synced ${successCount} items`,\n        });\n      } else {\n        appActions.addNotification({\n          type: 'warning',\n          title: 'Sync Partially Complete',\n          message: `Synced ${successCount} items, ${failureCount} failed`,\n        });\n      }\n\n      // Invalidate relevant queries to refetch fresh data\n      cacheUtils.invalidateEntity('workouts');\n      cacheUtils.invalidateEntity('progress');\n\n    } catch (error) {\n      console.error('Sync failed:', error);\n      appActions.addNotification({\n        type: 'error',\n        title: 'Sync Failed',\n        message: 'Failed to sync offline data. Will retry later.',\n      });\n    } finally {\n      this.syncInProgress = false;\n    }\n  }\n\n  /**\n   * Sync a single item\n   */\n  private async syncSingleItem(item: any): Promise<any> {\n    switch (item.type) {\n      case 'workout':\n        return this.syncWorkoutItem(item);\n      case 'progress':\n        return this.syncProgressItem(item);\n      case 'goal':\n        return this.syncGoalItem(item);\n      default:\n        throw new Error(`Unknown sync item type: ${item.type}`);\n    }\n  }\n\n  /**\n   * Sync workout-related items\n   */\n  private async syncWorkoutItem(item: any): Promise<any> {\n    switch (item.action) {\n      case 'create':\n        if (item.data.type === 'session') {\n          return WorkoutService.createWorkoutSession(item.data);\n        } else if (item.data.type === 'program') {\n          return WorkoutService.createWorkoutProgram(item.data);\n        }\n        break;\n      \n      case 'update':\n        if (item.data.type === 'session') {\n          return WorkoutService.updateWorkoutSession(item.data.id, item.data);\n        } else if (item.data.type === 'program') {\n          return WorkoutService.updateWorkoutProgram(item.data.id, item.data);\n        }\n        break;\n      \n      case 'delete':\n        if (item.data.type === 'session') {\n          return WorkoutService.deleteWorkoutSession(item.data.id);\n        } else if (item.data.type === 'program') {\n          return WorkoutService.deleteWorkoutProgram(item.data.id);\n        }\n        break;\n    }\n    \n    throw new Error(`Unknown workout sync action: ${item.action}`);\n  }\n\n  /**\n   * Sync progress-related items\n   */\n  private async syncProgressItem(item: any): Promise<any> {\n    switch (item.action) {\n      case 'create':\n        return ProgressService.createProgressRecord(item.data);\n      \n      case 'update':\n        return ProgressService.updateProgressRecord(item.data.id, item.data);\n      \n      case 'delete':\n        return ProgressService.deleteProgressRecord(item.data.id);\n    }\n    \n    throw new Error(`Unknown progress sync action: ${item.action}`);\n  }\n\n  /**\n   * Sync goal-related items\n   */\n  private async syncGoalItem(item: any): Promise<any> {\n    switch (item.action) {\n      case 'create':\n        return ProgressService.createFitnessGoal(item.data);\n      \n      case 'update':\n        return ProgressService.updateGoalProgress(item.data.id, item.data.currentValue);\n      \n      default:\n        throw new Error(`Unknown goal sync action: ${item.action}`);\n    }\n  }\n\n  /**\n   * Force sync all data\n   */\n  async forceSyncAll() {\n    await this.syncPendingData();\n  }\n\n  /**\n   * Clear all pending sync data\n   */\n  clearPendingSync() {\n    appActions.clearPendingSync();\n  }\n\n  /**\n   * Get sync status\n   */\n  getSyncStatus() {\n    const state = useAppStore.getState();\n    return {\n      isOnline: state.offline.isOnline,\n      pendingCount: state.offline.pendingSync.length,\n      lastSyncTime: state.offline.lastSyncTime,\n      syncInProgress: this.syncInProgress,\n    };\n  }\n}\n\n// ============================================================================\n// REACT HOOKS FOR SYNC MANAGEMENT\n// ============================================================================\n\n/**\n * Hook to use sync manager\n */\nexport function useSyncManager() {\n  const syncManager = SyncManager.getInstance();\n  \n  return {\n    addToSyncQueue: (item: Parameters<typeof syncManager.addToSyncQueue>[0]) => \n      syncManager.addToSyncQueue(item),\n    syncPendingData: () => syncManager.syncPendingData(),\n    forceSyncAll: () => syncManager.forceSyncAll(),\n    clearPendingSync: () => syncManager.clearPendingSync(),\n    getSyncStatus: () => syncManager.getSyncStatus(),\n  };\n}\n\n/**\n * Hook to get sync status\n */\nexport function useSyncStatus() {\n  const syncManager = SyncManager.getInstance();\n  const status = syncManager.getSyncStatus();\n  \n  return status;\n}\n\n// ============================================================================\n// OPTIMISTIC UPDATE UTILITIES\n// ============================================================================\n\nexport const optimisticUpdates = {\n  /**\n   * Optimistically update workout session\n   */\n  updateWorkoutSession: (sessionId: string, updates: any) => {\n    const queryKey = ['workouts', 'sessions', sessionId];\n    const previousData = cacheUtils.getQueryData(queryKey);\n\n    // Apply optimistic update\n    cacheUtils.setQueryData(queryKey, {\n      ...(previousData || {}),\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    });\n\n    return previousData;\n  },\n\n  /**\n   * Optimistically add progress record\n   */\n  addProgressRecord: (record: any) => {\n    const queryKey = ['progress', 'records'];\n    const previousData = cacheUtils.getQueryData<any>(queryKey);\n    \n    if (previousData?.data) {\n      const newRecord = {\n        ...record,\n        id: `temp-${Date.now()}`,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      cacheUtils.setQueryData(queryKey, {\n        ...previousData,\n        data: [newRecord, ...previousData.data],\n      });\n    }\n    \n    return previousData;\n  },\n\n  /**\n   * Revert optimistic update\n   */\n  revertUpdate: (queryKey: any[], previousData: any) => {\n    if (previousData !== undefined) {\n      cacheUtils.setQueryData(queryKey, previousData);\n    }\n  },\n};\n\n// ============================================================================\n// EXPORTS\n// ============================================================================\n\nexport default SyncManager;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAED;AACA;AACA;AACA;;;;;AAMO,MAAM;IACX,OAAe,SAAsB;IAC7B,iBAAiB,MAAM;IACvB,YAAwB,EAAE,CAAC;IAEnC,aAAsB;QACpB,IAAI,CAAC,qBAAqB;IAC5B;IAEA,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;GAEC,GACD,AAAQ,wBAAwB;QAC9B,uCAAmC;;QAAM;QAEzC,MAAM,eAAe;YACnB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;YAC3B,IAAI,CAAC,eAAe;QACtB;QAEA,MAAM,gBAAgB;YACpB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;QAC7B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,qBAAqB;QACrB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC,UAAU,MAAM;IAC7C;IAEA;;GAEC,GACD,eAAe,IAKd,EAAE;QACD,sIAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAE1B,oCAAoC;QACpC,IAAI,UAAU,MAAM,EAAE;YACpB,IAAI,CAAC,eAAe;QACtB;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB;QACtB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,UAAU,MAAM,EAAE;YAC5C;QACF;QAEA,MAAM,QAAQ,sIAAA,CAAA,cAAW,CAAC,QAAQ;QAClC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW;QAE9C,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B;QACF;QAEA,IAAI,CAAC,cAAc,GAAG;QAEtB,IAAI;YACF,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,QAAQ,EAAE,aAAa,MAAM,CAAC,iBAAiB,CAAC;YAC5D;YAEA,MAAM,cAAc,MAAM,QAAQ,UAAU,CAC1C,aAAa,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,cAAc,CAAC;YAG/C,kBAAkB;YAClB,IAAI,eAAe;YACnB,IAAI,eAAe;YAEnB,YAAY,OAAO,CAAC,CAAC,QAAQ;gBAC3B,MAAM,OAAO,YAAY,CAAC,MAAM;gBAEhC,IAAI,OAAO,MAAM,KAAK,aAAa;oBACjC;oBACA,sIAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE;gBACtC,OAAO;oBACL;oBACA,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM;gBAChE;YACF;YAEA,wBAAwB;YACxB,sIAAA,CAAA,aAAU,CAAC,kBAAkB;YAE7B,2BAA2B;YAC3B,IAAI,iBAAiB,GAAG;gBACtB,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;oBACzB,MAAM;oBACN,OAAO;oBACP,SAAS,CAAC,oBAAoB,EAAE,aAAa,MAAM,CAAC;gBACtD;YACF,OAAO;gBACL,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;oBACzB,MAAM;oBACN,OAAO;oBACP,SAAS,CAAC,OAAO,EAAE,aAAa,QAAQ,EAAE,aAAa,OAAO,CAAC;gBACjE;YACF;YAEA,oDAAoD;YACpD,gIAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;YAC5B,gIAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;QAE9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,sIAAA,CAAA,aAAU,CAAC,eAAe,CAAC;gBACzB,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,IAAI,CAAC,cAAc,GAAG;QACxB;IACF;IAEA;;GAEC,GACD,MAAc,eAAe,IAAS,EAAgB;QACpD,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,KAAK;gBACH,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC/B,KAAK;gBACH,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B;gBACE,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QAC1D;IACF;IAEA;;GAEC,GACD,MAAc,gBAAgB,IAAS,EAAgB;QACrD,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBAChC,OAAO,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI;gBACtD,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBACvC,OAAO,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI;gBACtD;gBACA;YAEF,KAAK;gBACH,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBAChC,OAAO,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI;gBACpE,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBACvC,OAAO,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI;gBACpE;gBACA;YAEF,KAAK;gBACH,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBAChC,OAAO,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE;gBACzD,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW;oBACvC,OAAO,4IAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE;gBACzD;gBACA;QACJ;QAEA,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK,MAAM,EAAE;IAC/D;IAEA;;GAEC,GACD,MAAc,iBAAiB,IAAS,EAAgB;QACtD,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO,4IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,KAAK,IAAI;YAEvD,KAAK;gBACH,OAAO,4IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI;YAErE,KAAK;gBACH,OAAO,4IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC,EAAE;QAC5D;QAEA,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,MAAM,EAAE;IAChE;IAEA;;GAEC,GACD,MAAc,aAAa,IAAS,EAAgB;QAClD,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO,4IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,KAAK,IAAI;YAEpD,KAAK;gBACH,OAAO,4IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,YAAY;YAEhF;gBACE,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,KAAK,MAAM,EAAE;QAC9D;IACF;IAEA;;GAEC,GACD,MAAM,eAAe;QACnB,MAAM,IAAI,CAAC,eAAe;IAC5B;IAEA;;GAEC,GACD,mBAAmB;QACjB,sIAAA,CAAA,aAAU,CAAC,gBAAgB;IAC7B;IAEA;;GAEC,GACD,gBAAgB;QACd,MAAM,QAAQ,sIAAA,CAAA,cAAW,CAAC,QAAQ;QAClC,OAAO;YACL,UAAU,MAAM,OAAO,CAAC,QAAQ;YAChC,cAAc,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM;YAC9C,cAAc,MAAM,OAAO,CAAC,YAAY;YACxC,gBAAgB,IAAI,CAAC,cAAc;QACrC;IACF;AACF;AASO,SAAS;IACd,MAAM,cAAc,YAAY,WAAW;IAE3C,OAAO;QACL,gBAAgB,CAAC,OACf,YAAY,cAAc,CAAC;QAC7B,iBAAiB,IAAM,YAAY,eAAe;QAClD,cAAc,IAAM,YAAY,YAAY;QAC5C,kBAAkB,IAAM,YAAY,gBAAgB;QACpD,eAAe,IAAM,YAAY,aAAa;IAChD;AACF;AAKO,SAAS;IACd,MAAM,cAAc,YAAY,WAAW;IAC3C,MAAM,SAAS,YAAY,aAAa;IAExC,OAAO;AACT;AAMO,MAAM,oBAAoB;IAC/B;;GAEC,GACD,sBAAsB,CAAC,WAAmB;QACxC,MAAM,WAAW;YAAC;YAAY;YAAY;SAAU;QACpD,MAAM,eAAe,gIAAA,CAAA,aAAU,CAAC,YAAY,CAAC;QAE7C,0BAA0B;QAC1B,gIAAA,CAAA,aAAU,CAAC,YAAY,CAAC,UAAU;YAChC,GAAI,gBAAgB,CAAC,CAAC;YACtB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,CAAC;QAClB,MAAM,WAAW;YAAC;YAAY;SAAU;QACxC,MAAM,eAAe,gIAAA,CAAA,aAAU,CAAC,YAAY,CAAM;QAElD,IAAI,cAAc,MAAM;YACtB,MAAM,YAAY;gBAChB,GAAG,MAAM;gBACT,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,gIAAA,CAAA,aAAU,CAAC,YAAY,CAAC,UAAU;gBAChC,GAAG,YAAY;gBACf,MAAM;oBAAC;uBAAc,aAAa,IAAI;iBAAC;YACzC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,cAAc,CAAC,UAAiB;QAC9B,IAAI,iBAAiB,WAAW;YAC9B,gIAAA,CAAA,aAAU,CAAC,YAAY,CAAC,UAAU;QACpC;IACF;AACF;uCAMe", "debugId": null}}, {"offset": {"line": 2792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/theme/mui-theme.ts"], "sourcesContent": ["import { createTheme } from '@mui/material/styles';\n\n// 运动风格的 Material UI 主题\nexport const fitnessTheme = createTheme({\n  palette: {\n    primary: {\n      main: '#FF6B35', // 活力橙色\n      light: '#FF8A65',\n      dark: '#E64A19',\n      contrastText: '#FFFFFF',\n    },\n    secondary: {\n      main: '#4CAF50', // 健康绿色\n      light: '#81C784',\n      dark: '#388E3C',\n      contrastText: '#FFFFFF',\n    },\n    background: {\n      default: '#FAFAFA',\n      paper: '#FFFFFF',\n    },\n    text: {\n      primary: '#212121',\n      secondary: '#757575',\n    },\n    error: {\n      main: '#F44336',\n    },\n    warning: {\n      main: '#FF9800',\n    },\n    info: {\n      main: '#2196F3',\n    },\n    success: {\n      main: '#4CAF50',\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '3rem',\n      fontWeight: 700,\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: '2.5rem',\n      fontWeight: 600,\n      lineHeight: 1.3,\n    },\n    h3: {\n      fontSize: '2rem',\n      fontWeight: 600,\n      lineHeight: 1.4,\n    },\n    h4: {\n      fontSize: '1.5rem',\n      fontWeight: 500,\n      lineHeight: 1.4,\n    },\n    h5: {\n      fontSize: '1.25rem',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n    h6: {\n      fontSize: '1rem',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.6,\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.5,\n    },\n    button: {\n      textTransform: 'none',\n      fontWeight: 600,\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 25,\n          padding: '12px 24px',\n          fontSize: '1rem',\n          fontWeight: 600,\n          textTransform: 'none',\n          boxShadow: 'none',\n          '&:hover': {\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n            transform: 'translateY(-2px)',\n            transition: 'all 0.3s ease',\n          },\n        },\n        contained: {\n          background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',\n          '&:hover': {\n            background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)',\n          },\n        },\n        outlined: {\n          borderWidth: 2,\n          '&:hover': {\n            borderWidth: 2,\n          },\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n          '&:hover': {\n            boxShadow: '0 8px 30px rgba(0,0,0,0.12)',\n            transform: 'translateY(-4px)',\n            transition: 'all 0.3s ease',\n          },\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#FFFFFF',\n          color: '#212121',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          borderRadius: 20,\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiFab: {\n      styleOverrides: {\n        root: {\n          background: 'linear-gradient(45deg, #FF6B35 30%, #FF8A65 90%)',\n          '&:hover': {\n            background: 'linear-gradient(45deg, #E64A19 30%, #FF6B35 90%)',\n            transform: 'scale(1.1)',\n          },\n        },\n      },\n    },\n  },\n});\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,eAAe,CAAA,GAAA,8MAAA,CAAA,cAAW,AAAD,EAAE;IACtC,SAAS;QACP,SAAS;YACP,MAAM;YACN,OAAO;YACP,MAAM;YACN,cAAc;QAChB;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,MAAM;YACN,cAAc;QAChB;QACA,YAAY;YACV,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,WAAW;QACb;QACA,OAAO;YACL,MAAM;QACR;QACA,SAAS;YACP,MAAM;QACR;QACA,MAAM;YACJ,MAAM;QACR;QACA,SAAS;YACP,MAAM;QACR;IACF;IACA,YAAY;QACV,YAAY;QACZ,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;QACd;QACA,OAAO;YACL,UAAU;YACV,YAAY;QACd;QACA,OAAO;YACL,UAAU;YACV,YAAY;QACd;QACA,QAAQ;YACN,eAAe;YACf,YAAY;QACd;IACF;IACA,OAAO;QACL,cAAc;IAChB;IACA,YAAY;QACV,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,SAAS;oBACT,UAAU;oBACV,YAAY;oBACZ,eAAe;oBACf,WAAW;oBACX,WAAW;wBACT,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;gBACF;gBACA,WAAW;oBACT,YAAY;oBACZ,WAAW;wBACT,YAAY;oBACd;gBACF;gBACA,UAAU;oBACR,aAAa;oBACb,WAAW;wBACT,aAAa;oBACf;gBACF;YACF;QACF;QACA,SAAS;YACP,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,WAAW;oBACX,WAAW;wBACT,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;gBACF;YACF;QACF;QACA,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,iBAAiB;oBACjB,OAAO;oBACP,WAAW;gBACb;YACF;QACF;QACA,SAAS;YACP,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,YAAY;gBACd;YACF;QACF;QACA,QAAQ;YACN,gBAAgB;gBACd,MAAM;oBACJ,YAAY;oBACZ,WAAW;wBACT,YAAY;wBACZ,WAAW;oBACb;gBACF;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/providers/app-providers.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { QueryProvider } from '@/lib/providers/query-provider';\nimport { AuthProvider } from '@/lib/providers/auth-provider';\nimport { SyncManager } from '@/lib/sync/sync-manager';\nimport { offlineUtils } from '@/lib/query/config';\nimport { fitnessTheme } from '@/lib/theme/mui-theme';\n\n/**\n * Main App Providers Component\n * Combines all providers and initializes app-wide functionality\n */\nexport function AppProviders({ children }: { children: React.ReactNode }) {\n  useEffect(() => {\n    // Initialize sync manager\n    const syncManager = SyncManager.getInstance();\n    \n    // Setup network listeners\n    const cleanupNetworkListeners = offlineUtils.setupNetworkListeners();\n    \n    // Cleanup on unmount\n    return () => {\n      if (cleanupNetworkListeners) {\n        cleanupNetworkListeners();\n      }\n    };\n  }, []);\n\n  return (\n    <ThemeProvider theme={fitnessTheme}>\n      <CssBaseline />\n      <QueryProvider>\n        <AuthProvider>\n          {children}\n        </AuthProvider>\n      </QueryProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAeO,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,0BAA0B;YAC1B,MAAM,cAAc,wIAAA,CAAA,cAAW,CAAC,WAAW;YAE3C,0BAA0B;YAC1B,MAAM,0BAA0B,gIAAA,CAAA,eAAY,CAAC,qBAAqB;YAElE,qBAAqB;YACrB;0CAAO;oBACL,IAAI,yBAAyB;wBAC3B;oBACF;gBACF;;QACF;iCAAG,EAAE;IAEL,qBACE,6LAAC,kNAAA,CAAA,gBAAa;QAAC,OAAO,sIAAA,CAAA,eAAY;;0BAChC,6LAAC,mNAAA,CAAA,cAAW;;;;;0BACZ,6LAAC,gJAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,+IAAA,CAAA,eAAY;8BACV;;;;;;;;;;;;;;;;;AAKX;GA1BgB;KAAA", "debugId": null}}, {"offset": {"line": 3044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/components/navigation/mui-navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  App<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>po<PERSON>,\n  Button,\n  IconButton,\n  Box,\n  Drawer,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Badge,\n  Avatar,\n  useMediaQuery,\n  useTheme,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  FitnessCenter,\n  DirectionsRun,\n  Assessment,\n  Notifications,\n  AccountCircle,\n  Close,\n} from '@mui/icons-material';\nimport Link from 'next/link';\nimport { useAppStore } from '@/lib/store/app-store';\n\nconst navigationItems = [\n  { label: '训练计划', href: '/workouts', icon: <FitnessCenter /> },\n  { label: '运动库', href: '/exercises', icon: <DirectionsRun /> },\n  { label: '进度追踪', href: '/progress', icon: <Assessment /> },\n];\n\nexport function MuiNavigation() {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n  \n  const { user, isOnline } = useAppStore();\n  const unreadCount = useAppStore((state) => \n    (state.ui.notifications || []).filter(n => !n.read).length\n  );\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const drawer = (\n    <Box sx={{ width: 250 }}>\n      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"h6\" sx={{ color: 'primary.main', fontWeight: 'bold' }}>\n          AI-fitness-singles\n        </Typography>\n        <IconButton onClick={handleDrawerToggle}>\n          <Close />\n        </IconButton>\n      </Box>\n      <List>\n        {navigationItems.map((item) => (\n          <ListItem key={item.href} component={Link} href={item.href} onClick={handleDrawerToggle}>\n            <ListItemIcon sx={{ color: 'primary.main' }}>\n              {item.icon}\n            </ListItemIcon>\n            <ListItemText \n              primary={item.label} \n              sx={{ \n                '& .MuiListItemText-primary': { \n                  fontWeight: 500,\n                  color: 'text.primary'\n                }\n              }}\n            />\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <>\n      <AppBar position=\"sticky\" elevation={0}>\n        <Toolbar>\n          {/* Mobile menu button */}\n          {isMobile && (\n            <IconButton\n              color=\"inherit\"\n              aria-label=\"open drawer\"\n              edge=\"start\"\n              onClick={handleDrawerToggle}\n              sx={{ mr: 2 }}\n            >\n              <MenuIcon />\n            </IconButton>\n          )}\n\n          {/* Logo */}\n          <Box component={Link} href=\"/\" sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none', color: 'inherit' }}>\n            <FitnessCenter sx={{ mr: 1, color: 'primary.main', fontSize: 32 }} />\n            <Typography\n              variant=\"h6\"\n              component=\"div\"\n              sx={{\n                fontWeight: 'bold',\n                color: 'primary.main',\n                display: { xs: 'none', sm: 'block' }\n              }}\n            >\n              AI-fitness-singles\n            </Typography>\n          </Box>\n\n          <Box sx={{ flexGrow: 1 }} />\n\n          {/* Desktop navigation */}\n          {!isMobile && (\n            <Box sx={{ display: 'flex', gap: 1 }}>\n              {navigationItems.map((item) => (\n                <Button\n                  key={item.href}\n                  component={Link}\n                  href={item.href}\n                  startIcon={item.icon}\n                  sx={{\n                    color: 'text.primary',\n                    fontWeight: 500,\n                    '&:hover': {\n                      backgroundColor: 'primary.light',\n                      color: 'white',\n                    },\n                  }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n            </Box>\n          )}\n\n          {/* Right side icons */}\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>\n            {/* Online status indicator */}\n            <Box\n              sx={{\n                width: 8,\n                height: 8,\n                borderRadius: '50%',\n                backgroundColor: isOnline ? 'success.main' : 'error.main',\n              }}\n            />\n\n            {/* Notifications */}\n            <IconButton color=\"inherit\">\n              <Badge badgeContent={unreadCount} color=\"error\">\n                <Notifications />\n              </Badge>\n            </IconButton>\n\n            {/* User avatar */}\n            {user ? (\n              <Avatar\n                sx={{\n                  width: 32,\n                  height: 32,\n                  bgcolor: 'primary.main',\n                  fontSize: '0.875rem',\n                }}\n              >\n                {user.name?.charAt(0) || 'U'}\n              </Avatar>\n            ) : (\n              <IconButton color=\"inherit\">\n                <AccountCircle />\n              </IconButton>\n            )}\n          </Box>\n        </Toolbar>\n      </AppBar>\n\n      {/* Mobile drawer */}\n      <Drawer\n        variant=\"temporary\"\n        open={mobileOpen}\n        onClose={handleDrawerToggle}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          display: { xs: 'block', md: 'none' },\n          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 250 },\n        }}\n      >\n        {drawer}\n      </Drawer>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AA9BA;;;;;;;;;;;;AAgCA,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAQ,MAAM;QAAa,oBAAM,6LAAC,qKAAA,CAAA,UAAa;;;;;IAAI;IAC5D;QAAE,OAAO;QAAO,MAAM;QAAc,oBAAM,6LAAC,qKAAA,CAAA,UAAa;;;;;IAAI;IAC5D;QAAE,OAAO;QAAQ,MAAM;QAAa,oBAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;IAAI;CAC1D;AAEM,SAAS;;IACd,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACrC,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAC/B,CAAC,MAAM,EAAE,CAAC,aAAa,IAAI,EAAE,EAAE,MAAM;0DAAC,CAAA,IAAK,CAAC,EAAE,IAAI;yDAAE,MAAM;;IAG5D,MAAM,qBAAqB;QACzB,cAAc,CAAC;IACjB;IAEA,MAAM,uBACJ,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,OAAO;QAAI;;0BACpB,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,GAAG;oBAAG,SAAS;oBAAQ,gBAAgB;oBAAiB,YAAY;gBAAS;;kCACtF,6LAAC,gNAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAK,IAAI;4BAAE,OAAO;4BAAgB,YAAY;wBAAO;kCAAG;;;;;;kCAG5E,6LAAC,gNAAA,CAAA,aAAU;wBAAC,SAAS;kCACnB,cAAA,6LAAC,6JAAA,CAAA,UAAK;;;;;;;;;;;;;;;;0BAGV,6LAAC,8LAAA,CAAA,OAAI;0BACF,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,0MAAA,CAAA,WAAQ;wBAAiB,WAAW,+JAAA,CAAA,UAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;;0CACnE,6LAAC,sNAAA,CAAA,eAAY;gCAAC,IAAI;oCAAE,OAAO;gCAAe;0CACvC,KAAK,IAAI;;;;;;0CAEZ,6LAAC,sNAAA,CAAA,eAAY;gCACX,SAAS,KAAK,KAAK;gCACnB,IAAI;oCACF,8BAA8B;wCAC5B,YAAY;wCACZ,OAAO;oCACT;gCACF;;;;;;;uBAXW,KAAK,IAAI;;;;;;;;;;;;;;;;IAmBhC,qBACE;;0BACE,6LAAC,oMAAA,CAAA,SAAM;gBAAC,UAAS;gBAAS,WAAW;0BACnC,cAAA,6LAAC,uMAAA,CAAA,UAAO;;wBAEL,0BACC,6LAAC,gNAAA,CAAA,aAAU;4BACT,OAAM;4BACN,cAAW;4BACX,MAAK;4BACL,SAAS;4BACT,IAAI;gCAAE,IAAI;4BAAE;sCAEZ,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;sCAKb,6LAAC,2LAAA,CAAA,MAAG;4BAAC,WAAW,+JAAA,CAAA,UAAI;4BAAE,MAAK;4BAAI,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,gBAAgB;gCAAQ,OAAO;4BAAU;;8CACnH,6LAAC,qKAAA,CAAA,UAAa;oCAAC,IAAI;wCAAE,IAAI;wCAAG,OAAO;wCAAgB,UAAU;oCAAG;;;;;;8CAChE,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,WAAU;oCACV,IAAI;wCACF,YAAY;wCACZ,OAAO;wCACP,SAAS;4CAAE,IAAI;4CAAQ,IAAI;wCAAQ;oCACrC;8CACD;;;;;;;;;;;;sCAKH,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,UAAU;4BAAE;;;;;;wBAGtB,CAAC,0BACA,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,KAAK;4BAAE;sCAChC,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,oMAAA,CAAA,SAAM;oCAEL,WAAW,+JAAA,CAAA,UAAI;oCACf,MAAM,KAAK,IAAI;oCACf,WAAW,KAAK,IAAI;oCACpB,IAAI;wCACF,OAAO;wCACP,YAAY;wCACZ,WAAW;4CACT,iBAAiB;4CACjB,OAAO;wCACT;oCACF;8CAEC,KAAK,KAAK;mCAbN,KAAK,IAAI;;;;;;;;;;sCAoBtB,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;gCAAG,IAAI;4BAAE;;8CAE9D,6LAAC,2LAAA,CAAA,MAAG;oCACF,IAAI;wCACF,OAAO;wCACP,QAAQ;wCACR,cAAc;wCACd,iBAAiB,WAAW,iBAAiB;oCAC/C;;;;;;8CAIF,6LAAC,gNAAA,CAAA,aAAU;oCAAC,OAAM;8CAChB,cAAA,6LAAC,iMAAA,CAAA,QAAK;wCAAC,cAAc;wCAAa,OAAM;kDACtC,cAAA,6LAAC,qKAAA,CAAA,UAAa;;;;;;;;;;;;;;;gCAKjB,qBACC,6LAAC,oMAAA,CAAA,SAAM;oCACL,IAAI;wCACF,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,UAAU;oCACZ;8CAEC,KAAK,IAAI,EAAE,OAAO,MAAM;;;;;yDAG3B,6LAAC,gNAAA,CAAA,aAAU;oCAAC,OAAM;8CAChB,cAAA,6LAAC,qKAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxB,6LAAC,oMAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,YAAY;oBACV,aAAa;gBACf;gBACA,IAAI;oBACF,SAAS;wBAAE,IAAI;wBAAS,IAAI;oBAAO;oBACnC,sBAAsB;wBAAE,WAAW;wBAAc,OAAO;oBAAI;gBAC9D;0BAEC;;;;;;;;AAIT;GAjKgB;;QACA,wMAAA,CAAA,WAAQ;QACL,iNAAA,CAAA,gBAAa;QAGH,sIAAA,CAAA,cAAW;QAClB,sIAAA,CAAA,cAAW;;;KANjB", "debugId": null}}]}