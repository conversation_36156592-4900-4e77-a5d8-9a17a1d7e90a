{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/api/exercises/public/route.ts"], "sourcesContent": ["/**\n * Public Exercises API Route\n * Calls workout-cool server action directly for public exercises\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Mock data for testing - replace with actual workout-cool integration\nconst mockExercises = [\n  {\n    id: '1',\n    name: 'Push-ups',\n    nameEn: 'Push-ups',\n    description: 'Classic bodyweight exercise for chest, shoulders, and triceps',\n    descriptionEn: 'Classic bodyweight exercise for chest, shoulders, and triceps',\n    instructions: 'Start in plank position, lower body to ground, push back up',\n    instructionsEn: 'Start in plank position, lower body to ground, push back up',\n    tips: 'Keep core tight, maintain straight line from head to heels',\n    tipsEn: 'Keep core tight, maintain straight line from head to heels',\n    imageUrl: '/images/exercises/pushups.jpg',\n    videoUrl: '/videos/exercises/pushups.mp4',\n    attributes: [\n      {\n        id: '1',\n        attributeName: { id: '1', name: 'Muscle Group', nameEn: 'Muscle Group' },\n        attributeValue: { id: '1', value: 'Chest', valueEn: 'Chest' },\n      },\n      {\n        id: '2',\n        attributeName: { id: '2', name: 'Equipment', nameEn: 'Equipment' },\n        attributeValue: { id: '2', value: 'None', valueEn: 'None' },\n      },\n    ],\n  },\n  {\n    id: '2',\n    name: 'Squats',\n    nameEn: 'Squats',\n    description: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings',\n    descriptionEn: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings',\n    instructions: 'Stand with feet shoulder-width apart, lower hips back and down, return to standing',\n    instructionsEn: 'Stand with feet shoulder-width apart, lower hips back and down, return to standing',\n    tips: 'Keep knees aligned with toes, chest up, weight in heels',\n    tipsEn: 'Keep knees aligned with toes, chest up, weight in heels',\n    imageUrl: '/images/exercises/squats.jpg',\n    videoUrl: '/videos/exercises/squats.mp4',\n    attributes: [\n      {\n        id: '3',\n        attributeName: { id: '1', name: 'Muscle Group', nameEn: 'Muscle Group' },\n        attributeValue: { id: '3', value: 'Legs', valueEn: 'Legs' },\n      },\n      {\n        id: '4',\n        attributeName: { id: '2', name: 'Equipment', nameEn: 'Equipment' },\n        attributeValue: { id: '2', value: 'None', valueEn: 'None' },\n      },\n    ],\n  },\n];\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const search = searchParams.get('search');\n\n    let filteredExercises = mockExercises;\n\n    // Apply search filter if provided\n    if (search) {\n      filteredExercises = mockExercises.filter(exercise =>\n        exercise.name.toLowerCase().includes(search.toLowerCase()) ||\n        exercise.description.toLowerCase().includes(search.toLowerCase())\n      );\n    }\n\n    // For now, return mock data\n    // TODO: Integrate with workout-cool server actions when available\n    return NextResponse.json(filteredExercises);\n  } catch (error) {\n    console.error('Error fetching public exercises:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch public exercises' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAEA,uEAAuE;AACvE,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;YACV;gBACE,IAAI;gBACJ,eAAe;oBAAE,IAAI;oBAAK,MAAM;oBAAgB,QAAQ;gBAAe;gBACvE,gBAAgB;oBAAE,IAAI;oBAAK,OAAO;oBAAS,SAAS;gBAAQ;YAC9D;YACA;gBACE,IAAI;gBACJ,eAAe;oBAAE,IAAI;oBAAK,MAAM;oBAAa,QAAQ;gBAAY;gBACjE,gBAAgB;oBAAE,IAAI;oBAAK,OAAO;oBAAQ,SAAS;gBAAO;YAC5D;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;YACV;gBACE,IAAI;gBACJ,eAAe;oBAAE,IAAI;oBAAK,MAAM;oBAAgB,QAAQ;gBAAe;gBACvE,gBAAgB;oBAAE,IAAI;oBAAK,OAAO;oBAAQ,SAAS;gBAAO;YAC5D;YACA;gBACE,IAAI;gBACJ,eAAe;oBAAE,IAAI;oBAAK,MAAM;oBAAa,QAAQ;gBAAY;gBACjE,gBAAgB;oBAAE,IAAI;oBAAK,OAAO;oBAAQ,SAAS;gBAAO;YAC5D;SACD;IACH;CACD;AAEM,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,oBAAoB;QAExB,kCAAkC;QAClC,IAAI,QAAQ;YACV,oBAAoB,cAAc,MAAM,CAAC,CAAA,WACvC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW,OACvD,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW;QAElE;QAEA,4BAA4B;QAC5B,kEAAkE;QAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAmC,GAC5C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}