import { NextRequest, NextResponse } from 'next/server';
import { locales, defaultLocale, getLocaleFromPathname } from './lib/i18n';

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Check if pathname already has a locale
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // If no locale in pathname, redirect to default locale
  if (!pathnameHasLocale) {
    // Get locale from cookie or use default
    const locale = request.cookies.get('locale')?.value || defaultLocale;

    // Only add locale prefix for non-default locale
    if (locale !== defaultLocale) {
      const newUrl = new URL(`/${locale}${pathname}`, request.url);
      return NextResponse.redirect(newUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Skip all internal paths (_next)
    '/((?!_next|api|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};
