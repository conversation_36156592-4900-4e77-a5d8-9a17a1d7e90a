{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/api/health/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n/**\n * Health Check API Endpoint\n * Used for monitoring and load balancer health checks\n */\n\nexport async function GET(request: NextRequest) {\n  try {\n    const healthData = {\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n      uptime: process.uptime(),\n      memory: {\n        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),\n        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),\n        external: Math.round(process.memoryUsage().external / 1024 / 1024),\n      },\n      checks: {\n        api: await checkApiHealth(),\n        database: await checkDatabaseHealth(),\n        external: await checkExternalServices(),\n      }\n    };\n\n    // Determine overall health status\n    const allChecksHealthy = Object.values(healthData.checks).every(check => check.status === 'healthy');\n    \n    if (!allChecksHealthy) {\n      healthData.status = 'degraded';\n    }\n\n    const statusCode = healthData.status === 'healthy' ? 200 : 503;\n\n    return NextResponse.json(healthData, { \n      status: statusCode,\n      headers: {\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n        'Pragma': 'no-cache',\n        'Expires': '0'\n      }\n    });\n\n  } catch (error) {\n    console.error('Health check failed:', error);\n    \n    return NextResponse.json({\n      status: 'unhealthy',\n      timestamp: new Date().toISOString(),\n      error: error instanceof Error ? error.message : 'Unknown error',\n      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n    }, { \n      status: 503,\n      headers: {\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n        'Pragma': 'no-cache',\n        'Expires': '0'\n      }\n    });\n  }\n}\n\n/**\n * Check API health\n */\nasync function checkApiHealth(): Promise<{ status: string; responseTime?: number; error?: string }> {\n  try {\n    const startTime = Date.now();\n\n    // Simple internal check - just verify we can respond\n    const responseTime = Date.now() - startTime;\n\n    return {\n      status: 'healthy',\n      responseTime\n    };\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n/**\n * Check database health (if applicable)\n */\nasync function checkDatabaseHealth(): Promise<{ status: string; responseTime?: number; error?: string }> {\n  try {\n    // If using a database, add connection check here\n    // For now, we'll just return healthy since we're using external APIs\n    return {\n      status: 'healthy',\n      responseTime: 0\n    };\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n/**\n * Check external services health\n */\nasync function checkExternalServices(): Promise<{ status: string; services?: any; error?: string }> {\n  try {\n    const services = {\n      workoutApi: await checkWorkoutApiHealth(),\n    };\n    \n    const allServicesHealthy = Object.values(services).every(service => service.status === 'healthy');\n    \n    return {\n      status: allServicesHealthy ? 'healthy' : 'degraded',\n      services\n    };\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n/**\n * Check workout API health\n */\nasync function checkWorkoutApiHealth(): Promise<{ status: string; responseTime?: number; error?: string }> {\n  try {\n    const startTime = Date.now();\n    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;\n\n    if (!apiBaseUrl) {\n      return {\n        status: 'degraded',\n        error: 'API base URL not configured'\n      };\n    }\n\n    // For now, just return healthy since we don't have the actual API running\n    // In production, this would make a real API call\n    const responseTime = Date.now() - startTime;\n\n    return {\n      status: 'healthy',\n      responseTime\n    };\n  } catch (error) {\n    return {\n      status: 'degraded',\n      error: error instanceof Error ? error.message : 'Connection failed'\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAOO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,aAAa;YACjB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAChD,aAAa,mDAAwB;YACrC,QAAQ,QAAQ,MAAM;YACtB,QAAQ;gBACN,MAAM,KAAK,KAAK,CAAC,QAAQ,WAAW,GAAG,QAAQ,GAAG,OAAO;gBACzD,OAAO,KAAK,KAAK,CAAC,QAAQ,WAAW,GAAG,SAAS,GAAG,OAAO;gBAC3D,UAAU,KAAK,KAAK,CAAC,QAAQ,WAAW,GAAG,QAAQ,GAAG,OAAO;YAC/D;YACA,QAAQ;gBACN,KAAK,MAAM;gBACX,UAAU,MAAM;gBAChB,UAAU,MAAM;YAClB;QACF;QAEA,kCAAkC;QAClC,MAAM,mBAAmB,OAAO,MAAM,CAAC,WAAW,MAAM,EAAE,KAAK,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAE1F,IAAI,CAAC,kBAAkB;YACrB,WAAW,MAAM,GAAG;QACtB;QAEA,MAAM,aAAa,WAAW,MAAM,KAAK,YAAY,MAAM;QAE3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,YAAY;YACnC,QAAQ;YACR,SAAS;gBACP,iBAAiB;gBACjB,UAAU;gBACV,WAAW;YACb;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAChD,aAAa,mDAAwB;QACvC,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,iBAAiB;gBACjB,UAAU;gBACV,WAAW;YACb;QACF;IACF;AACF;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,MAAM,YAAY,KAAK,GAAG;QAE1B,qDAAqD;QACrD,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,OAAO;YACL,QAAQ;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,iDAAiD;QACjD,qEAAqE;QACrE,OAAO;YACL,QAAQ;YACR,cAAc;QAChB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,MAAM,WAAW;YACf,YAAY,MAAM;QACpB;QAEA,MAAM,qBAAqB,OAAO,MAAM,CAAC,UAAU,KAAK,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;QAEvF,OAAO;YACL,QAAQ,qBAAqB,YAAY;YACzC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM;QAEN,uCAAiB;;QAKjB;QAEA,0EAA0E;QAC1E,iDAAiD;QACjD,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,OAAO;YACL,QAAQ;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF", "debugId": null}}]}