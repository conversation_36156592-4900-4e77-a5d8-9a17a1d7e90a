'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Tabs,
  Tab,
  Paper,
  LinearProgress,
  Chip,
  alpha,
  useTheme,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  CalendarToday as CalendarIcon,
  Target as TargetIcon,
  EmojiEvents as AwardIcon,
  LocalFireDepartment as FlameIcon,
  AccessTime as ClockIcon,
  BarChart as BarChartIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Visibility as EyeIcon,
  TrendingDown as TrendingDownIcon,
  Group as UsersIcon,
  Bolt as ZapIcon,
  Favorite as HeartIcon,
  Clear as ClearIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Timer as TimerIcon,
  FitnessCenter as FitnessCenterIcon,
} from '@mui/icons-material';
import {
  useProgressStats,
  useWorkoutStats,
  useBodyMeasurements,
  useFitnessGoals,
  useAchievements,
  useWorkoutCalendar,
  usePersonalRecords,
  useWorkoutIntensity,
  useExportProgressData
} from "@/lib/hooks/use-progress";
import { useAuth } from "@/lib/hooks/use-auth";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`progress-tabpanel-${index}`}
      aria-labelledby={`progress-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

export function MuiProgress() {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  
  const { isAuthenticated } = useAuth();
  
  // API hooks
  const { data: progressStats, isLoading: isLoadingProgress } = useProgressStats(selectedPeriod);
  const { data: workoutStats, isLoading: isLoadingWorkouts } = useWorkoutStats(selectedPeriod);
  const { data: bodyMeasurements, isLoading: isLoadingMeasurements } = useBodyMeasurements();
  const { data: fitnessGoals, isLoading: isLoadingGoals } = useFitnessGoals();
  const { data: achievements, isLoading: isLoadingAchievements } = useAchievements();
  const { data: workoutCalendar } = useWorkoutCalendar();
  const { data: personalRecords } = usePersonalRecords();
  const { data: workoutIntensity } = useWorkoutIntensity(selectedPeriod);

  // Mock data for demonstration
  const mockStats = [
    { 
      label: '本月训练', 
      value: '18次', 
      change: '+12%',
      trend: 'up',
      icon: <CalendarIcon />, 
      color: theme.palette.primary.main 
    },
    { 
      label: '总时长', 
      value: '720分钟', 
      change: '+8%',
      trend: 'up',
      icon: <TimerIcon />, 
      color: theme.palette.secondary.main 
    },
    { 
      label: '消耗卡路里', 
      value: '2,340', 
      change: '+15%',
      trend: 'up',
      icon: <FlameIcon />, 
      color: '#FF5722' 
    },
    { 
      label: '连续天数', 
      value: '12天', 
      change: '+3天',
      trend: 'up',
      icon: <AwardIcon />, 
      color: '#9C27B0' 
    },
  ];

  const mockGoals = [
    { id: 1, title: '每周训练4次', progress: 75, target: 4, current: 3, unit: '次' },
    { id: 2, title: '月度卡路里消耗', progress: 60, target: 5000, current: 3000, unit: '卡' },
    { id: 3, title: '体重目标', progress: 40, target: 70, current: 72, unit: 'kg' },
  ];

  const mockAchievements = [
    { id: 1, title: '连续训练7天', description: '坚持一周训练', icon: '🔥', earned: true, date: '2024-01-15' },
    { id: 2, title: '首次完成HIIT', description: '完成高强度间歇训练', icon: '⚡', earned: true, date: '2024-01-10' },
    { id: 3, title: '训练达人', description: '完成50次训练', icon: '🏆', earned: false, progress: 32 },
    { id: 4, title: '卡路里燃烧王', description: '单次训练消耗500卡路里', icon: '🔥', earned: false, progress: 80 },
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          py: { xs: 6, md: 8 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
                fontSize: { xs: '2.5rem', md: '3.5rem' }
              }}
            >
              进度追踪
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: 'text.secondary',
                mb: 4,
                fontWeight: 400,
                fontSize: { xs: '1.25rem', md: '1.5rem' }
              }}
            >
              记录您的健身历程，见证每一次进步
            </Typography>
          </Box>
        </Container>

        {/* Floating Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.secondary.main, 0.3)})`,
            animation: 'float 6s ease-in-out infinite',
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '30%',
            left: '5%',
            width: 60,
            height: 60,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,
            animation: 'float 4s ease-in-out infinite reverse',
          }}
        />
      </Box>

      {/* Quick Stats */}
      <Container maxWidth="lg" sx={{ mt: -2, position: 'relative', zIndex: 2, py: 4 }}>
        <Grid container spacing={3}>
          {mockStats.map((stat, index) => (
            <Grid size={{ xs: 12, sm: 6, lg: 3 }} key={index}>
              <Card
                sx={{
                  p: 3,
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',
                  backdropFilter: 'blur(15px)',
                  border: '1px solid rgba(255,255,255,0.3)',
                  borderRadius: 3,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: `0 8px 20px ${alpha(stat.color, 0.15)}`,
                    border: `1px solid ${alpha(stat.color, 0.2)}`,
                  }
                }}
              >
                <CardContent sx={{ p: '0 !important' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box
                      sx={{
                        display: 'inline-flex',
                        p: 1.5,
                        borderRadius: '50%',
                        bgcolor: alpha(stat.color, 0.12),
                        color: stat.color,
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Chip
                      label={stat.change}
                      size="small"
                      icon={stat.trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                      color={stat.trend === 'up' ? 'success' : 'error'}
                      variant="outlined"
                    />
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 500 }}>
                    {stat.label}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Period Selector */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
          <Paper sx={{ p: 1, borderRadius: 3 }}>
            <Button
              variant={selectedPeriod === 'week' ? 'contained' : 'text'}
              onClick={() => setSelectedPeriod('week')}
              sx={{ borderRadius: 2, textTransform: 'none', mx: 0.5 }}
            >
              本周
            </Button>
            <Button
              variant={selectedPeriod === 'month' ? 'contained' : 'text'}
              onClick={() => setSelectedPeriod('month')}
              sx={{ borderRadius: 2, textTransform: 'none', mx: 0.5 }}
            >
              本月
            </Button>
            <Button
              variant={selectedPeriod === 'year' ? 'contained' : 'text'}
              onClick={() => setSelectedPeriod('year')}
              sx={{ borderRadius: 2, textTransform: 'none', mx: 0.5 }}
            >
              本年
            </Button>
          </Paper>
        </Box>

        {/* Tabs */}
        <Paper sx={{ mb: 4, borderRadius: 3, overflow: 'hidden' }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            variant="fullWidth"
            sx={{
              '& .MuiTab-root': {
                fontWeight: 600,
                fontSize: '1rem',
                textTransform: 'none',
                py: 2,
              },
              '& .Mui-selected': {
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                color: 'white !important',
              }
            }}
          >
            <Tab label="概览" />
            <Tab label="目标" />
            <Tab label="成就" />
            <Tab label="历史" />
          </Tabs>
        </Paper>

        {/* Overview Tab */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            {/* Workout Chart */}
            <Grid size={{ xs: 12, lg: 8 }}>
              <Card sx={{ p: 3, borderRadius: 3, height: 400 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  训练趋势
                </Typography>
                <Box
                  sx={{
                    height: 300,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <BarChartIcon sx={{ fontSize: 64, color: 'text.secondary', opacity: 0.5 }} />
                  <Typography variant="body1" color="text.secondary" sx={{ ml: 2 }}>
                    图表数据加载中...
                  </Typography>
                </Box>
              </Card>
            </Grid>

            {/* Recent Activities */}
            <Grid size={{ xs: 12, lg: 4 }}>
              <Card sx={{ p: 3, borderRadius: 3, height: 400 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  最近活动
                </Typography>
                <List sx={{ p: 0 }}>
                  {[
                    { activity: '完成HIIT训练', time: '2小时前', icon: <ZapIcon />, color: theme.palette.primary.main },
                    { activity: '达成周目标', time: '1天前', icon: <TargetIcon />, color: theme.palette.secondary.main },
                    { activity: '力量训练', time: '2天前', icon: <FitnessCenterIcon />, color: '#FF5722' },
                    { activity: '获得新成就', time: '3天前', icon: <AwardIcon />, color: '#9C27B0' },
                  ].map((item, index) => (
                    <React.Fragment key={index}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: alpha(item.color, 0.1), color: item.color, width: 40, height: 40 }}>
                            {item.icon}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={item.activity}
                          secondary={item.time}
                          primaryTypographyProps={{ fontWeight: 500 }}
                        />
                      </ListItem>
                      {index < 3 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Goals Tab */}
        <TabPanel value={activeTab} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>
              健身目标
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 600,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              }}
            >
              添加目标
            </Button>
          </Box>

          <Grid container spacing={3}>
            {mockGoals.map((goal) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={goal.id}>
                <Card sx={{ p: 3, borderRadius: 3, height: '100%' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, flex: 1 }}>
                      {goal.title}
                    </Typography>
                    <Button variant="text" size="small" sx={{ minWidth: 'auto', p: 0.5 }}>
                      <EditIcon fontSize="small" />
                    </Button>
                  </Box>

                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        进度
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {goal.current}/{goal.target} {goal.unit}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={goal.progress}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                        }
                      }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {goal.progress}% 完成
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<EyeIcon />}
                      sx={{ flex: 1, borderRadius: 2, textTransform: 'none' }}
                    >
                      查看详情
                    </Button>
                    <Button
                      variant="text"
                      size="small"
                      sx={{ minWidth: 'auto', borderRadius: 2 }}
                    >
                      <CheckIcon />
                    </Button>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Achievements Tab */}
        <TabPanel value={activeTab} index={2}>
          <Typography variant="h5" sx={{ fontWeight: 700, mb: 3 }}>
            成就系统
          </Typography>

          <Grid container spacing={3}>
            {mockAchievements.map((achievement) => (
              <Grid size={{ xs: 12, sm: 6, lg: 3 }} key={achievement.id}>
                <Card
                  sx={{
                    p: 3,
                    borderRadius: 3,
                    height: '100%',
                    opacity: achievement.earned ? 1 : 0.7,
                    background: achievement.earned
                      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`
                      : 'background.paper',
                    border: achievement.earned ? `2px solid ${alpha(theme.palette.primary.main, 0.2)}` : '1px solid',
                    borderColor: achievement.earned ? 'transparent' : 'divider',
                  }}
                >
                  <Box sx={{ textAlign: 'center', mb: 2 }}>
                    <Typography variant="h2" sx={{ mb: 1 }}>
                      {achievement.icon}
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {achievement.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {achievement.description}
                    </Typography>
                  </Box>

                  {achievement.earned ? (
                    <Box sx={{ textAlign: 'center' }}>
                      <Chip
                        label={`获得于 ${achievement.date}`}
                        color="primary"
                        size="small"
                        icon={<CheckIcon />}
                      />
                    </Box>
                  ) : (
                    <Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          进度
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {achievement.progress}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={achievement.progress}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          bgcolor: alpha(theme.palette.grey[500], 0.2),
                        }}
                      />
                    </Box>
                  )}
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* History Tab */}
        <TabPanel value={activeTab} index={3}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>
              训练历史
            </Typography>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              sx={{ borderRadius: 3, textTransform: 'none', fontWeight: 600 }}
            >
              导出数据
            </Button>
          </Box>

          <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>
            <CalendarIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              训练历史记录
            </Typography>
            <Typography color="text.secondary" sx={{ mb: 3 }}>
              这里将显示您的详细训练历史和数据分析
            </Typography>
            <Button
              variant="contained"
              sx={{
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 600,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              }}
            >
              查看详细记录
            </Button>
          </Paper>
        </TabPanel>
      </Container>
    </Box>
  );
}
