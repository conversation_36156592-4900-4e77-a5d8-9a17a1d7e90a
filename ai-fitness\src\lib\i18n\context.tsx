'use client';

// I18n Context for client components
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Locale, defaultLocale, getLocaleFromPathname } from './index';
import { useTranslations } from './translations';
import { usePathname } from 'next/navigation';

interface I18nContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function I18nProvider({ 
  children, 
  initialLocale 
}: { 
  children: React.ReactNode;
  initialLocale?: Locale;
}) {
  const pathname = usePathname();
  const [locale, setLocaleState] = useState<Locale>(
    initialLocale || getLocaleFromPathname(pathname) || defaultLocale
  );
  
  const t = useTranslations(locale);
  
  useEffect(() => {
    // Update locale based on pathname changes
    const pathLocale = getLocaleFromPathname(pathname);
    if (pathLocale !== locale) {
      setLocaleState(pathLocale);
    }
  }, [pathname, locale]);
  
  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    // Set cookie
    document.cookie = `locale=${newLocale}; path=/; max-age=31536000`;
    
    // Navigate to new locale URL
    const currentPath = pathname;
    const pathWithoutLocale = currentPath.replace(/^\/[a-z]{2}/, '') || '/';
    const newPath = newLocale === defaultLocale ? pathWithoutLocale : `/${newLocale}${pathWithoutLocale}`;
    
    window.location.href = newPath;
  };
  
  return (
    <I18nContext.Provider value={{ locale, setLocale, t }}>
      {children}
    </I18nContext.Provider>
  );
}

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}
