"use client"

import { useParams } from "next/navigation"
import { Navigation } from "@/components/Navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingPage } from "@/components/ui/loading"
import { ErrorMessage } from "@/components/ui/error"
import {
  ArrowLeft,
  Heart,
  Share2,
  Play,
  Clock,
  Target,
  Zap,
  Users,
  Star,
  Plus,
  BookOpen,
  Calendar,
  CheckCircle
} from "lucide-react"
import { 
  useWorkoutProgram, 
  useJoinWorkoutProgram, 
  useLeaveWorkoutProgram,
  useCreateWorkoutSession 
} from "@/lib/hooks/use-workouts"
import { useAuth } from "@/lib/hooks/use-auth"
import Link from "next/link"

export default function WorkoutProgramDetailPage() {
  const params = useParams()
  const programId = params.id as string
  const { isAuthenticated } = useAuth()

  const { data: program, isLoading, error } = useWorkoutProgram(programId)
  const joinProgramMutation = useJoinWorkoutProgram()
  const leaveProgramMutation = useLeaveWorkoutProgram()
  const createSessionMutation = useCreateWorkoutSession()

  const handleJoinProgram = async () => {
    try {
      await joinProgramMutation.mutateAsync(programId)
    } catch (error) {
      console.error('Failed to join program:', error)
    }
  }

  const handleLeaveProgram = async () => {
    try {
      await leaveProgramMutation.mutateAsync(programId)
    } catch (error) {
      console.error('Failed to leave program:', error)
    }
  }

  const handleStartWorkout = async () => {
    try {
      const session = await createSessionMutation.mutateAsync({
        exercises: [],
        notes: `Started from program: ${program?.title}`
      })
      // Redirect to workout session
      window.location.href = `/workouts/sessions/${session.id}`
    } catch (error) {
      console.error('Failed to start workout:', error)
    }
  }

  if (isLoading) {
    return <LoadingPage />
  }

  if (error || !program) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <ErrorMessage 
            title="Program not found"
            message="The workout program you're looking for doesn't exist or has been removed."
            onRetry={() => window.location.reload()}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/workouts">
            <Button variant="ghost" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Workouts
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Program Header */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-2xl mb-2">{program.title}</CardTitle>
                    <CardDescription className="text-lg">{program.description}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Program Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{program.duration || 'N/A'}</div>
                    <div className="text-sm text-gray-600">Minutes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{program.difficulty || 'N/A'}</div>
                    <div className="text-sm text-gray-600">Difficulty</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{program.weeks?.reduce((total, week) => total + week.sessions.reduce((sessionTotal, session) => sessionTotal + session.exercises.length, 0), 0) || 0}</div>
                    <div className="text-sm text-gray-600">Exercises</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{program.participantCount || 0}</div>
                    <div className="text-sm text-gray-600">Participants</div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button 
                    size="lg" 
                    className="flex items-center gap-2"
                    onClick={handleStartWorkout}
                    disabled={createSessionMutation.isPending}
                  >
                    <Play className="h-5 w-5" />
                    {createSessionMutation.isPending ? 'Starting...' : 'Start Workout'}
                  </Button>
                  
                  {isAuthenticated && (
                    false ? (
                      <Button 
                        variant="outline" 
                        size="lg" 
                        className="flex items-center gap-2"
                        onClick={handleLeaveProgram}
                        disabled={leaveProgramMutation.isPending}
                      >
                        <CheckCircle className="h-5 w-5" />
                        {leaveProgramMutation.isPending ? 'Leaving...' : 'Joined'}
                      </Button>
                    ) : (
                      <Button 
                        variant="outline" 
                        size="lg" 
                        className="flex items-center gap-2"
                        onClick={handleJoinProgram}
                        disabled={joinProgramMutation.isPending}
                      >
                        <Plus className="h-5 w-5" />
                        {joinProgramMutation.isPending ? 'Joining...' : 'Join Program'}
                      </Button>
                    )
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Program Description */}
            {program.description && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    About This Program
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">{program.description}</p>
                </CardContent>
              </Card>
            )}

            {/* Exercises List */}
            {program.weeks && program.weeks.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Program Weeks ({program.weeks.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {program.weeks.map((week: any, index: number) => (
                      <div key={week.id || index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-4">
                          <span className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            {index + 1}
                          </span>
                          <div>
                            <h4 className="font-medium text-gray-900">Week {week.weekNumber || index + 1}</h4>
                            <p className="text-sm text-gray-600">
                              {week.sessions?.length || 0} sessions
                            </p>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          View Week
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Program Goals */}
            {program.goals && program.goals.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Program Goals
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {program.goals.map((goal: any, index: number) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-gray-700">{goal}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Program Info */}
            <Card>
              <CardHeader>
                <CardTitle>Program Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Category</span>
                  <Badge variant="outline">Fitness Program</Badge>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Duration
                  </span>
                  <span className="font-medium">{program.duration || 'N/A'} min</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600 flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Difficulty
                  </span>
                  <span className="font-medium">{program.difficulty || 'N/A'}</span>
                </div>

                {program.equipment && program.equipment.length > 0 && (
                  <div>
                    <span className="text-gray-600 block mb-2">Equipment Needed</span>
                    <div className="flex flex-wrap gap-1">
                      {program.equipment.map((item: any, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {program.rating && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      Rating
                    </span>
                    <span className="font-medium">{program.rating}/5</span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <span className="text-gray-600 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Participants
                  </span>
                  <span className="font-medium">{program.participantCount || 0}</span>
                </div>
              </CardContent>
            </Card>

            {/* Targeted Muscle Groups */}
            {false && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Muscle Groups
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Full Body</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Workout
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Program
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  View Similar
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
