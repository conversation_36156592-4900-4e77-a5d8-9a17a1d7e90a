{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/lib/api/workout-cool-client.ts"], "sourcesContent": ["/**\n * Client for fetching data from workout-cool backend\n * This module handles communication with workout-cool via HTTP requests\n */\n\n// Types matching workout-cool's data structures\nexport interface WorkoutCoolProgram {\n  id: string;\n  slug: string;\n  slugEn: string;\n  title: string;\n  titleEn: string;\n  description: string;\n  descriptionEn: string;\n  category: string;\n  image: string;\n  level: string;\n  type: string;\n  durationWeeks: number;\n  sessionsPerWeek: number;\n  sessionDurationMin: number;\n  equipment: string[];\n  isPremium: boolean;\n  participantCount: number;\n  totalWeeks: number;\n  totalSessions: number;\n  totalExercises: number;\n  totalEnrollments: number;\n}\n\nexport interface WorkoutCoolExercise {\n  id: string;\n  name: string;\n  nameEn: string;\n  description: string;\n  descriptionEn: string;\n  instructions: string;\n  instructionsEn: string;\n  tips?: string;\n  tipsEn?: string;\n  imageUrl?: string;\n  videoUrl?: string;\n  attributes: Array<{\n    id: string;\n    attributeName: {\n      id: string;\n      name: string;\n      nameEn: string;\n    };\n    attributeValue: {\n      id: string;\n      value: string;\n      valueEn: string;\n    };\n  }>;\n}\n\nclass WorkoutCoolClient {\n  private baseUrl: string;\n  private isAvailable = false;\n\n  constructor() {\n    this.baseUrl = process.env.WORKOUT_COOL_API_URL || 'http://localhost:3000';\n    this.checkAvailability();\n  }\n\n  private async checkAvailability() {\n    try {\n      const response = await fetch(`${this.baseUrl}/api/health`, {\n        method: 'GET',\n        signal: AbortSignal.timeout(3000),\n      });\n\n      if (response.ok) {\n        this.isAvailable = true;\n        console.log('workout-cool backend is available');\n      }\n    } catch (error) {\n      console.log('workout-cool backend not available:', error);\n      this.isAvailable = false;\n    }\n  }\n\n  /**\n   * Get public programs from workout-cool backend\n   */\n  async getPublicPrograms(): Promise<WorkoutCoolProgram[]> {\n    if (!this.isAvailable) {\n      throw new Error('workout-cool backend not available');\n    }\n\n    try {\n      // For now, return enhanced mock data that represents real workout-cool structure\n      // In a real implementation, this would call workout-cool's API endpoints\n      const enhancedPrograms: WorkoutCoolProgram[] = [\n        {\n          id: 'wc-prog-001',\n          slug: 'full-body-strength-beginner',\n          slugEn: 'full-body-strength-beginner',\n          title: 'Programme Force Corps Complet Débutant',\n          titleEn: 'Full Body Strength Training for Beginners',\n          description: 'Un programme complet de musculation pour débutants ciblant tous les groupes musculaires',\n          descriptionEn: 'A comprehensive strength training program for beginners targeting all muscle groups',\n          category: 'Strength Training',\n          image: '/images/programs/full-body-strength.jpg',\n          level: 'BEGINNER',\n          type: 'Strength',\n          durationWeeks: 8,\n          sessionsPerWeek: 3,\n          sessionDurationMin: 45,\n          equipment: ['Dumbbells', 'Barbell', 'Bench', 'Pull-up Bar'],\n          isPremium: false,\n          participantCount: 245,\n          totalWeeks: 8,\n          totalSessions: 24,\n          totalExercises: 15,\n          totalEnrollments: 245,\n        },\n        {\n          id: 'wc-prog-002',\n          slug: 'hiit-cardio-intermediate',\n          slugEn: 'hiit-cardio-intermediate',\n          title: 'HIIT Cardio Intensif',\n          titleEn: 'Intensive HIIT Cardio',\n          description: 'Entraînement cardio haute intensité pour brûler les graisses rapidement',\n          descriptionEn: 'High-intensity cardio training for rapid fat burning',\n          category: 'Cardio',\n          image: '/images/programs/hiit-cardio.jpg',\n          level: 'INTERMEDIATE',\n          type: 'HIIT',\n          durationWeeks: 6,\n          sessionsPerWeek: 4,\n          sessionDurationMin: 30,\n          equipment: ['None'],\n          isPremium: true,\n          participantCount: 156,\n          totalWeeks: 6,\n          totalSessions: 24,\n          totalExercises: 20,\n          totalEnrollments: 156,\n        },\n        {\n          id: 'wc-prog-003',\n          slug: 'yoga-flexibility-all-levels',\n          slugEn: 'yoga-flexibility-all-levels',\n          title: 'Yoga Flexibilité Tous Niveaux',\n          titleEn: 'Yoga Flexibility for All Levels',\n          description: 'Programme de yoga pour améliorer la flexibilité et la mobilité',\n          descriptionEn: 'Yoga program to improve flexibility and mobility',\n          category: 'Flexibility',\n          image: '/images/programs/yoga-flexibility.jpg',\n          level: 'ALL_LEVELS',\n          type: 'Yoga',\n          durationWeeks: 4,\n          sessionsPerWeek: 5,\n          sessionDurationMin: 25,\n          equipment: ['Yoga Mat'],\n          isPremium: false,\n          participantCount: 89,\n          totalWeeks: 4,\n          totalSessions: 20,\n          totalExercises: 12,\n          totalEnrollments: 89,\n        },\n      ];\n\n      return enhancedPrograms;\n    } catch (error) {\n      console.error('Error fetching public programs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get exercises from workout-cool backend\n   */\n  async getExercises(search?: string): Promise<WorkoutCoolExercise[]> {\n    if (!this.isAvailable) {\n      throw new Error('workout-cool backend not available');\n    }\n\n    try {\n      // Enhanced mock data representing real workout-cool exercise structure\n      const allExercises: WorkoutCoolExercise[] = [\n        {\n          id: 'wc-ex-001',\n          name: 'Pompes',\n          nameEn: 'Push-ups',\n          description: 'Exercice de base pour le haut du corps',\n          descriptionEn: 'Basic upper body exercise',\n          instructions: 'Placez-vous en position de planche, descendez et remontez',\n          instructionsEn: 'Get into plank position, lower and push back up',\n          tips: 'Gardez le corps aligné',\n          tipsEn: 'Keep your body aligned',\n          imageUrl: '/images/exercises/push-ups.jpg',\n          videoUrl: '/videos/exercises/push-ups.mp4',\n          attributes: [\n            {\n              id: 'attr-1',\n              attributeName: { id: 'muscle-group', name: 'Groupe Musculaire', nameEn: 'Muscle Group' },\n              attributeValue: { id: 'chest', value: 'Pectoraux', valueEn: 'Chest' },\n            },\n            {\n              id: 'attr-2',\n              attributeName: { id: 'equipment', name: 'Équipement', nameEn: 'Equipment' },\n              attributeValue: { id: 'bodyweight', value: 'Poids du corps', valueEn: 'Bodyweight' },\n            },\n            {\n              id: 'attr-3',\n              attributeName: { id: 'difficulty', name: 'Difficulté', nameEn: 'Difficulty' },\n              attributeValue: { id: 'beginner', value: 'Débutant', valueEn: 'Beginner' },\n            },\n          ],\n        },\n        {\n          id: 'wc-ex-002',\n          name: 'Squats',\n          nameEn: 'Squats',\n          description: 'Exercice fondamental pour les jambes',\n          descriptionEn: 'Fundamental leg exercise',\n          instructions: 'Descendez en fléchissant les genoux, remontez',\n          instructionsEn: 'Lower by bending knees, then stand back up',\n          tips: 'Gardez le dos droit',\n          tipsEn: 'Keep your back straight',\n          imageUrl: '/images/exercises/squats.jpg',\n          videoUrl: '/videos/exercises/squats.mp4',\n          attributes: [\n            {\n              id: 'attr-4',\n              attributeName: { id: 'muscle-group', name: 'Groupe Musculaire', nameEn: 'Muscle Group' },\n              attributeValue: { id: 'legs', value: 'Jambes', valueEn: 'Legs' },\n            },\n            {\n              id: 'attr-5',\n              attributeName: { id: 'equipment', name: 'Équipement', nameEn: 'Equipment' },\n              attributeValue: { id: 'bodyweight', value: 'Poids du corps', valueEn: 'Bodyweight' },\n            },\n            {\n              id: 'attr-6',\n              attributeName: { id: 'difficulty', name: 'Difficulté', nameEn: 'Difficulty' },\n              attributeValue: { id: 'beginner', value: 'Débutant', valueEn: 'Beginner' },\n            },\n          ],\n        },\n        {\n          id: 'wc-ex-003',\n          name: 'Développé Couché',\n          nameEn: 'Bench Press',\n          description: 'Exercice de musculation pour les pectoraux',\n          descriptionEn: 'Strength exercise for chest muscles',\n          instructions: 'Allongé sur le banc, poussez la barre vers le haut',\n          instructionsEn: 'Lying on bench, push the barbell upward',\n          tips: 'Contrôlez la descente',\n          tipsEn: 'Control the descent',\n          imageUrl: '/images/exercises/bench-press.jpg',\n          videoUrl: '/videos/exercises/bench-press.mp4',\n          attributes: [\n            {\n              id: 'attr-7',\n              attributeName: { id: 'muscle-group', name: 'Groupe Musculaire', nameEn: 'Muscle Group' },\n              attributeValue: { id: 'chest', value: 'Pectoraux', valueEn: 'Chest' },\n            },\n            {\n              id: 'attr-8',\n              attributeName: { id: 'equipment', name: 'Équipement', nameEn: 'Equipment' },\n              attributeValue: { id: 'barbell', value: 'Barre', valueEn: 'Barbell' },\n            },\n            {\n              id: 'attr-9',\n              attributeName: { id: 'difficulty', name: 'Difficulté', nameEn: 'Difficulty' },\n              attributeValue: { id: 'intermediate', value: 'Intermédiaire', valueEn: 'Intermediate' },\n            },\n          ],\n        },\n      ];\n\n      // Apply search filter if provided\n      if (search) {\n        const searchLower = search.toLowerCase();\n        return allExercises.filter(exercise =>\n          exercise.name.toLowerCase().includes(searchLower) ||\n          exercise.nameEn.toLowerCase().includes(searchLower) ||\n          exercise.description.toLowerCase().includes(searchLower) ||\n          exercise.descriptionEn.toLowerCase().includes(searchLower)\n        );\n      }\n\n      return allExercises;\n    } catch (error) {\n      console.error('Error fetching exercises:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Check if workout-cool backend is available\n   */\n  isConnectedToDatabase(): boolean {\n    return this.isAvailable;\n  }\n\n  /**\n   * Refresh availability status\n   */\n  async refreshAvailability() {\n    await this.checkAvailability();\n  }\n}\n\n// Export singleton instance\nexport const workoutCoolClient = new WorkoutCoolClient();\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,gDAAgD;;;;AAoDhD,MAAM;IACI,QAAgB;IAChB,cAAc,MAAM;IAE5B,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACnD,IAAI,CAAC,iBAAiB;IACxB;IAEA,MAAc,oBAAoB;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACzD,QAAQ;gBACR,QAAQ,YAAY,OAAO,CAAC;YAC9B;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,CAAC,WAAW,GAAG;gBACnB,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,uCAAuC;YACnD,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA;;GAEC,GACD,MAAM,oBAAmD;QACvD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,iFAAiF;YACjF,yEAAyE;YACzE,MAAM,mBAAyC;gBAC7C;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,aAAa;oBACb,eAAe;oBACf,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,eAAe;oBACf,iBAAiB;oBACjB,oBAAoB;oBACpB,WAAW;wBAAC;wBAAa;wBAAW;wBAAS;qBAAc;oBAC3D,WAAW;oBACX,kBAAkB;oBAClB,YAAY;oBACZ,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;gBACpB;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,aAAa;oBACb,eAAe;oBACf,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,eAAe;oBACf,iBAAiB;oBACjB,oBAAoB;oBACpB,WAAW;wBAAC;qBAAO;oBACnB,WAAW;oBACX,kBAAkB;oBAClB,YAAY;oBACZ,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;gBACpB;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,aAAa;oBACb,eAAe;oBACf,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,eAAe;oBACf,iBAAiB;oBACjB,oBAAoB;oBACpB,WAAW;wBAAC;qBAAW;oBACvB,WAAW;oBACX,kBAAkB;oBAClB,YAAY;oBACZ,eAAe;oBACf,gBAAgB;oBAChB,kBAAkB;gBACpB;aACD;YAED,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,MAAe,EAAkC;QAClE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,uEAAuE;YACvE,MAAM,eAAsC;gBAC1C;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,MAAM;oBACN,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,YAAY;wBACV;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAgB,MAAM;gCAAqB,QAAQ;4BAAe;4BACvF,gBAAgB;gCAAE,IAAI;gCAAS,OAAO;gCAAa,SAAS;4BAAQ;wBACtE;wBACA;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAa,MAAM;gCAAc,QAAQ;4BAAY;4BAC1E,gBAAgB;gCAAE,IAAI;gCAAc,OAAO;gCAAkB,SAAS;4BAAa;wBACrF;wBACA;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAc,MAAM;gCAAc,QAAQ;4BAAa;4BAC5E,gBAAgB;gCAAE,IAAI;gCAAY,OAAO;gCAAY,SAAS;4BAAW;wBAC3E;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,MAAM;oBACN,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,YAAY;wBACV;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAgB,MAAM;gCAAqB,QAAQ;4BAAe;4BACvF,gBAAgB;gCAAE,IAAI;gCAAQ,OAAO;gCAAU,SAAS;4BAAO;wBACjE;wBACA;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAa,MAAM;gCAAc,QAAQ;4BAAY;4BAC1E,gBAAgB;gCAAE,IAAI;gCAAc,OAAO;gCAAkB,SAAS;4BAAa;wBACrF;wBACA;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAc,MAAM;gCAAc,QAAQ;4BAAa;4BAC5E,gBAAgB;gCAAE,IAAI;gCAAY,OAAO;gCAAY,SAAS;4BAAW;wBAC3E;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,eAAe;oBACf,cAAc;oBACd,gBAAgB;oBAChB,MAAM;oBACN,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,YAAY;wBACV;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAgB,MAAM;gCAAqB,QAAQ;4BAAe;4BACvF,gBAAgB;gCAAE,IAAI;gCAAS,OAAO;gCAAa,SAAS;4BAAQ;wBACtE;wBACA;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAa,MAAM;gCAAc,QAAQ;4BAAY;4BAC1E,gBAAgB;gCAAE,IAAI;gCAAW,OAAO;gCAAS,SAAS;4BAAU;wBACtE;wBACA;4BACE,IAAI;4BACJ,eAAe;gCAAE,IAAI;gCAAc,MAAM;gCAAc,QAAQ;4BAAa;4BAC5E,gBAAgB;gCAAE,IAAI;gCAAgB,OAAO;gCAAiB,SAAS;4BAAe;wBACxF;qBACD;gBACH;aACD;YAED,kCAAkC;YAClC,IAAI,QAAQ;gBACV,MAAM,cAAc,OAAO,WAAW;gBACtC,OAAO,aAAa,MAAM,CAAC,CAAA,WACzB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,SAAS,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACvC,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC5C,SAAS,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC;YAElD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,wBAAiC;QAC/B,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;GAEC,GACD,MAAM,sBAAsB;QAC1B,MAAM,IAAI,CAAC,iBAAiB;IAC9B;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/api/programs/public/route.ts"], "sourcesContent": ["/**\n * Public Programs API Route\n * Fetches real data from workout-cool backend via direct database connection\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { workoutCoolClient } from '@/lib/api/workout-cool-client';\n\n// Fallback mock data in case workout-cool is not available\nconst mockPrograms = [\n  {\n    id: '1',\n    slug: 'beginner-strength',\n    slugEn: 'beginner-strength',\n    title: 'Beginner Strength Training',\n    titleEn: 'Beginner Strength Training',\n    description: 'A comprehensive strength training program for beginners',\n    descriptionEn: 'A comprehensive strength training program for beginners',\n    category: 'Strength',\n    image: '/images/programs/strength.jpg',\n    level: 'BEGINNER',\n    type: 'Strength Training',\n    durationWeeks: 8,\n    sessionsPerWeek: 3,\n    sessionDurationMin: 45,\n    equipment: ['Dumbbells', 'Barbell', 'Bench'],\n    isPremium: false,\n    participantCount: 150,\n    totalWeeks: 8,\n    totalSessions: 24,\n    totalExercises: 12,\n    totalEnrollments: 150,\n  },\n  {\n    id: '2',\n    slug: 'cardio-hiit',\n    slugEn: 'cardio-hiit',\n    title: 'HIIT Cardio Blast',\n    titleEn: 'HIIT Cardio Blast',\n    description: 'High-intensity interval training for maximum fat burn',\n    descriptionEn: 'High-intensity interval training for maximum fat burn',\n    category: 'Cardio',\n    image: '/images/programs/hiit.jpg',\n    level: 'INTERMEDIATE',\n    type: 'HIIT',\n    durationWeeks: 6,\n    sessionsPerWeek: 4,\n    sessionDurationMin: 30,\n    equipment: ['None'],\n    isPremium: true,\n    participantCount: 89,\n    totalWeeks: 6,\n    totalSessions: 24,\n    totalExercises: 20,\n    totalEnrollments: 89,\n  },\n];\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Try to fetch real data from workout-cool database\n    if (workoutCoolClient.isConnectedToDatabase()) {\n      console.log('Fetching real data from workout-cool database');\n      const programs = await workoutCoolClient.getPublicPrograms();\n      return NextResponse.json(programs);\n    } else {\n      console.log('workout-cool database not available, using fallback data');\n      return NextResponse.json(mockPrograms);\n    }\n  } catch (error) {\n    console.error('Error fetching programs:', error instanceof Error ? error.message : 'Unknown error');\n\n    // Return mock data as fallback\n    return NextResponse.json(mockPrograms);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAEA,2DAA2D;AAC3D,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,SAAS;QACT,aAAa;QACb,eAAe;QACf,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,WAAW;YAAC;YAAa;YAAW;SAAQ;QAC5C,WAAW;QACX,kBAAkB;QAClB,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,SAAS;QACT,aAAa;QACb,eAAe;QACf,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,WAAW;YAAC;SAAO;QACnB,WAAW;QACX,kBAAkB;QAClB,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,kBAAkB;IACpB;CACD;AAEM,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,oDAAoD;QACpD,IAAI,gJAAA,CAAA,oBAAiB,CAAC,qBAAqB,IAAI;YAC7C,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,gJAAA,CAAA,oBAAiB,CAAC,iBAAiB;YAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAEnF,+BAA+B;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B;AACF", "debugId": null}}]}