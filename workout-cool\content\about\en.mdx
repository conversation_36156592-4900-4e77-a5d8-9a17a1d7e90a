import Link from "next/link";

# About Workout.cool

## Why Workout.cool?

Workout.cool was born out of the desire to offer a reliable, modern, and actively maintained workout platform, after the original project <WorkoutLol variant="muted" /> was abandoned.

## The Story

Workout.cool is the result of a community-driven adventure.

I was the **first open source contributor** to the <WorkoutLol variant="muted" /> project.

This means I saw the project *come to life*, *grow*, then get **sold** and ultimately **abandoned** by its new owner.

Like many users, I felt a **deep frustration** and a *sense of abandonment* watching a tool I had contributed so much to disappear, with feature requests left unanswered and growing old.

---

*For months*, I tried to contact the new owner—**never receiving a single reply** despite many attempts (*about 15*).

Faced with this **silence** and the **distress of the community**, I decided to **take matters into my own hands**:

> Rather than let all this work vanish, **I relaunched an even more ambitious, modern, and open project for everyone.**

This project is not driven by profit, but by **passion** and a desire to serve the open source fitness community.

**Someone had to save the community—_I decided to be that someone!_**

## Open Source & Community

Workout.cool is open source, ensuring transparency, modularity, and scalability.  
Everyone is welcome to contribute—code, documentation, or ideas!

- [See the project on GitHub](https://github.com/Snouzy/workout-cool)
- [Buy a coffee to support](https://ko-fi.com/workoutcool)

## Join the Mission!

Want to contribute, suggest a feature, or simply support the project?  
Contact us or open an issue on GitHub!

**[<EMAIL>](mailto:<EMAIL>)**
