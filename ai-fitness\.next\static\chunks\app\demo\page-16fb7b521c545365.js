(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[436],{2382:(e,s,n)=>{Promise.resolve().then(n.bind(n,8035))},8035:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>C});var i=n(5155),a=n(2115),t=n(7516),l=n(5220),c=n(6126),r=n(6695),d=n(6517),x=n(4449),m=n(381),o=n(3904),h=n(4186),j=n(3861),u=n(9633),f=n(646),N=n(5339),g=n(3962),y=n(4290);function p(){let[e,s]=(0,a.useState)(!1),n=(0,g.eC)(),t=(0,y.dW)(),j=(0,g.wZ)(),{syncPendingData:u,forceSyncAll:f,clearPendingSync:N}=(0,y.aF)(),p=async()=>{try{await u()}catch(e){console.error("Manual sync failed:",e)}},v=async()=>{try{await f()}catch(e){console.error("Force sync failed:",e)}};return(0,i.jsxs)(r.Zp,{className:"w-full max-w-md",children:[(0,i.jsxs)(r.aR,{className:"pb-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[n.isOnline?(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-600"}):(0,i.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(r.ZB,{className:"text-sm",children:n.isOnline?"Online":"Offline"})]}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>s(!e),children:(0,i.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,i.jsx)(r.BT,{children:j?(0,i.jsxs)("span",{className:"text-orange-600",children:[n.pendingSync.length," items pending sync"]}):"All data synchronized"})]}),e&&(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{children:"Sync Status:"}),(0,i.jsx)(c.E,{variant:j?"destructive":"default",children:j?"Pending":"Up to date"})]}),n.lastSyncTime&&(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{children:"Last Sync:"}),(0,i.jsx)("span",{className:"text-gray-600",children:new Date(n.lastSyncTime).toLocaleTimeString()})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{children:"Pending Items:"}),(0,i.jsx)("span",{className:"text-gray-600",children:n.pendingSync.length})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(l.$,{onClick:p,disabled:!n.isOnline||t.syncInProgress,className:"w-full",size:"sm",children:t.syncInProgress?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Syncing..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Sync Now"]})}),j&&(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(l.$,{onClick:v,disabled:!n.isOnline,variant:"outline",size:"sm",className:"flex-1",children:"Force Sync"}),(0,i.jsx)(l.$,{onClick:N,variant:"destructive",size:"sm",className:"flex-1",children:"Clear Queue"})]})]}),n.pendingSync.length>0&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h4",{className:"text-sm font-medium",children:"Pending Items:"}),(0,i.jsxs)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:[n.pendingSync.slice(0,5).map(e=>(0,i.jsxs)("div",{className:"flex items-center justify-between text-xs p-2 bg-gray-50 rounded",children:[(0,i.jsxs)("span",{className:"capitalize",children:[e.action," ",e.type]}),(0,i.jsx)(h.A,{className:"h-3 w-3 text-gray-400"})]},e.id)),n.pendingSync.length>5&&(0,i.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["+",n.pendingSync.length-5," more items"]})]})]})]})]})}function v(){let[e,s]=(0,a.useState)(!1),n=(0,g.E$)(),t=(0,g.J9)(),d=(0,g.t0)(),{markNotificationRead:x,clearNotifications:o,updateSettings:h}=(0,g.CU)();return(0,i.jsxs)(r.Zp,{className:"w-full max-w-md",children:[(0,i.jsx)(r.aR,{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[d.notifications.workoutReminders?(0,i.jsx)(j.A,{className:"h-4 w-4 text-blue-600"}):(0,i.jsx)(u.A,{className:"h-4 w-4 text-gray-400"}),(0,i.jsx)(r.ZB,{className:"text-sm",children:"Notifications"}),t>0&&(0,i.jsx)(c.E,{variant:"destructive",className:"text-xs",children:t})]}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>s(!e),children:(0,i.jsx)(m.A,{className:"h-4 w-4"})})]})}),e&&(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"space-y-2",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm",children:"Workout Reminders"}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{h({notifications:{...d.notifications,workoutReminders:!d.notifications.workoutReminders}})},children:d.notifications.workoutReminders?"On":"Off"})]})}),n.length>0&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h4",{className:"text-sm font-medium",children:"Recent"}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",onClick:o,children:"Clear All"})]}),(0,i.jsx)("div",{className:"space-y-1 max-h-40 overflow-y-auto",children:n.slice(0,5).map(e=>(0,i.jsx)("div",{className:"p-2 rounded text-xs ".concat(e.read?"bg-gray-50":"bg-blue-50"),onClick:()=>x(e.id),children:(0,i.jsxs)("div",{className:"flex items-start gap-2",children:["success"===e.type&&(0,i.jsx)(f.A,{className:"h-3 w-3 text-green-600 mt-0.5"}),"error"===e.type&&(0,i.jsx)(N.A,{className:"h-3 w-3 text-red-600 mt-0.5"}),"warning"===e.type&&(0,i.jsx)(N.A,{className:"h-3 w-3 text-orange-600 mt-0.5"}),"info"===e.type&&(0,i.jsx)(N.A,{className:"h-3 w-3 text-blue-600 mt-0.5"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("div",{className:"font-medium",children:e.title}),(0,i.jsx)("div",{className:"text-gray-600",children:e.message}),(0,i.jsx)("div",{className:"text-gray-400 mt-1",children:new Date(e.timestamp).toLocaleTimeString()})]})]})},e.id))})]}),0===n.length&&(0,i.jsx)("div",{className:"text-center py-4 text-gray-500 text-sm",children:"No notifications"})]})]})}var w=n(1539),b=n(1243),S=n(4416),k=n(1284),A=n(4213);function C(){let[e,s]=(0,a.useState)(""),{addNotification:n,updateSettings:d,updateWorkoutPreferences:x,clearNotifications:h}=(0,g.CU)(),j=(0,g.t0)(),u=(0,g.n8)(),N=(0,g.E$)(),C=(0,g.eC)(),{addToSyncQueue:E}=(0,y.aF)(),O=e=>{n({type:e,...{success:{title:"Success!",message:"Operation completed successfully"},error:{title:"Error!",message:"Something went wrong"},warning:{title:"Warning!",message:"Please check your settings"},info:{title:"Info",message:"Here is some useful information"}}[e]})};return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(t.V,{}),(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"State Management Demo"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Demonstration of Zustand store, React Query, and sync management"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-5 w-5"}),"App Settings"]}),(0,i.jsx)(r.BT,{children:"Global application settings managed by Zustand"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Theme:"}),(0,i.jsx)(c.E,{variant:"outline",children:j.theme})]}),(0,i.jsx)(l.$,{onClick:()=>{d({theme:"light"===j.theme?"dark":"light"})},variant:"outline",className:"w-full",children:"Toggle Theme"}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Workout Reminders:"}),(0,i.jsx)(c.E,{variant:j.notifications.workoutReminders?"default":"secondary",children:j.notifications.workoutReminders?"On":"Off"})]}),(0,i.jsx)(l.$,{onClick:()=>{d({notifications:{...j.notifications,workoutReminders:!j.notifications.workoutReminders}})},variant:"outline",className:"w-full",children:"Toggle Notifications"}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Units:"}),(0,i.jsx)(c.E,{variant:"outline",children:j.units})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Language:"}),(0,i.jsx)(c.E,{variant:"outline",children:j.language})]})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(w.A,{className:"h-5 w-5"}),"Workout Preferences"]}),(0,i.jsx)(r.BT,{children:"User workout preferences stored in Zustand"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Preferred Difficulty:"}),(0,i.jsx)(c.E,{variant:"outline",children:u.preferredDifficulty})]}),(0,i.jsx)(l.$,{onClick:()=>{let e=["BEGINNER","INTERMEDIATE","ADVANCED"],s=(e.indexOf(u.preferredDifficulty)+1)%e.length;x({preferredDifficulty:e[s]})},variant:"outline",className:"w-full",children:"Change Difficulty"}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Default Duration:"}),(0,i.jsxs)(c.E,{variant:"outline",children:[u.defaultDuration," min"]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Rest Time:"}),(0,i.jsxs)(c.E,{variant:"outline",children:[u.restTimeBetweenSets,"s"]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Auto Start Next:"}),(0,i.jsx)(c.E,{variant:u.autoStartNextExercise?"default":"secondary",children:u.autoStartNextExercise?"Yes":"No"})]})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(b.A,{className:"h-5 w-5"}),"Notifications Demo"]}),(0,i.jsx)(r.BT,{children:"Test the notification system"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,i.jsxs)(l.$,{onClick:()=>O("success"),variant:"outline",className:"text-green-600",children:[(0,i.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Success"]}),(0,i.jsxs)(l.$,{onClick:()=>O("error"),variant:"outline",className:"text-red-600",children:[(0,i.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Error"]}),(0,i.jsxs)(l.$,{onClick:()=>O("warning"),variant:"outline",className:"text-orange-600",children:[(0,i.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Warning"]}),(0,i.jsxs)(l.$,{onClick:()=>O("info"),variant:"outline",className:"text-blue-600",children:[(0,i.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Info"]})]}),(0,i.jsx)(l.$,{onClick:h,variant:"destructive",className:"w-full",children:"Clear All Notifications"}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:["Current notifications: ",N.length]})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(A.A,{className:"h-5 w-5"}),"Sync Management Demo"]}),(0,i.jsx)(r.BT,{children:"Test offline sync functionality"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Online Status:"}),(0,i.jsx)(c.E,{variant:C.isOnline?"default":"destructive",children:C.isOnline?"Online":"Offline"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:"Pending Sync Items:"}),(0,i.jsx)(c.E,{variant:"outline",children:C.pendingSync.length})]}),(0,i.jsxs)(l.$,{onClick:()=>{E({type:"workout",action:"create",data:{type:"session",name:"Test Workout",duration:30,exercises:[]}})},variant:"outline",className:"w-full",children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Test Sync Item"]}),C.lastSyncTime&&(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:["Last sync: ",new Date(C.lastSyncTime).toLocaleString()]})]})]}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsx)(p,{})}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsx)(v,{})})]}),(0,i.jsxs)(r.Zp,{className:"mt-6",children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"State Debug Information"}),(0,i.jsx)(r.BT,{children:"Current state values for debugging"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Settings:"}),(0,i.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto",children:JSON.stringify(j,null,2)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Workout Preferences:"}),(0,i.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto",children:JSON.stringify(u,null,2)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Offline State:"}),(0,i.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto",children:JSON.stringify(C,null,2)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Recent Notifications:"}),(0,i.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32",children:JSON.stringify(N.slice(0,3),null,2)})]})]})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[76,96,358],()=>s(2382)),_N_E=e.O()}]);