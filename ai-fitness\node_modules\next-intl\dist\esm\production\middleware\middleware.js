import{NextResponse as e}from"next/server";import{receiveRoutingConfig as t}from"../routing/config.js";import{HEADER_LOCALE_NAME as r}from"../shared/constants.js";import{matchesPathname as o,normalizeTrailingSlash as a,getLocalePrefix as l,getLocalizedTemplate as n}from"../shared/utils.js";import s from"./getAlternateLinksHeaderValue.js";import i from"./resolveLocale.js";import c from"./syncCookie.js";import{sanitizePathname as d,isLocaleSupportedOnDomain as f,getNormalizedPathname as m,getPathnameMatch as h,getInternalTemplate as x,formatTemplatePathname as p,formatPathname as u,getBestMatchingDomain as U,applyBasePath as P,getLocaleAsPrefix as g}from"./utils.js";function v(v){const L=t(v);return function(t){let v;try{v=decodeURI(t.nextUrl.pathname)}catch{return e.next()}const j=d(v),{domain:w,locale:k}=i(L,t.headers,t.cookies,j),b=w?w.defaultLocale===k:k===L.defaultLocale,R=L.domains?.filter((e=>f(k,e)))||[],q=null!=L.domains&&!w;function y(o){const a=new URL(o,t.url);t.nextUrl.basePath&&(a.pathname=P(a.pathname,t.nextUrl.basePath));const l=new Headers(t.headers);return l.set(r,k),e.rewrite(a,{request:{headers:l}})}function H(r,o){const l=new URL(r,t.url);if(l.pathname=a(l.pathname),R.length>0&&!o&&w){const e=U(w,k,R);e&&(o=e.domain,e.defaultLocale===k&&"as-needed"===L.localePrefix.mode&&(l.pathname=m(l.pathname,L.locales,L.localePrefix)))}if(o&&(l.host=o,t.headers.get("x-forwarded-host"))){l.protocol=t.headers.get("x-forwarded-proto")??t.nextUrl.protocol;const e=o.split(":")[1];l.port=e??t.headers.get("x-forwarded-port")??""}return t.nextUrl.basePath&&(l.pathname=P(l.pathname,t.nextUrl.basePath)),T=!0,e.redirect(l.toString())}const z=m(j,L.locales,L.localePrefix),A=h(j,L.locales,L.localePrefix,w),C=null!=A,I="never"===L.localePrefix.mode||b&&"as-needed"===L.localePrefix.mode;let N,S,T,V=z;const B=L.pathnames;if(B){let e;if([e,S]=x(B,z,k),S){const r=B[S],a=n(r,k,S);if(o(a,z))V=p(z,a,S);else{let o;o=e?n(r,e,S):S;const s=I?void 0:l(k,L.localePrefix),i=p(z,o,a);N=H(u(i,s,t.nextUrl.search))}}}if(!N)if("/"!==V||C){const e=u(V,g(k),t.nextUrl.search);if(C){const r=u(z,A.prefix,t.nextUrl.search);if("never"===L.localePrefix.mode)N=H(u(z,void 0,t.nextUrl.search));else if(A.exact)if(b&&I)N=H(u(z,void 0,t.nextUrl.search));else if(L.domains){const t=U(w,A.locale,R);N=w?.domain===t?.domain||q?y(e):H(r,t?.domain)}else N=y(e);else N=H(r)}else N=I?y(e):H(u(z,l(k,L.localePrefix),t.nextUrl.search))}else N=I?y(u(V,g(k),t.nextUrl.search)):H(u(z,l(k,L.localePrefix),t.nextUrl.search));return c(t,N,k,L,w),!T&&"never"!==L.localePrefix.mode&&L.alternateLinks&&L.locales.length>1&&N.headers.set("Link",s({routing:L,internalTemplateName:S,localizedPathnames:null!=S&&B?B[S]:void 0,request:t,resolvedLocale:k})),N}}export{v as default};
