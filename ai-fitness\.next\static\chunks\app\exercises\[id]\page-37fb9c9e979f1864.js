(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[652],{813:(s,e,a)=>{Promise.resolve().then(a.bind(a,3262))},3262:(s,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>g});var i=a(5155),l=a(5695),n=a(7516),c=a(6695),r=a(5220),t=a(6126),d=a(7023),x=a(9579),m=a(7550),h=a(1976),j=a(6516),o=a(5690),u=a(4616),p=a(2659),N=a(4227),v=a(6874),f=a.n(v);function g(){var s;let e=(0,l.useParams)().id,{data:a,isLoading:v,error:g}=(0,N.A)(e);return v?(0,i.jsx)(d.AV,{}):g||!a?(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(n.V,{}),(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsx)(x.Kw,{title:"Exercise not found",message:"The exercise you're looking for doesn't exist or has been removed.",onRetry:()=>window.location.reload()})})]}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(n.V,{}),(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsx)(f(),{href:"/exercises",children:(0,i.jsxs)(r.$,{variant:"ghost",className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-4 w-4"}),"Back to Exercises"]})})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)(c.Zp,{children:[(0,i.jsx)(c.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{className:"text-2xl mb-2",children:a.name}),a.nameEn&&(0,i.jsx)(c.BT,{className:"text-lg",children:a.nameEn})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(r.$,{variant:"outline",size:"icon",children:(0,i.jsx)(h.A,{className:"h-4 w-4"})}),(0,i.jsx)(r.$,{variant:"outline",size:"icon",children:(0,i.jsx)(j.A,{className:"h-4 w-4"})})]})]})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:null==(s=a.attributes)?void 0:s.map((s,e)=>{var a,l;return(0,i.jsx)(t.E,{variant:"secondary",children:(null==(a=s.attributeName)?void 0:a.name)||(null==(l=s.attributeValue)?void 0:l.value)},e)})}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsxs)(r.$,{size:"lg",className:"flex items-center gap-2",children:[(0,i.jsx)(o.A,{className:"h-5 w-5"}),"Start Exercise"]}),(0,i.jsxs)(r.$,{variant:"outline",size:"lg",className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"h-5 w-5"}),"Add to Workout"]})]})]})]}),a.description&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsx)(c.aR,{children:(0,i.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(p.A,{className:"h-5 w-5"}),"Description"]})}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed",children:a.description})})]}),a.introduction&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsx)(c.aR,{children:(0,i.jsx)(c.ZB,{children:"Instructions"})}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,i.jsx)("p",{children:a.introduction})})})]}),a.description&&(0,i.jsxs)(c.Zp,{children:[(0,i.jsx)(c.aR,{children:(0,i.jsx)(c.ZB,{children:"Description"})}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,i.jsx)("p",{children:a.description})})})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)(c.Zp,{children:[(0,i.jsx)(c.aR,{children:(0,i.jsx)(c.ZB,{children:"Exercise Info"})}),(0,i.jsxs)(c.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Exercise ID"}),(0,i.jsx)("span",{className:"font-medium text-sm",children:a.id})]}),a.createdAt&&(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Created"}),(0,i.jsx)("span",{className:"font-medium text-sm",children:new Date(a.createdAt).toLocaleDateString()})]}),a.updatedAt&&(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Updated"}),(0,i.jsx)("span",{className:"font-medium text-sm",children:new Date(a.updatedAt).toLocaleDateString()})]})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsx)(c.aR,{children:(0,i.jsx)(c.ZB,{children:"Quick Actions"})}),(0,i.jsxs)(c.Wu,{className:"space-y-3",children:[(0,i.jsxs)(r.$,{variant:"outline",className:"w-full justify-start",children:[(0,i.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add to Favorites"]}),(0,i.jsxs)(r.$,{variant:"outline",className:"w-full justify-start",children:[(0,i.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Share Exercise"]}),(0,i.jsxs)(r.$,{variant:"outline",className:"w-full justify-start",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"View Similar"]})]})]})]})]})]})]})}}},s=>{var e=e=>s(s.s=e);s.O(0,[76,96,358],()=>e(813)),_N_E=s.O()}]);