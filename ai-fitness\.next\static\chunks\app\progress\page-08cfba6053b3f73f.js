(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{922:(e,s,t)=>{Promise.resolve().then(t.bind(t,2985))},2985:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var a=t(5155),l=t(2115),r=t(7516),i=t(6695),n=t(5220),c=t(6126),d=t(7023),x=t(4092),o=t(1788),m=t(2713),h=t(6785),j=t(9037),g=t(9074),u=t(3109),y=t(9397),p=t(4186),N=t(5607),v=t(1539),f=t(2657),b=t(4616),w=t(2960),k=t(5041),A=t(2450);let D={all:["progress"],records:()=>[...D.all,"records"],record:e=>[...D.records(),e],recordsList:e=>[...D.records(),"list",e],stats:e=>[...D.all,"stats",e],workoutStats:e=>[...D.all,"workout-stats",e],exerciseProgress:(e,s)=>[...D.all,"exercise",e,s],bodyMeasurements:e=>[...D.all,"body-measurements",e],goals:()=>[...D.all,"goals"],achievements:()=>[...D.all,"achievements"],calendar:(e,s)=>[...D.all,"calendar",e,s],personalRecords:()=>[...D.all,"personal-records"],strengthProgression:e=>[...D.all,"strength-progression",e],workoutIntensity:e=>[...D.all,"workout-intensity",e]};var W=t(5251);function S(){var e,s;let[t,S]=(0,l.useState)("overview"),[T,q]=(0,l.useState)("month"),{isAuthenticated:B}=(0,W.As)(),{data:L,isLoading:P}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return(0,w.I)({queryKey:D.stats(e),queryFn:()=>A.n.getProgressStats(e),staleTime:3e5})}(T),{data:R,isLoading:C}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return(0,w.I)({queryKey:D.workoutStats(e),queryFn:()=>A.n.getWorkoutStats(e),staleTime:3e5})}(T),{data:Z,isLoading:F}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return(0,w.I)({queryKey:D.bodyMeasurements(e),queryFn:()=>A.n.getBodyMeasurements(e),staleTime:6e5})}(T),{data:I,isLoading:E}=(0,w.I)({queryKey:D.goals(),queryFn:()=>A.n.getFitnessGoals(),staleTime:3e5}),{data:M,isLoading:K}=(0,w.I)({queryKey:D.achievements(),queryFn:()=>A.n.getAchievements(),staleTime:9e5}),{data:_,isLoading:G}=(0,w.I)({queryKey:D.personalRecords(),queryFn:()=>A.n.getPersonalRecords(),staleTime:6e5}),{data:O,isLoading:U}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return(0,w.I)({queryKey:D.workoutIntensity(e),queryFn:()=>A.n.getWorkoutIntensity(e),staleTime:6e5})}(T),V=new Date,{data:$,isLoading:Y}=(e=V.getFullYear(),s=V.getMonth()+1,(0,w.I)({queryKey:D.calendar(e,s),queryFn:()=>A.n.getWorkoutCalendar(e,s),staleTime:6e5})),H=(0,k.n)({mutationFn:e=>{let{format:s,period:t}=e;return A.n.exportProgressData(s,t)},onSuccess:(e,s)=>{let{format:t}=s,a=window.URL.createObjectURL(e),l=document.createElement("a");l.href=a,l.download="progress-data.".concat(t),document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(a)}}),Q=e=>{H.mutate({format:e,period:"all"})};return B?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(r.V,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Progress Dashboard"}),(0,a.jsx)("p",{className:"text-xl text-gray-600",children:"Track your fitness journey and celebrate your achievements"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex bg-white rounded-lg p-1 shadow-sm",children:["week","month","year"].map(e=>(0,a.jsx)("button",{onClick:()=>q(e),className:"px-3 py-1 rounded-md text-sm font-medium transition-colors ".concat(T===e?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Q("csv"),disabled:H.isPending,className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),H.isPending?"Exporting...":"Export"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm",children:[{id:"overview",label:"Overview",icon:m.A},{id:"goals",label:"Goals",icon:h.A},{id:"achievements",label:"Achievements",icon:j.A},{id:"history",label:"History",icon:g.A}].map(e=>(0,a.jsxs)("button",{onClick:()=>S(e.id),className:"flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat(t===e.id?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"),children:[(0,a.jsx)(e.icon,{className:"h-4 w-4"}),e.label]},e.id))}),"overview"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Workouts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C?"...":(null==R?void 0:R.totalWorkouts)||0}),(0,a.jsxs)("p",{className:"text-xs text-green-600 flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),"+12% from last ",T]})]}),(0,a.jsx)(y.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Duration"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C?"...":"".concat(Math.round(((null==R?void 0:R.totalDuration)||0)/60),"h")}),(0,a.jsxs)("p",{className:"text-xs text-green-600 flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),"+8% from last ",T]})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Calories Burned"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C?"...":(null==R?void 0:R.caloriesBurned)||0}),(0,a.jsxs)("p",{className:"text-xs text-green-600 flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),"+15% from last ",T]})]}),(0,a.jsx)(N.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Current Streak"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C?"...":(null==R?void 0:R.streakDays)||0}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"days"})]}),(0,a.jsx)(v.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Weekly Progress"}),(0,a.jsx)(i.BT,{children:"Your workout activity over time"})]}),(0,a.jsx)(i.Wu,{children:C?(0,a.jsx)(d.B0,{}):(null==R?void 0:R.weeklyProgress)?(0,a.jsx)("div",{className:"space-y-4",children:R.weeklyProgress.slice(-8).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e.date}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm",children:e.workouts})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsxs)("span",{className:"text-sm",children:[Math.round(e.duration/60),"h"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)("span",{className:"text-sm",children:e.calories})]})]})]},s))}):(0,a.jsx)(x.hQ,{})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Workout Calendar"}),(0,a.jsx)(i.BT,{children:"This month's activity"})]}),(0,a.jsx)(i.Wu,{children:Y?(0,a.jsx)(d.B0,{}):$?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-7 gap-2 text-center",children:[["S","M","T","W","T","F","S"].map(e=>(0,a.jsx)("div",{className:"text-xs font-medium text-gray-500 p-2",children:e},e)),$.calendar.slice(0,35).map((e,s)=>(0,a.jsx)("div",{className:"p-2 text-xs rounded ".concat(e.hasWorkout?"bg-blue-100 text-blue-800 font-medium":"text-gray-400"),children:new Date(e.date).getDate()},s))]}),(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-600",children:$.monthStats.totalWorkouts}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Workouts"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-green-600",children:[Math.round($.monthStats.totalDuration/60),"h"]}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Duration"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-lg font-bold text-orange-600",children:$.monthStats.activeDays}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Active Days"})]})]})})]}):(0,a.jsx)(x.hQ,{})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Recent Personal Records"}),(0,a.jsx)(i.BT,{children:"Your latest achievements"})]}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"View All"]})]})}),(0,a.jsx)(i.Wu,{children:G?(0,a.jsx)(d.B0,{}):(null==_?void 0:_.recentPRs)&&_.recentPRs.length>0?(0,a.jsx)("div",{className:"space-y-4",children:_.recentPRs.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.exerciseName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.recordType,": ",e.value," ",e.unit]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-green-600",children:["+",e.improvement," ",e.unit]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:new Date(e.achievedDate).toLocaleDateString()})]})]},s))}):(0,a.jsx)(x.cG,{})})]})]}),"goals"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Fitness Goals"}),(0,a.jsxs)(n.$,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Add Goal"]})]}),E?(0,a.jsx)(d.B0,{}):(null==I?void 0:I.goals)&&I.goals.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:I.goals.map(e=>(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{className:"text-lg",children:e.title}),(0,a.jsx)(i.BT,{children:e.description})]}),(0,a.jsx)(c.E,{variant:"completed"===e.status?"default":"secondary",children:e.status})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(e.progress,100),"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[e.currentValue," / ",e.targetValue," ",e.unit]}),(0,a.jsxs)("span",{children:["Due: ",new Date(e.deadline).toLocaleDateString()]})]})]})})]},e.id))}):(0,a.jsx)(x.qt,{})]}),"achievements"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Achievements & Badges"}),K?(0,a.jsx)(d.B0,{}):M?(0,a.jsxs)("div",{className:"space-y-8",children:[M.milestones&&M.milestones.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Milestones"}),(0,a.jsx)("div",{className:"space-y-3",children:M.milestones.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Achieved on ",new Date(e.achievedDate).toLocaleDateString()]})]})]},e.id))})]}),M.badges&&M.badges.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Badges"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:M.badges.map(e=>(0,a.jsx)(i.Zp,{className:e.earnedDate?"border-green-200":"opacity-60",children:(0,a.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:e.icon}),(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),e.earnedDate?(0,a.jsxs)(c.E,{variant:"default",children:["Earned ",new Date(e.earnedDate).toLocaleDateString()]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat((e.progress||0)/e.requirement*100,"%")}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.progress||0," / ",e.requirement]})]})]})},e.id))})]})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(j.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No achievements yet"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Start working out to earn your first badges!"})]})]}),"history"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Workout History"}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>Q("csv"),children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Export History"]})]}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Workout history"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your detailed workout history will appear here"})]})})})]})]})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(r.V,{}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Sign in to view your progress"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Track your fitness journey with detailed analytics and insights"}),(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsx)("a",{href:"/auth/signin",children:"Sign In"})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[76,96,358],()=>s(922)),_N_E=e.O()}]);