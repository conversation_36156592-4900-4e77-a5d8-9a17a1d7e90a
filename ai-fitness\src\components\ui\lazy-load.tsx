'use client';

import { useState, useEffect, useRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface LazyLoadProps {
  children: ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  fallback?: ReactNode;
  once?: boolean;
  onIntersect?: () => void;
}

export function LazyLoad({
  children,
  className,
  threshold = 0.1,
  rootMargin = '50px',
  fallback = null,
  once = true,
  onIntersect,
}: LazyLoadProps) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting;
        setIsIntersecting(isVisible);
        
        if (isVisible) {
          setHasIntersected(true);
          onIntersect?.();
          
          if (once) {
            observer.unobserve(element);
          }
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, once, onIntersect]);

  const shouldRender = once ? hasIntersected : isIntersecting;

  return (
    <div ref={ref} className={cn(className)}>
      {shouldRender ? children : fallback}
    </div>
  );
}

// Lazy load wrapper for heavy components
export function LazyComponent({
  children,
  loading,
  className,
  ...props
}: LazyLoadProps & { loading?: ReactNode }) {
  return (
    <LazyLoad
      className={className}
      fallback={
        loading || (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )
      }
      {...props}
    >
      {children}
    </LazyLoad>
  );
}

// Lazy load for images with skeleton
export function LazyImageContainer({
  children,
  className,
  skeletonClassName,
  ...props
}: LazyLoadProps & { skeletonClassName?: string }) {
  return (
    <LazyLoad
      className={className}
      fallback={
        <div 
          className={cn(
            "animate-pulse bg-gray-200 rounded",
            skeletonClassName
          )}
        />
      }
      {...props}
    >
      {children}
    </LazyLoad>
  );
}

// Lazy load for content sections
export function LazySection({
  children,
  title,
  className,
  ...props
}: LazyLoadProps & { title?: string }) {
  return (
    <LazyLoad
      className={cn("min-h-[200px]", className)}
      fallback={
        <div className="space-y-4 p-6">
          {title && (
            <div className="h-6 bg-gray-200 rounded animate-pulse w-1/3"></div>
          )}
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
          </div>
        </div>
      }
      {...props}
    >
      {children}
    </LazyLoad>
  );
}

// Hook for intersection observer
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting;
        setIsIntersecting(isVisible);
        
        if (isVisible && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, hasIntersected, options]);

  return { isIntersecting, hasIntersected };
}

// Lazy load for data fetching
export function useLazyData<T>(
  fetchFn: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [shouldFetch, setShouldFetch] = useState(false);

  const triggerFetch = () => setShouldFetch(true);

  useEffect(() => {
    if (!shouldFetch) return;

    let cancelled = false;
    setLoading(true);
    setError(null);

    fetchFn()
      .then((result) => {
        if (!cancelled) {
          setData(result);
        }
      })
      .catch((err) => {
        if (!cancelled) {
          setError(err);
        }
      })
      .finally(() => {
        if (!cancelled) {
          setLoading(false);
        }
      });

    return () => {
      cancelled = true;
    };
  }, [shouldFetch, ...dependencies]);

  return { data, loading, error, triggerFetch };
}
