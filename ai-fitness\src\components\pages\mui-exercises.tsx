'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Badge,
  Paper,
  Collapse,
  FormGroup,
  FormControlLabel,
  Checkbox,
  alpha,
  useTheme,
  CardMedia,
  Avatar,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  PlayArrow as PlayIcon,
  Add as AddIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  AccessTime as ClockIcon,
  TrendingUp as TargetIcon,
  Clear as ClearIcon,
  FitnessCenter as FitnessCenterIcon,
  Timer as TimerIcon,
  Star as StarIcon,
  Visibility as EyeIcon,
  SportsMartialArts as MuscleIcon,
  Build as EquipmentIcon,
} from '@mui/icons-material';
import { 
  useExercises, 
  useExerciseSearch, 
  useExerciseAttributes,
  useInfiniteExercises 
} from "@/lib/hooks/use-exercises";
import type { ExerciseSearchParams } from "@/lib/api/types";

export function MuiExercises() {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<ExerciseSearchParams>({
    equipment: [],
    muscles: [],
    difficulty: [],
    category: []
  });

  // Use search when there's a query, otherwise use filtered exercises
  const { data: searchResults, isLoading: isSearching } = useExerciseSearch(
    searchQuery, 
    20, 
    searchQuery ? selectedFilters : undefined
  );

  const { data: exercisesData, isLoading: isLoadingExercises } = useExercises({
    limit: 20,
    ...(!searchQuery ? selectedFilters : {})
  });

  const { data: attributesData } = useExerciseAttributes();

  const exercises = searchQuery ? searchResults?.data : exercisesData?.data;
  const isLoading = searchQuery ? isSearching : isLoadingExercises;

  const handleFilterChange = (type: keyof ExerciseSearchParams, value: string) => {
    setSelectedFilters(prev => {
      const currentValues = prev[type] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      return {
        ...prev,
        [type]: newValues
      };
    });
  };

  const clearFilters = () => {
    setSelectedFilters({
      equipment: [],
      muscles: [],
      difficulty: [],
      category: []
    });
  };

  const hasActiveFilters = Object.values(selectedFilters).some(arr => arr && arr.length > 0);

  const filteredExercises = exercises?.filter(exercise =>
    searchQuery === "" ||
    exercise.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    exercise.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          py: { xs: 6, md: 8 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
                fontSize: { xs: '2.5rem', md: '3.5rem' }
              }}
            >
              动作库
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: 'text.secondary',
                mb: 4,
                fontWeight: 400,
                fontSize: { xs: '1.25rem', md: '1.5rem' }
              }}
            >
              探索丰富的健身动作，掌握正确的训练技巧
            </Typography>
          </Box>
        </Container>

        {/* Floating Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: 80,
            height: 80,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.3)}, ${alpha(theme.palette.secondary.main, 0.3)})`,
            animation: 'float 6s ease-in-out infinite',
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '30%',
            left: '5%',
            width: 60,
            height: 60,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.secondary.main, 0.3)}, ${alpha(theme.palette.primary.main, 0.3)})`,
            animation: 'float 4s ease-in-out infinite reverse',
          }}
        />
      </Box>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Search and Filters */}
        <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, mb: 3, flexDirection: { xs: 'column', md: 'row' } }}>
            <TextField
              fullWidth
              placeholder="搜索健身动作..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 3,
                }
              }}
            />
            <Button
              variant="outlined"
              onClick={() => setShowFilters(!showFilters)}
              startIcon={<FilterIcon />}
              endIcon={hasActiveFilters && (
                <Badge badgeContent={Object.values(selectedFilters).reduce((acc, arr) => acc + (arr?.length || 0), 0)} color="primary">
                  <Box />
                </Badge>
              )}
              sx={{
                borderRadius: 3,
                minWidth: 120,
                fontWeight: 600,
                textTransform: 'none',
              }}
            >
              筛选
            </Button>
          </Box>

          <Collapse in={showFilters}>
            <Box sx={{ pt: 3, borderTop: 1, borderColor: 'divider' }}>
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    器械
                  </Typography>
                  <FormGroup>
                    {['哑铃', '杠铃', '器械', '自重', '弹力带'].map((equipment) => (
                      <FormControlLabel
                        key={equipment}
                        control={
                          <Checkbox
                            checked={selectedFilters.equipment?.includes(equipment) || false}
                            onChange={() => handleFilterChange('equipment', equipment)}
                            sx={{ color: theme.palette.primary.main }}
                          />
                        }
                        label={equipment}
                      />
                    ))}
                  </FormGroup>
                </Grid>
                <Grid size={{ xs: 12, md: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    目标肌群
                  </Typography>
                  <FormGroup>
                    {['胸部', '背部', '肩部', '手臂', '腿部', '核心'].map((muscle) => (
                      <FormControlLabel
                        key={muscle}
                        control={
                          <Checkbox
                            checked={selectedFilters.muscles?.includes(muscle) || false}
                            onChange={() => handleFilterChange('muscles', muscle)}
                            sx={{ color: theme.palette.secondary.main }}
                          />
                        }
                        label={muscle}
                      />
                    ))}
                  </FormGroup>
                </Grid>
                <Grid size={{ xs: 12, md: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    难度
                  </Typography>
                  <FormGroup>
                    {['初级', '中级', '高级'].map((difficulty) => (
                      <FormControlLabel
                        key={difficulty}
                        control={
                          <Checkbox
                            checked={selectedFilters.difficulty?.includes(difficulty) || false}
                            onChange={() => handleFilterChange('difficulty', difficulty)}
                            sx={{ color: '#9C27B0' }}
                          />
                        }
                        label={difficulty}
                      />
                    ))}
                  </FormGroup>
                </Grid>
                <Grid size={{ xs: 12, md: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    类别
                  </Typography>
                  <FormGroup>
                    {['力量', '有氧', '柔韧', '平衡'].map((category) => (
                      <FormControlLabel
                        key={category}
                        control={
                          <Checkbox
                            checked={selectedFilters.category?.includes(category) || false}
                            onChange={() => handleFilterChange('category', category)}
                            sx={{ color: '#FF9800' }}
                          />
                        }
                        label={category}
                      />
                    ))}
                  </FormGroup>
                </Grid>
              </Grid>
              {hasActiveFilters && (
                <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
                  <Button
                    variant="outlined"
                    onClick={clearFilters}
                    startIcon={<ClearIcon />}
                    sx={{ borderRadius: 3, textTransform: 'none' }}
                  >
                    清除筛选
                  </Button>
                </Box>
              )}
            </Box>
          </Collapse>
        </Paper>

        {/* Exercises Grid */}
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 3, color: 'text.primary' }}>
            {searchQuery ? `搜索结果: "${searchQuery}"` : '所有动作'}
          </Typography>

          <Grid container spacing={3}>
            {isLoading ? (
              Array.from({ length: 12 }).map((_, index) => (
                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={index}>
                  <Card sx={{ height: 320, borderRadius: 3 }}>
                    <Box sx={{ height: 180, bgcolor: 'grey.200', animation: 'pulse 1.5s ease-in-out infinite' }} />
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ bgcolor: 'grey.200', height: 20, borderRadius: 1, mb: 1, animation: 'pulse 1.5s ease-in-out infinite' }} />
                      <Box sx={{ bgcolor: 'grey.200', height: 16, borderRadius: 1, animation: 'pulse 1.5s ease-in-out infinite' }} />
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : filteredExercises.length === 0 ? (
              <Grid size={12}>
                <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 3 }}>
                  <FitnessCenterIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    {searchQuery ? '未找到相关动作' : '暂无健身动作'}
                  </Typography>
                  <Typography color="text.secondary">
                    {searchQuery ? '尝试调整搜索条件' : '敬请期待更多精彩内容'}
                  </Typography>
                </Paper>
              </Grid>
            ) : (
              filteredExercises.map((exercise) => (
                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={exercise.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 3,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: theme.shadows[8],
                      }
                    }}
                  >
                    {/* Exercise Image/Video Placeholder */}
                    <Box
                      sx={{
                        height: 180,
                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        position: 'relative',
                      }}
                    >
                      <FitnessCenterIcon sx={{ fontSize: 48, color: theme.palette.primary.main, opacity: 0.7 }} />
                      <IconButton
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          bgcolor: 'rgba(255,255,255,0.9)',
                          '&:hover': { bgcolor: 'white' }
                        }}
                        size="small"
                      >
                        <FavoriteBorderIcon fontSize="small" />
                      </IconButton>
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 8,
                          right: 8,
                          bgcolor: 'rgba(0,0,0,0.7)',
                          color: 'white',
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                        }}
                      >
                        <PlayIcon fontSize="small" />
                        <Typography variant="caption">演示</Typography>
                      </Box>
                    </Box>

                    <CardContent sx={{ p: 2, height: 140, display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 1, fontSize: '1rem' }}>
                        {exercise.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          flex: 1,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }}
                      >
                        {exercise.description || '专业健身动作指导'}
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 0.5, mb: 2, flexWrap: 'wrap' }}>
                        <Chip
                          icon={<MuscleIcon />}
                          label={exercise.primaryMuscles?.[0] || '全身'}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 24 }}
                        />
                        <Chip
                          icon={<EquipmentIcon />}
                          label={exercise.equipment || '自重'}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 24 }}
                        />
                      </Box>

                      <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                        <Button
                          variant="contained"
                          size="small"
                          startIcon={<EyeIcon />}
                          sx={{
                            flex: 1,
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 600,
                            fontSize: '0.8rem',
                            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          }}
                        >
                          查看
                        </Button>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{ borderRadius: 2, minWidth: 'auto', px: 1 }}
                        >
                          <AddIcon fontSize="small" />
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>

          {/* Load More Button */}
          {!isLoading && filteredExercises.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 4,
                }}
              >
                加载更多
              </Button>
            </Box>
          )}
        </Box>
      </Container>
    </Box>
  );
}
