(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[43],{1169:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>W});var i=a(5155),l=a(5695),t=a(7516),n=a(6695),c=a(5220),r=a(6126),d=a(7023),x=a(9579),m=a(7550),h=a(1976),o=a(6516),j=a(5690),g=a(4616),N=a(2659),u=a(6785),p=a(646),f=a(4186),v=a(8564),w=a(7580),y=a(9074),b=a(9150),k=a(5251),A=a(6874),P=a.n(A);function W(){var e;let s=(0,l.useParams)().id,{isAuthenticated:a}=(0,k.As)(),{data:A,isLoading:W,error:Z}=(0,b.Rc)(s),R=(0,b.t2)();(0,b.ij)();let $=(0,b.bR)(),B=async()=>{try{await R.mutateAsync(s)}catch(e){console.error("Failed to join program:",e)}},C=async()=>{try{let e=await $.mutateAsync({exercises:[],notes:"Started from program: ".concat(null==A?void 0:A.title)});window.location.href="/workouts/sessions/".concat(e.id)}catch(e){console.error("Failed to start workout:",e)}};return W?(0,i.jsx)(d.AV,{}):Z||!A?(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(t.V,{}),(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsx)(x.Kw,{title:"Program not found",message:"The workout program you're looking for doesn't exist or has been removed.",onRetry:()=>window.location.reload()})})]}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(t.V,{}),(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsx)(P(),{href:"/workouts",children:(0,i.jsxs)(c.$,{variant:"ghost",className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-4 w-4"}),"Back to Workouts"]})})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(n.ZB,{className:"text-2xl mb-2",children:A.title}),(0,i.jsx)(n.BT,{className:"text-lg",children:A.description})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(c.$,{variant:"outline",size:"icon",children:(0,i.jsx)(h.A,{className:"h-4 w-4"})}),(0,i.jsx)(c.$,{variant:"outline",size:"icon",children:(0,i.jsx)(o.A,{className:"h-4 w-4"})})]})]})}),(0,i.jsxs)(n.Wu,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:A.duration||"N/A"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Minutes"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-green-600",children:A.difficulty||"N/A"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Difficulty"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:(null==(e=A.weeks)?void 0:e.reduce((e,s)=>e+s.sessions.reduce((e,s)=>e+s.exercises.length,0),0))||0}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Exercises"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:A.participantCount||0}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Participants"})]})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsxs)(c.$,{size:"lg",className:"flex items-center gap-2",onClick:C,disabled:$.isPending,children:[(0,i.jsx)(j.A,{className:"h-5 w-5"}),$.isPending?"Starting...":"Start Workout"]}),a&&(0,i.jsxs)(c.$,{variant:"outline",size:"lg",className:"flex items-center gap-2",onClick:B,disabled:R.isPending,children:[(0,i.jsx)(g.A,{className:"h-5 w-5"}),R.isPending?"Joining...":"Join Program"]})]})]})]}),A.description&&(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-5 w-5"}),"About This Program"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed",children:A.description})})]}),A.weeks&&A.weeks.length>0&&(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{children:["Program Weeks (",A.weeks.length,")"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("div",{className:"space-y-4",children:A.weeks.map((e,s)=>{var a;return(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)("span",{className:"flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium",children:s+1}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-gray-900",children:["Week ",e.weekNumber||s+1]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(null==(a=e.sessions)?void 0:a.length)||0," sessions"]})]})]}),(0,i.jsx)(c.$,{variant:"outline",size:"sm",children:"View Week"})]},e.id||s)})})})]}),A.goals&&A.goals.length>0&&(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"h-5 w-5"}),"Program Goals"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:A.goals.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)("span",{className:"text-gray-700",children:e})]},s))})})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Program Info"})}),(0,i.jsxs)(n.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Category"}),(0,i.jsx)(r.E,{variant:"outline",children:"Fitness Program"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),"Duration"]}),(0,i.jsxs)("span",{className:"font-medium",children:[A.duration||"N/A"," min"]})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"h-4 w-4"}),"Difficulty"]}),(0,i.jsx)("span",{className:"font-medium",children:A.difficulty||"N/A"})]}),A.equipment&&A.equipment.length>0&&(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-600 block mb-2",children:"Equipment Needed"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:A.equipment.map((e,s)=>(0,i.jsx)(r.E,{variant:"secondary",className:"text-xs",children:e},s))})]}),A.rating&&(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),"Rating"]}),(0,i.jsxs)("span",{className:"font-medium",children:[A.rating,"/5"]})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("span",{className:"text-gray-600 flex items-center gap-2",children:[(0,i.jsx)(w.A,{className:"h-4 w-4"}),"Participants"]}),(0,i.jsx)("span",{className:"font-medium",children:A.participantCount||0})]})]})]}),!1,(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Quick Actions"})}),(0,i.jsxs)(n.Wu,{className:"space-y-3",children:[(0,i.jsxs)(c.$,{variant:"outline",className:"w-full justify-start",children:[(0,i.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Schedule Workout"]}),(0,i.jsxs)(c.$,{variant:"outline",className:"w-full justify-start",children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Share Program"]}),(0,i.jsxs)(c.$,{variant:"outline",className:"w-full justify-start",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"View Similar"]})]})]})]})]})]})]})}},2330:(e,s,a)=>{Promise.resolve().then(a.bind(a,1169))}},e=>{var s=s=>e(e.s=s);e.O(0,[76,96,358],()=>s(2330)),_N_E=e.O()}]);