"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar<PERSON>hart3, User, LogOut, Wifi, Wif<PERSON>Off, <PERSON> } from "lucide-react"
import { useAuth, useSignOut } from "@/lib/hooks/use-auth"
import { 
  useOfflineState, 
  useUnreadNotificationCount, 
  useHasPendingSync 
} from "@/lib/store/app-store"

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const { user, isAuthenticated, isLoading } = useAuth()
  const signOutMutation = useSignOut()
  
  // State management hooks
  const offlineState = useOfflineState()
  const unreadCount = useUnreadNotificationCount()
  const hasPendingSync = useHasPendingSync()

  const toggleMenu = () => setIsOpen(!isOpen)

  const handleSignOut = async () => {
    try {
      await signOutMutation.mutateAsync()
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Dumbbell className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900">AI-fitness-singles</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/workouts"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Workouts
            </Link>
            <Link
              href="/exercises"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Exercises
            </Link>
            <Link
              href="/progress"
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Progress
            </Link>

            {/* Status Indicators */}
            <div className="flex items-center space-x-4">
              {/* Online/Offline Status */}
              <div className="flex items-center space-x-1">
                {offlineState.isOnline ? (
                  <Wifi className="h-4 w-4 text-green-600" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-600" />
                )}
                {hasPendingSync && (
                  <Badge variant="outline" className="text-xs">
                    {offlineState.pendingSync.length}
                  </Badge>
                )}
              </div>

              {/* Notifications */}
              <div className="relative">
                <Bell className="h-4 w-4 text-gray-600" />
                {unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-4 w-4 p-0 text-xs flex items-center justify-center"
                  >
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Badge>
                )}
              </div>
            </div>

            {/* Auth Section */}
            {isLoading ? (
              <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
            ) : isAuthenticated && user ? (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Welcome, {user.name || user.email}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSignOut}
                  disabled={signOutMutation.isPending}
                  className="flex items-center space-x-1"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign Out</span>
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/auth/signin">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/auth/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMenu}
              className="p-2"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t">
              {/* Status Bar for Mobile */}
              <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg mb-3">
                <div className="flex items-center space-x-2">
                  {offlineState.isOnline ? (
                    <Wifi className="h-4 w-4 text-green-600" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-red-600" />
                  )}
                  <span className="text-sm text-gray-600">
                    {offlineState.isOnline ? 'Online' : 'Offline'}
                  </span>
                  {hasPendingSync && (
                    <Badge variant="outline" className="text-xs">
                      {offlineState.pendingSync.length} pending
                    </Badge>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Bell className="h-4 w-4 text-gray-600" />
                  {unreadCount > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Navigation Links */}
              <Link
                href="/workouts"
                className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md"
                onClick={() => setIsOpen(false)}
              >
                <div className="flex items-center space-x-2">
                  <Dumbbell className="h-5 w-5" />
                  <span>Workouts</span>
                </div>
              </Link>
              <Link
                href="/exercises"
                className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md"
                onClick={() => setIsOpen(false)}
              >
                <div className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Exercises</span>
                </div>
              </Link>
              <Link
                href="/progress"
                className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md"
                onClick={() => setIsOpen(false)}
              >
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Progress</span>
                </div>
              </Link>

              {/* Auth Section for Mobile */}
              <div className="pt-4 border-t">
                {isLoading ? (
                  <div className="px-3 py-2">
                    <div className="w-full h-8 bg-gray-200 rounded animate-pulse" />
                  </div>
                ) : isAuthenticated && user ? (
                  <div className="space-y-2">
                    <div className="px-3 py-2">
                      <div className="flex items-center space-x-2">
                        <User className="h-5 w-5 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {user.name || user.email}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSignOut}
                      disabled={signOutMutation.isPending}
                      className="w-full justify-start px-3"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Sign Out
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2 px-3">
                    <Button variant="ghost" size="sm" className="w-full" asChild>
                      <Link href="/auth/signin" onClick={() => setIsOpen(false)}>
                        Sign In
                      </Link>
                    </Button>
                    <Button size="sm" className="w-full" asChild>
                      <Link href="/auth/signup" onClick={() => setIsOpen(false)}>
                        Sign Up
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
