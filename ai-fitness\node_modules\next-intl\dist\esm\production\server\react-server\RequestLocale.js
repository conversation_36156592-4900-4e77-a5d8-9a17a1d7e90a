import{headers as t}from"next/headers";import{cache as e}from"react";import{HEADER_LOCALE_NAME as n}from"../../shared/constants.js";import{isPromise as r}from"../../shared/utils.js";import{getCachedRequestLocale as o}from"./RequestLocaleCache.js";const s=e((async function(){const e=t();return r(e)?await e:e}));const i=e((async function(){let t;try{t=(await s()).get(n)||void 0}catch(t){if(t instanceof Error&&"DYNAMIC_SERVER_USAGE"===t.digest){const e=new Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:t});throw e.digest=t.digest,e}throw t}return t}));async function a(){return o()||await i()}export{a as getRequestLocale};
