(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},7080:(e,t,r)=>{"use strict";r.d(t,{AppProviders:()=>v});var s=r(5155),n=r(2115),u=r(7017),l=r(6715);function i(e){let{children:t}=e,[r]=(0,n.useState)(()=>new u.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>(null==t?void 0:t.status)!==401&&(null==t?void 0:t.status)!==403&&(null==t?void 0:t.status)!==422&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchInterval:!1},mutations:{retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<2,retryDelay:e=>Math.min(1e3*2**e,1e4)}}}));return(0,s.jsxs)(l.Ht,{client:r,children:[t,!1]})}var a=r(5251);let o=(0,n.createContext)(void 0);function c(e){let{children:t}=e,{user:r,isAuthenticated:u,isLoading:l,error:i}=(0,a.As)(),[c,h]=(0,n.useState)(0);return(0,n.useEffect)(()=>{r&&1?localStorage.setItem("auth-user",JSON.stringify(r)):localStorage.removeItem("auth-user")},[r]),(0,s.jsx)(o.Provider,{value:{user:r,isAuthenticated:u,isLoading:l,error:i,refreshAuth:()=>{h(e=>e+1)}},children:t})}var h=r(4290),d=r(9916);function v(e){let{children:t}=e;return(0,n.useEffect)(()=>{h.Dj.getInstance();let e=d.Pb.setupNetworkListeners();return()=>{e&&e()}},[]),(0,s.jsx)(i,{children:(0,s.jsx)(c,{children:t})})}},8230:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5356,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,7080))}},e=>{var t=t=>e(e.s=t);e.O(0,[444,76,96,358],()=>t(8230)),_N_E=e.O()}]);