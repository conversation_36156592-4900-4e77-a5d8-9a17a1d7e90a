import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { BreadcrumbStructuredData } from '@/components/seo/structured-data';

export interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  // Prepare structured data
  const structuredDataItems = items.map(item => ({
    name: item.name,
    url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://ai-fitness-singles.vercel.app'}${item.href}`
  }));

  return (
    <>
      <BreadcrumbStructuredData items={structuredDataItems} />
      <nav 
        className={`flex ${className}`} 
        aria-label="Breadcrumb"
        role="navigation"
      >
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          {/* Home link */}
          <li className="inline-flex items-center">
            <Link
              href="/"
              className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"
              aria-label="Home"
            >
              <Home className="w-4 h-4 mr-2" />
              Home
            </Link>
          </li>

          {/* Breadcrumb items */}
          {items.map((item, index) => (
            <li key={item.href} className="inline-flex items-center">
              <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />
              {item.current || index === items.length - 1 ? (
                <span 
                  className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400"
                  aria-current="page"
                >
                  {item.name}
                </span>
              ) : (
                <Link
                  href={item.href}
                  className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white"
                >
                  {item.name}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}

// Utility function to generate breadcrumbs from pathname
export function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  segments.forEach((segment, index) => {
    const href = '/' + segments.slice(0, index + 1).join('/');
    const name = formatSegmentName(segment);
    
    breadcrumbs.push({
      name,
      href,
      current: index === segments.length - 1
    });
  });

  return breadcrumbs;
}

// Format segment names for display
function formatSegmentName(segment: string): string {
  // Handle dynamic routes
  if (segment.startsWith('[') && segment.endsWith(']')) {
    return 'Details';
  }

  // Convert kebab-case to Title Case
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Predefined breadcrumb configurations for specific pages
export const breadcrumbConfigs: Record<string, BreadcrumbItem[]> = {
  '/exercises': [
    { name: 'Exercises', href: '/exercises' }
  ],
  '/workouts': [
    { name: 'Workouts', href: '/workouts' }
  ],
  '/progress': [
    { name: 'Progress', href: '/progress' }
  ],
  '/dashboard': [
    { name: 'Dashboard', href: '/dashboard' }
  ],
  '/auth/signin': [
    { name: 'Sign In', href: '/auth/signin' }
  ],
  '/auth/signup': [
    { name: 'Sign Up', href: '/auth/signup' }
  ]
};
