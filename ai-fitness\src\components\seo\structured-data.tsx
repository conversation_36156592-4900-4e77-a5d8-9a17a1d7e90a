import Script from 'next/script';

interface StructuredDataProps {
  data: Record<string, any>;
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data)
      }}
    />
  );
}

// Exercise structured data
export function ExerciseStructuredData({ exercise }: { exercise: any }) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'ExerciseAction',
    name: exercise.name,
    description: exercise.description,
    target: {
      '@type': 'BodyPart',
      name: exercise.targetMuscles?.join(', ') || 'Full Body'
    },
    instrument: exercise.equipment || 'Bodyweight',
    exerciseType: exercise.category || 'Strength Training',
    difficulty: exercise.difficulty || 'Intermediate',
    instructions: exercise.instructions || exercise.description
  };

  return <StructuredData data={schema} />;
}

// Workout program structured data
export function WorkoutProgramStructuredData({ program }: { program: any }) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'ExercisePlan',
    name: program.title || program.name,
    description: program.description,
    category: 'Fitness',
    exerciseType: program.type || 'Strength Training',
    intensity: program.difficulty || 'Intermediate',
    workload: program.duration ? `${program.duration} minutes` : undefined,
    repetitions: program.weeks?.length ? `${program.weeks.length} weeks` : undefined,
    restPeriods: program.restDays ? `${program.restDays} rest days` : undefined
  };

  return <StructuredData data={schema} />;
}

// Article structured data for blog posts or guides
export function ArticleStructuredData({ 
  title, 
  description, 
  author, 
  datePublished, 
  dateModified,
  image,
  url 
}: {
  title: string;
  description: string;
  author?: string;
  datePublished?: string;
  dateModified?: string;
  image?: string;
  url?: string;
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    author: {
      '@type': 'Person',
      name: author || 'AI-fitness-singles Team'
    },
    publisher: {
      '@type': 'Organization',
      name: 'AI-fitness-singles',
      logo: {
        '@type': 'ImageObject',
        url: '/images/logo.png'
      }
    },
    datePublished,
    dateModified: dateModified || datePublished,
    image: image ? {
      '@type': 'ImageObject',
      url: image
    } : undefined,
    url,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url
    }
  };

  return <StructuredData data={schema} />;
}

// FAQ structured data
export function FAQStructuredData({ faqs }: { faqs: Array<{ question: string; answer: string }> }) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return <StructuredData data={schema} />;
}

// Breadcrumb structured data
export function BreadcrumbStructuredData({ items }: { 
  items: Array<{ name: string; url: string }> 
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };

  return <StructuredData data={schema} />;
}

// Rating/Review structured data
export function ReviewStructuredData({ 
  itemName,
  rating,
  reviewCount,
  bestRating = 5,
  worstRating = 1 
}: {
  itemName: string;
  rating: number;
  reviewCount: number;
  bestRating?: number;
  worstRating?: number;
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: itemName,
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: rating,
      reviewCount,
      bestRating,
      worstRating
    }
  };

  return <StructuredData data={schema} />;
}
