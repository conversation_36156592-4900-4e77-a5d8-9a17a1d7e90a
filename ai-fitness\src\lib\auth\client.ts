/**
 * Better Auth Client Configuration for AI-fitness
 * Provides client-side authentication functionality
 */

import { createAuthClient } from "better-auth/react";
import { inferAdditionalFields } from "better-auth/client/plugins";
import { auth } from "./config";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3000",
  plugins: [
    inferAdditionalFields<typeof auth>(),
  ],
});

// Export commonly used hooks and functions
export const {
  useSession,
  signIn,
  signUp,
  signOut,
  getSession,
} = authClient;

/**
 * Custom hook to check if user is authenticated
 */
export function useAuth() {
  const { data: session, isPending, error } = useSession();
  
  return {
    user: session?.user || null,
    isAuthenticated: !!session?.user,
    isLoading: isPending,
    error,
    session,
  };
}

/**
 * Custom hook to check if user is admin
 */
export function useIsAdmin() {
  const { user, isAuthenticated } = useAuth();
  return isAuthenticated && user?.role === 'admin';
}

/**
 * Custom hook to check if user is premium
 */
export function useIsPremium() {
  const { user, isAuthenticated } = useAuth();
  return isAuthenticated && user?.isPremium === true;
}

/**
 * Sign in with email and password
 */
export async function signInWithEmail(email: string, password: string) {
  try {
    const result = await signIn.email({
      email,
      password,
    });
    
    if (result.error) {
      throw new Error(result.error.message);
    }
    
    return result;
  } catch (error) {
    console.error('Sign in error:', error);
    throw error;
  }
}

/**
 * Sign up with email and password
 */
export async function signUpWithEmail(
  email: string,
  password: string,
  firstName: string,
  lastName: string
) {
  try {
    const result = await signUp.email({
      email,
      password,
      name: `${firstName} ${lastName}`,
      firstName,
      lastName,
    });
    
    if (result.error) {
      throw new Error(result.error.message);
    }
    
    return result;
  } catch (error) {
    console.error('Sign up error:', error);
    throw error;
  }
}

/**
 * Sign out user
 */
export async function signOutUser() {
  try {
    await signOut();
  } catch (error) {
    console.error('Sign out error:', error);
    throw error;
  }
}

/**
 * Get current session on server side
 */
export async function getServerSession() {
  try {
    return await getSession();
  } catch (error) {
    console.error('Get session error:', error);
    return null;
  }
}
