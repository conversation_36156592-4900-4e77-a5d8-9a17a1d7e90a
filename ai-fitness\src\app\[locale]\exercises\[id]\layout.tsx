import { Metadata } from 'next';
import { generateExerciseMetadata } from '@/lib/seo/utils';
import { Breadcrumb } from '@/components/ui/breadcrumb';

// This would typically fetch exercise data from your API
async function getExercise(id: string) {
  // Mock exercise data - replace with actual API call
  const mockExercises: Record<string, any> = {
    'push-ups': {
      name: 'Push-ups',
      description: 'A classic bodyweight exercise that targets the chest, shoulders, and triceps. Perfect for building upper body strength.',
      targetMuscles: ['Chest', 'Shoulders', 'Triceps'],
      equipment: 'Bodyweight',
      difficulty: 'Beginner',
      category: 'Strength Training'
    },
    'squats': {
      name: 'Squats',
      description: 'A fundamental lower body exercise that targets the quadriceps, glutes, and hamstrings.',
      targetMuscles: ['Quadriceps', 'Glutes', 'Hamstrings'],
      equipment: 'Bodyweight',
      difficulty: 'Beginner',
      category: 'Strength Training'
    },
    'deadlifts': {
      name: 'Deadlifts',
      description: 'A compound exercise that works multiple muscle groups, primarily the posterior chain.',
      targetMuscles: ['Hamstrings', 'Glutes', 'Lower Back', 'Traps'],
      equipment: 'Barbell',
      difficulty: 'Intermediate',
      category: 'Strength Training'
    }
  };

  return mockExercises[id] || {
    name: 'Exercise',
    description: 'Detailed exercise instructions and form guidance.',
    targetMuscles: ['Multiple'],
    equipment: 'Various',
    difficulty: 'All Levels',
    category: 'Fitness'
  };
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const exercise = await getExercise(params.id);
  return generateExerciseMetadata(exercise);
}

export default function ExerciseDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { id: string };
}) {
  const breadcrumbItems = [
    { name: 'Exercises', href: '/exercises' },
    { name: 'Exercise Details', href: `/exercises/${params.id}` }
  ];

  return (
    <div>
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      {children}
    </div>
  );
}
