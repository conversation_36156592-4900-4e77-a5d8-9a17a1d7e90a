"use strict";(()=>{var e={};e.id=302,e.ids=[302],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10716:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>p});var a=t(96559),o=t(48088),i=t(37719),n=t(46333);let l=(0,t(36718).li)({baseURL:"http://localhost:3000",trustedOrigins:["http://localhost:3004","http://localhost:3000","https://ai-fitness-singles.vercel.app"],database:{provider:"postgresql",url:process.env.DATABASE_URL||"postgresql://username:password@localhost:5432/workout_cool"},session:{expiresIn:604800,updateAge:86400,cookieCache:{enabled:!0,maxAge:300}},emailAndPassword:{enabled:!0,requireEmailVerification:!1,sendResetPassword:async({user:e,url:r})=>{console.log(`Password reset for ${e.email}: ${r}`)}},user:{additionalFields:{firstName:{type:"string",required:!1},lastName:{type:"string",required:!1},role:{type:"string",required:!1,defaultValue:"user"},isPremium:{type:"boolean",required:!1,defaultValue:!1},locale:{type:"string",required:!1,defaultValue:"en"}}},plugins:[(0,n.d)()],advanced:{generateId:()=>crypto.randomUUID(),crossSubDomainCookies:{enabled:!1}}}),{POST:p,GET:u}=(0,n.O)(l),d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/[...all]/route",pathname:"/api/auth/[...all]",filename:"route",bundlePath:"app/api/auth/[...all]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\api\\auth\\[...all]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:g,serverHooks:m}=d;function h(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:g})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:e=>{e.exports=require("node:crypto")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[96,76],()=>t(10716));module.exports=s})();