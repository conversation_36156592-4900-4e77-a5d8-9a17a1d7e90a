export interface ReleaseNote {
  date: string;
  titleKey: string;
  contentKey: string;
}

export const releaseNotes: ReleaseNote[] = [
  {
    date: "2025-06-23",
    titleKey: "release_notes.notes.note_2025_06_23.title",
    contentKey: "release_notes.notes.note_2025_06_23.content",
  },
  {
    date: "2025-06-22",
    titleKey: "release_notes.notes.note_2025_06_22.title",
    contentKey: "release_notes.notes.note_2025_06_22.content",
  },
  {
    date: "2025-06-19",
    titleKey: "release_notes.notes.note_2025_06_19.title",
    contentKey: "release_notes.notes.note_2025_06_19.content",
  },
  {
    date: "2025-06-18",
    titleKey: "release_notes.notes.note_2025_06_18.title",
    contentKey: "release_notes.notes.note_2025_06_18.content",
  },
  {
    date: "2025-06-01",
    titleKey: "release_notes.notes.note_2025_06_01.title",
    contentKey: "release_notes.notes.note_2025_06_01.content",
  },
  {
    date: "2025-05-20",
    title<PERSON>ey: "release_notes.notes.note_2025_05_20.title",
    contentKey: "release_notes.notes.note_2025_05_20.content",
  },
];
