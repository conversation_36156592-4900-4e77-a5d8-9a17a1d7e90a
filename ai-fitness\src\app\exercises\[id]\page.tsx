"use client"

import { useParams } from "next/navigation"
import { Navigation } from "@/components/Navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingPage } from "@/components/ui/loading"
import { ErrorMessage } from "@/components/ui/error"
import {
  ArrowLeft,
  Heart,
  Share2,
  Play,
  Clock,
  Target,
  Zap,
  Users,
  Star,
  Plus,
  BookOpen
} from "lucide-react"
import { useExercise } from "@/lib/hooks/use-exercises"
import Link from "next/link"

export default function ExerciseDetailPage() {
  const params = useParams()
  const exerciseId = params.id as string

  const { data: exercise, isLoading, error } = useExercise(exerciseId)

  if (isLoading) {
    return <LoadingPage />
  }

  if (error || !exercise) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <ErrorMessage 
            title="Exercise not found"
            message="The exercise you're looking for doesn't exist or has been removed."
            onRetry={() => window.location.reload()}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/exercises">
            <Button variant="ghost" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Exercises
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Exercise Header */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-2xl mb-2">{exercise.name}</CardTitle>
                    {exercise.nameEn && (
                      <CardDescription className="text-lg">{exercise.nameEn}</CardDescription>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Exercise Attributes */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {exercise.attributes?.map((attr, index) => (
                    <Badge key={index} variant="secondary">
                      {attr.attributeName?.name || attr.attributeValue?.value}
                    </Badge>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button size="lg" className="flex items-center gap-2">
                    <Play className="h-5 w-5" />
                    Start Exercise
                  </Button>
                  <Button variant="outline" size="lg" className="flex items-center gap-2">
                    <Plus className="h-5 w-5" />
                    Add to Workout
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Exercise Description */}
            {exercise.description && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Description
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">{exercise.description}</p>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            {exercise.introduction && (
              <Card>
                <CardHeader>
                  <CardTitle>Instructions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <p>{exercise.introduction}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Description */}
            {exercise.description && (
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <p>{exercise.description}</p>
                  </div>
                </CardContent>
              </Card>
            )}


          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Exercise Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Exercise Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Exercise ID</span>
                  <span className="font-medium text-sm">{exercise.id}</span>
                </div>

                {exercise.createdAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Created</span>
                    <span className="font-medium text-sm">{new Date(exercise.createdAt).toLocaleDateString()}</span>
                  </div>
                )}

                {exercise.updatedAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Updated</span>
                    <span className="font-medium text-sm">{new Date(exercise.updatedAt).toLocaleDateString()}</span>
                  </div>
                )}


              </CardContent>
            </Card>



            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Add to Favorites
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Exercise
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  View Similar
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
