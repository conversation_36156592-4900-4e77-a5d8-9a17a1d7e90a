import Link from 'next/link';
import { cn } from '@/lib/utils';

// Internal link component with SEO optimization
interface InternalLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  title?: string;
  rel?: string;
  prefetch?: boolean;
  replace?: boolean;
  scroll?: boolean;
  shallow?: boolean;
}

export function InternalLink({
  href,
  children,
  className,
  title,
  rel,
  prefetch = true,
  replace = false,
  scroll = true,
  shallow = false,
}: InternalLinkProps) {
  return (
    <Link
      href={href}
      className={cn("text-blue-600 hover:text-blue-800 underline", className)}
      title={title}
      rel={rel}
      prefetch={prefetch}
      replace={replace}
      scroll={scroll}
      shallow={shallow}
    >
      {children}
    </Link>
  );
}

// Related content links
interface RelatedLinksProps {
  title?: string;
  links: Array<{
    href: string;
    title: string;
    description?: string;
    category?: string;
  }>;
  className?: string;
}

export function RelatedLinks({ title = "Related Content", links, className }: RelatedLinksProps) {
  if (links.length === 0) return null;

  return (
    <aside className={cn("bg-gray-50 p-6 rounded-lg", className)} aria-label="Related content">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <nav aria-label="Related links">
        <ul className="space-y-3">
          {links.map((link, index) => (
            <li key={index}>
              <InternalLink
                href={link.href}
                title={link.description}
                className="block hover:bg-white p-3 rounded transition-colors"
              >
                <div className="font-medium">{link.title}</div>
                {link.description && (
                  <div className="text-sm text-gray-600 mt-1">{link.description}</div>
                )}
                {link.category && (
                  <div className="text-xs text-blue-600 mt-1">{link.category}</div>
                )}
              </InternalLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}

// Exercise category links
export function ExerciseCategoryLinks({ className }: { className?: string }) {
  const categories = [
    { name: 'Strength Training', href: '/exercises?category=strength', description: 'Build muscle and increase strength' },
    { name: 'Cardio', href: '/exercises?category=cardio', description: 'Improve cardiovascular health' },
    { name: 'Flexibility', href: '/exercises?category=flexibility', description: 'Enhance mobility and flexibility' },
    { name: 'Bodyweight', href: '/exercises?category=bodyweight', description: 'No equipment needed' },
    { name: 'Weight Training', href: '/exercises?category=weights', description: 'Exercises with weights' },
    { name: 'Functional', href: '/exercises?category=functional', description: 'Real-world movement patterns' },
  ];

  return (
    <nav className={cn("grid grid-cols-2 md:grid-cols-3 gap-4", className)} aria-label="Exercise categories">
      {categories.map((category) => (
        <InternalLink
          key={category.name}
          href={category.href}
          className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all no-underline"
          title={category.description}
        >
          <div className="font-medium text-gray-900">{category.name}</div>
          <div className="text-sm text-gray-600 mt-1">{category.description}</div>
        </InternalLink>
      ))}
    </nav>
  );
}

// Workout program links
export function WorkoutProgramLinks({ className }: { className?: string }) {
  const programs = [
    { name: 'Beginner Programs', href: '/workouts?level=beginner', description: 'Perfect for fitness newcomers' },
    { name: 'Intermediate Programs', href: '/workouts?level=intermediate', description: 'Step up your training' },
    { name: 'Advanced Programs', href: '/workouts?level=advanced', description: 'Challenge yourself' },
    { name: 'Weight Loss', href: '/workouts?goal=weight-loss', description: 'Burn calories effectively' },
    { name: 'Muscle Building', href: '/workouts?goal=muscle-gain', description: 'Build lean muscle mass' },
    { name: 'Endurance', href: '/workouts?goal=endurance', description: 'Improve stamina and endurance' },
  ];

  return (
    <nav className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", className)} aria-label="Workout programs">
      {programs.map((program) => (
        <InternalLink
          key={program.name}
          href={program.href}
          className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all no-underline"
          title={program.description}
        >
          <div className="font-medium text-gray-900">{program.name}</div>
          <div className="text-sm text-gray-600 mt-1">{program.description}</div>
        </InternalLink>
      ))}
    </nav>
  );
}

// Contextual navigation links
interface ContextualNavProps {
  currentPage: string;
  className?: string;
}

export function ContextualNav({ currentPage, className }: ContextualNavProps) {
  const getContextualLinks = (page: string) => {
    switch (page) {
      case 'exercises':
        return [
          { href: '/workouts', text: 'Browse Workout Programs', description: 'Find complete workout routines' },
          { href: '/progress', text: 'Track Your Progress', description: 'Monitor your fitness journey' },
          { href: '/dashboard', text: 'Your Dashboard', description: 'Personal fitness overview' },
        ];
      case 'workouts':
        return [
          { href: '/exercises', text: 'Exercise Database', description: 'Explore individual exercises' },
          { href: '/progress', text: 'Track Your Progress', description: 'Monitor workout performance' },
          { href: '/dashboard', text: 'Your Dashboard', description: 'Personal fitness overview' },
        ];
      case 'progress':
        return [
          { href: '/exercises', text: 'Exercise Database', description: 'Find new exercises to try' },
          { href: '/workouts', text: 'Workout Programs', description: 'Discover new training routines' },
          { href: '/dashboard', text: 'Your Dashboard', description: 'Complete fitness overview' },
        ];
      default:
        return [
          { href: '/exercises', text: 'Exercise Database', description: 'Comprehensive exercise library' },
          { href: '/workouts', text: 'Workout Programs', description: 'AI-powered training plans' },
          { href: '/progress', text: 'Progress Tracking', description: 'Monitor your improvements' },
        ];
    }
  };

  const links = getContextualLinks(currentPage);

  return (
    <nav className={cn("bg-blue-50 p-6 rounded-lg", className)} aria-label="Related pages">
      <h3 className="text-lg font-semibold mb-4 text-blue-900">Explore More</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {links.map((link) => (
          <InternalLink
            key={link.href}
            href={link.href}
            className="block p-3 bg-white rounded border hover:border-blue-300 hover:shadow-sm transition-all no-underline"
            title={link.description}
          >
            <div className="font-medium text-gray-900">{link.text}</div>
            <div className="text-sm text-gray-600 mt-1">{link.description}</div>
          </InternalLink>
        ))}
      </div>
    </nav>
  );
}

// Pagination with SEO-friendly links
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  className?: string;
}

export function SEOPagination({ currentPage, totalPages, baseUrl, className }: PaginationProps) {
  const getPageUrl = (page: number) => {
    return page === 1 ? baseUrl : `${baseUrl}?page=${page}`;
  };

  const renderPageLink = (page: number, isCurrent: boolean = false) => (
    <InternalLink
      key={page}
      href={getPageUrl(page)}
      className={cn(
        "px-3 py-2 rounded border",
        isCurrent 
          ? "bg-blue-600 text-white border-blue-600" 
          : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50",
        "no-underline"
      )}
      title={`Go to page ${page}`}
      rel={isCurrent ? undefined : "nofollow"}
    >
      {page}
    </InternalLink>
  );

  return (
    <nav className={cn("flex justify-center items-center space-x-2", className)} aria-label="Pagination">
      {currentPage > 1 && (
        <InternalLink
          href={getPageUrl(currentPage - 1)}
          className="px-3 py-2 rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50 no-underline"
          title="Go to previous page"
          rel="prev"
        >
          Previous
        </InternalLink>
      )}
      
      {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
        const page = Math.max(1, currentPage - 2) + i;
        if (page <= totalPages) {
          return renderPageLink(page, page === currentPage);
        }
        return null;
      })}
      
      {currentPage < totalPages && (
        <InternalLink
          href={getPageUrl(currentPage + 1)}
          className="px-3 py-2 rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50 no-underline"
          title="Go to next page"
          rel="next"
        >
          Next
        </InternalLink>
      )}
    </nav>
  );
}
