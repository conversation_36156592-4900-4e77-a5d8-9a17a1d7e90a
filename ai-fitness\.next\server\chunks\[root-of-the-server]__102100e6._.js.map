{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/fitness-singles/ai-fitness/src/app/api/programs/public/route.ts"], "sourcesContent": ["/**\n * Public Programs API Route\n * Calls workout-cool server action directly for public programs\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Mock data for testing - replace with actual workout-cool integration\nconst mockPrograms = [\n  {\n    id: '1',\n    slug: 'beginner-strength',\n    slugEn: 'beginner-strength',\n    title: 'Beginner Strength Training',\n    titleEn: 'Beginner Strength Training',\n    description: 'A comprehensive strength training program for beginners',\n    descriptionEn: 'A comprehensive strength training program for beginners',\n    category: 'Strength',\n    image: '/images/programs/strength.jpg',\n    level: 'BEGINNER',\n    type: 'Strength Training',\n    durationWeeks: 8,\n    sessionsPerWeek: 3,\n    sessionDurationMin: 45,\n    equipment: ['Dumbbells', 'Barbell', 'Bench'],\n    isPremium: false,\n    participantCount: 150,\n    totalWeeks: 8,\n    totalSessions: 24,\n    totalExercises: 12,\n    totalEnrollments: 150,\n  },\n  {\n    id: '2',\n    slug: 'cardio-hiit',\n    slugEn: 'cardio-hiit',\n    title: 'HIIT Cardio Blast',\n    titleEn: 'HIIT Cardio Blast',\n    description: 'High-intensity interval training for maximum fat burn',\n    descriptionEn: 'High-intensity interval training for maximum fat burn',\n    category: 'Cardio',\n    image: '/images/programs/hiit.jpg',\n    level: 'INTERMEDIATE',\n    type: 'HIIT',\n    durationWeeks: 6,\n    sessionsPerWeek: 4,\n    sessionDurationMin: 30,\n    equipment: ['None'],\n    isPremium: true,\n    participantCount: 89,\n    totalWeeks: 6,\n    totalSessions: 24,\n    totalExercises: 20,\n    totalEnrollments: 89,\n  },\n];\n\nexport async function GET(request: NextRequest) {\n  try {\n    // For now, return mock data\n    // TODO: Integrate with workout-cool server actions when available\n    return NextResponse.json(mockPrograms);\n  } catch (error) {\n    console.error('Error fetching public programs:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch public programs' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAEA,uEAAuE;AACvE,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,SAAS;QACT,aAAa;QACb,eAAe;QACf,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,WAAW;YAAC;YAAa;YAAW;SAAQ;QAC5C,WAAW;QACX,kBAAkB;QAClB,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,SAAS;QACT,aAAa;QACb,eAAe;QACf,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,WAAW;YAAC;SAAO;QACnB,WAAW;QACX,kBAAkB;QAClB,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,kBAAkB;IACpB;CACD;AAEM,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,4BAA4B;QAC5B,kEAAkE;QAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}