'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Ty<PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';

interface Program {
  id: string;
  title: string;
  description: string;
  category: string;
  level: string;
  durationWeeks: number;
  sessionsPerWeek: number;
  equipment: string[];
  isPremium: boolean;
}

interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string;
}

export default function ApiTestPage() {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testProgramsAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/programs/public');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setPrograms(data);
    } catch (err) {
      setError(`Programs API Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testExercisesAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/exercises/public');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setExercises(data);
    } catch (err) {
      setError(`Exercises API Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Auto-test APIs on page load
    testProgramsAPI();
    testExercisesAPI();
  }, []);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom align="center">
        API Integration Test
      </Typography>
      
      <Typography variant="h6" color="text.secondary" align="center" sx={{ mb: 4 }}>
        Testing workout-cool API integration
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={4}>
        {/* Programs Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h5" component="h2">
                  Programs API Test
                </Typography>
                <Button 
                  variant="outlined" 
                  onClick={testProgramsAPI}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? 'Testing...' : 'Test Programs API'}
                </Button>
              </Box>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Endpoint: /api/programs/public
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              {programs.length > 0 ? (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Found {programs.length} programs:
                  </Typography>
                  {programs.map((program) => (
                    <Card key={program.id} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="h6">{program.title}</Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {program.description}
                        </Typography>
                        <Box display="flex" gap={1} flexWrap="wrap" mt={1}>
                          <Typography variant="caption" sx={{ 
                            px: 1, 
                            py: 0.5, 
                            bgcolor: 'primary.light', 
                            color: 'primary.contrastText',
                            borderRadius: 1 
                          }}>
                            {program.category}
                          </Typography>
                          <Typography variant="caption" sx={{ 
                            px: 1, 
                            py: 0.5, 
                            bgcolor: 'secondary.light', 
                            color: 'secondary.contrastText',
                            borderRadius: 1 
                          }}>
                            {program.level}
                          </Typography>
                          <Typography variant="caption" sx={{ 
                            px: 1, 
                            py: 0.5, 
                            bgcolor: 'info.light', 
                            color: 'info.contrastText',
                            borderRadius: 1 
                          }}>
                            {program.durationWeeks} weeks
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No programs loaded yet. Click "Test Programs API" to load data.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Exercises Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h5" component="h2">
                  Exercises API Test
                </Typography>
                <Button 
                  variant="outlined" 
                  onClick={testExercisesAPI}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? 'Testing...' : 'Test Exercises API'}
                </Button>
              </Box>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Endpoint: /api/exercises/public
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              {exercises.length > 0 ? (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Found {exercises.length} exercises:
                  </Typography>
                  {exercises.map((exercise) => (
                    <Card key={exercise.id} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="h6">{exercise.name}</Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {exercise.description}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          <strong>Instructions:</strong> {exercise.instructions}
                        </Typography>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No exercises loaded yet. Click "Test Exercises API" to load data.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
