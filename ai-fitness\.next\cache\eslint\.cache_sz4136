[{"C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\api\\auth\\[...all]\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\api\\health\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signin\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signup\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\demo\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\[id]\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\progress\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\[id]\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\Navigation.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\providers\\app-providers.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\badge.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\button.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\card.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\empty-state.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\error.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\input.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\loading.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\progress.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\sync-status.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\client.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\config.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\errors.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\index.ts": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\auth.ts": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\exercises.ts": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\progress.ts": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\workouts.ts": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\types.ts": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\auth\\client.ts": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\auth\\config.ts": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-auth.ts": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-exercises.ts": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-progress.ts": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-workouts.ts": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\providers\\auth-provider.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\providers\\query-provider.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\query\\config.ts": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\store\\app-store.ts": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\sync\\sync-manager.ts": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\utils.ts": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\stores\\auth-store.ts": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\stores\\workout-store.ts": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\types\\index.ts": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\__tests__\\page.test.tsx": "49"}, {"size": 246, "mtime": 1751125944120, "results": "50", "hashOfConfig": "51"}, {"size": 4278, "mtime": 1751133401229, "results": "52", "hashOfConfig": "51"}, {"size": 7826, "mtime": 1751126084928, "results": "53", "hashOfConfig": "51"}, {"size": 12053, "mtime": 1751126128665, "results": "54", "hashOfConfig": "51"}, {"size": 7494, "mtime": 1751118596236, "results": "55", "hashOfConfig": "51"}, {"size": 11941, "mtime": 1751132928102, "results": "56", "hashOfConfig": "51"}, {"size": 11452, "mtime": 1751134545904, "results": "57", "hashOfConfig": "51"}, {"size": 7253, "mtime": 1751134446424, "results": "58", "hashOfConfig": "51"}, {"size": 848, "mtime": 1751132643225, "results": "59", "hashOfConfig": "51"}, {"size": 6847, "mtime": 1751133864748, "results": "60", "hashOfConfig": "51"}, {"size": 23058, "mtime": 1751132240217, "results": "61", "hashOfConfig": "51"}, {"size": 28161, "mtime": 1751137582846, "results": "62", "hashOfConfig": "51"}, {"size": 13947, "mtime": 1751137268468, "results": "63", "hashOfConfig": "51"}, {"size": 9414, "mtime": 1751132764727, "results": "64", "hashOfConfig": "51"}, {"size": 944, "mtime": 1751132624223, "results": "65", "hashOfConfig": "51"}, {"size": 1128, "mtime": 1751121952788, "results": "66", "hashOfConfig": "51"}, {"size": 1902, "mtime": 1751118303386, "results": "67", "hashOfConfig": "51"}, {"size": 1828, "mtime": 1751118315500, "results": "68", "hashOfConfig": "51"}, {"size": 5676, "mtime": 1751124556582, "results": "69", "hashOfConfig": "51"}, {"size": 5926, "mtime": 1751124529214, "results": "70", "hashOfConfig": "51"}, {"size": 823, "mtime": 1751133624111, "results": "71", "hashOfConfig": "51"}, {"size": 2821, "mtime": 1751124503226, "results": "72", "hashOfConfig": "51"}, {"size": 791, "mtime": 1751121961974, "results": "73", "hashOfConfig": "51"}, {"size": 10268, "mtime": 1751132686675, "results": "74", "hashOfConfig": "51"}, {"size": 6027, "mtime": 1751135918631, "results": "75", "hashOfConfig": "51"}, {"size": 2866, "mtime": 1751137897105, "results": "76", "hashOfConfig": "51"}, {"size": 4812, "mtime": 1751125433720, "results": "77", "hashOfConfig": "51"}, {"size": 1235, "mtime": 1751125478647, "results": "78", "hashOfConfig": "51"}, {"size": 3808, "mtime": 1751125963080, "results": "79", "hashOfConfig": "51"}, {"size": 5095, "mtime": 1751125828753, "results": "80", "hashOfConfig": "51"}, {"size": 9619, "mtime": 1751132119417, "results": "81", "hashOfConfig": "51"}, {"size": 7702, "mtime": 1751137640772, "results": "82", "hashOfConfig": "51"}, {"size": 8023, "mtime": 1751132390015, "results": "83", "hashOfConfig": "51"}, {"size": 2554, "mtime": 1751137935904, "results": "84", "hashOfConfig": "51"}, {"size": 2170, "mtime": 1751125919179, "results": "85", "hashOfConfig": "51"}, {"size": 5630, "mtime": 1751125987765, "results": "86", "hashOfConfig": "51"}, {"size": 5053, "mtime": 1751125854461, "results": "87", "hashOfConfig": "51"}, {"size": 9700, "mtime": 1751132158752, "results": "88", "hashOfConfig": "51"}, {"size": 8462, "mtime": 1751138075059, "results": "89", "hashOfConfig": "51"}, {"size": 4811, "mtime": 1751126011717, "results": "90", "hashOfConfig": "51"}, {"size": 2791, "mtime": 1751138211149, "results": "91", "hashOfConfig": "51"}, {"size": 9541, "mtime": 1751132488416, "results": "92", "hashOfConfig": "51"}, {"size": 10284, "mtime": 1751138323148, "results": "93", "hashOfConfig": "51"}, {"size": 9293, "mtime": 1751132525858, "results": "94", "hashOfConfig": "51"}, {"size": 1271, "mtime": 1751118292132, "results": "95", "hashOfConfig": "51"}, {"size": 3601, "mtime": 1751118350146, "results": "96", "hashOfConfig": "51"}, {"size": 6095, "mtime": 1751118378740, "results": "97", "hashOfConfig": "51"}, {"size": 2530, "mtime": 1751118331895, "results": "98", "hashOfConfig": "51"}, {"size": 1897, "mtime": 1751121205498, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1iphdnl", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\api\\auth\\[...all]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\api\\health\\route.ts", ["247", "248"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signin\\page.tsx", ["249"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\auth\\signup\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\dashboard\\page.tsx", ["250"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\demo\\page.tsx", ["251", "252"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\page.tsx", ["253", "254", "255", "256", "257", "258", "259", "260", "261", "262"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\exercises\\[id]\\page.tsx", ["263", "264", "265", "266", "267"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\page.tsx", ["268", "269"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\progress\\page.tsx", ["270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285", "286"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\page.tsx", ["287", "288", "289", "290", "291", "292"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\app\\workouts\\[id]\\page.tsx", ["293", "294", "295"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\providers\\app-providers.tsx", ["296"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\empty-state.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\error.tsx", ["297", "298"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\input.tsx", ["299"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\components\\ui\\sync-status.tsx", ["300"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\client.ts", ["301", "302", "303", "304", "305", "306"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\errors.ts", ["307", "308", "309", "310", "311", "312", "313", "314", "315"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\auth.ts", ["316"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\exercises.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\progress.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\services\\workouts.ts", ["317", "318"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\api\\types.ts", ["319", "320", "321", "322", "323"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\auth\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\auth\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-exercises.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-progress.ts", ["324"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\hooks\\use-workouts.ts", ["325", "326", "327", "328"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\providers\\auth-provider.tsx", ["329"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\providers\\query-provider.tsx", ["330", "331"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\query\\config.ts", ["332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\store\\app-store.ts", ["346", "347"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\sync\\sync-manager.ts", ["348", "349", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\stores\\auth-store.ts", ["364"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\stores\\workout-store.ts", ["365"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\fitness-singles\\ai-fitness\\src\\__tests__\\page.test.tsx", [], [], {"ruleId": "366", "severity": 1, "message": "367", "line": 8, "column": 27, "nodeType": null, "messageId": "368", "endLine": 8, "endColumn": 34}, {"ruleId": "369", "severity": 1, "message": "370", "line": 110, "column": 78, "nodeType": "371", "messageId": "372", "endLine": 110, "endColumn": 81, "suggestions": "373"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 174, "column": 18, "nodeType": "376", "messageId": "377", "suggestions": "378"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 100, "column": 24, "nodeType": "376", "messageId": "377", "suggestions": "379"}, {"ruleId": "366", "severity": 1, "message": "380", "line": 29, "column": 10, "nodeType": null, "messageId": "368", "endLine": 29, "endColumn": 18}, {"ruleId": "366", "severity": 1, "message": "381", "line": 29, "column": 20, "nodeType": null, "messageId": "368", "endLine": 29, "endColumn": 31}, {"ruleId": "366", "severity": 1, "message": "382", "line": 3, "column": 20, "nodeType": null, "messageId": "368", "endLine": 3, "endColumn": 29}, {"ruleId": "366", "severity": 1, "message": "383", "line": 15, "column": 3, "nodeType": null, "messageId": "368", "endLine": 15, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "384", "line": 16, "column": 3, "nodeType": null, "messageId": "368", "endLine": 16, "endColumn": 9}, {"ruleId": "366", "severity": 1, "message": "385", "line": 19, "column": 3, "nodeType": null, "messageId": "368", "endLine": 19, "endColumn": 6}, {"ruleId": "366", "severity": 1, "message": "386", "line": 20, "column": 3, "nodeType": null, "messageId": "368", "endLine": 20, "endColumn": 7}, {"ruleId": "366", "severity": 1, "message": "387", "line": 21, "column": 3, "nodeType": null, "messageId": "368", "endLine": 21, "endColumn": 11}, {"ruleId": "366", "severity": 1, "message": "388", "line": 22, "column": 3, "nodeType": null, "messageId": "368", "endLine": 22, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "389", "line": 23, "column": 3, "nodeType": null, "messageId": "368", "endLine": 23, "endColumn": 11}, {"ruleId": "366", "severity": 1, "message": "390", "line": 25, "column": 3, "nodeType": null, "messageId": "368", "endLine": 25, "endColumn": 6}, {"ruleId": "366", "severity": 1, "message": "391", "line": 32, "column": 3, "nodeType": null, "messageId": "368", "endLine": 32, "endColumn": 23}, {"ruleId": "366", "severity": 1, "message": "383", "line": 15, "column": 3, "nodeType": null, "messageId": "368", "endLine": 15, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "384", "line": 16, "column": 3, "nodeType": null, "messageId": "368", "endLine": 16, "endColumn": 9}, {"ruleId": "366", "severity": 1, "message": "385", "line": 17, "column": 3, "nodeType": null, "messageId": "368", "endLine": 17, "endColumn": 6}, {"ruleId": "366", "severity": 1, "message": "388", "line": 18, "column": 3, "nodeType": null, "messageId": "368", "endLine": 18, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "386", "line": 19, "column": 3, "nodeType": null, "messageId": "368", "endLine": 19, "endColumn": 7}, {"ruleId": "366", "severity": 1, "message": "392", "line": 5, "column": 16, "nodeType": null, "messageId": "368", "endLine": 5, "endColumn": 27}, {"ruleId": "366", "severity": 1, "message": "393", "line": 7, "column": 23, "nodeType": null, "messageId": "368", "endLine": 7, "endColumn": 34}, {"ruleId": "366", "severity": 1, "message": "394", "line": 8, "column": 10, "nodeType": null, "messageId": "368", "endLine": 8, "endColumn": 21}, {"ruleId": "366", "severity": 1, "message": "395", "line": 9, "column": 10, "nodeType": null, "messageId": "368", "endLine": 9, "endColumn": 22}, {"ruleId": "366", "severity": 1, "message": "396", "line": 21, "column": 3, "nodeType": null, "messageId": "368", "endLine": 21, "endColumn": 11}, {"ruleId": "366", "severity": 1, "message": "397", "line": 24, "column": 3, "nodeType": null, "messageId": "368", "endLine": 24, "endColumn": 15}, {"ruleId": "366", "severity": 1, "message": "388", "line": 25, "column": 3, "nodeType": null, "messageId": "368", "endLine": 25, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "398", "line": 27, "column": 3, "nodeType": null, "messageId": "368", "endLine": 27, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "399", "line": 28, "column": 3, "nodeType": null, "messageId": "368", "endLine": 28, "endColumn": 4}, {"ruleId": "366", "severity": 1, "message": "400", "line": 29, "column": 3, "nodeType": null, "messageId": "368", "endLine": 29, "endColumn": 7}, {"ruleId": "366", "severity": 1, "message": "401", "line": 30, "column": 3, "nodeType": null, "messageId": "368", "endLine": 30, "endColumn": 8}, {"ruleId": "366", "severity": 1, "message": "402", "line": 52, "column": 17, "nodeType": null, "messageId": "368", "endLine": 52, "endColumn": 30}, {"ruleId": "366", "severity": 1, "message": "403", "line": 52, "column": 43, "nodeType": null, "messageId": "368", "endLine": 52, "endColumn": 60}, {"ruleId": "366", "severity": 1, "message": "404", "line": 54, "column": 17, "nodeType": null, "messageId": "368", "endLine": 54, "endColumn": 33}, {"ruleId": "366", "severity": 1, "message": "405", "line": 54, "column": 46, "nodeType": null, "messageId": "368", "endLine": 54, "endColumn": 59}, {"ruleId": "366", "severity": 1, "message": "406", "line": 58, "column": 17, "nodeType": null, "messageId": "368", "endLine": 58, "endColumn": 33}, {"ruleId": "366", "severity": 1, "message": "407", "line": 58, "column": 46, "nodeType": null, "messageId": "368", "endLine": 58, "endColumn": 64}, {"ruleId": "369", "severity": 1, "message": "370", "line": 147, "column": 53, "nodeType": "371", "messageId": "372", "endLine": 147, "endColumn": 56, "suggestions": "408"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 278, "column": 46, "nodeType": "376", "messageId": "377", "suggestions": "409"}, {"ruleId": "366", "severity": 1, "message": "394", "line": 9, "column": 10, "nodeType": null, "messageId": "368", "endLine": 9, "endColumn": 21}, {"ruleId": "366", "severity": 1, "message": "385", "line": 15, "column": 3, "nodeType": null, "messageId": "368", "endLine": 15, "endColumn": 6}, {"ruleId": "366", "severity": 1, "message": "386", "line": 21, "column": 3, "nodeType": null, "messageId": "368", "endLine": 21, "endColumn": 7}, {"ruleId": "366", "severity": 1, "message": "400", "line": 25, "column": 3, "nodeType": null, "messageId": "368", "endLine": 25, "endColumn": 7}, {"ruleId": "366", "severity": 1, "message": "410", "line": 26, "column": 3, "nodeType": null, "messageId": "368", "endLine": 26, "endColumn": 9}, {"ruleId": "366", "severity": 1, "message": "411", "line": 104, "column": 37, "nodeType": null, "messageId": "368", "endLine": 104, "endColumn": 46}, {"ruleId": "369", "severity": 1, "message": "370", "line": 213, "column": 47, "nodeType": "371", "messageId": "372", "endLine": 213, "endColumn": 50, "suggestions": "412"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 247, "column": 47, "nodeType": "371", "messageId": "372", "endLine": 247, "endColumn": 50, "suggestions": "413"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 292, "column": 53, "nodeType": "371", "messageId": "372", "endLine": 292, "endColumn": 56, "suggestions": "414"}, {"ruleId": "366", "severity": 1, "message": "415", "line": 16, "column": 11, "nodeType": null, "messageId": "368", "endLine": 16, "endColumn": 22}, {"ruleId": "374", "severity": 1, "message": "375", "line": 152, "column": 27, "nodeType": "376", "messageId": "377", "suggestions": "416"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 152, "column": 48, "nodeType": "376", "messageId": "377", "suggestions": "417"}, {"ruleId": "418", "severity": 1, "message": "419", "line": 4, "column": 18, "nodeType": "420", "messageId": "421", "endLine": 4, "endColumn": 28, "suggestions": "422"}, {"ruleId": "366", "severity": 1, "message": "423", "line": 14, "column": 3, "nodeType": null, "messageId": "368", "endLine": 14, "endColumn": 11}, {"ruleId": "369", "severity": 1, "message": "370", "line": 15, "column": 34, "nodeType": "371", "messageId": "372", "endLine": 15, "endColumn": 37, "suggestions": "424"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 88, "column": 15, "nodeType": "371", "messageId": "372", "endLine": 88, "endColumn": 18, "suggestions": "425"}, {"ruleId": "366", "severity": 1, "message": "426", "line": 91, "column": 14, "nodeType": null, "messageId": "368", "endLine": 91, "endColumn": 19}, {"ruleId": "369", "severity": 1, "message": "370", "line": 189, "column": 37, "nodeType": "371", "messageId": "372", "endLine": 189, "endColumn": 40, "suggestions": "427"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 200, "column": 36, "nodeType": "371", "messageId": "372", "endLine": 200, "endColumn": 39, "suggestions": "428"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 211, "column": 38, "nodeType": "371", "messageId": "372", "endLine": 211, "endColumn": 41, "suggestions": "429"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 9, "column": 29, "nodeType": "371", "messageId": "372", "endLine": 9, "endColumn": 32, "suggestions": "430"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 11, "column": 79, "nodeType": "371", "messageId": "372", "endLine": 11, "endColumn": 82, "suggestions": "431"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 133, "column": 35, "nodeType": "371", "messageId": "372", "endLine": 133, "endColumn": 38, "suggestions": "432"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 140, "column": 39, "nodeType": "371", "messageId": "372", "endLine": 140, "endColumn": 42, "suggestions": "433"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 147, "column": 46, "nodeType": "371", "messageId": "372", "endLine": 147, "endColumn": 49, "suggestions": "434"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 154, "column": 42, "nodeType": "371", "messageId": "372", "endLine": 154, "endColumn": 45, "suggestions": "435"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 161, "column": 40, "nodeType": "371", "messageId": "372", "endLine": 161, "endColumn": 43, "suggestions": "436"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 180, "column": 37, "nodeType": "371", "messageId": "372", "endLine": 180, "endColumn": 40, "suggestions": "437"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 191, "column": 36, "nodeType": "371", "messageId": "372", "endLine": 191, "endColumn": 39, "suggestions": "438"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 42, "column": 21, "nodeType": "371", "messageId": "372", "endLine": 42, "endColumn": 24, "suggestions": "439"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 131, "column": 43, "nodeType": "371", "messageId": "372", "endLine": 131, "endColumn": 46, "suggestions": "440"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 140, "column": 11, "nodeType": "371", "messageId": "372", "endLine": 140, "endColumn": 14, "suggestions": "441"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 298, "column": 34, "nodeType": "371", "messageId": "372", "endLine": 298, "endColumn": 37, "suggestions": "442"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 321, "column": 13, "nodeType": "371", "messageId": "372", "endLine": 321, "endColumn": 16, "suggestions": "443"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 338, "column": 29, "nodeType": "371", "messageId": "372", "endLine": 338, "endColumn": 32, "suggestions": "444"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 351, "column": 29, "nodeType": "371", "messageId": "372", "endLine": 351, "endColumn": 32, "suggestions": "445"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 358, "column": 29, "nodeType": "371", "messageId": "372", "endLine": 358, "endColumn": 32, "suggestions": "446"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 17, "column": 25, "nodeType": "371", "messageId": "372", "endLine": 17, "endColumn": 28, "suggestions": "447"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 16, "column": 26, "nodeType": "371", "messageId": "372", "endLine": 16, "endColumn": 29, "suggestions": "448"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 19, "column": 26, "nodeType": "371", "messageId": "372", "endLine": 19, "endColumn": 29, "suggestions": "449"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 24, "column": 21, "nodeType": "371", "messageId": "372", "endLine": 24, "endColumn": 24, "suggestions": "450"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 203, "column": 24, "nodeType": "371", "messageId": "372", "endLine": 203, "endColumn": 27, "suggestions": "451"}, {"ruleId": "366", "severity": 1, "message": "452", "line": 28, "column": 10, "nodeType": null, "messageId": "368", "endLine": 28, "endColumn": 20}, {"ruleId": "369", "severity": 1, "message": "370", "line": 29, "column": 42, "nodeType": "371", "messageId": "372", "endLine": 29, "endColumn": 45, "suggestions": "453"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 58, "column": 42, "nodeType": "371", "messageId": "372", "endLine": 58, "endColumn": 45, "suggestions": "454"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 20, "column": 34, "nodeType": "371", "messageId": "372", "endLine": 20, "endColumn": 37, "suggestions": "455"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 35, "column": 34, "nodeType": "371", "messageId": "372", "endLine": 35, "endColumn": 37, "suggestions": "456"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 43, "column": 22, "nodeType": "371", "messageId": "372", "endLine": 43, "endColumn": 25, "suggestions": "457"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 109, "column": 30, "nodeType": "371", "messageId": "372", "endLine": 109, "endColumn": 33, "suggestions": "458"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 109, "column": 60, "nodeType": "371", "messageId": "372", "endLine": 109, "endColumn": 63, "suggestions": "459"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 120, "column": 31, "nodeType": "371", "messageId": "372", "endLine": 120, "endColumn": 34, "suggestions": "460"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 127, "column": 31, "nodeType": "371", "messageId": "372", "endLine": 127, "endColumn": 34, "suggestions": "461"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 141, "column": 29, "nodeType": "371", "messageId": "372", "endLine": 141, "endColumn": 32, "suggestions": "462"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 152, "column": 30, "nodeType": "371", "messageId": "372", "endLine": 152, "endColumn": 33, "suggestions": "463"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 180, "column": 21, "nodeType": "371", "messageId": "372", "endLine": 180, "endColumn": 24, "suggestions": "464"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 194, "column": 24, "nodeType": "371", "messageId": "372", "endLine": 194, "endColumn": 27, "suggestions": "465"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 262, "column": 62, "nodeType": "371", "messageId": "372", "endLine": 262, "endColumn": 65, "suggestions": "466"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 264, "column": 21, "nodeType": "371", "messageId": "372", "endLine": 264, "endColumn": 24, "suggestions": "467"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 265, "column": 25, "nodeType": "371", "messageId": "372", "endLine": 265, "endColumn": 28, "suggestions": "468"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 69, "column": 11, "nodeType": "371", "messageId": "372", "endLine": 69, "endColumn": 14, "suggestions": "469"}, {"ruleId": "366", "severity": 1, "message": "470", "line": 164, "column": 17, "nodeType": null, "messageId": "368", "endLine": 164, "endColumn": 20}, {"ruleId": "366", "severity": 1, "message": "471", "line": 9, "column": 10, "nodeType": null, "messageId": "368", "endLine": 9, "endColumn": 21}, {"ruleId": "369", "severity": 1, "message": "370", "line": 18, "column": 28, "nodeType": "371", "messageId": "372", "endLine": 18, "endColumn": 31, "suggestions": "472"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 59, "column": 11, "nodeType": "371", "messageId": "372", "endLine": 59, "endColumn": 14, "suggestions": "473"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 151, "column": 38, "nodeType": "371", "messageId": "372", "endLine": 151, "endColumn": 41, "suggestions": "474"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 151, "column": 52, "nodeType": "371", "messageId": "372", "endLine": 151, "endColumn": 55, "suggestions": "475"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 167, "column": 39, "nodeType": "371", "messageId": "372", "endLine": 167, "endColumn": 42, "suggestions": "476"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 167, "column": 53, "nodeType": "371", "messageId": "372", "endLine": 167, "endColumn": 56, "suggestions": "477"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 200, "column": 40, "nodeType": "371", "messageId": "372", "endLine": 200, "endColumn": 43, "suggestions": "478"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 200, "column": 54, "nodeType": "371", "messageId": "372", "endLine": 200, "endColumn": 57, "suggestions": "479"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 218, "column": 36, "nodeType": "371", "messageId": "372", "endLine": 218, "endColumn": 39, "suggestions": "480"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 218, "column": 50, "nodeType": "371", "messageId": "372", "endLine": 218, "endColumn": 53, "suggestions": "481"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 297, "column": 54, "nodeType": "371", "messageId": "372", "endLine": 297, "endColumn": 57, "suggestions": "482"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 314, "column": 31, "nodeType": "371", "messageId": "372", "endLine": 314, "endColumn": 34, "suggestions": "483"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 316, "column": 50, "nodeType": "371", "messageId": "372", "endLine": 316, "endColumn": 53, "suggestions": "484"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 338, "column": 28, "nodeType": "371", "messageId": "372", "endLine": 338, "endColumn": 31, "suggestions": "485"}, {"ruleId": "369", "severity": 1, "message": "370", "line": 338, "column": 49, "nodeType": "371", "messageId": "372", "endLine": 338, "endColumn": 52, "suggestions": "486"}, {"ruleId": "366", "severity": 1, "message": "487", "line": 53, "column": 28, "nodeType": null, "messageId": "368", "endLine": 53, "endColumn": 36}, {"ruleId": "366", "severity": 1, "message": "488", "line": 92, "column": 13, "nodeType": null, "messageId": "368", "endLine": 92, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["489", "490"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["491", "492", "493", "494"], ["495", "496", "497", "498"], "'testData' is assigned a value but never used.", "'setTestData' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Clock' is defined but never used.", "'Target' is defined but never used.", "'Zap' is defined but never used.", "'Star' is defined but never used.", "'BookOpen' is defined but never used.", "'Users' is defined but never used.", "'Dumbbell' is defined but never used.", "'Eye' is defined but never used.", "'useInfiniteExercises' is defined but never used.", "'CardContent' is defined but never used.", "'LoadingCard' is defined but never used.", "'LoadingPage' is defined but never used.", "'ErrorMessage' is defined but never used.", "'Settings' is defined but never used.", "'TrendingDown' is defined but never used.", "'Heart' is defined but never used.", "'X' is defined but never used.", "'Edit' is defined but never used.", "'Check' is defined but never used.", "'progressStats' is assigned a value but never used.", "'isLoadingProgress' is assigned a value but never used.", "'bodyMeasurements' is assigned a value but never used.", "'isLoadingBody' is assigned a value but never used.", "'workoutIntensity' is assigned a value but never used.", "'isLoadingIntensity' is assigned a value but never used.", ["499", "500"], ["501", "502", "503", "504"], "'Trash2' is defined but never used.", "'programId' is defined but never used.", ["505", "506"], ["507", "508"], ["509", "510"], "'syncManager' is assigned a value but never used.", ["511", "512", "513", "514"], ["515", "516", "517", "518"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["519"], "'Database' is defined but never used.", ["520", "521"], ["522", "523"], "'error' is defined but never used.", ["524", "525"], ["526", "527"], ["528", "529"], ["530", "531"], ["532", "533"], ["534", "535"], ["536", "537"], ["538", "539"], ["540", "541"], ["542", "543"], ["544", "545"], ["546", "547"], ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], ["564", "565"], ["566", "567"], ["568", "569"], ["570", "571"], ["572", "573"], "'refreshKey' is assigned a value but never used.", ["574", "575"], ["576", "577"], ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], ["590", "591"], ["592", "593"], ["594", "595"], ["596", "597"], ["598", "599"], ["600", "601"], ["602", "603"], ["604", "605"], ["606", "607"], "'get' is defined but never used.", "'queryClient' is defined but never used.", ["608", "609"], ["610", "611"], ["612", "613"], ["614", "615"], ["616", "617"], ["618", "619"], ["620", "621"], ["622", "623"], ["624", "625"], ["626", "627"], ["628", "629"], ["630", "631"], ["632", "633"], ["634", "635"], ["636", "637"], "'password' is defined but never used.", "'completedSession' is assigned a value but never used.", {"messageId": "638", "fix": "639", "desc": "640"}, {"messageId": "641", "fix": "642", "desc": "643"}, {"messageId": "644", "data": "645", "fix": "646", "desc": "647"}, {"messageId": "644", "data": "648", "fix": "649", "desc": "650"}, {"messageId": "644", "data": "651", "fix": "652", "desc": "653"}, {"messageId": "644", "data": "654", "fix": "655", "desc": "656"}, {"messageId": "644", "data": "657", "fix": "658", "desc": "647"}, {"messageId": "644", "data": "659", "fix": "660", "desc": "650"}, {"messageId": "644", "data": "661", "fix": "662", "desc": "653"}, {"messageId": "644", "data": "663", "fix": "664", "desc": "656"}, {"messageId": "638", "fix": "665", "desc": "640"}, {"messageId": "641", "fix": "666", "desc": "643"}, {"messageId": "644", "data": "667", "fix": "668", "desc": "647"}, {"messageId": "644", "data": "669", "fix": "670", "desc": "650"}, {"messageId": "644", "data": "671", "fix": "672", "desc": "653"}, {"messageId": "644", "data": "673", "fix": "674", "desc": "656"}, {"messageId": "638", "fix": "675", "desc": "640"}, {"messageId": "641", "fix": "676", "desc": "643"}, {"messageId": "638", "fix": "677", "desc": "640"}, {"messageId": "641", "fix": "678", "desc": "643"}, {"messageId": "638", "fix": "679", "desc": "640"}, {"messageId": "641", "fix": "680", "desc": "643"}, {"messageId": "644", "data": "681", "fix": "682", "desc": "647"}, {"messageId": "644", "data": "683", "fix": "684", "desc": "650"}, {"messageId": "644", "data": "685", "fix": "686", "desc": "653"}, {"messageId": "644", "data": "687", "fix": "688", "desc": "656"}, {"messageId": "644", "data": "689", "fix": "690", "desc": "647"}, {"messageId": "644", "data": "691", "fix": "692", "desc": "650"}, {"messageId": "644", "data": "693", "fix": "694", "desc": "653"}, {"messageId": "644", "data": "695", "fix": "696", "desc": "656"}, {"messageId": "697", "fix": "698", "desc": "699"}, {"messageId": "638", "fix": "700", "desc": "640"}, {"messageId": "641", "fix": "701", "desc": "643"}, {"messageId": "638", "fix": "702", "desc": "640"}, {"messageId": "641", "fix": "703", "desc": "643"}, {"messageId": "638", "fix": "704", "desc": "640"}, {"messageId": "641", "fix": "705", "desc": "643"}, {"messageId": "638", "fix": "706", "desc": "640"}, {"messageId": "641", "fix": "707", "desc": "643"}, {"messageId": "638", "fix": "708", "desc": "640"}, {"messageId": "641", "fix": "709", "desc": "643"}, {"messageId": "638", "fix": "710", "desc": "640"}, {"messageId": "641", "fix": "711", "desc": "643"}, {"messageId": "638", "fix": "712", "desc": "640"}, {"messageId": "641", "fix": "713", "desc": "643"}, {"messageId": "638", "fix": "714", "desc": "640"}, {"messageId": "641", "fix": "715", "desc": "643"}, {"messageId": "638", "fix": "716", "desc": "640"}, {"messageId": "641", "fix": "717", "desc": "643"}, {"messageId": "638", "fix": "718", "desc": "640"}, {"messageId": "641", "fix": "719", "desc": "643"}, {"messageId": "638", "fix": "720", "desc": "640"}, {"messageId": "641", "fix": "721", "desc": "643"}, {"messageId": "638", "fix": "722", "desc": "640"}, {"messageId": "641", "fix": "723", "desc": "643"}, {"messageId": "638", "fix": "724", "desc": "640"}, {"messageId": "641", "fix": "725", "desc": "643"}, {"messageId": "638", "fix": "726", "desc": "640"}, {"messageId": "641", "fix": "727", "desc": "643"}, {"messageId": "638", "fix": "728", "desc": "640"}, {"messageId": "641", "fix": "729", "desc": "643"}, {"messageId": "638", "fix": "730", "desc": "640"}, {"messageId": "641", "fix": "731", "desc": "643"}, {"messageId": "638", "fix": "732", "desc": "640"}, {"messageId": "641", "fix": "733", "desc": "643"}, {"messageId": "638", "fix": "734", "desc": "640"}, {"messageId": "641", "fix": "735", "desc": "643"}, {"messageId": "638", "fix": "736", "desc": "640"}, {"messageId": "641", "fix": "737", "desc": "643"}, {"messageId": "638", "fix": "738", "desc": "640"}, {"messageId": "641", "fix": "739", "desc": "643"}, {"messageId": "638", "fix": "740", "desc": "640"}, {"messageId": "641", "fix": "741", "desc": "643"}, {"messageId": "638", "fix": "742", "desc": "640"}, {"messageId": "641", "fix": "743", "desc": "643"}, {"messageId": "638", "fix": "744", "desc": "640"}, {"messageId": "641", "fix": "745", "desc": "643"}, {"messageId": "638", "fix": "746", "desc": "640"}, {"messageId": "641", "fix": "747", "desc": "643"}, {"messageId": "638", "fix": "748", "desc": "640"}, {"messageId": "641", "fix": "749", "desc": "643"}, {"messageId": "638", "fix": "750", "desc": "640"}, {"messageId": "641", "fix": "751", "desc": "643"}, {"messageId": "638", "fix": "752", "desc": "640"}, {"messageId": "641", "fix": "753", "desc": "643"}, {"messageId": "638", "fix": "754", "desc": "640"}, {"messageId": "641", "fix": "755", "desc": "643"}, {"messageId": "638", "fix": "756", "desc": "640"}, {"messageId": "641", "fix": "757", "desc": "643"}, {"messageId": "638", "fix": "758", "desc": "640"}, {"messageId": "641", "fix": "759", "desc": "643"}, {"messageId": "638", "fix": "760", "desc": "640"}, {"messageId": "641", "fix": "761", "desc": "643"}, {"messageId": "638", "fix": "762", "desc": "640"}, {"messageId": "641", "fix": "763", "desc": "643"}, {"messageId": "638", "fix": "764", "desc": "640"}, {"messageId": "641", "fix": "765", "desc": "643"}, {"messageId": "638", "fix": "766", "desc": "640"}, {"messageId": "641", "fix": "767", "desc": "643"}, {"messageId": "638", "fix": "768", "desc": "640"}, {"messageId": "641", "fix": "769", "desc": "643"}, {"messageId": "638", "fix": "770", "desc": "640"}, {"messageId": "641", "fix": "771", "desc": "643"}, {"messageId": "638", "fix": "772", "desc": "640"}, {"messageId": "641", "fix": "773", "desc": "643"}, {"messageId": "638", "fix": "774", "desc": "640"}, {"messageId": "641", "fix": "775", "desc": "643"}, {"messageId": "638", "fix": "776", "desc": "640"}, {"messageId": "641", "fix": "777", "desc": "643"}, {"messageId": "638", "fix": "778", "desc": "640"}, {"messageId": "641", "fix": "779", "desc": "643"}, {"messageId": "638", "fix": "780", "desc": "640"}, {"messageId": "641", "fix": "781", "desc": "643"}, {"messageId": "638", "fix": "782", "desc": "640"}, {"messageId": "641", "fix": "783", "desc": "643"}, {"messageId": "638", "fix": "784", "desc": "640"}, {"messageId": "641", "fix": "785", "desc": "643"}, {"messageId": "638", "fix": "786", "desc": "640"}, {"messageId": "641", "fix": "787", "desc": "643"}, {"messageId": "638", "fix": "788", "desc": "640"}, {"messageId": "641", "fix": "789", "desc": "643"}, {"messageId": "638", "fix": "790", "desc": "640"}, {"messageId": "641", "fix": "791", "desc": "643"}, {"messageId": "638", "fix": "792", "desc": "640"}, {"messageId": "641", "fix": "793", "desc": "643"}, {"messageId": "638", "fix": "794", "desc": "640"}, {"messageId": "641", "fix": "795", "desc": "643"}, {"messageId": "638", "fix": "796", "desc": "640"}, {"messageId": "641", "fix": "797", "desc": "643"}, {"messageId": "638", "fix": "798", "desc": "640"}, {"messageId": "641", "fix": "799", "desc": "643"}, {"messageId": "638", "fix": "800", "desc": "640"}, {"messageId": "641", "fix": "801", "desc": "643"}, {"messageId": "638", "fix": "802", "desc": "640"}, {"messageId": "641", "fix": "803", "desc": "643"}, {"messageId": "638", "fix": "804", "desc": "640"}, {"messageId": "641", "fix": "805", "desc": "643"}, {"messageId": "638", "fix": "806", "desc": "640"}, {"messageId": "641", "fix": "807", "desc": "643"}, {"messageId": "638", "fix": "808", "desc": "640"}, {"messageId": "641", "fix": "809", "desc": "643"}, {"messageId": "638", "fix": "810", "desc": "640"}, {"messageId": "641", "fix": "811", "desc": "643"}, {"messageId": "638", "fix": "812", "desc": "640"}, {"messageId": "641", "fix": "813", "desc": "643"}, {"messageId": "638", "fix": "814", "desc": "640"}, {"messageId": "641", "fix": "815", "desc": "643"}, {"messageId": "638", "fix": "816", "desc": "640"}, {"messageId": "641", "fix": "817", "desc": "643"}, "suggestUnknown", {"range": "818", "text": "819"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "820", "text": "821"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "822"}, {"range": "823", "text": "824"}, "Replace with `&apos;`.", {"alt": "825"}, {"range": "826", "text": "827"}, "Replace with `&lsquo;`.", {"alt": "828"}, {"range": "829", "text": "830"}, "Replace with `&#39;`.", {"alt": "831"}, {"range": "832", "text": "833"}, "Replace with `&rsquo;`.", {"alt": "822"}, {"range": "834", "text": "835"}, {"alt": "825"}, {"range": "836", "text": "837"}, {"alt": "828"}, {"range": "838", "text": "839"}, {"alt": "831"}, {"range": "840", "text": "841"}, {"range": "842", "text": "819"}, {"range": "843", "text": "821"}, {"alt": "822"}, {"range": "844", "text": "845"}, {"alt": "825"}, {"range": "846", "text": "847"}, {"alt": "828"}, {"range": "848", "text": "849"}, {"alt": "831"}, {"range": "850", "text": "851"}, {"range": "852", "text": "819"}, {"range": "853", "text": "821"}, {"range": "854", "text": "819"}, {"range": "855", "text": "821"}, {"range": "856", "text": "819"}, {"range": "857", "text": "821"}, {"alt": "822"}, {"range": "858", "text": "859"}, {"alt": "825"}, {"range": "860", "text": "861"}, {"alt": "828"}, {"range": "862", "text": "863"}, {"alt": "831"}, {"range": "864", "text": "865"}, {"alt": "822"}, {"range": "866", "text": "867"}, {"alt": "825"}, {"range": "868", "text": "869"}, {"alt": "828"}, {"range": "870", "text": "871"}, {"alt": "831"}, {"range": "872", "text": "873"}, "replaceEmptyInterfaceWithSuper", {"range": "874", "text": "875"}, "Replace empty interface with a type alias.", {"range": "876", "text": "819"}, {"range": "877", "text": "821"}, {"range": "878", "text": "819"}, {"range": "879", "text": "821"}, {"range": "880", "text": "819"}, {"range": "881", "text": "821"}, {"range": "882", "text": "819"}, {"range": "883", "text": "821"}, {"range": "884", "text": "819"}, {"range": "885", "text": "821"}, {"range": "886", "text": "819"}, {"range": "887", "text": "821"}, {"range": "888", "text": "819"}, {"range": "889", "text": "821"}, {"range": "890", "text": "819"}, {"range": "891", "text": "821"}, {"range": "892", "text": "819"}, {"range": "893", "text": "821"}, {"range": "894", "text": "819"}, {"range": "895", "text": "821"}, {"range": "896", "text": "819"}, {"range": "897", "text": "821"}, {"range": "898", "text": "819"}, {"range": "899", "text": "821"}, {"range": "900", "text": "819"}, {"range": "901", "text": "821"}, {"range": "902", "text": "819"}, {"range": "903", "text": "821"}, {"range": "904", "text": "819"}, {"range": "905", "text": "821"}, {"range": "906", "text": "819"}, {"range": "907", "text": "821"}, {"range": "908", "text": "819"}, {"range": "909", "text": "821"}, {"range": "910", "text": "819"}, {"range": "911", "text": "821"}, {"range": "912", "text": "819"}, {"range": "913", "text": "821"}, {"range": "914", "text": "819"}, {"range": "915", "text": "821"}, {"range": "916", "text": "819"}, {"range": "917", "text": "821"}, {"range": "918", "text": "819"}, {"range": "919", "text": "821"}, {"range": "920", "text": "819"}, {"range": "921", "text": "821"}, {"range": "922", "text": "819"}, {"range": "923", "text": "821"}, {"range": "924", "text": "819"}, {"range": "925", "text": "821"}, {"range": "926", "text": "819"}, {"range": "927", "text": "821"}, {"range": "928", "text": "819"}, {"range": "929", "text": "821"}, {"range": "930", "text": "819"}, {"range": "931", "text": "821"}, {"range": "932", "text": "819"}, {"range": "933", "text": "821"}, {"range": "934", "text": "819"}, {"range": "935", "text": "821"}, {"range": "936", "text": "819"}, {"range": "937", "text": "821"}, {"range": "938", "text": "819"}, {"range": "939", "text": "821"}, {"range": "940", "text": "819"}, {"range": "941", "text": "821"}, {"range": "942", "text": "819"}, {"range": "943", "text": "821"}, {"range": "944", "text": "819"}, {"range": "945", "text": "821"}, {"range": "946", "text": "819"}, {"range": "947", "text": "821"}, {"range": "948", "text": "819"}, {"range": "949", "text": "821"}, {"range": "950", "text": "819"}, {"range": "951", "text": "821"}, {"range": "952", "text": "819"}, {"range": "953", "text": "821"}, {"range": "954", "text": "819"}, {"range": "955", "text": "821"}, {"range": "956", "text": "819"}, {"range": "957", "text": "821"}, {"range": "958", "text": "819"}, {"range": "959", "text": "821"}, {"range": "960", "text": "819"}, {"range": "961", "text": "821"}, {"range": "962", "text": "819"}, {"range": "963", "text": "821"}, {"range": "964", "text": "819"}, {"range": "965", "text": "821"}, {"range": "966", "text": "819"}, {"range": "967", "text": "821"}, {"range": "968", "text": "819"}, {"range": "969", "text": "821"}, {"range": "970", "text": "819"}, {"range": "971", "text": "821"}, {"range": "972", "text": "819"}, {"range": "973", "text": "821"}, {"range": "974", "text": "819"}, {"range": "975", "text": "821"}, {"range": "976", "text": "819"}, {"range": "977", "text": "821"}, {"range": "978", "text": "819"}, {"range": "979", "text": "821"}, {"range": "980", "text": "819"}, {"range": "981", "text": "821"}, {"range": "982", "text": "819"}, {"range": "983", "text": "821"}, {"range": "984", "text": "819"}, {"range": "985", "text": "821"}, {"range": "986", "text": "819"}, {"range": "987", "text": "821"}, {"range": "988", "text": "819"}, {"range": "989", "text": "821"}, {"range": "990", "text": "819"}, {"range": "991", "text": "821"}, {"range": "992", "text": "819"}, {"range": "993", "text": "821"}, [3046, 3049], "unknown", [3046, 3049], "never", "&apos;", [7507, 7544], "\n              Don&apos;t have an account?", "&lsquo;", [7507, 7544], "\n              Don&lsquo;t have an account?", "&#39;", [7507, 7544], "\n              Don&#39;t have an account?", "&rsquo;", [7507, 7544], "\n              Don&rsquo;t have an account?", [3415, 3466], "\n                  Today&apos;s Workout\n                ", [3415, 3466], "\n                  Today&lsquo;s Workout\n                ", [3415, 3466], "\n                  Today&#39;s Workout\n                ", [3415, 3466], "\n                  Today&rsquo;s Workout\n                ", [5135, 5138], [5135, 5138], [11111, 11132], "This month&apos;s activity", [11111, 11132], "This month&lsquo;s activity", [11111, 11132], "This month&#39;s activity", [11111, 11132], "This month&rsquo;s activity", [7752, 7755], [7752, 7755], [9354, 9357], [9354, 9357], [11291, 11294], [11291, 11294], [5414, 5473], " you&apos;re looking for doesn't exist or has been moved.\n      ", [5414, 5473], " you&lsquo;re looking for doesn't exist or has been moved.\n      ", [5414, 5473], " you&#39;re looking for doesn't exist or has been moved.\n      ", [5414, 5473], " you&rsquo;re looking for doesn't exist or has been moved.\n      ", [5414, 5473], " you're looking for doesn&apos;t exist or has been moved.\n      ", [5414, 5473], " you're looking for doesn&lsquo;t exist or has been moved.\n      ", [5414, 5473], " you're looking for doesn&#39;t exist or has been moved.\n      ", [5414, 5473], " you're looking for doesn&rsquo;t exist or has been moved.\n      ", [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [398, 401], [398, 401], [2224, 2227], [2224, 2227], [5048, 5051], [5048, 5051], [5308, 5311], [5308, 5311], [5571, 5574], [5571, 5574], [262, 265], [262, 265], [346, 349], [346, 349], [3388, 3391], [3388, 3391], [3552, 3555], [3552, 3555], [3739, 3742], [3739, 3742], [3931, 3934], [3931, 3934], [4107, 4110], [4107, 4110], [4422, 4425], [4422, 4425], [4609, 4612], [4609, 4612], [1112, 1115], [1112, 1115], [3988, 3991], [3988, 3991], [4201, 4204], [4201, 4204], [6245, 6248], [6245, 6248], [6638, 6641], [6638, 6641], [7034, 7037], [7034, 7037], [7326, 7329], [7326, 7329], [7460, 7463], [7460, 7463], [559, 562], [559, 562], [519, 522], [519, 522], [736, 739], [736, 739], [1143, 1146], [1143, 1146], [6190, 6193], [6190, 6193], [928, 931], [928, 931], [1981, 1984], [1981, 1984], [835, 838], [835, 838], [1327, 1330], [1327, 1330], [1569, 1572], [1569, 1572], [3416, 3419], [3416, 3419], [3446, 3449], [3446, 3449], [3683, 3686], [3683, 3686], [3825, 3828], [3825, 3828], [4076, 4079], [4076, 4079], [4282, 4285], [4282, 4285], [5018, 5021], [5018, 5021], [5770, 5773], [5770, 5773], [7954, 7957], [7954, 7957], [8016, 8019], [8016, 8019], [8045, 8048], [8045, 8048], [1849, 1852], [1849, 1852], [670, 673], [670, 673], [1610, 1613], [1610, 1613], [3910, 3913], [3910, 3913], [3924, 3927], [3924, 3927], [4320, 4323], [4320, 4323], [4334, 4337], [4334, 4337], [5397, 5400], [5397, 5400], [5411, 5414], [5411, 5414], [5892, 5895], [5892, 5895], [5906, 5909], [5906, 5909], [7962, 7965], [7962, 7965], [8376, 8379], [8376, 8379], [8481, 8484], [8481, 8484], [8954, 8957], [8954, 8957], [8975, 8978], [8975, 8978]]