(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[76],{303:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return s}});let s=r(4252)._(r(4232)).default.createContext({})},465:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("dumbbell",[["path",{d:"M17.596 12.768a2 2 0 1 0 2.829-2.829l-1.768-1.767a2 2 0 0 0 2.828-2.829l-2.828-2.828a2 2 0 0 0-2.829 2.828l-1.767-1.768a2 2 0 1 0-2.829 2.829z",key:"9m4mmf"}],["path",{d:"m2.5 21.5 1.4-1.4",key:"17g3f0"}],["path",{d:"m20.1 3.9 1.4-1.4",key:"1qn309"}],["path",{d:"M5.343 21.485a2 2 0 1 0 2.829-2.828l1.767 1.768a2 2 0 1 0 2.829-2.829l-6.364-6.364a2 2 0 1 0-2.829 2.829l1.768 1.767a2 2 0 0 0-2.828 2.829z",key:"1t2c92"}],["path",{d:"m9.6 14.4 4.8-4.8",key:"6umqxw"}]])},472:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let s=r(4252),n=r(7876),i=s._(r(4232)),a=r(2746);async function o(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,a.loadGetInitialProps)(t,r)}}class l extends i.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,n.jsx)(e,{...t})}}l.origGetInitialProps=o,l.getInitialProps=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},920:(e,t,r)=>{"use strict";r.d(t,{m:()=>i});var s=r(5910),n=r(2020),i=new class extends s.Q{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!n.S$&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}}},1239:(e,t,r)=>{"use strict";r.d(t,{t:()=>i});var s=r(5910),n=r(2020),i=new class extends s.Q{#s=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!n.S$&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#s!==e&&(this.#s=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#s}}},1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1539:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1976:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2020:(e,t,r)=>{"use strict";r.d(t,{Cp:()=>f,EN:()=>p,Eh:()=>c,F$:()=>h,GU:()=>O,MK:()=>u,S$:()=>s,ZM:()=>E,ZZ:()=>S,Zw:()=>i,d2:()=>l,f8:()=>m,gn:()=>a,hT:()=>k,j3:()=>o,lQ:()=>n,nJ:()=>d,pl:()=>x,y9:()=>w,yy:()=>v});var s="undefined"==typeof window||"Deno"in globalThis;function n(){}function i(e,t){return"function"==typeof e?e(t):e}function a(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function l(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function u(e,t){let{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:a,stale:o}=e;if(a){if(s){if(t.queryHash!==h(a,t.options))return!1}else if(!f(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!n||n===t.state.fetchStatus)&&(!i||!!i(t))}function d(e,t){let{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(p(t.options.mutationKey)!==p(i))return!1}else if(!f(t.options.mutationKey,i))return!1}return(!s||t.state.status===s)&&(!n||!!n(t))}function h(e,t){return(t?.queryKeyHashFn||p)(e)}function p(e){return JSON.stringify(e,(e,t)=>g(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function f(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>f(e[r],t[r]))}function m(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function y(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function g(e){if(!b(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!b(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function b(e){return"[object Object]"===Object.prototype.toString.call(e)}function v(e){return new Promise(t=>{setTimeout(t,e)})}function x(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function e(t,r){if(t===r)return t;let s=y(t)&&y(r);if(s||g(t)&&g(r)){let n=s?t:Object.keys(t),i=n.length,a=s?r:Object.keys(r),o=a.length,l=s?[]:{},c=new Set(n),u=0;for(let n=0;n<o;n++){let i=s?n:a[n];(!s&&c.has(i)||s)&&void 0===t[i]&&void 0===r[i]?(l[i]=void 0,u++):(l[i]=e(t[i],r[i]),l[i]===t[i]&&void 0!==t[i]&&u++)}return i===o&&u===i?t:l}return r}(e,t):t}function w(e,t,r=0){let s=[...e,t];return r&&s.length>r?s.slice(1):s}function S(e,t,r=0){let s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var k=Symbol();function E(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==k?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}function O(e,t){return"function"==typeof e?e(...t):!!e}},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,a=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let i=n(t)||n(s);return a[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...c}[t]):({...o,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2450:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var s=r(7283),n=r(6866);class i{static async getProgressRecords(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.type&&t.append("type",e.type),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate),e.exerciseId&&t.append("exerciseId",e.exerciseId),e.workoutId&&t.append("workoutId",e.workoutId);let r=t.toString(),i=r?"".concat(n.i.ENDPOINTS.PROGRESS.LIST,"?").concat(r):n.i.ENDPOINTS.PROGRESS.LIST;return s.u.get(i)}static async getProgressRecord(e){return s.u.get(n.i.ENDPOINTS.PROGRESS.DETAILS(e))}static async createProgressRecord(e){return s.u.post(n.i.ENDPOINTS.PROGRESS.CREATE,e)}static async updateProgressRecord(e,t){return s.u.patch(n.i.ENDPOINTS.PROGRESS.UPDATE(e),t)}static async deleteProgressRecord(e){return s.u.delete(n.i.ENDPOINTS.PROGRESS.DELETE(e))}static async getProgressStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return s.u.get("".concat(n.i.ENDPOINTS.PROGRESS.STATS,"?period=").concat(e))}static async getWorkoutStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return s.u.get("/api/progress/workout-stats?period=".concat(e))}static async getExerciseProgress(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"month";return s.u.get("/api/progress/exercise/".concat(e,"?period=").concat(t))}static async getBodyMeasurements(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return s.u.get("/api/progress/body-measurements?period=".concat(e))}static async addBodyMeasurement(e){return s.u.post("/api/progress/body-measurements",e)}static async getFitnessGoals(){return s.u.get("/api/progress/goals")}static async createFitnessGoal(e){return s.u.post("/api/progress/goals",e)}static async updateGoalProgress(e,t){return s.u.patch("/api/progress/goals/".concat(e),{currentValue:t})}static async getAchievements(){return s.u.get("/api/progress/achievements")}static async getWorkoutCalendar(e,t){return s.u.get("/api/progress/calendar?year=".concat(e,"&month=").concat(t))}static async getPersonalRecords(){return s.u.get("/api/progress/personal-records")}static async getStrengthProgression(e){let t=e?"?exercises=".concat(e.join(",")):"";return s.u.get("/api/progress/strength-progression".concat(t))}static async exportProgressData(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"csv",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all",r=await fetch("".concat(n.i.BASE_URL,"/api/progress/export?format=").concat(e,"&period=").concat(t),{method:"GET",headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth-token"))}});if(!r.ok)throw Error("Failed to export data");return r.blob()}static async getWorkoutIntensity(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return s.u.get("/api/progress/workout-intensity?period=".concat(e))}}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(5155),n=r(2115),i=r(4907);let a=n.forwardRef((e,t)=>{let{className:r,type:n,...a}=e;return(0,s.jsx)("input",{type:n,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});a.displayName="Input"},2596:(e,t,r)=>{"use strict";function s(){for(var e,t,r=0,s="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,s,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(s=e(t[r]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s);return n}(e))&&(s&&(s+=" "),s+=t);return s}r.d(t,{$:()=>s})},2659:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},2664:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let s=r(9991),n=r(7102);function i(e){if(!(0,s.isAbsoluteUrl)(e))return!0;try{let t=(0,s.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,n.hasBasePath)(r.pathname)}catch(e){return!1}}},2713:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return o},urlObjectKeys:function(){return a}});let s=r(6966)._(r(8859)),n=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",a=e.pathname||"",o=e.hash||"",l=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),l&&"object"==typeof l&&(l=String(s.urlQueryToSearchParams(l)));let u=e.search||l&&"?"+l||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||n.test(i))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),o&&"#"!==o[0]&&(o="#"+o),u&&"?"!==u[0]&&(u="?"+u),""+i+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+o}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return i(e)}},2881:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var s=r(7283),n=r(6866);class i{static async getWorkoutSessions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.status&&t.append("status",e.status),e.programId&&t.append("programId",e.programId),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate);let r=t.toString(),i=r?"".concat(n.i.ENDPOINTS.WORKOUTS.LIST,"?").concat(r):n.i.ENDPOINTS.WORKOUTS.LIST;return s.u.get(i)}static async getWorkoutSession(e){return s.u.get(n.i.ENDPOINTS.WORKOUTS.DETAILS(e))}static async createWorkoutSession(e){return s.u.post(n.i.ENDPOINTS.WORKOUTS.CREATE,e)}static async updateWorkoutSession(e,t){return s.u.patch(n.i.ENDPOINTS.WORKOUTS.UPDATE(e),t)}static async deleteWorkoutSession(e){return s.u.delete(n.i.ENDPOINTS.WORKOUTS.DELETE(e))}static async startWorkoutSession(e){return s.u.post("".concat(n.i.ENDPOINTS.WORKOUTS.DETAILS(e),"/start"))}static async completeWorkoutSession(e,t){return s.u.post("".concat(n.i.ENDPOINTS.WORKOUTS.DETAILS(e),"/complete"),t)}static async getWorkoutPrograms(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.category&&t.append("category",e.category),e.difficulty&&t.append("difficulty",e.difficulty),e.duration&&t.append("duration",e.duration);let r=t.toString(),i=r?"".concat(n.i.ENDPOINTS.PROGRAMS.LIST,"?").concat(r):n.i.ENDPOINTS.PROGRAMS.LIST;return s.u.get(i)}static async getWorkoutProgram(e){return s.u.get(n.i.ENDPOINTS.PROGRAMS.DETAILS(e))}static async createWorkoutProgram(e){return s.u.post(n.i.ENDPOINTS.PROGRAMS.CREATE,e)}static async updateWorkoutProgram(e,t){return s.u.patch(n.i.ENDPOINTS.PROGRAMS.UPDATE(e),t)}static async deleteWorkoutProgram(e){return s.u.delete(n.i.ENDPOINTS.PROGRAMS.DELETE(e))}static async joinWorkoutProgram(e){return s.u.post("".concat(n.i.ENDPOINTS.PROGRAMS.DETAILS(e),"/join"))}static async leaveWorkoutProgram(e){return s.u.post("".concat(n.i.ENDPOINTS.PROGRAMS.DETAILS(e),"/leave"))}static async getUserPrograms(){return(await s.u.get("/api/user/programs")).programs||[]}static async getPopularPrograms(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=new URLSearchParams({sort:"popular",limit:e.toString()}),r="".concat(n.i.ENDPOINTS.PROGRAMS.LIST,"?").concat(t.toString());return(await s.u.get(r)).data||[]}static async getRecommendedPrograms(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6,t=new URLSearchParams({recommended:"true",limit:e.toString()}),r="".concat(n.i.ENDPOINTS.PROGRAMS.LIST,"?").concat(t.toString());return(await s.u.get(r)).data||[]}static async generateAIWorkout(e){return s.u.post("/api/workouts/generate",e)}static async getWorkoutStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"month";return s.u.get("/api/workouts/stats?period=".concat(e))}static async getWorkoutHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.startDate&&t.append("startDate",e.startDate),e.endDate&&t.append("endDate",e.endDate);let r=t.toString();return s.u.get(r?"/api/workouts/history?".concat(r):"/api/workouts/history")}}},2960:(e,t,r)=>{"use strict";r.d(t,{I:()=>j});var s=r(920),n=r(7165),i=r(9853),a=r(5910),o=r(3504),l=r(2020),c=class extends a.Q{constructor(e,t){super(),this.options=t,this.#n=e,this.#i=null,this.#a=(0,o.T)(),this.options.experimental_prefetchInRender||this.#a.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#n;#o=void 0;#l=void 0;#c=void 0;#u;#d;#a;#i;#h;#p;#f;#m;#y;#g;#b=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#o.addObserver(this),u(this.#o,this.options)?this.#v():this.updateResult(),this.#x())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#o,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#o,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#w(),this.#S(),this.#o.removeObserver(this)}setOptions(e){let t=this.options,r=this.#o;if(this.options=this.#n.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#o))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#k(),this.#o.setOptions(this.options),t._defaulted&&!(0,l.f8)(this.options,t)&&this.#n.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#o,observer:this});let s=this.hasListeners();s&&h(this.#o,r,this.options,t)&&this.#v(),this.updateResult(),s&&(this.#o!==r||(0,l.Eh)(this.options.enabled,this.#o)!==(0,l.Eh)(t.enabled,this.#o)||(0,l.d2)(this.options.staleTime,this.#o)!==(0,l.d2)(t.staleTime,this.#o))&&this.#E();let n=this.#O();s&&(this.#o!==r||(0,l.Eh)(this.options.enabled,this.#o)!==(0,l.Eh)(t.enabled,this.#o)||n!==this.#g)&&this.#P(n)}getOptimisticResult(e){var t,r;let s=this.#n.getQueryCache().build(this.#n,e),n=this.createResult(s,e);return t=this,r=n,(0,l.f8)(t.getCurrentResult(),r)||(this.#c=n,this.#d=this.options,this.#u=this.#o.state),n}getCurrentResult(){return this.#c}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#b.add(e)}getCurrentQuery(){return this.#o}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#n.defaultQueryOptions(e),r=this.#n.getQueryCache().build(this.#n,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#v({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#c))}#v(e){this.#k();let t=this.#o.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.lQ)),t}#E(){this.#w();let e=(0,l.d2)(this.options.staleTime,this.#o);if(l.S$||this.#c.isStale||!(0,l.gn)(e))return;let t=(0,l.j3)(this.#c.dataUpdatedAt,e);this.#m=setTimeout(()=>{this.#c.isStale||this.updateResult()},t+1)}#O(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#o):this.options.refetchInterval)??!1}#P(e){this.#S(),this.#g=e,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#o)&&(0,l.gn)(this.#g)&&0!==this.#g&&(this.#y=setInterval(()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#v()},this.#g))}#x(){this.#E(),this.#P(this.#O())}#w(){this.#m&&(clearTimeout(this.#m),this.#m=void 0)}#S(){this.#y&&(clearInterval(this.#y),this.#y=void 0)}createResult(e,t){let r,s=this.#o,n=this.options,a=this.#c,c=this.#u,d=this.#d,f=e!==s?e.state:this.#l,{state:m}=e,y={...m},g=!1;if(t._optimisticResults){let r=this.hasListeners(),a=!r&&u(e,t),o=r&&h(e,s,t,n);(a||o)&&(y={...y,...(0,i.k)(m.data,e.options)}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:b,errorUpdatedAt:v,status:x}=y;r=y.data;let w=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===x){let e;a?.isPlaceholderData&&t.placeholderData===d?.placeholderData?(e=a.data,w=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#f?.state.data,this.#f):t.placeholderData,void 0!==e&&(x="success",r=(0,l.pl)(a?.data,e,t),g=!0)}if(t.select&&void 0!==r&&!w)if(a&&r===c?.data&&t.select===this.#h)r=this.#p;else try{this.#h=t.select,r=t.select(r),r=(0,l.pl)(a?.data,r,t),this.#p=r,this.#i=null}catch(e){this.#i=e}this.#i&&(b=this.#i,r=this.#p,v=Date.now(),x="error");let S="fetching"===y.fetchStatus,k="pending"===x,E="error"===x,O=k&&S,P=void 0!==r,j={status:x,fetchStatus:y.fetchStatus,isPending:k,isSuccess:"success"===x,isError:E,isInitialLoading:O,isLoading:O,data:r,dataUpdatedAt:y.dataUpdatedAt,error:b,errorUpdatedAt:v,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>f.dataUpdateCount||y.errorUpdateCount>f.errorUpdateCount,isFetching:S,isRefetching:S&&!k,isLoadingError:E&&!P,isPaused:"paused"===y.fetchStatus,isPlaceholderData:g,isRefetchError:E&&P,isStale:p(e,t),refetch:this.refetch,promise:this.#a};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===j.status?e.reject(j.error):void 0!==j.data&&e.resolve(j.data)},r=()=>{t(this.#a=j.promise=(0,o.T)())},n=this.#a;switch(n.status){case"pending":e.queryHash===s.queryHash&&t(n);break;case"fulfilled":("error"===j.status||j.data!==n.value)&&r();break;case"rejected":("error"!==j.status||j.error!==n.reason)&&r()}}return j}updateResult(){let e=this.#c,t=this.createResult(this.#o,this.options);this.#u=this.#o.state,this.#d=this.options,void 0!==this.#u.data&&(this.#f=this.#o),(0,l.f8)(t,e)||(this.#c=t,this.#j({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#b.size)return!0;let s=new Set(r??this.#b);return this.options.throwOnError&&s.add("error"),Object.keys(this.#c).some(t=>this.#c[t]!==e[t]&&s.has(t))})()}))}#k(){let e=this.#n.getQueryCache().build(this.#n,this.options);if(e===this.#o)return;let t=this.#o;this.#o=e,this.#l=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#x()}#j(e){n.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#c)}),this.#n.getQueryCache().notify({query:this.#o,type:"observerResultsUpdated"})})}};function u(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,r){if(!1!==(0,l.Eh)(t.enabled,e)&&"static"!==(0,l.d2)(t.staleTime,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function h(e,t,r,s){return(e!==t||!1===(0,l.Eh)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&e.isStaleByTime((0,l.d2)(t.staleTime,e))}var f=r(2115),m=r(6715);r(5155);var y=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),g=()=>f.useContext(y),b=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},v=e=>{f.useEffect(()=>{e.clearReset()},[e])},x=e=>{let{result:t,errorResetBoundary:r,throwOnError:s,query:n,suspense:i}=e;return t.isError&&!r.isReset()&&!t.isFetching&&n&&(i&&void 0===t.data||(0,l.GU)(s,[t.error,n]))},w=f.createContext(!1),S=()=>f.useContext(w);w.Provider;var k=e=>{if(e.suspense){let t=e=>"static"===e?e:Math.max(null!=e?e:1e3,1e3),r=e.staleTime;e.staleTime="function"==typeof r?function(){for(var e=arguments.length,s=Array(e),n=0;n<e;n++)s[n]=arguments[n];return t(r(...s))}:t(r),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3))}},E=(e,t)=>e.isLoading&&e.isFetching&&!t,O=(e,t)=>(null==e?void 0:e.suspense)&&t.isPending,P=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function j(e,t){return function(e,t,r){var s,i,a,o,c;let u=S(),d=g(),h=(0,m.jE)(r),p=h.defaultQueryOptions(e);null==(i=h.getDefaultOptions().queries)||null==(s=i._experimental_beforeQuery)||s.call(i,p),p._optimisticResults=u?"isRestoring":"optimistic",k(p),b(p,d),v(d);let y=!h.getQueryCache().get(p.queryHash),[w]=f.useState(()=>new t(h,p)),j=w.getOptimisticResult(p),R=!u&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback(e=>{let t=R?w.subscribe(n.jG.batchCalls(e)):l.lQ;return w.updateResult(),t},[w,R]),()=>w.getCurrentResult(),()=>w.getCurrentResult()),f.useEffect(()=>{w.setOptions(p)},[p,w]),O(p,j))throw P(p,w,d);if(x({result:j,errorResetBoundary:d,throwOnError:p.throwOnError,query:h.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw j.error;if(null==(o=h.getDefaultOptions().queries)||null==(a=o._experimental_afterQuery)||a.call(o,p,j),p.experimental_prefetchInRender&&!l.S$&&E(j,u)){let e=y?P(p,w,d):null==(c=h.getQueryCache().get(p.queryHash))?void 0:c.promise;null==e||e.catch(l.lQ).finally(()=>{w.updateResult()})}return p.notifyOnChangeProps?j:w.trackResult(j)}(e,c,t)}},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},3504:(e,t,r)=>{"use strict";r.d(t,{T:()=>n,b:()=>i});var s=r(2020);function n(){let e,t,r=new Promise((r,s)=>{e=r,t=s});function s(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},r.reject=e=>{s({status:"rejected",reason:e}),t(e)},r}function i(e){let t;if(e.then(e=>(t=e,e),s.lQ)?.catch(s.lQ),void 0!==t)return{data:t}}},3776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let s=r(4232),n=s.useLayoutEffect,i=s.useEffect;function a(e){let{headManager:t,reduceComponentsToState:r}=e;function a(){if(t&&t.mountedInstances){let n=s.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(n,e))}}return n(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),n(()=>(t&&(t._pendingUpdate=a),()=>{t&&(t._pendingUpdate=a)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},3861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},3904:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},3962:(e,t,r)=>{"use strict";let s;r.d(t,{tR:()=>ec,CU:()=>er,wZ:()=>el,E$:()=>ei,eC:()=>ea,t0:()=>es,J9:()=>eo,n8:()=>en});var n,i=r(2115);let a=e=>{let t,r=new Set,s=(e,s)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=s?s:"object"!=typeof n||null===n)?n:Object.assign({},t,n),r.forEach(r=>r(t,e))}},n=()=>t,i={setState:s,getState:n,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(s,n,i);return i},o=e=>e?a(e):a,l=e=>e,c=e=>{let t=o(e),r=e=>(function(e,t=l){let r=i.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return i.useDebugValue(r),r})(t,e);return Object.assign(r,t),r};function u(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var s;let n=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(s=r.getItem(e))?s:null;return i instanceof Promise?i.then(n):n(i)},setItem:(e,s)=>r.setItem(e,JSON.stringify(s,null==t?void 0:t.replacer)),removeItem:e=>r.removeItem(e)}}let d=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>d(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>d(t)(e)}}};var h=Symbol.for("immer-nothing"),p=Symbol.for("immer-draftable"),f=Symbol.for("immer-state");function m(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var y=Object.getPrototypeOf;function g(e){return!!e&&!!e[f]}function b(e){return!!e&&(x(e)||Array.isArray(e)||!!e[p]||!!e.constructor?.[p]||O(e)||P(e))}var v=Object.prototype.constructor.toString();function x(e){if(!e||"object"!=typeof e)return!1;let t=y(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===v}function w(e,t){0===S(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,s)=>t(s,r,e))}function S(e){let t=e[f];return t?t.type_:Array.isArray(e)?1:O(e)?2:3*!!P(e)}function k(e,t){return 2===S(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function E(e,t,r){let s=S(e);2===s?e.set(t,r):3===s?e.add(r):e[t]=r}function O(e){return e instanceof Map}function P(e){return e instanceof Set}function j(e){return e.copy_||e.base_}function R(e,t){if(O(e))return new Map(e);if(P(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=x(e);if(!0!==t&&("class_only"!==t||r)){let t=y(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[f];let r=Reflect.ownKeys(t);for(let s=0;s<r.length;s++){let n=r[s],i=t[n];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[n]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[n]})}return Object.create(y(e),t)}}function N(e,t=!1){return T(e)||g(e)||!b(e)||(S(e)>1&&(e.set=e.add=e.clear=e.delete=A),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>N(t,!0))),e}function A(){m(2)}function T(e){return Object.isFrozen(e)}var C={};function I(e){let t=C[e];return t||m(0,e),t}function _(e,t){t&&(I("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function D(e){M(e),e.drafts_.forEach(F),e.drafts_=null}function M(e){e===n&&(n=e.parent_)}function q(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function F(e){let t=e[f];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function Q(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[f].modified_&&(D(t),m(4)),b(e)&&(e=U(t,e),t.parent_||z(t,e)),t.patches_&&I("Patches").generateReplacementPatches_(r[f].base_,e,t.patches_,t.inversePatches_)):e=U(t,r,[]),D(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==h?e:void 0}function U(e,t,r){if(T(t))return t;let s=t[f];if(!s)return w(t,(n,i)=>L(e,s,t,n,i,r)),t;if(s.scope_!==e)return t;if(!s.modified_)return z(e,s.base_,!0),s.base_;if(!s.finalized_){s.finalized_=!0,s.scope_.unfinalizedDrafts_--;let t=s.copy_,n=t,i=!1;3===s.type_&&(n=new Set(t),t.clear(),i=!0),w(n,(n,a)=>L(e,s,t,n,a,r,i)),z(e,t,!1),r&&e.patches_&&I("Patches").generatePatches_(s,r,e.patches_,e.inversePatches_)}return s.copy_}function L(e,t,r,s,n,i,a){if(g(n)){let a=U(e,n,i&&t&&3!==t.type_&&!k(t.assigned_,s)?i.concat(s):void 0);if(E(r,s,a),!g(a))return;e.canAutoFreeze_=!1}else a&&r.add(n);if(b(n)&&!T(n)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;U(e,n),(!t||!t.scope_.parent_)&&"symbol"!=typeof s&&Object.prototype.propertyIsEnumerable.call(r,s)&&z(e,n)}}function z(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&N(t,r)}var G={get(e,t){if(t===f)return e;let r=j(e);if(!k(r,t)){var s=e,n=r,i=t;let a=H(n,i);return a?"value"in a?a.value:a.get?.call(s.draft_):void 0}let a=r[t];return e.finalized_||!b(a)?a:a===K(e.base_,t)?(B(e),e.copy_[t]=V(a,e)):a},has:(e,t)=>t in j(e),ownKeys:e=>Reflect.ownKeys(j(e)),set(e,t,r){let s=H(j(e),t);if(s?.set)return s.set.call(e.draft_,r),!0;if(!e.modified_){let s=K(j(e),t),n=s?.[f];if(n&&n.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===s?0!==r||1/r==1/s:r!=r&&s!=s)&&(void 0!==r||k(e.base_,t)))return!0;B(e),$(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==K(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,B(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=j(e),s=Reflect.getOwnPropertyDescriptor(r,t);return s?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:s.enumerable,value:r[t]}:s},defineProperty(){m(11)},getPrototypeOf:e=>y(e.base_),setPrototypeOf(){m(12)}},W={};function K(e,t){let r=e[f];return(r?j(r):e)[t]}function H(e,t){if(!(t in e))return;let r=y(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=y(r)}}function $(e){!e.modified_&&(e.modified_=!0,e.parent_&&$(e.parent_))}function B(e){e.copy_||(e.copy_=R(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function V(e,t){let r=O(e)?I("MapSet").proxyMap_(e,t):P(e)?I("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),s={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=s,a=G;r&&(i=[s],a=W);let{revoke:o,proxy:l}=Proxy.revocable(i,a);return s.draft_=l,s.revoke_=o,l}(e,t);return(t?t.scope_:n).drafts_.push(r),r}w(G,(e,t)=>{W[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),W.deleteProperty=function(e,t){return W.set.call(this,e,t,void 0)},W.set=function(e,t,r){return G.set.call(this,e[0],t,r,e[0])};var Z=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let s;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let s=this;return function(e=r,...n){return s.produce(e,e=>t.call(this,e,...n))}}if("function"!=typeof t&&m(6),void 0!==r&&"function"!=typeof r&&m(7),b(e)){let n=q(this),i=V(e,void 0),a=!0;try{s=t(i),a=!1}finally{a?D(n):M(n)}return _(n,r),Q(s,n)}if(e&&"object"==typeof e)m(1,e);else{if(void 0===(s=t(e))&&(s=e),s===h&&(s=void 0),this.autoFreeze_&&N(s,!0),r){let t=[],n=[];I("Patches").generateReplacementPatches_(e,s,t,n),r(t,n)}return s}},this.produceWithPatches=(e,t)=>{let r,s;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,s=t}),r,s]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;b(e)||m(8),g(e)&&(g(t=e)||m(10,t),e=function e(t){let r;if(!b(t)||T(t))return t;let s=t[f];if(s){if(!s.modified_)return s.base_;s.finalized_=!0,r=R(t,s.scope_.immer_.useStrictShallowCopy_)}else r=R(t,!0);return w(r,(t,s)=>{E(r,t,e(s))}),s&&(s.finalized_=!1),r}(t));let r=q(this),s=V(e,void 0);return s[f].isManual_=!0,M(r),s}finishDraft(e,t){let r=e&&e[f];r&&r.isManual_||m(9);let{scope_:s}=r;return _(s,t),Q(void 0,s)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let s=t[r];if(0===s.path.length&&"replace"===s.op){e=s.value;break}}r>-1&&(t=t.slice(r+1));let s=I("Patches").applyPatches_;return g(e)?s(e,t):this.produce(e,e=>s(e,t))}},J=Z.produce;Z.produceWithPatches.bind(Z),Z.setAutoFreeze.bind(Z),Z.setUseStrictShallowCopy.bind(Z),Z.applyPatches.bind(Z),Z.createDraft.bind(Z),Z.finishDraft.bind(Z);let X={theme:"system",language:"en",units:"metric",notifications:{workoutReminders:!0,progressUpdates:!0,achievements:!0,marketing:!1},privacy:{shareProgress:!1,showInLeaderboards:!0,allowDataCollection:!0}},Y={defaultDuration:45,preferredDifficulty:"INTERMEDIATE",favoriteCategories:[],excludedEquipment:[],restTimeBetweenSets:60,autoStartNextExercise:!1,playWorkoutMusic:!0,voiceInstructions:!1},ee={sidebarCollapsed:!1,activeWorkoutSession:null,currentPage:"/",breadcrumbs:[],notifications:[],modals:{workoutComplete:!1,goalAchieved:!1,subscriptionPrompt:!1}},et={isOnline:!0,pendingSync:[],lastSyncTime:null},er=(e=>e?c(e):c)()(((e,t)=>(r,s,n)=>{let i,a={storage:u(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1,l=new Set,c=new Set,h=a.storage;if(!h)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...e)},s,n);let p=()=>{let e=a.partialize({...s()});return h.setItem(a.name,{state:e,version:a.version})},f=n.setState;n.setState=(e,t)=>{f(e,t),p()};let m=e((...e)=>{r(...e),p()},s,n);n.getInitialState=()=>m;let y=()=>{var e,t;if(!h)return;o=!1,l.forEach(e=>{var t;return e(null!=(t=s())?t:m)});let n=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=s())?e:m))||void 0;return d(h.getItem.bind(h))(a.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];else{if(a.migrate){let t=a.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,o]=e;if(r(i=a.merge(o,null!=(t=s())?t:m),!0),n)return p()}).then(()=>{null==n||n(i,void 0),i=s(),o=!0,c.forEach(e=>e(i))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{a={...a,...e},e.storage&&(h=e.storage)},clearStorage:()=>{null==h||h.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>y(),hasHydrated:()=>o,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},a.skipHydration||y(),i||m})((s=(e,t)=>({user:null,isAuthenticated:!1,settings:X,workoutPreferences:Y,ui:ee,offline:et,setUser:t=>e(e=>{e.user=t}),setAuthenticated:t=>e(e=>{e.isAuthenticated=t,t||(e.user=null)}),updateSettings:t=>e(e=>{Object.assign(e.settings,t)}),updateWorkoutPreferences:t=>e(e=>{Object.assign(e.workoutPreferences,t)}),updateUIState:t=>e(e=>{Object.assign(e.ui,t)}),addNotification:t=>e(e=>{let r={...t,id:"notification-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),timestamp:Date.now(),read:!1};e.ui.notifications.unshift(r),e.ui.notifications.length>50&&(e.ui.notifications=e.ui.notifications.slice(0,50))}),markNotificationRead:t=>e(e=>{let r=e.ui.notifications.find(e=>e.id===t);r&&(r.read=!0)}),clearNotifications:()=>e(e=>{e.ui.notifications=[]}),setOnlineStatus:t=>e(e=>{e.offline.isOnline=t}),addPendingSync:t=>e(e=>{let r={...t,id:"sync-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),timestamp:Date.now()};e.offline.pendingSync.push(r)}),removePendingSync:t=>e(e=>{e.offline.pendingSync=e.offline.pendingSync.filter(e=>e.id!==t)}),clearPendingSync:()=>e(e=>{e.offline.pendingSync=[]}),updateLastSyncTime:()=>e(e=>{e.offline.lastSyncTime=Date.now()}),reset:()=>e(()=>({user:null,isAuthenticated:!1,settings:X,workoutPreferences:Y,ui:ee,offline:et}))}),(e,t,r)=>(r.setState=(t,r,...s)=>e("function"==typeof t?J(t):t,r,...s),s(r.setState,t,r))),{name:"ai-fitness-app-store",storage:u(()=>localStorage),partialize:e=>({settings:e.settings,workoutPreferences:e.workoutPreferences,ui:{sidebarCollapsed:e.ui.sidebarCollapsed},offline:{pendingSync:e.offline.pendingSync,lastSyncTime:e.offline.lastSyncTime}})})),es=()=>er(e=>e.settings),en=()=>er(e=>e.workoutPreferences),ei=()=>er(e=>e.ui.notifications),ea=()=>er(e=>e.offline),eo=()=>er(e=>e.ui.notifications.filter(e=>!e.read).length),el=()=>er(e=>e.offline.pendingSync.length>0),ec={setUser:e=>er.getState().setUser(e),setAuthenticated:e=>er.getState().setAuthenticated(e),updateSettings:e=>er.getState().updateSettings(e),updateWorkoutPreferences:e=>er.getState().updateWorkoutPreferences(e),addNotification:e=>er.getState().addNotification(e),setOnlineStatus:e=>er.getState().setOnlineStatus(e),addPendingSync:e=>er.getState().addPendingSync(e),removePendingSync:e=>er.getState().removePendingSync(e),updateLastSyncTime:()=>er.getState().updateLastSyncTime(),reset:()=>er.getState().reset()}},4092:(e,t,r)=>{"use strict";r.d(t,{cG:()=>b,d2:()=>m,hQ:()=>y,qt:()=>g,sM:()=>f,wT:()=>v});var s=r(5155),n=r(7924),i=r(4616),a=r(465),o=r(9397),l=r(3109),c=r(6785),u=r(9074),d=r(5220),h=r(4907);function p(e){let{icon:t,title:r,description:a,action:o,className:l}=e;return(0,s.jsxs)("div",{className:(0,h.cn)("flex flex-col items-center justify-center p-12 text-center",l),children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6",children:t||(0,s.jsx)(n.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-md",children:a}),o&&(0,s.jsxs)(d.$,{onClick:o.onClick,className:"flex items-center gap-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),o.label]})]})}function f(e){let{onCreateWorkout:t}=e;return(0,s.jsx)(p,{icon:(0,s.jsx)(a.A,{className:"h-8 w-8 text-gray-400"}),title:"No workouts yet",description:"Start your fitness journey by creating your first workout plan. Choose from our templates or build your own custom routine.",action:t?{label:"Create First Workout",onClick:t}:void 0})}function m(e){let{onAddExercise:t}=e;return(0,s.jsx)(p,{icon:(0,s.jsx)(o.A,{className:"h-8 w-8 text-gray-400"}),title:"No exercises found",description:"We couldn't find any exercises matching your criteria. Try adjusting your filters or browse our complete exercise database.",action:t?{label:"Browse All Exercises",onClick:t}:void 0})}function y(e){let{onStartWorkout:t}=e;return(0,s.jsx)(p,{icon:(0,s.jsx)(l.A,{className:"h-8 w-8 text-gray-400"}),title:"No progress data yet",description:"Complete your first workout to start tracking your fitness progress. We'll show you detailed analytics and insights as you build your routine.",action:t?{label:"Start First Workout",onClick:t}:void 0})}function g(e){let{onCreateGoal:t}=e;return(0,s.jsx)(p,{icon:(0,s.jsx)(c.A,{className:"h-8 w-8 text-gray-400"}),title:"No goals set",description:"Set your first fitness goal to stay motivated and track your progress. Choose from weekly workouts, monthly targets, or create custom goals.",action:t?{label:"Set First Goal",onClick:t}:void 0})}function b(e){let{onStartWorkout:t}=e;return(0,s.jsx)(p,{icon:(0,s.jsx)(u.A,{className:"h-8 w-8 text-gray-400"}),title:"No workout history",description:"Your workout history will appear here once you complete your first session. Start working out to build your fitness timeline.",action:t?{label:"Start Working Out",onClick:t}:void 0})}function v(e){let{searchTerm:t,onClearSearch:r}=e;return(0,s.jsx)(p,{icon:(0,s.jsx)(n.A,{className:"h-8 w-8 text-gray-400"}),title:'No results for "'.concat(t,'"'),description:"We couldn't find anything matching your search. Try different keywords or browse our categories to discover new content.",action:r?{label:"Clear Search",onClick:r}:void 0})}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4227:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,d9:()=>d,IS:()=>c,Fb:()=>l});var s=r(2960),n=r(7283),i=r(6866);class a{static async getExercises(){var e,t,r,s;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=new URLSearchParams;a.search&&o.append("search",a.search),(null==(e=a.equipment)?void 0:e.length)&&a.equipment.forEach(e=>o.append("equipment",e)),(null==(t=a.muscles)?void 0:t.length)&&a.muscles.forEach(e=>o.append("muscles",e)),(null==(r=a.difficulty)?void 0:r.length)&&a.difficulty.forEach(e=>o.append("difficulty",e)),(null==(s=a.category)?void 0:s.length)&&a.category.forEach(e=>o.append("category",e)),a.limit&&o.append("limit",a.limit.toString()),a.offset&&o.append("offset",a.offset.toString());let l=o.toString(),c=l?"".concat(i.i.ENDPOINTS.EXERCISES.LIST,"?").concat(l):i.i.ENDPOINTS.EXERCISES.LIST;return n.u.get(c)}static async searchExercises(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=new URLSearchParams({q:e,limit:t.toString()}),s="".concat(i.i.ENDPOINTS.EXERCISES.SEARCH,"?").concat(r.toString());return(await n.u.get(s)).exercises||[]}static async getExerciseById(e){let t=i.i.ENDPOINTS.EXERCISES.DETAILS(e);return n.u.get(t)}static async getExerciseAttributes(){return n.u.get(i.i.ENDPOINTS.EXERCISES.ATTRIBUTES)}static async getExercisesByFilters(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,s=new URLSearchParams;e.forEach(e=>s.append("equipment",e)),t.forEach(e=>s.append("muscles",e)),s.append("limit",r.toString());let a="".concat(i.i.ENDPOINTS.EXERCISES.LIST,"?").concat(s.toString());return(await n.u.get(a)).data||[]}static async getRandomExercises(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6,t=new URLSearchParams({random:"true",limit:e.toString()}),r="".concat(i.i.ENDPOINTS.EXERCISES.LIST,"?").concat(t.toString());return(await n.u.get(r)).data||[]}static async getPopularExercises(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=new URLSearchParams({sort:"popular",limit:e.toString()}),r="".concat(i.i.ENDPOINTS.EXERCISES.LIST,"?").concat(t.toString());return(await n.u.get(r)).data||[]}static async getExercisesByMuscleGroup(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=new URLSearchParams({muscles:e,limit:t.toString()}),s="".concat(i.i.ENDPOINTS.EXERCISES.LIST,"?").concat(r.toString());return(await n.u.get(s)).data||[]}static async getExercisesByEquipment(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=new URLSearchParams({equipment:e,limit:t.toString()}),s="".concat(i.i.ENDPOINTS.EXERCISES.LIST,"?").concat(r.toString());return(await n.u.get(s)).data||[]}}let o={all:["exercises"],lists:()=>[...o.all,"list"],list:e=>[...o.lists(),e],details:()=>[...o.all,"detail"],detail:e=>[...o.details(),e],search:e=>[...o.all,"search",e],attributes:()=>[...o.all,"attributes"],random:e=>[...o.all,"random",e],popular:e=>[...o.all,"popular",e],byMuscle:(e,t)=>[...o.all,"muscle",e,t],byEquipment:(e,t)=>[...o.all,"equipment",e,t],byFilters:(e,t,r)=>[...o.all,"filters",{equipment:e,muscles:t,limit:r}]};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,s.I)({queryKey:o.list(e),queryFn:()=>a.getExercises(e),enabled:t,staleTime:3e5})}function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,s.I)({queryKey:o.search(e),queryFn:()=>a.searchExercises(e,t),enabled:r&&e.length>0,staleTime:12e4})}function u(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,s.I)({queryKey:o.detail(e),queryFn:()=>a.getExerciseById(e),enabled:t&&!!e,staleTime:6e5})}function d(){return(0,s.I)({queryKey:o.attributes(),queryFn:()=>a.getExerciseAttributes(),staleTime:18e5})}},4290:(e,t,r)=>{"use strict";r.d(t,{Dj:()=>o,aF:()=>l,dW:()=>c});var s=r(3962),n=r(2881),i=r(2450),a=r(9916);class o{static getInstance(){return o.instance||(o.instance=new o),o.instance}setupNetworkListeners(){window.addEventListener("online",()=>{s.tR.setOnlineStatus(!0),this.syncPendingData()}),window.addEventListener("offline",()=>{s.tR.setOnlineStatus(!1)}),s.tR.setOnlineStatus(navigator.onLine)}addToSyncQueue(e){s.tR.addPendingSync(e),navigator.onLine&&this.syncPendingData()}async syncPendingData(){if(this.syncInProgress||!navigator.onLine)return;let e=s.CU.getState().offline.pendingSync;if(0!==e.length){this.syncInProgress=!0;try{s.tR.addNotification({type:"info",title:"Syncing Data",message:"Syncing ".concat(e.length," pending items...")});let t=await Promise.allSettled(e.map(e=>this.syncSingleItem(e))),r=0,n=0;t.forEach((t,i)=>{let a=e[i];"fulfilled"===t.status?(r++,s.tR.removePendingSync(a.id)):(n++,console.error("Failed to sync item ".concat(a.id,":"),t.reason))}),s.tR.updateLastSyncTime(),0===n?s.tR.addNotification({type:"success",title:"Sync Complete",message:"Successfully synced ".concat(r," items")}):s.tR.addNotification({type:"warning",title:"Sync Partially Complete",message:"Synced ".concat(r," items, ").concat(n," failed")}),a.GN.invalidateEntity("workouts"),a.GN.invalidateEntity("progress")}catch(e){console.error("Sync failed:",e),s.tR.addNotification({type:"error",title:"Sync Failed",message:"Failed to sync offline data. Will retry later."})}finally{this.syncInProgress=!1}}}async syncSingleItem(e){switch(e.type){case"workout":return this.syncWorkoutItem(e);case"progress":return this.syncProgressItem(e);case"goal":return this.syncGoalItem(e);default:throw Error("Unknown sync item type: ".concat(e.type))}}async syncWorkoutItem(e){switch(e.action){case"create":if("session"===e.data.type)return n.f.createWorkoutSession(e.data);if("program"===e.data.type)return n.f.createWorkoutProgram(e.data);break;case"update":if("session"===e.data.type)return n.f.updateWorkoutSession(e.data.id,e.data);if("program"===e.data.type)return n.f.updateWorkoutProgram(e.data.id,e.data);break;case"delete":if("session"===e.data.type)return n.f.deleteWorkoutSession(e.data.id);if("program"===e.data.type)return n.f.deleteWorkoutProgram(e.data.id)}throw Error("Unknown workout sync action: ".concat(e.action))}async syncProgressItem(e){switch(e.action){case"create":return i.n.createProgressRecord(e.data);case"update":return i.n.updateProgressRecord(e.data.id,e.data);case"delete":return i.n.deleteProgressRecord(e.data.id)}throw Error("Unknown progress sync action: ".concat(e.action))}async syncGoalItem(e){switch(e.action){case"create":return i.n.createFitnessGoal(e.data);case"update":return i.n.updateGoalProgress(e.data.id,e.data.currentValue);default:throw Error("Unknown goal sync action: ".concat(e.action))}}async forceSyncAll(){await this.syncPendingData()}clearPendingSync(){s.tR.clearPendingSync()}getSyncStatus(){let e=s.CU.getState();return{isOnline:e.offline.isOnline,pendingCount:e.offline.pendingSync.length,lastSyncTime:e.offline.lastSyncTime,syncInProgress:this.syncInProgress}}constructor(){this.syncInProgress=!1,this.syncQueue=[],this.setupNetworkListeners()}}function l(){let e=o.getInstance();return{addToSyncQueue:t=>e.addToSyncQueue(t),syncPendingData:()=>e.syncPendingData(),forceSyncAll:()=>e.forceSyncAll(),clearPendingSync:()=>e.clearPendingSync(),getSyncStatus:()=>e.getSyncStatus()}}function c(){return o.getInstance().getSyncStatus()}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4441:()=>{},4449:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},4560:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,s:()=>a});var s=r(7165),n=r(7948),i=r(6784),a=class extends n.k{#R;#N;#A;constructor(e){super(),this.mutationId=e.mutationId,this.#N=e.mutationCache,this.#R=[],this.state=e.state||o(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#R.includes(e)||(this.#R.push(e),this.clearGcTimeout(),this.#N.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#R=this.#R.filter(t=>t!==e),this.scheduleGc(),this.#N.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#R.length||("pending"===this.state.status?this.scheduleGc():this.#N.remove(this))}continue(){return this.#A?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#T({type:"continue"})};this.#A=(0,i.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#T({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#T({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#N.canRun(this)});let r="pending"===this.state.status,s=!this.#A.canStart();try{if(r)t();else{this.#T({type:"pending",variables:e,isPaused:s}),await this.#N.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#T({type:"pending",context:t,variables:e,isPaused:s})}let n=await this.#A.start();return await this.#N.config.onSuccess?.(n,e,this.state.context,this),await this.options.onSuccess?.(n,e,this.state.context),await this.#N.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,e,this.state.context),this.#T({type:"success",data:n}),n}catch(t){try{throw await this.#N.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#N.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#T({type:"error",error:t})}}finally{this.#N.runNext(this)}}#T(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),s.jG.batch(()=>{this.#R.forEach(t=>{t.onMutationUpdate(e)}),this.#N.notify({mutation:this,type:"updated",action:e})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4907:(e,t,r)=>{"use strict";r.d(t,{cn:()=>ed});var s=r(2596);let n=e=>{let t=l(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||o(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&s[e]?[...n,...s[e]]:n}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],s=t.nextPart.get(r),n=s?i(e.slice(1),s):void 0;if(n)return n;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},a=/^\[(.+)\]$/,o=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},l=e=>{let{theme:t,classGroups:r}=e,s={nextPart:new Map,validators:[]};for(let e in r)c(r[e],s,e,t);return s},c=(e,t,r,s)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return d(e)?void c(e(s),t,r,s):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{c(n,u(t,e),r,s)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,h=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,s=new Map,n=(n,i)=>{r.set(n,i),++t>e&&(t=0,s=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=s.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},p=e=>{let{prefix:t,experimentalParseClassName:r}=e,s=e=>{let t,r=[],s=0,n=0,i=0;for(let a=0;a<e.length;a++){let o=e[a];if(0===s&&0===n){if(":"===o){r.push(e.slice(i,a)),i=a+1;continue}if("/"===o){t=a;continue}}"["===o?s++:"]"===o?s--:"("===o?n++:")"===o&&n--}let a=0===r.length?e:e.substring(i),o=f(a);return{modifiers:r,hasImportantModifier:o!==a,baseClassName:o,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=s;s=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=s;s=t=>r({className:t,parseClassName:e})}return s},f=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],s=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...s.sort(),e),s=[]):s.push(e)}),r.push(...s.sort()),r}},y=e=>({cache:h(e.cacheSize),parseClassName:p(e),sortModifiers:m(e),...n(e)}),g=/\s+/,b=(e,t)=>{let{parseClassName:r,getClassGroupId:s,getConflictingClassGroupIds:n,sortModifiers:i}=t,a=[],o=e.trim().split(g),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:p}=r(t);if(c){l=t+(l.length>0?" "+l:l);continue}let f=!!p,m=s(f?h.substring(0,p):h);if(!m){if(!f||!(m=s(h))){l=t+(l.length>0?" "+l:l);continue}f=!1}let y=i(u).join(":"),g=d?y+"!":y,b=g+m;if(a.includes(b))continue;a.push(b);let v=n(m,f);for(let e=0;e<v.length;++e){let t=v[e];a.push(g+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,r=0,s="";for(;r<arguments.length;)(e=arguments[r++])&&(t=x(e))&&(s&&(s+=" "),s+=t);return s}let x=e=>{let t;if("string"==typeof e)return e;let r="";for(let s=0;s<e.length;s++)e[s]&&(t=x(e[s]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},S=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,O=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,j=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,R=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,N=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,A=e=>E.test(e),T=e=>!!e&&!Number.isNaN(Number(e)),C=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&T(e.slice(0,-1)),_=e=>O.test(e),D=()=>!0,M=e=>P.test(e)&&!j.test(e),q=()=>!1,F=e=>R.test(e),Q=e=>N.test(e),U=e=>!z(e)&&!B(e),L=e=>et(e,ei,q),z=e=>S.test(e),G=e=>et(e,ea,M),W=e=>et(e,eo,T),K=e=>et(e,es,q),H=e=>et(e,en,Q),$=e=>et(e,ec,F),B=e=>k.test(e),V=e=>er(e,ea),Z=e=>er(e,el),J=e=>er(e,es),X=e=>er(e,ei),Y=e=>er(e,en),ee=e=>er(e,ec,!0),et=(e,t,r)=>{let s=S.exec(e);return!!s&&(s[1]?t(s[1]):r(s[2]))},er=(e,t,r=!1)=>{let s=k.exec(e);return!!s&&(s[1]?t(s[1]):r)},es=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,eo=e=>"number"===e,el=e=>"family-name"===e,ec=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,s,n,i=function(o){return s=(r=y(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,i=a,a(o)};function a(e){let t=s(e);if(t)return t;let i=b(e,r);return n(e,i),i}return function(){return i(v.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),s=w("font-weight"),n=w("tracking"),i=w("leading"),a=w("breakpoint"),o=w("container"),l=w("spacing"),c=w("radius"),u=w("shadow"),d=w("inset-shadow"),h=w("text-shadow"),p=w("drop-shadow"),f=w("blur"),m=w("perspective"),y=w("aspect"),g=w("ease"),b=w("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],S=()=>[...x(),B,z],k=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],O=()=>[B,z,l],P=()=>[A,"full","auto",...O()],j=()=>[C,"none","subgrid",B,z],R=()=>["auto",{span:["full",C,B,z]},C,B,z],N=()=>[C,"auto",B,z],M=()=>["auto","min","max","fr",B,z],q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],Q=()=>["auto",...O()],et=()=>[A,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...O()],er=()=>[e,B,z],es=()=>[...x(),J,K,{position:[B,z]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",X,L,{size:[B,z]}],ea=()=>[I,V,G],eo=()=>["","none","full",c,B,z],el=()=>["",T,V,G],ec=()=>["solid","dashed","dotted","double"],eu=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[T,I,J,K],eh=()=>["","none",f,B,z],ep=()=>["none",T,B,z],ef=()=>["none",T,B,z],em=()=>[T,B,z],ey=()=>[A,"full",...O()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[_],breakpoint:[_],color:[D],container:[_],"drop-shadow":[_],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[_],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[_],shadow:[_],spacing:["px",T],text:[_],"text-shadow":[_],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",A,z,B,y]}],container:["container"],columns:[{columns:[T,z,B,o]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:S()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[C,"auto",B,z]}],basis:[{basis:[A,"full","auto",o,...O()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[T,A,"auto","initial","none",z]}],grow:[{grow:["",T,B,z]}],shrink:[{shrink:["",T,B,z]}],order:[{order:[C,"first","last","none",B,z]}],"grid-cols":[{"grid-cols":j()}],"col-start-end":[{col:R()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":j()}],"row-start-end":[{row:R()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:O()}],"gap-x":[{"gap-x":O()}],"gap-y":[{"gap-y":O()}],"justify-content":[{justify:[...q(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...q()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":q()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:O()}],px:[{px:O()}],py:[{py:O()}],ps:[{ps:O()}],pe:[{pe:O()}],pt:[{pt:O()}],pr:[{pr:O()}],pb:[{pb:O()}],pl:[{pl:O()}],m:[{m:Q()}],mx:[{mx:Q()}],my:[{my:Q()}],ms:[{ms:Q()}],me:[{me:Q()}],mt:[{mt:Q()}],mr:[{mr:Q()}],mb:[{mb:Q()}],ml:[{ml:Q()}],"space-x":[{"space-x":O()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":O()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[o,"screen",...et()]}],"min-w":[{"min-w":[o,"screen","none",...et()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,V,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,B,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,z]}],"font-family":[{font:[Z,z,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,B,z]}],"line-clamp":[{"line-clamp":[T,"none",B,W]}],leading:[{leading:[i,...O()]}],"list-image":[{"list-image":["none",B,z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",B,z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[T,"from-font","auto",B,G]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[T,"auto",B,z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",B,z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",B,z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:es()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},C,B,z],radial:["",B,z],conic:[C,B,z]},Y,H]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:eo()}],"rounded-s":[{"rounded-s":eo()}],"rounded-e":[{"rounded-e":eo()}],"rounded-t":[{"rounded-t":eo()}],"rounded-r":[{"rounded-r":eo()}],"rounded-b":[{"rounded-b":eo()}],"rounded-l":[{"rounded-l":eo()}],"rounded-ss":[{"rounded-ss":eo()}],"rounded-se":[{"rounded-se":eo()}],"rounded-ee":[{"rounded-ee":eo()}],"rounded-es":[{"rounded-es":eo()}],"rounded-tl":[{"rounded-tl":eo()}],"rounded-tr":[{"rounded-tr":eo()}],"rounded-br":[{"rounded-br":eo()}],"rounded-bl":[{"rounded-bl":eo()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[T,B,z]}],"outline-w":[{outline:["",T,V,G]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,ee,$]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,ee,$]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[T,G]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",h,ee,$]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[T,B,z]}],"mix-blend":[{"mix-blend":[...eu(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":eu()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[T]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[B,z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[T]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:es()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",B,z]}],filter:[{filter:["","none",B,z]}],blur:[{blur:eh()}],brightness:[{brightness:[T,B,z]}],contrast:[{contrast:[T,B,z]}],"drop-shadow":[{"drop-shadow":["","none",p,ee,$]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",T,B,z]}],"hue-rotate":[{"hue-rotate":[T,B,z]}],invert:[{invert:["",T,B,z]}],saturate:[{saturate:[T,B,z]}],sepia:[{sepia:["",T,B,z]}],"backdrop-filter":[{"backdrop-filter":["","none",B,z]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[T,B,z]}],"backdrop-contrast":[{"backdrop-contrast":[T,B,z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",T,B,z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[T,B,z]}],"backdrop-invert":[{"backdrop-invert":["",T,B,z]}],"backdrop-opacity":[{"backdrop-opacity":[T,B,z]}],"backdrop-saturate":[{"backdrop-saturate":[T,B,z]}],"backdrop-sepia":[{"backdrop-sepia":["",T,B,z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":O()}],"border-spacing-x":[{"border-spacing-x":O()}],"border-spacing-y":[{"border-spacing-y":O()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",B,z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[T,"initial",B,z]}],ease:[{ease:["linear","initial",g,B,z]}],delay:[{delay:[T,B,z]}],animate:[{animate:["none",b,B,z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,B,z]}],"perspective-origin":[{"perspective-origin":S()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[B,z,"","none","gpu","cpu"]}],"transform-origin":[{origin:S()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ey()}],"translate-x":[{"translate-x":ey()}],"translate-y":[{"translate-y":ey()}],"translate-z":[{"translate-z":ey()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",B,z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",B,z]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[T,V,G,W]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ed(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return eu((0,s.$)(t))}},5041:(e,t,r)=>{"use strict";r.d(t,{n:()=>u});var s=r(2115),n=r(4560),i=r(7165),a=r(5910),o=r(2020),l=class extends a.Q{#n;#c=void 0;#C;#I;constructor(e,t){super(),this.#n=e,this.setOptions(t),this.bindMethods(),this.#_()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#n.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#n.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#C,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#C?.state.status==="pending"&&this.#C.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#C?.removeObserver(this)}onMutationUpdate(e){this.#_(),this.#j(e)}getCurrentResult(){return this.#c}reset(){this.#C?.removeObserver(this),this.#C=void 0,this.#_(),this.#j()}mutate(e,t){return this.#I=t,this.#C?.removeObserver(this),this.#C=this.#n.getMutationCache().build(this.#n,this.options),this.#C.addObserver(this),this.#C.execute(e)}#_(){let e=this.#C?.state??(0,n.$)();this.#c={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#j(e){i.jG.batch(()=>{if(this.#I&&this.hasListeners()){let t=this.#c.variables,r=this.#c.context;e?.type==="success"?(this.#I.onSuccess?.(e.data,t,r),this.#I.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#I.onError?.(e.error,t,r),this.#I.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#c)})})}},c=r(6715);function u(e,t){let r=(0,c.jE)(t),[n]=s.useState(()=>new l(r,e));s.useEffect(()=>{n.setOptions(e)},[n,e]);let a=s.useSyncExternalStore(s.useCallback(e=>n.subscribe(i.jG.batchCalls(e)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),u=s.useCallback((e,t)=>{n.mutate(e,t).catch(o.lQ)},[n]);if(a.error&&(0,o.GU)(n.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:u,mutateAsync:a.mutate}}},5220:(e,t,r)=>{"use strict";r.d(t,{$:()=>h});var s=r(5155),n=r(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var a;let e,o,l=(a=r,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let s in t){let n=e[s],i=t[s];/^on[A-Z]/.test(s)?n&&i?r[s]=(...e)=>{let t=i(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...i}:"className"===s&&(r[s]=[n,i].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=i(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():i(e[t],null)}}}}(t,l):l),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,o=n.Children.toArray(i),c=o.find(l);if(c){let e=c.props.children,i=o.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}("Slot"),o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var c=r(2085),u=r(4907);let d=(0,c.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),h=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:o=!1,...l}=e;return(0,s.jsx)(o?a:"button",{className:(0,u.cn)(d({variant:n,size:i,className:r})),ref:t,...l})});h.displayName="Button"},5251:(e,t,r)=>{"use strict";r.d(t,{As:()=>u,go:()=>d,Rt:()=>p,yC:()=>h});var s=r(2960),n=r(6715),i=r(5041),a=r(7283),o=r(6866);class l{static async signIn(e){return a.u.post(o.i.ENDPOINTS.AUTH.SIGNIN,e,{requireAuth:!1})}static async signUp(e){return a.u.post(o.i.ENDPOINTS.AUTH.SIGNUP,e,{requireAuth:!1})}static async signOut(){return a.u.post(o.i.ENDPOINTS.AUTH.SIGNOUT)}static async getSession(){try{return await a.u.get(o.i.ENDPOINTS.AUTH.SESSION)}catch(e){if(401===e.status)return null;throw e}}static async requestPasswordReset(e){return a.u.post(o.i.ENDPOINTS.AUTH.RESET_PASSWORD,{email:e},{requireAuth:!1})}static async resetPassword(e,t){return a.u.post(o.i.ENDPOINTS.AUTH.RESET_PASSWORD,{token:e,password:t},{requireAuth:!1})}static async verifyEmail(e){return a.u.post("/api/auth/verify-email",{token:e},{requireAuth:!1})}static async refreshToken(){return a.u.post("/api/auth/refresh")}static async updateProfile(e){return a.u.patch("/api/auth/profile",e)}static async changePassword(e,t){return a.u.post("/api/auth/change-password",{currentPassword:e,newPassword:t})}static async deleteAccount(e){return a.u.delete("/api/auth/account",{body:JSON.stringify({password:e})})}static async getUserSessions(){return a.u.get("/api/auth/sessions")}static async revokeSession(e){return a.u.delete("/api/auth/sessions/".concat(e))}static async revokeAllOtherSessions(){return a.u.post("/api/auth/sessions/revoke-all")}}let c={all:["auth"],session:()=>[...c.all,"session"],sessions:()=>[...c.all,"sessions"]};function u(){let{data:e,isLoading:t,error:r}=(0,s.I)({queryKey:c.session(),queryFn:()=>l.getSession(),staleTime:3e5,retry:!1});return{user:(null==e?void 0:e.user)||null,isAuthenticated:!!(null==e?void 0:e.user),isLoading:t,error:r,session:e}}function d(){let e=(0,n.jE)();return(0,i.n)({mutationFn:e=>l.signIn(e),onSuccess:t=>{e.setQueryData(c.session(),t),e.invalidateQueries()},onError:e=>{console.error("Sign in failed:",e)}})}function h(){let e=(0,n.jE)();return(0,i.n)({mutationFn:e=>l.signUp(e),onSuccess:t=>{e.setQueryData(c.session(),t),e.invalidateQueries()},onError:e=>{console.error("Sign up failed:",e)}})}function p(){let e=(0,n.jE)();return(0,i.n)({mutationFn:()=>l.signOut(),onSuccess:()=>{e.setQueryData(c.session(),null),e.clear()},onError:t=>{console.error("Sign out failed:",t),e.setQueryData(c.session(),null),e.clear()}})}},5607:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},5679:(e,t,r)=>{"use strict";var s=r(5364);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},defaultHead:function(){return h}});let n=r(4252),i=r(8365),a=r(7876),o=i._(r(4232)),l=n._(r(3776)),c=r(303),u=r(8831),d=r(6807);function h(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(6079);let f=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(h(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return n=>{let i=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?i=!1:t.add(n.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=n.props[t],r=s[t]||new Set;("name"!==t||!a)&&r.has(e)?i=!1:(r.add(e),s[t]=r)}}}return i}}()).reverse().map((e,t)=>{let n=e.key||t;if(s.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:n})})}let y=function(e){let{children:t}=e,r=(0,o.useContext)(c.AmpStateContext),s=(0,o.useContext)(u.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:m,headManager:s,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5690:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},5910:(e,t,r)=>{"use strict";r.d(t,{Q:()=>s});var s=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},6079:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(5155);r(2115);var n=r(2085),i=r(4907);let a=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...n}=e;return(0,s.jsx)("div",{className:(0,i.cn)(a({variant:r}),t),...n})}},6516:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6517:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let s=r(2115);function n(e,t){let r=(0,s.useRef)(null),n=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=i(e,s)),t&&(n.current=i(t,s))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>l,Zp:()=>a,aR:()=>o});var s=r(5155),n=r(2115),i=r(4907);let a=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...n})});a.displayName="Card";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",r),...n})});l.displayName="CardTitle";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...n})});c.displayName="CardDescription";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...n})});u.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"},6715:(e,t,r)=>{"use strict";r.d(t,{Ht:()=>o,jE:()=>a});var s=r(2115),n=r(5155),i=s.createContext(void 0),a=e=>{let t=s.useContext(i);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},o=e=>{let{client:t,children:r}=e;return s.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,n.jsx)(i.Provider,{value:t,children:r})}},6784:(e,t,r)=>{"use strict";r.d(t,{II:()=>d,v_:()=>l,wm:()=>u});var s=r(920),n=r(1239),i=r(3504),a=r(2020);function o(e){return Math.min(1e3*2**e,3e4)}function l(e){return(e??"online")!=="online"||n.t.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function u(e){return e instanceof c}function d(e){let t,r=!1,u=0,d=!1,h=(0,i.T)(),p=()=>s.m.isFocused()&&("always"===e.networkMode||n.t.isOnline())&&e.canRun(),f=()=>l(e.networkMode)&&e.canRun(),m=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),h.resolve(r))},y=r=>{d||(d=!0,e.onError?.(r),t?.(),h.reject(r))},g=()=>new Promise(r=>{t=e=>{(d||p())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,d||e.onContinue?.()}),b=()=>{let t;if(d)return;let s=0===u?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(m).catch(t=>{if(d)return;let s=e.retry??3*!a.S$,n=e.retryDelay??o,i="function"==typeof n?n(u,t):n,l=!0===s||"number"==typeof s&&u<s||"function"==typeof s&&s(u,t);if(r||!l)return void y(t);u++,e.onFail?.(u,t),(0,a.yy)(i).then(()=>p()?void 0:g()).then(()=>{r?y(t):b()})})};return{promise:h,cancel:t=>{d||(y(new c(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:f,start:()=>(f()?b():g().then(b),h)}}},6785:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},6807:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},6866:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});let s={BASE_URL:"http://localhost:3000",ENDPOINTS:{AUTH:{SIGNIN:"/api/auth/signin",SIGNUP:"/api/auth/signup",SIGNOUT:"/api/auth/signout",SESSION:"/api/auth/session",RESET_PASSWORD:"/api/auth/reset-password"},EXERCISES:{LIST:"/api/exercises",SEARCH:"/api/exercises/search",DETAILS:e=>"/api/exercises/".concat(e),ATTRIBUTES:"/api/exercises/attributes"},WORKOUTS:{LIST:"/api/workout-sessions",CREATE:"/api/workout-sessions",DETAILS:e=>"/api/workout-sessions/".concat(e),UPDATE:e=>"/api/workout-sessions/".concat(e),DELETE:e=>"/api/workout-sessions/".concat(e),COMPLETE:e=>"/api/workout-sessions/".concat(e,"/complete"),SYNC:"/api/workout-sessions/sync"},PROGRAMS:{LIST:"/api/programs",CREATE:"/api/programs",DETAILS:e=>"/api/programs/".concat(e),UPDATE:e=>"/api/programs/".concat(e),DELETE:e=>"/api/programs/".concat(e),ENROLL:e=>"/api/programs/".concat(e,"/enroll"),SESSIONS:e=>"/api/programs/".concat(e,"/sessions"),START_SESSION:"/api/programs/sessions/start",COMPLETE_SESSION:"/api/programs/sessions/complete"},PROGRESS:{LIST:"/api/progress",DETAILS:e=>"/api/progress/".concat(e),CREATE:"/api/progress",UPDATE:e=>"/api/progress/".concat(e),DELETE:e=>"/api/progress/".concat(e),OVERVIEW:"/api/progress/overview",STATS:"/api/progress/stats",HISTORY:"/api/progress/history",GOALS:"/api/progress/goals",EXPORT:"/api/progress/export"},PREMIUM:{PLANS:"/api/premium/plans",SUBSCRIPTION:"/api/premium/subscription",CHECKOUT:"/api/premium/checkout",BILLING_PORTAL:"/api/premium/billing-portal"},USERS:{PROFILE:"/api/users/profile",UPDATE_PROFILE:"/api/users/profile",PREFERENCES:"/api/users/preferences"}},TIMEOUT:{DEFAULT:1e4,UPLOAD:3e4,DOWNLOAD:6e4},RETRY:{ATTEMPTS:3,DELAY:1e3,BACKOFF_FACTOR:2}}},6874:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return b}});let s=r(6966),n=r(5155),i=s._(r(2115)),a=r(2757),o=r(5227),l=r(9818),c=r(6654),u=r(9991),d=r(5929);r(3230);let h=r(4930),p=r(2664),f=r(6634);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function y(e){let t,r,s,[a,y]=(0,i.useOptimistic)(h.IDLE_LINK_STATUS),b=(0,i.useRef)(null),{href:v,as:x,children:w,prefetch:S=null,passHref:k,replace:E,shallow:O,scroll:P,onClick:j,onMouseEnter:R,onTouchStart:N,legacyBehavior:A=!1,onNavigate:T,ref:C,unstable_dynamicOnHover:I,..._}=e;t=w,A&&("string"==typeof t||"number"==typeof t)&&(t=(0,n.jsx)("a",{children:t}));let D=i.default.useContext(o.AppRouterContext),M=!1!==S,q=null===S?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:F,as:Q}=i.default.useMemo(()=>{let e=m(v);return{href:e,as:x?m(x):e}},[v,x]);A&&(r=i.default.Children.only(t));let U=A?r&&"object"==typeof r&&r.ref:C,L=i.default.useCallback(e=>(null!==D&&(b.current=(0,h.mountLinkInstance)(e,F,D,q,M,y)),()=>{b.current&&((0,h.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,h.unmountPrefetchableInstance)(e)}),[M,F,D,q,y]),z={ref:(0,c.useMergedRef)(L,U),onClick(e){A||"function"!=typeof j||j(e),A&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),D&&(e.defaultPrevented||function(e,t,r,s,n,a,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,f.dispatchNavigateAction)(r||t,n?"replace":"push",null==a||a,s.current)})}}(e,F,Q,b,E,P,T))},onMouseEnter(e){A||"function"!=typeof R||R(e),A&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),D&&M&&(0,h.onNavigationIntent)(e.currentTarget,!0===I)},onTouchStart:function(e){A||"function"!=typeof N||N(e),A&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),D&&M&&(0,h.onNavigationIntent)(e.currentTarget,!0===I)}};return(0,u.isAbsoluteUrl)(Q)?z.href=Q:A&&!k&&("a"!==r.type||"href"in r.props)||(z.href=(0,d.addBasePath)(Q)),s=A?i.default.cloneElement(r,z):(0,n.jsx)("a",{..._,...z,children:t}),(0,n.jsx)(g.Provider,{value:a,children:s})}r(3180);let g=(0,i.createContext)(h.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6932:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7017:(e,t,r)=>{"use strict";r.d(t,{E:()=>m});var s=r(2020),n=r(9853),i=r(7165),a=r(5910),o=class extends a.Q{constructor(e={}){super(),this.config=e,this.#D=new Map}#D;build(e,t,r){let i=t.queryKey,a=t.queryHash??(0,s.F$)(i,t),o=this.get(a);return o||(o=new n.X({client:e,queryKey:i,queryHash:a,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(i)}),this.add(o)),o}add(e){this.#D.has(e.queryHash)||(this.#D.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#D.get(e.queryHash);t&&(e.destroy(),t===e&&this.#D.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#D.get(e)}getAll(){return[...this.#D.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,s.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,s.MK)(e,t)):t}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},l=r(4560),c=class extends a.Q{constructor(e={}){super(),this.config=e,this.#M=new Set,this.#q=new Map,this.#F=0}#M;#q;#F;build(e,t,r){let s=new l.s({mutationCache:this,mutationId:++this.#F,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#M.add(e);let t=u(e);if("string"==typeof t){let r=this.#q.get(t);r?r.push(e):this.#q.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#M.delete(e)){let t=u(e);if("string"==typeof t){let r=this.#q.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#q.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=u(e);if("string"!=typeof t)return!0;{let r=this.#q.get(t),s=r?.find(e=>"pending"===e.state.status);return!s||s===e}}runNext(e){let t=u(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#q.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){i.jG.batch(()=>{this.#M.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#M.clear(),this.#q.clear()})}getAll(){return Array.from(this.#M)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,s.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,s.nJ)(e,t))}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return i.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(s.lQ))))}};function u(e){return e.options.scope?.id}var d=r(920),h=r(1239);function p(e){return{onFetch:(t,r)=>{let n=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],o=t.state.data?.pageParams||[],l={pages:[],pageParams:[]},c=0,u=async()=>{let r=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},d=(0,s.ZM)(t.options,t.fetchOptions),h=async(e,n,i)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let a=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:n,direction:i?"backward":"forward",meta:t.options.meta};return u(e),e})(),o=await d(a),{maxPages:l}=t.options,c=i?s.ZZ:s.y9;return{pages:c(e.pages,o,l),pageParams:c(e.pageParams,n,l)}};if(i&&a.length){let e="backward"===i,t={pages:a,pageParams:o},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:f)(n,t);l=await h(t,r,e)}else{let t=e??a.length;do{let e=0===c?o[0]??n.initialPageParam:f(n,l);if(c>0&&null==e)break;l=await h(l,e),c++}while(c<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=u}}}function f(e,{pages:t,pageParams:r}){let s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}var m=class{#Q;#N;#U;#L;#z;#G;#W;#K;constructor(e={}){this.#Q=e.queryCache||new o,this.#N=e.mutationCache||new c,this.#U=e.defaultOptions||{},this.#L=new Map,this.#z=new Map,this.#G=0}mount(){this.#G++,1===this.#G&&(this.#W=d.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#Q.onFocus())}),this.#K=h.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#Q.onOnline())}))}unmount(){this.#G--,0===this.#G&&(this.#W?.(),this.#W=void 0,this.#K?.(),this.#K=void 0)}isFetching(e){return this.#Q.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#N.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#Q.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#Q.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,s.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#Q.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let n=this.defaultQueryOptions({queryKey:e}),i=this.#Q.get(n.queryHash),a=i?.state.data,o=(0,s.Zw)(t,a);if(void 0!==o)return this.#Q.build(this,n).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return i.jG.batch(()=>this.#Q.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#Q.get(t.queryHash)?.state}removeQueries(e){let t=this.#Q;i.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#Q;return i.jG.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(i.jG.batch(()=>this.#Q.findAll(e).map(e=>e.cancel(r)))).then(s.lQ).catch(s.lQ)}invalidateQueries(e,t={}){return i.jG.batch(()=>(this.#Q.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(i.jG.batch(()=>this.#Q.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(s.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(s.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#Q.build(this,t);return r.isStaleByTime((0,s.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(s.lQ).catch(s.lQ)}fetchInfiniteQuery(e){return e.behavior=p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(s.lQ).catch(s.lQ)}ensureInfiniteQueryData(e){return e.behavior=p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return h.t.isOnline()?this.#N.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#Q}getMutationCache(){return this.#N}getDefaultOptions(){return this.#U}setDefaultOptions(e){this.#U=e}setQueryDefaults(e,t){this.#L.set((0,s.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#L.values()],r={};return t.forEach(t=>{(0,s.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#z.set((0,s.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#z.values()],r={};return t.forEach(t=>{(0,s.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#U.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,s.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===s.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#U.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#Q.clear(),this.#N.clear()}}},7023:(e,t,r)=>{"use strict";r.d(t,{AV:()=>o,B0:()=>a,z0:()=>l});var s=r(5155),n=r(4907);function i(e){let{size:t="md",className:r}=e;return(0,s.jsx)("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t],r)})}function a(e){let{className:t}=e;return(0,s.jsx)("div",{className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow animate-pulse",t),children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2 mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})})}function o(e){let{title:t="Loading...",description:r="Please wait while we load your content"}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-16",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/3 mx-auto mb-4 animate-pulse"}),(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto animate-pulse"})]})})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,s.jsx)(i,{size:"lg"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:t}),(0,s.jsx)("p",{className:"text-gray-600 text-center max-w-md",children:r})]})})]})}function l(e){let{items:t=6,className:r}=e;return(0,s.jsx)("div",{className:(0,n.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),children:Array.from({length:t}).map((e,t)=>(0,s.jsx)(a,{},t))})}},7165:(e,t,r)=>{"use strict";r.d(t,{jG:()=>n});var s=e=>setTimeout(e,0),n=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=s,a=s=>{t?e.push(s):i(()=>{r(s)})},o=()=>{let t=e;e=[],t.length&&i(()=>{n(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||o()}return r},batchCalls:e=>(...t)=>{a(()=>{e(...t)})},schedule:a,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}()},7283:(e,t,r)=>{"use strict";r.d(t,{u:()=>a});var s=r(6866),n=r(8467);class i{getAuthToken(){{let e=localStorage.getItem("auth-token");if(e)return e;let t=localStorage.getItem("auth-user");if(t)try{return JSON.parse(t).token||null}catch(e){}}return null}buildHeaders(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={...this.defaultHeaders};if(!1!==e.requireAuth){let e=this.getAuthToken();e&&(t.Authorization="Bearer ".concat(e))}return e.headers&&Object.assign(t,e.headers),t}async handleResponse(e){let t,r=e.headers.get("content-type"),s=null==r?void 0:r.includes("application/json");try{t=s?await e.json():await e.text()}catch(t){throw new n.hD("Failed to parse response",e.status)}if(!e.ok)switch(e.status){case 401:throw new n.v3(t.message||"Authentication required");case 422:throw new n.yI(t.message||"Validation failed",t.errors);case 404:throw new n.hD(t.message||"Resource not found",404);case 500:throw new n.hD(t.message||"Internal server error",500);default:throw new n.hD(t.message||"Request failed",e.status)}return t}async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{timeout:r=s.i.TIMEOUT.DEFAULT,retries:i=s.i.RETRY.ATTEMPTS,...a}=t,o=new AbortController,l=setTimeout(()=>o.abort(),r),c={...a,headers:this.buildHeaders(t),signal:o.signal},u=null;for(let t=0;t<=i;t++)try{let t=await fetch("".concat(this.baseUrl).concat(e),c);return clearTimeout(l),await this.handleResponse(t)}catch(r){if(u=r,r instanceof n.v3||r instanceof n.yI)throw r;if(t===i)break;let e=s.i.RETRY.DELAY*Math.pow(s.i.RETRY.BACKOFF_FACTOR,t);await new Promise(t=>setTimeout(t,e))}if(clearTimeout(l),u){if("AbortError"===u.name)throw new n.Dr("Request timeout");if("TypeError"===u.name)throw new n.Dr("Network error - please check your connection");throw u}throw Error("Request failed after all retries")}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.makeRequest(e,{...t,method:"GET"})}async post(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.makeRequest(e,{...r,method:"POST",body:t?JSON.stringify(t):void 0})}async put(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.makeRequest(e,{...r,method:"PUT",body:t?JSON.stringify(t):void 0})}async patch(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.makeRequest(e,{...r,method:"PATCH",body:t?JSON.stringify(t):void 0})}async delete(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.makeRequest(e,{...t,method:"DELETE"})}constructor(){this.baseUrl=s.i.BASE_URL,this.defaultHeaders={"Content-Type":"application/json",Accept:"application/json"}}}let a=new i},7516:(e,t,r)=>{"use strict";r.d(t,{V:()=>S});var s=r(5155),n=r(2115),i=r(6874),a=r.n(i),o=r(5220),l=r(6126),c=r(465),u=r(6517),d=r(4449),h=r(3861),p=r(9946);let f=(0,p.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var m=r(4416);let y=(0,p.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var g=r(2659),b=r(2713);let v=(0,p.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var x=r(5251),w=r(3962);function S(){let[e,t]=(0,n.useState)(!1),{user:r,isAuthenticated:i,isLoading:p}=(0,x.As)(),S=(0,x.Rt)(),k=(0,w.eC)(),E=(0,w.J9)(),O=(0,w.wZ)(),P=async()=>{try{await S.mutateAsync()}catch(e){console.error("Sign out failed:",e)}};return(0,s.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)(a(),{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI-fitness-singles"})]}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)(a(),{href:"/workouts",className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Workouts"}),(0,s.jsx)(a(),{href:"/exercises",className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Exercises"}),(0,s.jsx)(a(),{href:"/progress",className:"text-gray-600 hover:text-blue-600 transition-colors",children:"Progress"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[k.isOnline?(0,s.jsx)(u.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-600"}),O&&(0,s.jsx)(l.E,{variant:"outline",className:"text-xs",children:k.pendingSync.length})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-gray-600"}),E>0&&(0,s.jsx)(l.E,{variant:"destructive",className:"absolute -top-2 -right-2 h-4 w-4 p-0 text-xs flex items-center justify-center",children:E>9?"9+":E})]})]}),p?(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"}):i&&r?(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:["Welcome, ",r.name||r.email]}),(0,s.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:P,disabled:S.isPending,className:"flex items-center space-x-1",children:[(0,s.jsx)(f,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Sign Out"})]})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsx)(a(),{href:"/auth/signin",children:"Sign In"})}),(0,s.jsx)(o.$,{size:"sm",asChild:!0,children:(0,s.jsx)(a(),{href:"/auth/signup",children:"Sign Up"})})]})]}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"p-2",children:e?(0,s.jsx)(m.A,{className:"h-6 w-6"}):(0,s.jsx)(y,{className:"h-6 w-6"})})})]}),e&&(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[k.isOnline?(0,s.jsx)(u.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:k.isOnline?"Online":"Offline"}),O&&(0,s.jsxs)(l.E,{variant:"outline",className:"text-xs",children:[k.pendingSync.length," pending"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-gray-600"}),E>0&&(0,s.jsx)(l.E,{variant:"destructive",className:"text-xs",children:E})]})]}),(0,s.jsx)(a(),{href:"/workouts",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>t(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Workouts"})]})}),(0,s.jsx)(a(),{href:"/exercises",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>t(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Exercises"})]})}),(0,s.jsx)(a(),{href:"/progress",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>t(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(b.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Progress"})]})}),(0,s.jsx)("div",{className:"pt-4 border-t",children:p?(0,s.jsx)("div",{className:"px-3 py-2",children:(0,s.jsx)("div",{className:"w-full h-8 bg-gray-200 rounded animate-pulse"})}):i&&r?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"px-3 py-2",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(v,{className:"h-5 w-5 text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:r.name||r.email})]})}),(0,s.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:P,disabled:S.isPending,className:"w-full justify-start px-3",children:[(0,s.jsx)(f,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]}):(0,s.jsxs)("div",{className:"space-y-2 px-3",children:[(0,s.jsx)(o.$,{variant:"ghost",size:"sm",className:"w-full",asChild:!0,children:(0,s.jsx)(a(),{href:"/auth/signin",onClick:()=>t(!1),children:"Sign In"})}),(0,s.jsx)(o.$,{size:"sm",className:"w-full",asChild:!0,children:(0,s.jsx)(a(),{href:"/auth/signup",onClick:()=>t(!1),children:"Sign Up"})})]})})]})})]})})}},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7580:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},7948:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});var s=r(2020),n=class{#H;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.gn)(this.gcTime)&&(this.#H=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s.S$?1/0:3e5))}clearGcTimeout(){this.#H&&(clearTimeout(this.#H),this.#H=void 0)}}},8467:(e,t,r)=>{"use strict";r.d(t,{Dr:()=>n,hD:()=>s,u1:()=>o,v3:()=>i,yI:()=>a});class s extends Error{get isClientError(){return this.status>=400&&this.status<500}get isServerError(){return this.status>=500}toJSON(){return{name:this.name,message:this.message,status:this.status,code:this.code,details:this.details,stack:this.stack}}constructor(e,t=500,r,n){super(e),this.name="ApiError",this.status=t,this.code=r,this.details=n,Error.captureStackTrace&&Error.captureStackTrace(this,s)}}class n extends s{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class i extends s{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class a extends s{getFieldError(e){let t=this.fieldErrors[e];return t&&t.length>0?t[0]:null}hasFieldError(e){var t;return!!(null==(t=this.fieldErrors[e])?void 0:t.length)}getAllFieldErrors(){return Object.values(this.fieldErrors).flat()}constructor(e="Validation failed",t={}){super(e,422,"VALIDATION_ERROR",t),this.name="ValidationError",this.fieldErrors=t}}function o(e){return e instanceof s||e instanceof Error?e.message:"string"==typeof e?e:"An unexpected error occurred"}},8859:(e,t)=>{"use strict";function r(e){let t={};for(let[r,s]of e.entries()){let e=t[r];void 0===e?t[r]=s:Array.isArray(e)?e.push(s):t[r]=[e,s]}return t}function s(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,s(e));else t.set(r,s(n));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,s]of t.entries())e.append(r,s)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},9037:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9150:(e,t,r)=>{"use strict";r.d(t,{Mm:()=>u,My:()=>l,Ox:()=>f,Rc:()=>d,bR:()=>c,ij:()=>y,mS:()=>p,sK:()=>h,t2:()=>m});var s=r(2960),n=r(6715),i=r(5041),a=r(2881);let o={all:["workouts"],sessions:()=>[...o.all,"sessions"],session:e=>[...o.sessions(),e],sessionsList:e=>[...o.sessions(),"list",e],programs:()=>[...o.all,"programs"],program:e=>[...o.programs(),e],programsList:e=>[...o.programs(),"list",e],userPrograms:()=>[...o.programs(),"user"],popular:e=>[...o.programs(),"popular",e],recommended:e=>[...o.programs(),"recommended",e],stats:e=>[...o.all,"stats",e],history:e=>[...o.all,"history",e]};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,s.I)({queryKey:o.sessionsList(e),queryFn:()=>a.f.getWorkoutSessions(e),staleTime:12e4})}function c(){let e=(0,n.jE)();return(0,i.n)({mutationFn:e=>a.f.createWorkoutSession(e),onSuccess:()=>{e.invalidateQueries({queryKey:o.sessions()})}})}function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,s.I)({queryKey:o.programsList(e),queryFn:()=>a.f.getWorkoutPrograms(e),staleTime:6e5})}function d(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,s.I)({queryKey:o.program(e),queryFn:()=>a.f.getWorkoutProgram(e),enabled:t&&!!e,staleTime:6e5})}function h(){return(0,s.I)({queryKey:o.userPrograms(),queryFn:()=>a.f.getUserPrograms(),staleTime:3e5})}function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return(0,s.I)({queryKey:o.popular(e),queryFn:()=>a.f.getPopularPrograms(e),staleTime:9e5})}function f(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;return(0,s.I)({queryKey:o.recommended(e),queryFn:()=>a.f.getRecommendedPrograms(e),staleTime:6e5})}function m(){let e=(0,n.jE)();return(0,i.n)({mutationFn:e=>a.f.joinWorkoutProgram(e),onSuccess:()=>{e.invalidateQueries({queryKey:o.userPrograms()})}})}function y(){let e=(0,n.jE)();return(0,i.n)({mutationFn:e=>a.f.leaveWorkoutProgram(e),onSuccess:()=>{e.invalidateQueries({queryKey:o.userPrograms()})}})}},9341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let s=r(4252),n=r(7876),i=s._(r(4232)),a=s._(r(5679)),o={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function l(e){let{req:t,res:r,err:s}=e;return{statusCode:r&&r.statusCode?r.statusCode:s?s.statusCode:404,hostname:window.location.hostname}}let c={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class u extends i.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||o[e]||"An unexpected error has occurred";return(0,n.jsxs)("div",{style:c.error,children:[(0,n.jsx)(a.default,{children:(0,n.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,n.jsxs)("div",{style:c.desc,children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,n.jsx)("h1",{className:"next-error-h1",style:c.h1,children:e}):null,(0,n.jsx)("div",{style:c.wrap,children:(0,n.jsxs)("h2",{style:c.h2,children:[this.props.title||e?r:(0,n.jsxs)(n.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,n.jsxs)(n.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}u.displayName="ErrorPage",u.getInitialProps=l,u.origGetInitialProps=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9393:()=>{},9397:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},9579:(e,t,r)=>{"use strict";r.d(t,{Kw:()=>c,M:()=>u});var s=r(5155),n=r(1243),i=r(3904);let a=(0,r(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var o=r(5220),l=r(4907);function c(e){let{title:t="Something went wrong",message:r="An unexpected error occurred. Please try again.",onRetry:a,className:c}=e;return(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-col items-center justify-center p-8 text-center",c),children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-md",children:r}),a&&(0,s.jsxs)(o.$,{onClick:a,className:"flex items-center gap-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),"Try Again"]})]})}function u(e){let{title:t="Oops! Something went wrong",message:r="We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.",onRetry:l,showHomeButton:c=!0}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)("section",{className:"bg-gradient-to-br from-red-50 to-orange-100 py-16",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-4",children:"Error"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Something unexpected happened"})]})})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px]",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(n.A,{className:"h-12 w-12 text-red-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:t}),(0,s.jsx)("p",{className:"text-gray-600 text-center max-w-2xl mb-8",children:r}),(0,s.jsxs)("div",{className:"flex gap-4",children:[l&&(0,s.jsxs)(o.$,{onClick:l,className:"flex items-center gap-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),"Try Again"]}),c&&(0,s.jsxs)(o.$,{variant:"outline",onClick:()=>window.location.href="/",className:"flex items-center gap-2",children:[(0,s.jsx)(a,{className:"h-4 w-4"}),"Go Home"]})]})]})})]})}},9853:(e,t,r)=>{"use strict";r.d(t,{X:()=>o,k:()=>l});var s=r(2020),n=r(7165),i=r(6784),a=r(7948),o=class extends a.k{#$;#B;#V;#n;#A;#U;#Z;constructor(e){super(),this.#Z=!1,this.#U=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#n=e.client,this.#V=this.#n.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#$=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#$,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#A?.promise}setOptions(e){this.options={...this.#U,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#V.remove(this)}setData(e,t){let r=(0,s.pl)(this.state.data,e,this.options);return this.#T({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#T({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#A?.promise;return this.#A?.cancel(e),t?t.then(s.lQ).catch(s.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#$)}isActive(){return this.observers.some(e=>!1!==(0,s.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===(0,s.d2)(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!(0,s.j3)(this.state.dataUpdatedAt,e))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#A?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#A?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#V.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#A&&(this.#Z?this.#A.cancel({revert:!0}):this.#A.cancelRetry()),this.scheduleGc()),this.#V.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#T({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#A)return this.#A.continueRetry(),this.#A.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#Z=!0,r.signal)})},a=()=>{let e=(0,s.ZM)(this.options,t),r=(()=>{let e={client:this.#n,queryKey:this.queryKey,meta:this.meta};return n(e),e})();return(this.#Z=!1,this.options.persister)?this.options.persister(e,r,this):e(r)},o=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#n,state:this.state,fetchFn:a};return n(e),e})();this.options.behavior?.onFetch(o,this),this.#B=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==o.fetchOptions?.meta)&&this.#T({type:"fetch",meta:o.fetchOptions?.meta});let l=e=>{(0,i.wm)(e)&&e.silent||this.#T({type:"error",error:e}),(0,i.wm)(e)||(this.#V.config.onError?.(e,this),this.#V.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#A=(0,i.II)({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void l(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){l(e);return}this.#V.config.onSuccess?.(e,this),this.#V.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:l,onFail:(e,t)=>{this.#T({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#T({type:"pause"})},onContinue:()=>{this.#T({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#A.start()}#T(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...l(t.data,this.options),fetchMeta:e.meta??null};case"success":return this.#B=void 0,{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,i.wm)(r)&&r.revert&&this.#B)return{...this.#B,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),n.jG.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#V.notify({query:this,type:"updated",action:e})})}};function l(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,i.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},9916:(e,t,r)=>{"use strict";r.d(t,{GN:()=>y,Pb:()=>b});var s=r(7017),n=r(3504);function i(e){return e}function a(e){return e.state.isPaused}function o(e){return"success"===e.state.status}function l(e){return!0}var c=["added","removed","updated"];function u(e){return c.includes(e)}async function d({queryClient:e,persister:t,maxAge:r=864e5,buster:s="",hydrateOptions:a}){try{let o=await t.restoreClient();if(o)if(!o.timestamp)return t.removeClient();else{let l=Date.now()-o.timestamp>r,c=o.buster!==s;if(l||c)return t.removeClient();!function(e,t,r){if("object"!=typeof t||null===t)return;let s=e.getMutationCache(),a=e.getQueryCache(),o=r?.defaultOptions?.deserializeData??e.getDefaultOptions().hydrate?.deserializeData??i,l=t.mutations||[],c=t.queries||[];l.forEach(({state:t,...n})=>{s.build(e,{...e.getDefaultOptions().hydrate?.mutations,...r?.defaultOptions?.mutations,...n},t)}),c.forEach(({queryKey:t,state:s,queryHash:i,meta:l,promise:c,dehydratedAt:u})=>{let d=c?(0,n.b)(c):void 0,h=void 0===s.data?d?.data:s.data,p=void 0===h?h:o(h),f=a.get(i),m=f?.state.status==="pending",y=f?.state.fetchStatus==="fetching";if(f){let e=d&&void 0!==u&&u>f.state.dataUpdatedAt;if(s.dataUpdatedAt>f.state.dataUpdatedAt||e){let{fetchStatus:e,...t}=s;f.setState({...t,data:p})}}else f=a.build(e,{...e.getDefaultOptions().hydrate?.queries,...r?.defaultOptions?.queries,queryKey:t,queryHash:i,meta:l},{...s,data:p,fetchStatus:"idle",status:void 0!==p?"success":s.status});c&&!m&&!y&&(void 0===u||u>f.state.dataUpdatedAt)&&f.fetch(void 0,{initialPromise:Promise.resolve(c).then(o)})})}(e,o.clientState,a)}}catch(e){throw await t.removeClient(),e}}async function h({queryClient:e,persister:t,buster:r="",dehydrateOptions:s}){let n={buster:r,timestamp:Date.now(),clientState:function(e,t={}){let r=t.shouldDehydrateMutation??e.getDefaultOptions().dehydrate?.shouldDehydrateMutation??a,s=e.getMutationCache().getAll().flatMap(e=>r(e)?[{mutationKey:e.options.mutationKey,state:e.state,...e.options.scope&&{scope:e.options.scope},...e.meta&&{meta:e.meta}}]:[]),n=t.shouldDehydrateQuery??e.getDefaultOptions().dehydrate?.shouldDehydrateQuery??o,c=t.shouldRedactErrors??e.getDefaultOptions().dehydrate?.shouldRedactErrors??l,u=t.serializeData??e.getDefaultOptions().dehydrate?.serializeData??i;return{mutations:s,queries:e.getQueryCache().getAll().flatMap(e=>n(e)?[{dehydratedAt:Date.now(),state:{...e.state,...void 0!==e.state.data&&{data:u(e.state.data)}},queryKey:e.queryKey,queryHash:e.queryHash,..."pending"===e.state.status&&{promise:e.promise?.then(u).catch(e=>c(e)?Promise.reject(Error("redacted")):Promise.reject(e))},...e.meta&&{meta:e.meta}}]:[])}}(e,s)};await t.persistClient(n)}function p(){}var f=r(3962);let m=new s.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<1,onError:e=>{console.error("Mutation error:",e),f.tR.addNotification({type:"error",title:"Operation Failed",message:(null==e?void 0:e.message)||"An unexpected error occurred"})}}}});!function(e){let t,r=!1;()=>{r=!0,t?.()},d(e).then(()=>{r||(t=function(e){let t=e.queryClient.getQueryCache().subscribe(t=>{u(t.type)&&h(e)}),r=e.queryClient.getMutationCache().subscribe(t=>{u(t.type)&&h(e)});return()=>{t(),r()}}(e))})}({queryClient:m,persister:function({storage:e,key:t="REACT_QUERY_OFFLINE_CACHE",throttleTime:r=1e3,serialize:s=JSON.stringify,deserialize:n=JSON.parse,retry:i}){if(e){let a=r=>{try{e.setItem(t,s(r));return}catch(e){return e}};return{persistClient:function(e,t=100){let r,s=null;return function(...n){r=n,null===s&&(s=setTimeout(()=>{e(...r),s=null},t))}}(e=>{let t=e,r=a(t),s=0;for(;r&&t;)s++,(t=i?.({persistedClient:t,error:r,errorCount:s}))&&(r=a(t))},r),restoreClient:()=>{let r=e.getItem(t);if(r)return n(r)},removeClient:()=>{e.removeItem(t)}}}return{persistClient:p,restoreClient:p,removeClient:p}}({storage:window.localStorage,key:"ai-fitness-query-cache",serialize:JSON.stringify,deserialize:JSON.parse}),maxAge:864e5,buster:"1.0.0"});let y={invalidateEntity:e=>{m.invalidateQueries({queryKey:[e]})},removeEntity:(e,t)=>{t?m.removeQueries({queryKey:[e,t]}):m.removeQueries({queryKey:[e]})},prefetch:async(e,t)=>{await m.prefetchQuery({queryKey:e,queryFn:t,staleTime:6e5})},setQueryData:(e,t)=>{m.setQueryData(e,t)},getQueryData:e=>m.getQueryData(e),clearAll:()=>{m.clear()},resetQueries:e=>{e?m.resetQueries({queryKey:e}):m.resetQueries()},cancelQueries:e=>{e?m.cancelQueries({queryKey:e}):m.cancelQueries()}},g={auth:{all:["auth"],session:()=>[...g.auth.all,"session"],user:()=>[...g.auth.all,"user"]},exercises:{all:["exercises"],lists:()=>[...g.exercises.all,"list"],list:e=>[...g.exercises.lists(),e],details:()=>[...g.exercises.all,"detail"],detail:e=>[...g.exercises.details(),e],search:e=>[...g.exercises.all,"search",e],attributes:()=>[...g.exercises.all,"attributes"]},workouts:{all:["workouts"],sessions:()=>[...g.workouts.all,"sessions"],session:e=>[...g.workouts.sessions(),e],programs:()=>[...g.workouts.all,"programs"],program:e=>[...g.workouts.programs(),e],history:e=>[...g.workouts.all,"history",e],stats:e=>[...g.workouts.all,"stats",e]},progress:{all:["progress"],records:()=>[...g.progress.all,"records"],record:e=>[...g.progress.records(),e],stats:e=>[...g.progress.all,"stats",e],goals:()=>[...g.progress.all,"goals"],achievements:()=>[...g.progress.all,"achievements"],calendar:(e,t)=>[...g.progress.all,"calendar",e,t]},user:{all:["user"],profile:()=>[...g.user.all,"profile"],preferences:()=>[...g.user.all,"preferences"],subscription:()=>[...g.user.all,"subscription"]}},b={isOnline:()=>"undefined"==typeof navigator||navigator.onLine,setupNetworkListeners:()=>{let e=()=>{f.tR.setOnlineStatus(!0),m.refetchQueries()},t=()=>{f.tR.setOnlineStatus(!1)};return window.addEventListener("online",e),window.addEventListener("offline",t),f.tR.setOnlineStatus(navigator.onLine),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},queueOfflineMutation:(e,t,r)=>{f.tR.addPendingSync({type:e,action:t,data:r})}};m.getQueryCache().subscribe(e=>{e.type}),m.getMutationCache().subscribe(e=>{"updated"===e.type&&"success"===e.mutation.state.status&&f.tR.addNotification({type:"success",title:"Success",message:"Operation completed successfully"})})},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:u="",children:d,iconNode:h,...p}=e;return(0,s.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:r,strokeWidth:a?24*Number(i)/Number(n):i,className:o("lucide",u),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{className:l,...c}=r;return(0,s.createElement)(u,{ref:i,iconNode:t,className:o("lucide-".concat(n(a(e))),"lucide-".concat(e),l),...c})});return r.displayName=a(e),r}},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return s},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function s(e){let t,r=!1;return function(){for(var s=arguments.length,n=Array(s),i=0;i<s;i++)n[i]=arguments[i];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>n.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let s=await e.getInitialProps(t);if(r&&c(r))return s;if(!s)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class m extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);