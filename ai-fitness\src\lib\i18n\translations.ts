// Translation utilities
import { Locale } from './index';
import enMessages from '../../../messages/en.json';
import zhMessages from '../../../messages/zh.json';

const messages = {
  en: enMessages,
  zh: zhMessages,
};

export function getMessages(locale: Locale) {
  return messages[locale] || messages.en;
}

// Simple translation function
export function t(key: string, locale: Locale, params?: Record<string, string | number>): string {
  const msgs = getMessages(locale);
  
  // Navigate through nested object using dot notation
  const keys = key.split('.');
  let value: any = msgs;
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      console.warn(`Translation key not found: ${key}`);
      return key; // Return key as fallback
    }
  }
  
  if (typeof value !== 'string') {
    console.warn(`Translation value is not a string: ${key}`);
    return key;
  }
  
  // Replace parameters if provided
  if (params) {
    return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
      return params[paramKey]?.toString() || match;
    });
  }
  
  return value;
}

// Hook for client components
export function useTranslations(locale: Locale) {
  return (key: string, params?: Record<string, string | number>) => t(key, locale, params);
}

// Server function for getting translations
export function getTranslations(locale: Locale) {
  return (key: string, params?: Record<string, string | number>) => t(key, locale, params);
}
